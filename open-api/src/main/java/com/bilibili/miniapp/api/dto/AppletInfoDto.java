package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppletInfoDto implements Serializable {

    private static final long serialVersionUID = 6407130954767192932L;

    private Long avid;

    private Boolean isMiniProgram;

    private String jumpUrl;

    private String errorInfo;

    private String appId;

    private Long seasonId;

    private Long episodeId;
}
