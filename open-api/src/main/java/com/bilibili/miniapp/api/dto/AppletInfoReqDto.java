package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppletInfoReqDto implements Serializable {
    private static final long serialVersionUID = 5760245325691537309L;

    private String appId;
//    private Integer appletType;
    private Long seasonId;

    /**
     * 是否返回ep
     */
    private boolean appendEpisode;
    /**
     * 是否返回ep的稿件宽高
     * 如果为true，则前提是{@link #appendEpisode} = true才生效
     */
    private boolean appendVideoDimension;
    /**
     * 是否返回作者详细信息（头像和昵称）
     */
    private boolean appendAuthorDetail;
}
