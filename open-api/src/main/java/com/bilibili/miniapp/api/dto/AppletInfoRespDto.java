package com.bilibili.miniapp.api.dto;

import com.bilibili.miniapp.api.dto.applet.season.AppletAuthor;
import com.bilibili.miniapp.api.dto.applet.season.AppletSeasonDto;
import com.bilibili.miniapp.api.dto.applet.season.SectionDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppletInfoRespDto implements Serializable {

    private static final long serialVersionUID = 3174784525788011654L;

    /**
     * 小程序appid
     */
    private String appId;

    /**
     * 小程序名称
     */
    private String title;

    /**
     * 小程序icon地址
     */
    private String icon;

    /**
     * 小程序基础链接
     * <a href="https://miniapp.bilibili.com/applet/bili60acc738c0e02ca1">老版本框架</a>
     * <a href="https://miniapp.bilibili.com/appletx/bili60acc738c0e02ca1">新版本框架</a>
     */
    private String appletBaseUrl;

    private Integer appletVersion;

    private String customizedPath;

    private Map<String, Object> customizedParams;

//    /**
//     * 小程序类型
//     * @see com.bilibili.miniapp.api.dto.MiniAppType
//     */
//    private Integer appletType;

    private AppletSeasonDto season;
}
