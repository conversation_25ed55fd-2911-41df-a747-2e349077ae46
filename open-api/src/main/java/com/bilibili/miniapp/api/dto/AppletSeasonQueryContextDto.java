package com.bilibili.miniapp.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/20
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppletSeasonQueryContextDto implements Serializable {

    private static final long serialVersionUID = 3013805877776024929L;
    private List<Long> aids;

    @JSONField(name = "source_from")
    private Integer sourceFrom;
}
