package com.bilibili.miniapp.api.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
public class CompanyAdmissionAuditInfoDto {
    private Long id;
    private List<String> additionalMaterials;
    private String businessLicense;
    private String companyName;
    private String contactEmail;
    private String creditCode;
    private String officialWebsite;
    private String operatorName;
    private String phoneNumber;
    private Long mid;
    private String createTime;
    private Integer auditStatus;
    private String failReason;
}
