package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpBeiAnResultDto {
    private List<BeiAnStatusDto> beiAnStatus;
    private List<AuditStatusDto> auditStatus;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class BeiAnStatusDto {
        private Long flowId;
        // 结果代码 9001-业务进入待短信核验状态 9002-业务进入待人工审核状态
        private Long jgdm;
        // 结果代码描述
        private String dmms;
        // 操作类型 1-新增备案 2-新增互联网信息服务 3-新增接入
        private Integer czlx;
        // 备注
        private String bz;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class AuditStatusDto {
        private Long flowId;
        // 审核人姓名
        private String shrxm;
        // 审核人电话号码
        private String shrDhhm;
        // 审核时间 yyyy-MM-dd HH:mm:ss
        private String shsj;
        // 审核意见
        private String shyj;
        // 审核结果 0-审核拒绝 1-审核通过
        private Integer shjg;
        // 操作类型 1-新增备案 2-新增互联网信息服务 3-新增接入
        private Integer czlx;
    }
}