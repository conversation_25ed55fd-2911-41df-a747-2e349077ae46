package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 备案基础代码
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpConfigDto {

    private List<IcpDocumentTypeDto> documentTypeList;
    private List<IcpAreaCodeDto> areaCodeList;
    private List<IcpOrgTypeDto> orgTypeList;
    private List<IcpAppServiceTypeDto> appServiceTypeList;


    /**
     * 证件类型
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpDocumentTypeDto {
        private Long code;
        private String name;
        private Long orgCode;
        private Integer status;
    }

    /**
     * 区域列表
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpAreaCodeDto {
        private Long code;
        private String name;
        private Integer type;
        private Integer status;
    }

    /**
     * 单位性质
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpOrgTypeDto {
        private Long code;
        private String name;
        private Integer status;
    }

    /**
     * APP服务类型
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpAppServiceTypeDto {
        private Long code;
        private String name;
        private Long parentCode;
        private Integer status;
    }
}
