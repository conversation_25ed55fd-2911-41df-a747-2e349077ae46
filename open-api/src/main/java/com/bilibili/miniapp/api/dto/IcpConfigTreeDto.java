package com.bilibili.miniapp.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IcpConfigTreeDto {
    private Long value;
    private String label;
    private List<IcpConfigTreeDto> children = new ArrayList<>();
}