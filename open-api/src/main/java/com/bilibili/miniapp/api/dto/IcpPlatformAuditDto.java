package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IcpPlatformAuditDto {
    // 审核流程id
    private Long flowId;
    // 审核人
    private String operator;
    // 1-审核通过 2-审核不通过
    private Integer auditResult;
    // 审核不通过原因
    private List<IcpPlatformAuditDetailDto> failReason;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IcpPlatformAuditDetailDto {
        // 未通过字段
        private Integer fieldObj;
        // 审核未通过字段
        private String fieldName;
        // 未通过原因
        private String reason;
        // 未通过字段名称
        private String name;
    }
}
