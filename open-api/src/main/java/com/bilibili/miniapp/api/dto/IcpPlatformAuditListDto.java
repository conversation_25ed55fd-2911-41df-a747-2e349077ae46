package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IcpPlatformAuditListDto {

    private Long id;

    private Long flowId;

    private String appName;

    private String appLogo;

    private String appId;

    private String companyName;

    private Long submitTime;

    private Integer auditStatus;

    private String operator;

    private Long auditTime;
}
