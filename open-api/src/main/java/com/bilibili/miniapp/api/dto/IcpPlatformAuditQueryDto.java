package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IcpPlatformAuditQueryDto {

    // 模糊匹配
    private String appName;

    private List<String> appId;

    // 模糊匹配
    private String companyName;

    private Integer auditStatus;

    private Integer pageNum;

    private Integer pageSize;
}
