package com.bilibili.miniapp.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/27
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IcpRemoteConfigDto {
    private List<IcpConfigTreeDto> areaCode;
    private List<IcpConfigTreeDto> orgType;
    private List<IcpConfigTreeDto> serviceType;
}
