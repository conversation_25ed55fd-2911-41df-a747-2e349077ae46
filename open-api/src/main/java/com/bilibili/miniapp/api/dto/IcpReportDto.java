package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IcpReportDto {

    private String appId;
    private IcpCompanyDto company;
    private IcpAppDto app;
    private List<IcpAttachmentDto> attachment;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IcpCompanyDto {
        private Integer type;
        private Integer licenseType;
        private String licensePhoto;
        private String name;
        private String licenseNo;
        private Integer licenseProvince;
        private Integer licenseCity;
        private Integer licenseCounty;
        private String licenseDetailAddress;
        private Integer contactProvince;
        private Integer contactCity;
        private Integer contactCounty;
        private String contactDetailAddress;
        private String remark;
        private Integer fzrLicenseType;
        private String fzrCardNo;
        private String fzrName;
        private String fzrCardFront;
        private String fzrCardReverse;
        private Long fzrCardBegin;
        private Long fzrCardEnd;
        private Integer fzrCardLongEffect;
        private String fzrPhone;
        private String fzrEmail;
        private String fzrEmergency;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IcpAppDto {
        private String name;
        private String appId;
        private Integer serviceType;
        private Integer approvalPre;
        private Integer approvalType;
        private String approvalIsbnNo;
        private List<String> approvalAttachment;
        private String remark;
        private Integer fzrLicenseType;
        private String verifyPhoto;
        private String fzrCard;
        private String fzrCardNo;
        private String fzrName;
        private Long fzrCardBegin;
        private Long fzrCardEnd;
        private Integer fzrCardLongEffect;
        private String fzrPhone;
        private String fzrEmail;
        private String fzrEmergency;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IcpAttachmentDto {
        private Integer type;
        private Integer format;
        private String content;
    }
}