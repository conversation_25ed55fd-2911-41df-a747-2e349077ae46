package com.bilibili.miniapp.api.dto;

import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IcpReportStatusDto {

    /**
     * 流程id
     */
    private Long flowId;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * @see IcpFlowStatusEnum
     * 备案流程状态
     */
    private Integer status;

    /**
     * 备案号 备案完成才有
     */
    private String recordNumber;

    /**
     * 平台审核失败原因
     */
    private List<IcpPlatformAuditDetailDto> platformFailReason;

    /**
     * 管局审核失败原因
     */
    private String govFailReason;

    /**
     * 备案审核通过时间
     */
    private Long auditPassTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IcpPlatformAuditDetailDto {

        private Integer fieldObj;

        /**
         * 审核未通过字段
         */
        private String fieldName;

        /**
         * 未通过原因
         */
        private String reason;

        /**
         * 未通过字段名称
         */
        private String name;
    }
}
