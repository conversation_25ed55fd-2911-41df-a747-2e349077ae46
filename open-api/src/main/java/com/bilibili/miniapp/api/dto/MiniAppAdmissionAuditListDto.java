package com.bilibili.miniapp.api.dto;

import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import lombok.Data;

/**
 * 列表对象
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
public class MiniAppAdmissionAuditListDto {

    private String appId;

    private Long id;

    private String appName;

    private String createTime;

    /**
     * @see AppCompanyAuditStatus
     */
    private Integer auditStatus;

}
