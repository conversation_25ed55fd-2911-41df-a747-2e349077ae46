package com.bilibili.miniapp.api.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/8
 */
@Data
public class MiniAppAuditInfoDto {
    //private String admissionId;
    private String appDescription;
    private String appId;
    private String appLogo;
    private String appName;
    private List<String> categoryCertifications;
    /**
     * @see com.bilibili.miniapp.open.common.enums.CategoryType
     */
    private String categoryName;
}
