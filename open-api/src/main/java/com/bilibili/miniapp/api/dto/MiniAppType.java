package com.bilibili.miniapp.api.dto;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/20
 **/

@Getter
public enum MiniAppType {

    SHORT(1, "短剧"),
    ;

    final Integer code;
    final String desc;
    MiniAppType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MiniAppType of(Integer code) {
        for (MiniAppType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown MiniAppType code: " + code);
    }
}
