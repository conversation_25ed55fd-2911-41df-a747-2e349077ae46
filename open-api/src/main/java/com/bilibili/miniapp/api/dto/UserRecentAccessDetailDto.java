package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/27
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserRecentAccessDetailDto implements Serializable {

    private static final long serialVersionUID = -1L;

    private String appId;

    private Integer type;

    /**
     * 图标
     */
    private String icon;

    /**
     * 跳转链接，会根据用户上传小程序包判断使用的是新框架还是老框架
     */
    private String linkUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 简介
     */
    private String summary;

    /**
     * 最近访问时间
     */
    private Long recentVisitTime;
}
