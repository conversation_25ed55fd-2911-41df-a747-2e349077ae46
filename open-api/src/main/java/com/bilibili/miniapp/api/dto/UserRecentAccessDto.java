package com.bilibili.miniapp.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/27
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserRecentAccessDto implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 是否展示更多
     */
    private boolean showMore;

    /**
     * 「全部」落地页的跳转链接
     */
    private String moreLinkUrl;

    private Integer pageNum;

    private Integer pageSize;

    private List<UserRecentAccessDetailDto> list;
}
