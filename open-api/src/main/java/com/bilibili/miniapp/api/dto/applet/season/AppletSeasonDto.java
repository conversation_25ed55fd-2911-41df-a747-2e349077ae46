package com.bilibili.miniapp.api.dto.applet.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppletSeasonDto implements Serializable {

    private static final long serialVersionUID = 7737449465082947896L;

    //剧id
    private Long seasonId;
    //剧封面
    private String cover;
    //剧标题
    private String title;
    //剧子标题
    private String subTitle;
    //媒资风格
    private List<String> styles;
    //是否已完结，1：已完结，0：未完结，-1：未知
    private Integer isFinish;
    //总集数
    private Integer epCount;

    private AppletAuthor author;

    private List<SectionDto> sections;

}
