package com.bilibili.miniapp.api.dto.applet.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EpisodeDto implements Serializable {
    private static final long serialVersionUID = -2781462050314685148L;
    // 剧id
    private Long seasonId;
    // 集id
    private Long episodeId;
    // 节id
    private Long sectionId;
    //集顺序，比如第1集，第2集
    private Integer ord;
    //这个比较随意，比如第1集，也可能是第一集，也可能是首集，主要看投稿怎么设置
    private String title;
    //类似于集描述，展示在标题的右侧
    private String longTitle;
    //集封面
    private String cover;
    // 稿件avid
    private Long aid;
    private Long cid;
    private Integer width;
    private Integer height;
}
