package com.bilibili.miniapp.api.dto.applet.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SectionDto implements Serializable {
    private static final long serialVersionUID = -6242461419844591432L;

    // 分节id，全局唯一
    private Long sectionId;
    /**
     * 分节类型
     *
     * @see com.bilibili.miniapp.open.common.enums.OgvSectionType
     */
    private Integer sectionType;

    private List<EpisodeDto> episodes;
}
