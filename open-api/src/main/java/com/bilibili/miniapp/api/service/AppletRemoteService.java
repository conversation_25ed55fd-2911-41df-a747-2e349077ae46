package com.bilibili.miniapp.api.service;

import com.bilibili.miniapp.api.dto.AppletInfoDto;
import com.bilibili.miniapp.api.dto.AppletInfoReqDto;
import com.bilibili.miniapp.api.dto.AppletInfoRespDto;
import com.bilibili.miniapp.api.dto.AppletSeasonQueryContextDto;
import com.bilibili.miniapp.open.common.entity.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

public interface AppletRemoteService {

    /**
     * 获取短剧小程序信息
     * @param context
     * @return
     */
    Response<List<AppletInfoDto>> batchGetShortAppletInfo(AppletSeasonQueryContextDto context);


    /**
     * 获取短剧小程序信息
     * @param appletInfoReqDto
     * @return
     */
    Response<AppletInfoRespDto> getAppletInfo(AppletInfoReqDto appletInfoReqDto) throws Exception;

}
