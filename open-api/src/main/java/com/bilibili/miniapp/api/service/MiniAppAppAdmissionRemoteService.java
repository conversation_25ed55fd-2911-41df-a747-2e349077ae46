package com.bilibili.miniapp.api.service;

import com.bilibili.miniapp.api.dto.*;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.common.enums.MiniAppModifyType;

/**
 * 运营后台审核接口
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface MiniAppAppAdmissionRemoteService {

    Response<PageResult<MiniAppAdmissionAuditListDto>> queryAdmissionAuditList(String appId,
                                                                               Integer auditStatus,
                                                                               Integer type,
                                                                               Integer page,
                                                                               Integer size);

    Response<MiniAppAuditDetailDto> queryAdmissionAuditDetail(Long id);

    Response<Void> passOrRejectAdmission(AdmissionAuditReqDto auditReqDto);

}