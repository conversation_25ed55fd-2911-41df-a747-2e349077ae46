package com.bilibili.miniapp.api.service;

import com.bilibili.miniapp.api.dto.CompanyAdmissionAuditListDto;
import com.bilibili.miniapp.api.dto.CompanyAdmissionAuditInfoDto;
import com.bilibili.miniapp.api.dto.AdmissionAuditReqDto;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;

/**
 * 运营后台审核接口
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface MiniAppCompanyAdmissionRemoteService {


    Response<PageResult<CompanyAdmissionAuditListDto>> queryAdmissionAuditList(String companyName,
                                                                               Integer auditStatus,
                                                                               Integer page,
                                                                               Integer size);

    Response<CompanyAdmissionAuditInfoDto> queryAdmissionAuditDetail(Long id);

    Response<Void> passOrRejectAdmission(AdmissionAuditReqDto auditReqDto);

}