package com.bilibili.miniapp.api.service;

import com.bilibili.miniapp.api.dto.*;
import com.bilibili.miniapp.api.dto.IcpRemoteConfigDto;
import com.bilibili.miniapp.open.common.entity.Response;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/
public interface MiniAppIcpAuditRemoteService {

    Response<IcpReportDto> getIcpReportInfo(Long flowId);

    Response<Void> handleIcpAuditResult(IcpPlatformAuditDto auditDto);

    Response<PageInfo<IcpPlatformAuditListDto>> queryIcpAuditList(IcpPlatformAuditQueryDto queryDto);

    Response<IcpReportStatusDto> getIcpReportStatus(String appId);

    Response<IcpRemoteConfigDto> getIcpConfig();
}
