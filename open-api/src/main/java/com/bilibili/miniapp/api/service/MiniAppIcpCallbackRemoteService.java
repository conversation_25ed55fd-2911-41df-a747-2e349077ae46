package com.bilibili.miniapp.api.service;

import com.bilibili.miniapp.api.dto.IcpBeiAnResultDto;
import com.bilibili.miniapp.api.dto.IcpConfigDto;
import com.bilibili.miniapp.open.common.entity.Response;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/
public interface MiniAppIcpCallbackRemoteService {

    /**
     * 获取备案配置
     *
     * @return 备案配置
     */
    Response<Void> handleIcpConfig(IcpConfigDto icpConfigDto);

    Response<Void> handleIcpBeiAnResult(IcpBeiAnResultDto icpBeiAnResultDto);
}
