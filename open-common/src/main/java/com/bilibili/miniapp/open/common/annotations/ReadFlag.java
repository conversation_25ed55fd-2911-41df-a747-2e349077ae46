package com.bilibili.miniapp.open.common.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025/1/8 17:23
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ReadFlag {
    F[] value() default {};

    enum F {
        DB,
        REDIS,
        LOCAL_CACHE
    }
}