package com.bilibili.miniapp.open.common.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 1、不要用在非基本类型（含包装类型）的集合属性上如Collection和Map之类的
 * <p>
 * 2、不要用在非基本类型（含包装类型）的数组属性
 * <p>
 *
 * <AUTHOR>
 * @date 2025/01/06 11:37
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface Sign {
    /**
     * 签名的参数key
     * <p>
     * 比如某字段myName=yakin，但是在计算签名时使用的是my_name=yakin，因此可以指定key=my_name
     */
    String key() default "";
}
