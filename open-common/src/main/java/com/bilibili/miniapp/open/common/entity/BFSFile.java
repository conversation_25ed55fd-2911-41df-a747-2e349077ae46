package com.bilibili.miniapp.open.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.InputStream;
import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BFSFile implements Serializable {

    private static final long serialVersionUID = -4698239755137016327L;
    private long size;
    private String mimeType;
    private String fileName;
    private byte[] bytes;
    private InputStream inputStream;

}
