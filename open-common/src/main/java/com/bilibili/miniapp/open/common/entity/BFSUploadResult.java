package com.bilibili.miniapp.open.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class BFSUploadResult implements Serializable {
    private static final long serialVersionUID = 4059939157178446773L;
    private String url;
    private String hashCode;
    private String md5;
    private String token;
}
