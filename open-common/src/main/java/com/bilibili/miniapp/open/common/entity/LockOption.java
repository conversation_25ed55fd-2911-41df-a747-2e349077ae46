package com.bilibili.miniapp.open.common.entity;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/15 19:22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class LockOption {
    //wait time to acquire
    private long waitTime;
    //lease time after acquired
    private long leaseTime;
    private TimeUnit unit;
    private String errMsg;

    public static final LockOption DEFAULT_OPTION = LockOption.builder()
            .waitTime(15)
            .leaseTime(30)
            .unit(TimeUnit.SECONDS)
            .errMsg(ErrorCodeType.CONCURRENT_OPERATE.getMessage())
            .build();
}
