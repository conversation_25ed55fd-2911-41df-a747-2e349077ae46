package com.bilibili.miniapp.open.common.entity;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/16
 **/
public class PatternConstant {

    // 小程序名称 支持大小写字母、数字和中文，字符长度为4～24个，一个汉子对应两个字符
//    public static final Pattern MINI_APP_NAME_PATTERN = Pattern.compile("^(?=(?:[^\\x00-\\xff]*[\\x00-\\xff]){0,24}$)[a-zA-Z0-9\\u4e00-\\u9fa5]{4,24}$");

    // 定义允许的字符模式：大小写字母、数字、中文
    public static final Pattern ALLOWED_CHARS_PATTERN = Pattern.compile("^[a-zA-Z0-9\\u4e00-\\u9fa5]+$");
}
