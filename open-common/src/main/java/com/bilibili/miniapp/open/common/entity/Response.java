package com.bilibili.miniapp.open.common.entity;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@ToString
@Data
@NoArgsConstructor
public class Response<E> {
    private E data;
    private Integer code;
    private String message;
    private long current_time;

    private Response(E data, Integer code, String message) {
        this.data = data;
        this.code = code;
        this.message = message;
    }

    public static <E> Response<E> SUCCESS() {
        return SUCCESS(null);
    }

    public static <E> Response<E> SUCCESS(E result) {
        Response<E> response = new Response<>(result, ErrorCodeType.SUCCESS.getCode(), ErrorCodeType.SUCCESS.getMessage());
        response.setCurrent_time(System.currentTimeMillis());
        return response;
    }

    public static <E> Response<E> FAIL(Integer error_code, String error_msg) {
        return new Response<>(null, error_code, error_msg);
    }

    public static <E> Response<E> FAIL(String error_msg) {
        return new Response<>(null, ErrorCodeType.BAD_REQUEST.getCode(), error_msg);
    }

    public static <E> Response<E> FAIL(ErrorCodeType errorCodeType) {
        return new Response<>(null, errorCodeType.getCode(), errorCodeType.getMessage());
    }

    public static <E> Response<E> FAIL(ErrorCodeType errorCodeType, String errorMsg) {
        return new Response<>(null, errorCodeType.getCode(), String.format("%s：%s", errorCodeType.getMessage(), errorMsg));
    }
}
