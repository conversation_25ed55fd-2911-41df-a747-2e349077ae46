package com.bilibili.miniapp.open.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <a href="https://blog.csdn.net/problc/article/details/143199679">各种加密算法简单区别</a>
 *
 * <AUTHOR>
 * @date 2025/1/13 20:41
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SecretKeyPair {
    /**
     * 密钥
     *
     * @see javax.crypto.SecretKey
     */
    private String key;
    /**
     * 随机向量
     *
     * @see javax.crypto.spec.IvParameterSpec
     */
    private String iv;
}
