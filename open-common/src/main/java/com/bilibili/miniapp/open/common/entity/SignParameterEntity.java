package com.bilibili.miniapp.open.common.entity;

import com.bilibili.miniapp.open.common.annotations.Sign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 需要签名的参数
 *
 * <AUTHOR>
 * @date 2025/1/10 10:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignParameterEntity {
    /**
     * 参数1：一般是请求参数，或者上游定制的特定签名参数
     */
    private Map<String, Object> param1;

    /**
     * 参数2：一般是一个复合的实体，通常结合{@link Sign}注解搭配使用
     *
     * @see Sign
     */
    private Object param2;

    /**
     * 用于签名的时间戳（单位毫秒），必传
     */
    private long ts;
}
