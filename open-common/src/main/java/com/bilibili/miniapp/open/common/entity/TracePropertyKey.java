package com.bilibili.miniapp.open.common.entity;

/**
 * <AUTHOR>
 * @date 2025/1/16 20:43
 */
public final class TracePropertyKey {
    //投放账号id
    public static final String ACCOUNT_ID = "account_id";
    //合集id（剧id）
    public static final String ROUTER_ALBUM_ID = "router_albumId";
    //集id
    public static final String ROUTER_EPISODE_ID = "router_episodeId";
    //计划id
    public static final String CAMPAIGN_ID = "campaign_id";
    //创意id
    public static final String CREATIVE_ID = "creative_id";
    //单元id
    public static final String UNIT_ID = "unit_id";
    //前端埋点
    public static final String FROM = "from";
    //前端埋点
    public static final String FROM_SPMID = "from_spmid";
    //归因埋点（和资源位相关）
    public static final String SOURCE_FROM = "sourcefrom";
    //归因
    public static final String TRACK_ID = "track_id";
}
