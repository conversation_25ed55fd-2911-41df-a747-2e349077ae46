package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@Getter
@AllArgsConstructor
public enum AppCompanyAuditStatus {

    PENDING(0, "待审核"),
    PASSED(1, "审核通过"),
    REJECTED(2, "审核不通过");

    private final int code;
    private final String desc;


    public static AppCompanyAuditStatus getByCode(Integer code) {
        for (AppCompanyAuditStatus auditStatus : AppCompanyAuditStatus.values()) {
            if (Objects.equals(code, auditStatus.getCode())) {
                return auditStatus;
            }
        }
        return null;
    }
}
