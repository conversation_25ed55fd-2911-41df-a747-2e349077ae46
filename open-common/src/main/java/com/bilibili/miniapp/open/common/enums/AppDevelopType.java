package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@AllArgsConstructor
@Getter
public enum AppDevelopType {

    DEFAULT(0, "默认"),
    TEMPLATE(1, "模板");

    private final int code;
    private final String desc;

    public static AppDevelopType getByCode(int code) {
        for (AppDevelopType appDevelopType : values()) {
            if (appDevelopType.getCode() == code) {
                return appDevelopType;
            }
        }
        return DEFAULT;
    }
}
