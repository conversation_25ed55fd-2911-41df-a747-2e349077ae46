package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.util.EnvUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 临时使用，后续拓展类目时废弃，使用mini_app_cat表接入category 列表能力
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
@Getter
@AllArgsConstructor
public enum CategoryType {

    UNKNOWN(0, 0, 0, ""),

    NOVEL(1, 254, 254, "文娱/网络小说"),

    VIDEO(2, 255, 255, "文娱/长视频"),

    SHORT_DRAMA(3, 10077, 10540, "文娱/微短剧"),
    ;
    private final int code;

    private final int uatMappingId;

    private final int prodMappingId;

    private final String desc;

    public static int getMappingId(int code) {
        CategoryType categoryType = getByCode(code);
        if (categoryType == null) {
            return 0;
        }
        return EnvUtil.isProdOrPre() ? categoryType.getProdMappingId() : categoryType.getUatMappingId();
    }

    public static CategoryType getByMappingId(int mappingId) {
        for (CategoryType categoryType : values()) {
            if (EnvUtil.isProdOrPre() ? categoryType.getProdMappingId() == mappingId : categoryType.getUatMappingId() == mappingId) {
                return categoryType;
            }
        }
        return UNKNOWN;
    }

    public static CategoryType getByCode(int code) {
        for (CategoryType categoryType : values()) {
            if (categoryType.getCode() == code) {
                return categoryType;
            }
        }
        return UNKNOWN;
    }
}