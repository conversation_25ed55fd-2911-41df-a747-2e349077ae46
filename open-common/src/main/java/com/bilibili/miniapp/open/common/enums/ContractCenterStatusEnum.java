package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同中台服务状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Getter
@AllArgsConstructor
public enum ContractCenterStatusEnum {

    PENDING_REVIEW(101, "待复审"),
    REJECTED(2, "复审失败"),
    PENDING_SIGNATURE(100, "复审成功待签署"),
    SIGNED(4, "签署成功"),
    ;

    private final int code;
    private final String desc;

    public static ContractCenterStatusEnum getByCode(Integer code) {
        for (ContractCenterStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}