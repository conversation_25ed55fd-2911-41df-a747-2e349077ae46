package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28
 **/

@Getter
@AllArgsConstructor
public enum IcpApproveTypeEnum {

    ICP_APPROVE_TYPE_PUBLISH(1, "出版", 2, true),
    ICP_APPROVE_TYPE_MEDICAL(2, "药品和医疗器械", 5, true),
    ICP_APPROVE_TYPE_CULTURE(3, "文化", 9, true),
    ICP_APPROVE_TYPE_BROADCAST(4, "广播电影电视节目", 10, true),
    ICP_APPROVE_TYPE_NEWS(5, "新闻", 1, true),
    ICP_APPROVE_TYPE_CAR(6, "网络预约车", 12, true),
    ICP_APPROVE_TYPE_FINANCE(7, "互联网金融", 13, true),
    ICP_APPROVE_TYPE_TRAINING(8, "校外培训（高中及以下阶段）", 14, true),
    ICP_APPROVE_TYPE_RELIGION(9, "宗教", 15, true),
    ICP_APPROVE_TYPE_EDUCATION(10, "教育", 3, true),
    ICP_APPROVE_TYPE_HEALTH(11, "医疗保健", 4, true);

    private final Integer type;
    private final String desc;
    private final Integer serviceType;
    private final Boolean needApproval;

    public static IcpApproveTypeEnum getByType(Integer type) {
        for (IcpApproveTypeEnum value : IcpApproveTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}
