package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/19
 **/

@AllArgsConstructor
@Getter
public enum IcpFlowStatusEnum {

    // 0-尚未备案，1-平台审核中，2-平台审核未通过，3-工信部短信核验中，4-工信部短信核验超时，5-管局审核中，6-管局审核未通过，7-备案已完成
    ICP_STATUS_NOT_REGISTER(0, "尚未备案"),
    ICP_STATUS_PLATFORM_AUDIT(1, "平台审核中"),
    ICP_STATUS_PLATFORM_AUDIT_FAIL(2, "平台审核未通过"),
    ICP_STATUS_GOV_SMS_VERIFY(3, "工信部短信核验中"),
    ICP_STATUS_GOV_SMS_VERIFY_TIMEOUT(4, "工信部短信核验超时"),
    ICP_STATUS_GOV_AUDIT(5, "管局审核中"),
    ICP_STATUS_GOV_AUDIT_FAIL(6, "管局审核未通过"),
    ICP_STATUS_REGISTER_SUCCESS(7, "备案已完成"),
    ;

    private final Integer status;
    private final String desc;

    public static List<Integer> canEditStatus() {
        return List.of(ICP_STATUS_PLATFORM_AUDIT_FAIL.status, ICP_STATUS_GOV_AUDIT_FAIL.status);
    }

    public static IcpFlowStatusEnum getByStatus(Integer status) {
        for (IcpFlowStatusEnum value : IcpFlowStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
}
