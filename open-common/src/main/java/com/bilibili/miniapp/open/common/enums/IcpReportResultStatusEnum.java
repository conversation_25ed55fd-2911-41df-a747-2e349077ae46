package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@AllArgsConstructor
@Getter
public enum IcpReportResultStatusEnum {
    // 2029-同一个主体下，该App，小程序或快应用上报的名称已存在或已提交备案申请，请勿重复报备 1090-短信核验未通过 9001-业务进入待短信核验状态 9002-业务进入待人工审核状态
    ICP_REPORT_RESULT_STATUS_2029(2029L, "同一个主体下，该App，小程序或快应用上报的名称已存在或已提交备案申请，请勿重复报备"),
    ICP_REPORT_RESULT_STATUS_2002(2002L, "该接入商或接入平台已经提交了新增接入申请，不能重复上报，请等待审核结果"),
    ICP_REPORT_RESULT_STATUS_1090(1090L, "短信核验未通过"),
    ICP_REPORT_RESULT_STATUS_9001(9001L, "业务进入待短信核验状态"),
    ICP_REPORT_RESULT_STATUS_9002(9002L, "业务进入待人工审核状态"),
    ;

    private final Long code;
    private final String desc;

    public static IcpReportResultStatusEnum getByCode(Long code) {
        for (IcpReportResultStatusEnum status : IcpReportResultStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
