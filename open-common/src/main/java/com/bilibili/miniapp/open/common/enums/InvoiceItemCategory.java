package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 开票项目类别枚举
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@AllArgsConstructor
@Getter
public enum InvoiceItemCategory {
    
    /**
     * 信息技术服务*信息服务费
     */
    INFORMATION_TECHNOLOGY_SERVICE(1, "信息技术服务*信息服务费"),
    
    /**
     * 广告服务*广告发布费
     */
    ADVERTISING_SERVICE(2, "广告服务*广告发布费");

    private final int code;
    private final String desc;

    public static InvoiceItemCategory getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown invoice item category code=" + code));
    }

    public static InvoiceItemCategory getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(null);
    }
}
