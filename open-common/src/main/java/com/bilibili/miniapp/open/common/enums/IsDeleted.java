package com.bilibili.miniapp.open.common.enums;


public enum IsDeleted {

    VALID(0, "有效"),
    DELETED(1, "删除");

    private final int code;

    private final String name;

    private IsDeleted(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public static IsDeleted getByCode(int code) {
        for (IsDeleted bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code.");
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
