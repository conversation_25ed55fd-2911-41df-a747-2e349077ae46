package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/28
 */
@Getter
@AllArgsConstructor
public enum MiniAppContractStatusEnum {

    WAITING_CONFIRMATION(0, "待确认签署信息"),
    REVIEWING(1, "审核中"),
    REVIEW_FAILED(2, "审核未通过"),
    WAITING_SIGNATURE(3, "待签署"),
    INACTIVE(4, "未生效"),
    EFFECTIVE(5, "生效中"),
    EXPIRED(6, "已失效");

    private final int code;
    private final String desc;

    public static MiniAppContractStatusEnum confirmed() {
        return REVIEWING;
    }

    public static MiniAppContractStatusEnum getByContractCenterStatus(Integer contractCenterStatus) {
        if (contractCenterStatus == null) {
            return null;
        }

        ContractCenterStatusEnum status = ContractCenterStatusEnum.getByCode(contractCenterStatus);
        if (status == null) {
            return null;
        }
        switch (status) {
            case PENDING_REVIEW:
                return REVIEWING;
            case REJECTED:
                return REVIEW_FAILED;
            case PENDING_SIGNATURE:
                return WAITING_SIGNATURE;
            case SIGNED:
                return INACTIVE;
            default:
                return null;
        }
    }

    public static MiniAppContractStatusEnum expired() {
        return EXPIRED;
    }

    public static MiniAppContractStatusEnum effective() {
        return EFFECTIVE;
    }

    /**
     * 根据状态码获取枚举
     */
    public static MiniAppContractStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MiniAppContractStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态码是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}