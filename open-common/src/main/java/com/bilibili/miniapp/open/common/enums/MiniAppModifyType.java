package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/8
 */
@Getter
@AllArgsConstructor
public enum MiniAppModifyType {

    NEW(0, "新建"),
    UPDATE(1, "修改");

    private final int code;
    private final String desc;


    public static MiniAppModifyType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MiniAppModifyType value : MiniAppModifyType.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
