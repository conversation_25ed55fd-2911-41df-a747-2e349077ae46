package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
@Getter
@AllArgsConstructor
public enum MiniAppPermission {

    SUPER_ADMIN(1, "超级管理员"),
    DEVELOPER(2, "开发者权限"),
    EXPERIENCE(3, "体验权限"),
    OPERATION(4, "运营权限");

    private final int code;
    private final String desc;


    public static MiniAppPermission getByCode(Integer code) {
        for (MiniAppPermission value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
