package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@Getter
@AllArgsConstructor
public enum MiniAppStatus {

    //0-未发布，1-已发布
    UNPUBLISHED(0, "未发布"),
    PUBLISHED(1, "已发布"),
    ;

    private final int code;
    private final String desc;

    public static MiniAppStatus getByCode(Integer code) {
        for (MiniAppStatus value : MiniAppStatus.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
