package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/17 18:17
 */
@AllArgsConstructor
@Getter
public enum NotifyStatus {
    NO(0, "未回调"),
    SUCCESS(1, "回调成功"),
    FAILED(2, "回调失败"),
    ;

    private final Integer code;
    private final String name;

    public static NotifyStatus getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown notify status code=" + code));
    }

    public static NotifyStatus getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(NO);
    }
}
