package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/27 16:06
 */
@Getter
@AllArgsConstructor
public enum OgvSectionType {
    FORMAL(0, "正片"),
    PREVIEW(1, "预告"),
    OTHER(2, "其他");

    private final int code;
    private final String desc;

    public static OgvSectionType getByCode(int code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(code, v.getCode()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("unknown type:" + code));
    }

    public static OgvSectionType getByCodeWithoutEx(int code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(code, v.getCode()))
                .findFirst()
                .orElse(OTHER);
    }
}
