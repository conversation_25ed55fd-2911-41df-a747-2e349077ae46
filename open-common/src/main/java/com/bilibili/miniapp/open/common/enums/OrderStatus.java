package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 21:13
 */
@AllArgsConstructor
@Getter
public enum OrderStatus {
    UNKNOWN(-1, "unknown"),
    //目前订单状态只有初始状态
    INIT(0, "初始"),
    ;

    private final Integer code;
    private final String name;

    public static OrderStatus getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown order status code=" + code));
    }

    public static OrderStatus getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
