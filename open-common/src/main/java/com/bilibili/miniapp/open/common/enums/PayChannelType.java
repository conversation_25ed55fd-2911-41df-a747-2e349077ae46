package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=*********">支付回调信息</a>
 * <p>
 * 支付渠道，alipay(支付宝)、open_alipay(支付宝2.0)、ali_global（支付宝跨境）、wechat(微信) ,wx_global(微信跨境) ,paypal(paypal),
 * iap(In App Purchase)、qpay(QQ支付)、hua<PERSON>(花呗支付)、ali_bank（网银支付）、bocom（交行信用卡支付）、bp（B币支付）、
 * <p>
 * ott(云视听小电视支付)、ali_withhold(支付宝代扣)、ali_period_withhold(支付宝周期性代扣)、wechat_score（微信信用分）、ali_hua<PERSON>(花呗)、
 * ali_score(支付宝预授权) 、cmbPay(招行一网通支付) 、wechat_partner(微信服务商)
 *
 * <AUTHOR>
 * @date 2025/1/14 21:45
 */
@AllArgsConstructor
@Getter
public enum PayChannelType {
    UNKNOWN(0, "unknown", ""),
    //重点
    ALIPAY(1, "alipay", "支付宝"),
    //重点
    WECHAT(2, "wechat", "微信"),
    //重点
    BP(3, "bp", "B币支付"),
    OPEN_ALIPAY(4, "open_alipay", "支付宝2.0"),
    ALI_GLOBAL(5, "ali_global", "支付宝跨境"),
    WX_GLOBAL(6, "wx_global", "微信跨境"),
    PAYPAL(7, "paypal", "Paypal"),
    IAP(8, "iap", "IAP"),
    Q_PAY(9, "qpay", "QQ支付"),
    HUA_BEI(10, "huabei", "花呗支付"),
    ALI_BANK(11, "ali_bank", "网银支付"),
    BO_COM(12, "bocom", "交行信用卡支付"),
    OTT(13, "ott", "云视听小电视支付"),
    ALI_WITHHOLD(14, "ali_withhold", "支付宝代扣"),
    ALI_PERIOD_WITHHOLD(15, "ali_period_withhold", "支付宝周期性代扣"),
    WECHAT_SCORE(16, "wechat_score", "微信信用分"),
    ALI_HUA_BEI(17, "ali_huabei", "花呗"),
    ALI_SCORE(18, "ali_score", "支付宝预授权"),
    CMB_PAY(19, "cmbPay", "招行一网通支付"),
    WECHAT_PARTNER(20, "wechat_partner", "微信服务商"),
    ;

    private final Integer code;
    private final String channel;
    private final String desc;


    public static PayChannelType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown pay channel code=" + code));
    }

    public static PayChannelType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static PayChannelType getByChannel(String channel) {
        return Arrays.stream(values())
                .filter(v -> v.getChannel().equalsIgnoreCase(channel))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown pay channel=" + channel));
    }

    public static PayChannelType getByChannelWithoutEx(String channel) {
        return Arrays.stream(values())
                .filter(v -> v.getChannel().equalsIgnoreCase(channel))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
