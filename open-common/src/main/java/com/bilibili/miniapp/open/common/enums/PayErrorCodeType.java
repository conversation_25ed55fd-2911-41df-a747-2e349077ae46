package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 支付中台的常用错误码
 *
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957973">B币快捷支付</a>
 *
 * <AUTHOR>
 * @date 2025/1/20 22:07
 */
@Getter
@AllArgsConstructor
public enum PayErrorCodeType {
    SUCCESS(0L, true, "success"),
    INSUFFICIENT_BALANCE(800409904L, true, "余额不足"),
    REPEATED_PAYMENT(8004010046L, true, "订单已支付，请勿重新支付"),
    RISK_PAYMENT(800570001L, true, "当前交易存在风险"),
    ;

    private final Long code;
    private final boolean graceful;
    private final String message;

    /**
     * 是否是对用户友好的错误信息
     */
    public static boolean isGracefully(Long code) {
        return Arrays.stream(values()).anyMatch(v -> Objects.equals(v.getCode(), code) && v.isGraceful());
    }
}
