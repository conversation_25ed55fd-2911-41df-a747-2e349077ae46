package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 21:13
 */
@AllArgsConstructor
@Getter
public enum PayStatus {
    UNKNOWN(-1, "UNKNOWN", "unknown"),
    NO(0, "NOT", "未支付"),
    SUCCESS(1, "SUCCESS", "支付成功"),
    FAILED(2, "FAIL", "支付失败"),
    CANCEL(3, "PAY_CANCEL", "支付取消"),
    CONFIRMED(4, "CONFIRMED", "已确认"),
    ;

    private final Integer code;
    private final String name;
    private final String desc;

    public static PayStatus getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown pay status code=" + code));
    }

    public static PayStatus getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static PayStatus getByNameWithoutEx(String name) {
        return Arrays.stream(values())
                .filter(v -> v.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 当前状态是否可以支付
     */
    public static boolean isPermitPay(Integer status) {
        PayStatus payStatus = getByCodeWithoutEx(status);
        return Objects.equals(payStatus, NO) || Objects.equals(payStatus, FAILED);
    }
}
