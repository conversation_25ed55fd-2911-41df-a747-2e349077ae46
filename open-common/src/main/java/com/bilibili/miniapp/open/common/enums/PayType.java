package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 支付类型
 *
 * <AUTHOR>
 * @date 2025/1/20 16:12
 */
@AllArgsConstructor
@Getter
public enum PayType {
    //SDK支付，此时pay_param即支付参数，此时pay_info用不到
    SDK_PAY(0, "SDK支付"),
    //开平支付，此时用户确认后使用pay_info发起支付确认
    OPEN_PAY(1, "开平支付"),
    //需要充值后再支付，此时pay_param即充值参数，充值完成后，使用pay_info发起支付确认；
    RECHARGE_THEN_OPEN_PAY(2, "需要充值后再走开平支付"),
    ;

    private final Integer code;
    private final String desc;


    public static PayType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown pay code=" + code));
    }

    public static PayType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(SDK_PAY);
    }
}
