package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 21:02
 */
@AllArgsConstructor
@Getter
public enum PlatformType {
    UNKNOWN(0, "unknown") {
        @Override
        public boolean isSupportsBpPay() {
            return false;
        }
    },
    IOS(1, "ios") {
        @Override
        public boolean isSupportsBpPay() {
            return true;
        }
    },
    ANDROID(2, "android") {
        @Override
        public boolean isSupportsBpPay() {
            return false;
        }
    },
    ;

    private final Integer code;
    private final String name;

    /**
     * 是否支持B币支付
     */
    public abstract boolean isSupportsBpPay();


    public static PlatformType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown platform code=" + code));
    }

    public static PlatformType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static PlatformType getByName(String name) {
        return Arrays.stream(values())
                .filter(v -> v.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown platform name=" + name));
    }
}
