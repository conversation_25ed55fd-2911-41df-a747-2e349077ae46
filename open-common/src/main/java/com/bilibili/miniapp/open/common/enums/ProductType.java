package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 21:02
 */
@AllArgsConstructor
@Getter
public enum ProductType {
    UNKNOWN(0, "未知"),
    SEASON(1, "解锁剧集"),
    COUPON_OR_CARD(2, "购买优惠券/储值卡"),
    MEMBER(3, "购买会员"),
    VIRTUAL_CURRENCY(4, "购买虚拟货币"),
    ;

    private final Integer code;
    private final String desc;


    public static ProductType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown product code=" + code));
    }

    public static ProductType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
