package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957982">业务方发起退款</a>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957983">支付平台退款回调业务方</a>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957984">退款订单查询-退款状态</a>
 * 退款状态
 * REFUND_CREATE(退款中)、REFUND_FAIL（退款失败，可重新发起，需采用相同退款参数）、
 * REFUND_SUCCESS（退款成功）、REFUND_NOT_SUPPORT（渠道不支持退款）、REFUND_CHANGE（退款异常，需要人工干预）
 *
 * <AUTHOR>
 * @date 2025/1/19 15:37
 */
@AllArgsConstructor
@Getter
public enum RefundStatus {
    UNKNOWN(-1, "UNKNOWN", ImmutableList.of(), "unknown"),
    CREATE_INIT(0, "CREATE_INIT", ImmutableList.of(), "尚未发起退款（待发起退款）"),//支付中台没有这个状态，是开平枚举的
    CREATE_SUCCESS(1, "CREATE_SUCCESS", ImmutableList.of("REFUND_CREATE"), "发起退款成功（退款中）"),
    CREATE_FAILED(2, "CREATE_FAILED", ImmutableList.of(), "发起退款失败）"),//支付中台没有这个状态，是开平枚举的
    SUCCESS(3, "SUCCESS", ImmutableList.of("REFUND_SUCCESS"), "退款成功"),
    FAILED(4, "FAILED", ImmutableList.of("REFUND_FAIL", "REFUND_NOT_SUPPORT", "REFUND_CHANGE"), "退款失败");

    private final Integer code;
    private final String name;
    private final List<String> payPlatformRefundStatus;
    private final String desc;


    public static RefundStatus getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown refund status code=" + code));
    }

    public static RefundStatus getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static RefundStatus getByPayPlatformRefundStatusWithoutEx(String status) {
        return Arrays.stream(values())
                .filter(v -> v.getPayPlatformRefundStatus().contains(status))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
