package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/17 21:17
 */
@AllArgsConstructor
@Getter
public enum RetryBizType {
    UNKNOWN(0, "未知", new Integer[]{}),
    ORDER_CHANGE_PUBLISH_MQ(1, "订单变更消息投递", new Integer[]
            {5, 10, 20, 40, 60}),
    ORDER_PAY_NOTIFY_DEVELOPER(2, "订单支付结果回调给开发者", new Integer[]
            {5, 10, 20, 60, 3 * 60, 5 * 60, 30 * 60, 60 * 60}),
    ORDER_REFUND_PUBLISH_MQ(3, "订单退款消息投递", new Integer[]
            {5, 10, 20, 40, 60}),
    ORDER_REFUND_NOTIFY_DEVELOPER(4, "订单退款结果回调给开发者", new Integer[]
            {5, 10, 20, 60, 3 * 60, 5 * 60, 30 * 60, 60 * 60}),
    ;

    private final Integer code;
    private final String desc;
    //时间重试梯度策略，单位秒
    @NotNull
    private final Integer[] timeRetryStrategy;

    public static RetryBizType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown retry status code=" + code));
    }

    public static RetryBizType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
