package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/17 21:15
 */
@AllArgsConstructor
@Getter
public enum RetryStatus {
    FAILED(0, "失败（默认）"),
    SUCCESS(1, "重试成功"),
    FAILED_TERMINAL(2, "失败终态（达到重试上限后依然失败）"),
    ;

    private final Integer code;
    private final String desc;

    public static RetryStatus getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown retry status code=" + code));
    }

    public static RetryStatus getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(FAILED);
    }
}
