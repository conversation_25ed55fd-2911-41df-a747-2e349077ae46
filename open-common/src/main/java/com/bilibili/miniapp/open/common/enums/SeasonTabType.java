package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@AllArgsConstructor
@Getter
public enum SeasonTabType {

    //0-热门推荐，1-新剧速递
    HOT(0, "热门推荐"),
    NEW(1, "新剧速递"),

    ;
    private final int code;

    private final String desc;


    public static SeasonTabType getByCode(Integer code) {
        for (SeasonTabType seasonTabType : SeasonTabType.values()) {
            if (seasonTabType.getCode() == code) {
                return seasonTabType;
            }
        }
        return null;
    }

}
