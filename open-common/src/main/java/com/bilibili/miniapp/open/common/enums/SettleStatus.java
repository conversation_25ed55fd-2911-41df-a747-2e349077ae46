package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 21:13
 */
@AllArgsConstructor
@Getter
public enum SettleStatus {
    UNKNOWN(-1, "未知"),
    NO(0, "未结算"),
    SUCCESS(1, "结算成功"),
    FAILED(2, "结算失败"),
    ;

    private final Integer code;
    private final String name;

    public static SettleStatus getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown settle status code=" + code));
    }

    public static SettleStatus getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
