package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */

@AllArgsConstructor
@Getter
public enum SortType {
    ASC(1, "升序") {
        @Override
        public String getOrderByClause(String sortField) {
            if (sortField == null || "".equals(sortField)) {
                throw new IllegalArgumentException("sortField is null");
            }
            return sortField.concat(" ASC");
        }
    },
    DESC(0, "降序") {
        @Override
        public String getOrderByClause(String sortField) {
            if (sortField == null || "".equals(sortField)) {
                throw new IllegalArgumentException("sortField is null");
            }
            return sortField.concat(" DESC");
        }
    };

    private final int code;
    private final String desc;

    public static SortType getByCode(int code) {
        for (SortType status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }

        throw new IllegalArgumentException("unknown sort type");
    }

    public abstract String getOrderByClause(String sortField);
}

