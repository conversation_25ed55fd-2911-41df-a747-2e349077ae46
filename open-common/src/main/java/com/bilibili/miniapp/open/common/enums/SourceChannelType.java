package com.bilibili.miniapp.open.common.enums;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/14 22:22
 */
@AllArgsConstructor
@Getter
public enum SourceChannelType {
    UNKNOWN(0, "unknown"),
    AD(1, "商业广告流量"),
    NATURAL(2, "自然流量"),
    ;

    private final Integer code;
    private final String desc;


    public static SourceChannelType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "unknown source channel code=" + code));
    }

    public static SourceChannelType getByCodeWithoutEx(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
