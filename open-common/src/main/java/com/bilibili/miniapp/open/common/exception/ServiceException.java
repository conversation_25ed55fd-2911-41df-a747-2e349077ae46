package com.bilibili.miniapp.open.common.exception;


import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import lombok.Data;

@Data
public class ServiceException extends RuntimeException {

    private static final long serialVersionUID = 5070029629720644499L;
    private Integer code;
    private String message;

    public ServiceException() {
        super();
    }

    public ServiceException(ErrorCodeType errorCodeType) {
        super(errorCodeType.getMessage());
        this.code = errorCodeType.getCode();
        this.message = errorCodeType.getMessage();
    }

    public ServiceException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }

    public ServiceException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ServiceException(Throwable cause, String message) {
        super(message, cause);
        this.code = 500;
        this.message = message;
    }
}
