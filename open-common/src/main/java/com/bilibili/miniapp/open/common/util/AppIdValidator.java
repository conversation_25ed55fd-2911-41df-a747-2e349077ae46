package com.bilibili.miniapp.open.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * AppId格式验证工具类
 */
public class AppIdValidator {

    /**
     * 有效的AppId格式：以bili开头，后跟MD5哈希值（16位或32位十六进制字符）
     * MD5哈希值由数字和a-f的字母组成
     */
    private static final Pattern VALID_APPID_PATTERN = Pattern.compile("^bili[0-9a-f]+$", Pattern.CASE_INSENSITIVE);

    /**
     * 判断AppId是否为有效格式（以bili开头，后跟MD5哈希值）
     *
     * @param appId 需要验证的AppId
     * @return 如果格式为bili开头后跟MD5哈希值则返回true，否则返回false
     */
    public static boolean isApplet(String appId) {
        if (StringUtils.isBlank(appId)) {
            return false;
        }

        // 检查是否符合格式：bili开头，后跟MD5哈希值
        if (!VALID_APPID_PATTERN.matcher(appId).matches()) {
            return false;
        }

        // 检查长度：bili + MD5哈希值（16位或32位）
        int length = appId.length();
        return length == 20; // "bili" (4) + MD5 (16)
    }

    /**
     * 过滤AppId，只保留符合格式的AppId
     *
     * @param appId 需要过滤的AppId
     * @return 如果格式符合则返回原AppId，否则返回null
     */
    public static String filterAppId(String appId) {
        return isApplet(appId) ? appId : null;
    }
}
