package com.bilibili.miniapp.open.common.util;

import com.bilibili.bvid.BVIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Slf4j
public final class ArchiveUtil {
    public static Long convert2Avid(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return 0L;
        }
        keyword = keyword.trim();
        if (keyword.toUpperCase().startsWith("BV")) {
            try {
                return BVIDUtils.bvToAv(keyword);
            } catch (Exception e) {
                log.warn("bvId:" + keyword + "解析异常", e);
            }
        }
        return 0L;
    }
}
