package com.bilibili.miniapp.open.common.util;

import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

/**
 * 自定义Assert的目的是为了抛出{@link ServiceException}异常，规范错误码{@link ErrorCodeType}
 *
 * <AUTHOR>
 * @date 2025/1/6 21:12
 * @see ServiceException
 * @see ErrorCodeType
 */
public class AssertUtil {

    public static void hasText(String text, ErrorCodeType errorCodeType) {
        if (StringUtils.isBlank(text)) {
            throw new ServiceException(errorCodeType);
        }
    }

    public static void hasText(String text, Integer errorCode, String message) {
        if (StringUtils.isBlank(text)) {
            throw new ServiceException(errorCode, message);
        }
    }

    public static void notNull(Object value, ErrorCodeType errorCodeType) {
        if (value == null) {
            throw new ServiceException(errorCodeType);
        }
    }

    public static void notNull(Object value, Integer errorCode, String message) {
        if (value == null) {
            throw new ServiceException(errorCode, message);
        }
    }

    public static void isNull(Object value, ErrorCodeType errorCodeType) {
        if (value != null) {
            throw new ServiceException(errorCodeType);
        }
    }

    public static void isNull(Object value, Integer errorCode, String message) {
        if (value != null) {
            throw new ServiceException(errorCode, message);
        }
    }

    public static void isTrue(boolean expression, ErrorCodeType errorCodeType) {
        if (!expression) {
            throw new ServiceException(errorCodeType);
        }
    }

    public static void isTrue(boolean expression, Integer errorCode, String message) {
        if (!expression) {
            throw new ServiceException(errorCode, message);
        }
    }

    public static void notEmpty(Collection<?> collection, ErrorCodeType errorCodeType) {
        if (collection == null || collection.isEmpty()) {
            throw new ServiceException(errorCodeType);
        }
    }

    public static void notEmpty(Collection<?> collection, Integer errorCode, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new ServiceException(errorCode, message);
        }
    }

    public static void notContainsAny(String text, char[] searchChars, Integer errorCode, String message) {
        if (StringUtils.containsAny(text, searchChars)) {
            throw new ServiceException(errorCode, message);
        }
    }

}
