package com.bilibili.miniapp.open.common.util;

import com.bilibili.miniapp.open.common.entity.SecretKeyPair;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2025/1/13 20:10
 */
@Slf4j
public class EncryptUtil {
    /**
     * <a href="https://blog.csdn.net/problc/article/details/143199679">各种加密算法简单区别</a>
     */
    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";

    private static final String AES_DESC = "AES";

    private static final char[] CHARS = new char[]{
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
            'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
            'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D',
            'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
            'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
            'Y', 'Z', '+', '_', '!', '~', '-', '$', '^'};

    /**
     * @return 生成16位的密钥对
     */
    public static SecretKeyPair generateSecretKeyPair() {
        StringBuilder keyBuilder = new StringBuilder();
        StringBuilder ivBuilder = new StringBuilder();
        Random random = new Random();
        int len = CHARS.length;
        for (int i = 0; i < 16; i++) {
            keyBuilder.append(CHARS[random.nextInt(len)]);
            ivBuilder.append(CHARS[random.nextInt(len)]);
        }
        return new SecretKeyPair(keyBuilder.toString(), ivBuilder.toString());
    }

    public static String generateSecretIv() {
        StringBuilder ivBuilder = new StringBuilder();
        SecureRandom random = new SecureRandom();
        int len = CHARS.length;
        for (int i = 0; i < 16; i++) {
            ivBuilder.append(CHARS[random.nextInt(len)]);
        }
        return ivBuilder.toString();
    }

    /**
     * @param data          需要加密的明文
     * @param secretKeyPair 密钥
     * @return 经过Base64编码之后的密文
     */
    public static String encrypt(String data, SecretKeyPair secretKeyPair) throws Exception {
        validateSecretKeyPair(secretKeyPair);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKeyPair.getKey().getBytes(StandardCharsets.UTF_8), AES_DESC);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(secretKeyPair.getIv().getBytes(StandardCharsets.UTF_8));
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] valueByte = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(valueByte);
    }

    /**
     * @param data          经过Base64编码之后的密文
     * @param secretKeyPair 密钥
     * @return 解密后的明文
     */
    public static String decrypt(String data, SecretKeyPair secretKeyPair) throws Exception {
        validateSecretKeyPair(secretKeyPair);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKeyPair.getKey().getBytes(StandardCharsets.UTF_8), AES_DESC);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(secretKeyPair.getIv().getBytes(StandardCharsets.UTF_8));
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] encryptBytes = Base64.getDecoder().decode(data.getBytes(StandardCharsets.UTF_8));
        byte[] valueByte = cipher.doFinal(encryptBytes);
        return new String(valueByte, StandardCharsets.UTF_8);
    }

    public static String decryptWithUrlSafe(String encryptedData, SecretKeyPair secretKeyPair) throws Exception {

        validateSecretKeyPair(secretKeyPair);
        // 创建AES密钥规格
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKeyPair.getKey().getBytes(StandardCharsets.UTF_8), AES_DESC);
        // 创建偏移量规格
        IvParameterSpec ivParameterSpec = new IvParameterSpec(secretKeyPair.getIv().getBytes(StandardCharsets.UTF_8));
        // 获取Cipher实例
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        // 初始化解密
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
        // Base64解码
        byte[] decodedEncryptedData = Base64.getUrlDecoder().decode(encryptedData.getBytes(StandardCharsets.UTF_8));
        // 解密数据
        byte[] original = cipher.doFinal(decodedEncryptedData);
        // 转换为字符串
        return new String(original, StandardCharsets.UTF_8);
    }

    private static void validateSecretKeyPair(SecretKeyPair secretKeyPair) {
        AssertUtil.isTrue(secretKeyPair != null
                        && secretKeyPair.getKey() != null && secretKeyPair.getKey().length() == 16
                        && secretKeyPair.getIv() != null && secretKeyPair.getIv().length() == 16,
                ErrorCodeType.BAD_DATA.getCode(), "invalid secretKey");
    }
}
