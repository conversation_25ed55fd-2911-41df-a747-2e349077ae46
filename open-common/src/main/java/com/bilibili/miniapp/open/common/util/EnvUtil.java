package com.bilibili.miniapp.open.common.util;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
public final class EnvUtil {

    /**
     * 是否是预发 or 生产
     */
    public static boolean isProdOrPre() {
        String env = resolveConfig("DEPLOY_ENV", "deploy_env").orElse("");
        return "pre".equals(env) || "prod".equals(env);
    }

    private static Optional<String> resolveConfig(String envName, String propertyName) {
        String value = System.getenv(envName);
        if (Objects.nonNull(value)) {
            return Optional.of(value);
        }
        return Optional.ofNullable(System.getProperty(propertyName));
    }
}
