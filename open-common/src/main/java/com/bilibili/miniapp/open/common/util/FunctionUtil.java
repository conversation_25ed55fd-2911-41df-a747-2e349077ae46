package com.bilibili.miniapp.open.common.util;

import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.function.BinaryOperator;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/3 11:50
 */
public final class FunctionUtil {

    public static <T> BinaryOperator<T> override() {
        return (T t1, T t2) -> t2;
    }

    public static <I, O> O invokeWithoutEx(I input, Function<I, O> func, Supplier<O> defaultSuppler) {
        try {
            return func.apply(input);
        } catch (Throwable e) {
        }
        return defaultSuppler.get();
    }

    public static <T> boolean notNull(T value, Consumer<T> consumer) {
        if (value != null) {
            consumer.accept(value);
            return true;
        }
        return false;
    }

    public static <T> boolean notEmpty(List<T> list, Consumer<List<T>> consumer) {
        if (Objects.nonNull(list) && !list.isEmpty()) {
            consumer.accept(list);
            return true;
        }
        return false;
    }

    public static final Function<String, String> PREFIX_LIKE = v -> v + "%";
    public static final Function<String, String> SUFFIX_LIKE = v -> "%" + v;
    public static final Function<String, String> LIKE = v -> "%" + v + "%";

    public static boolean hasText(String value, Function<String, String> decorator, Consumer<String> consumer) {
        if (value != null && !(value = value.trim()).isEmpty()) {
            consumer.accept(decorator != null ? decorator.apply(value) : value);
            return true;
        }
        return false;
    }

    public static <T> boolean notNull(T value, T defaultValue, Consumer<T> consumer) {
        value = value != null ? value : defaultValue;
        if (value != null) {
            consumer.accept(value);
            return true;
        }
        return false;
    }

    public static void hasText(String value, Consumer<String> consumer) {
        if (Objects.nonNull(value) && !value.isEmpty()) {
            consumer.accept(value);
        }
    }

    /**
     * @param batchSize 单批次处理的元素数量，只有小于{@param keys}数据量时，才会走分批处理逻辑，否则由当前调用线程直接处理
     */
    public static <K, R> List<R> batch(List<K> keys, Function<List<K>, List<R>> func, int batchSize) {
        return batch(keys, func, batchSize, ForkJoinPool.commonPool());
    }

    public static <K, R> List<R> batch(List<K> keys, Function<List<K>, List<R>> func, int batchSize, ExecutorService executorService) {
        if (keys == null || keys.isEmpty()) return Lists.newArrayList();
        if (batchSize >= keys.size()) {
            return func.apply(keys);
        }
        List<CompletableFuture<List<R>>> futures = Lists.newArrayList();
        //list的分割本质上是调的keys的subList方法，如果keys是个ArrayList，那么partitions中的元素是ArrayList$SubList
        //但是ArrayList$SubList是不支持序列化的，即没有实现Serializable接口，那么可能就会出现个问题:
        //如果func依赖的参数必须要求是可Serializable的，比如Spring的HttpInvoker，它会要求所有复合对象必须是Serializable，
        //因此如果partition是HttpInvoker func调用的参数，那么会报nested exception is java.io.NotSerializableException: java.util.ArrayList$SubList
        //因此在for循环中对partition进行了适配
        List<List<K>> partitions = Lists.partition(keys, batchSize);
        for (List<K> partition : partitions) {
            Callable<List<R>> task = TraceUtil.decorate(() -> func.apply(Lists.newArrayList(partition)));
            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    if (Objects.isNull(Thread.currentThread().getContextClassLoader())) {
                        //fix:NullPointerException at com.atomikos.util.DynamicProxySupport.getClassLoadersToTry#191
                        //本质：JDK8的ForkJoinPool中线程可能从调用者继承，如果调用者线程从JNI的非java线程而来，可能导致contextClassLoader为null
                        Thread.currentThread().setContextClassLoader(ClassLoader.getSystemClassLoader());
                    }
                    return task.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, executorService));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return futures.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (InterruptedException | ExecutionException e) {
                        throw new RuntimeException(e);
                    }
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
}
