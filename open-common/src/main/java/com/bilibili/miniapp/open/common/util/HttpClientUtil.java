package com.bilibili.miniapp.open.common.util;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HeaderElement;
import org.apache.http.HeaderElementIterator;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.Args;

/**
 * <AUTHOR>
 * @date 2024/9/6 20:51
 */
public final class HttpClientUtil {


    public static CloseableHttpClient buildHttpClient(HttpConfig config) {
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        return buildHttpClient(config, manager);
    }

    public static CloseableHttpClient buildHttpClient(HttpConfig config, PoolingHttpClientConnectionManager manager) {
        HttpClientBuilder builder = HttpClients.custom();

        if (config.getDefaultKeepAlive() > 0) {
            builder.setKeepAliveStrategy(new DefaultTimeKeepAliveStrategy(config.getDefaultKeepAlive()));
        } else {
            builder.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy());
        }

        if (StringUtils.isNotBlank(config.getProxyHost())) {
            builder.setProxy(new HttpHost(config.getProxyHost(), config.getProxyPort()));
        }

        if (config.getMaxPoolSize() > 0) {
            manager.setMaxTotal(config.getMaxPoolSize());
            manager.setDefaultMaxPerRoute(config.getMaxPoolSize());
        }
        builder.setConnectionManager(manager);

        return builder.build();
    }

    @Data
    @Builder
    public static class HttpConfig {
        /**
         * 最大连接池
         */
        private int maxPoolSize;

        /**
         * 代理host
         */
        private String proxyHost;

        /**
         * 代理port
         */
        private int proxyPort;

        /**
         * response没有声明保持连接时间时，用默认保持时间，单位：毫秒
         */
        private int defaultKeepAlive;
    }

    static class DefaultTimeKeepAliveStrategy implements ConnectionKeepAliveStrategy {

        private final int defaultDuration;

        public DefaultTimeKeepAliveStrategy(int defaultDuration) {
            this.defaultDuration = defaultDuration;
        }

        @Override
        public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
            Args.notNull(response, "HTTP response");
            final HeaderElementIterator it = new BasicHeaderElementIterator(
                    response.headerIterator(HTTP.CONN_KEEP_ALIVE));
            while (it.hasNext()) {
                final HeaderElement he = it.nextElement();
                final String param = he.getName();
                final String value = he.getValue();
                if (value != null && param.equalsIgnoreCase("timeout")) {
                    try {
                        return Long.parseLong(value) * 1000;
                    } catch(final NumberFormatException ignore) {
                    }
                }
            }
            return defaultDuration;
        }
    }
}
