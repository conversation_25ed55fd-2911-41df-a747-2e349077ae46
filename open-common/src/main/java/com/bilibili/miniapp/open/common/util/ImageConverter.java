package com.bilibili.miniapp.open.common.util;

import javax.imageio.*;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.*;
import java.util.Base64;

public class ImageConverter {

    private static final int MAX_BASE64_SIZE = 200 * 1024;     // 200KB
    private static final double BASE64_RATIO = 0.75;           // Base64尺寸换算系数

    private static final float INITIAL_QUALITY = 0.85f;
    private static final float MIN_QUALITY = 0.1f;
    private static final float SCALE_FACTOR = 0.9f;

    private static BufferedImage readImage(String imageUrl) throws IOException {
        return ImageIO.read(new URL(imageUrl));
    }

    /**
     * Optimize image with default JPEG format
     */
    public static String optimizeImage(String imageUrl) throws Exception {
        return optimizeImage(imageUrl, "jpeg");
    }

    /**
     * Optimize image with specified format (jpg, jpeg or png)
     */
    public static String optimizeImage(String imageUrl, String format) throws Exception {
        if (!isSupportedFormat(format)) {
            throw new IllegalArgumentException("Unsupported format. Only jpg, jpeg and png are supported.");
        }

        BufferedImage originalImage = readImage(imageUrl);
        return processImage(convertToRGB(originalImage), format);
    }

    private static boolean isSupportedFormat(String format) {
        return format.equalsIgnoreCase("jpg") ||
                format.equalsIgnoreCase("jpeg") ||
                format.equalsIgnoreCase("png");
    }

    private static BufferedImage convertToRGB(BufferedImage image) {
        if (image.getType() != BufferedImage.TYPE_INT_RGB) {
            BufferedImage newImage = new BufferedImage(
                    image.getWidth(),
                    image.getHeight(),
                    BufferedImage.TYPE_INT_RGB);
            newImage.createGraphics().drawImage(image, 0, 0, null);
            return newImage;
        }
        return image;
    }

    private static String processImage(BufferedImage image, String format) throws Exception {
        // Try quality compression first
        String result = tryQualityCompression(image, new ByteArrayOutputStream(), format);
        if (result != null) {
            return result;
        }
        // If quality compression fails, try scaling compression
        return tryScaleCompression(image, format);
    }

    private static String tryQualityCompression(BufferedImage image, ByteArrayOutputStream output, String format)
            throws Exception {
        ImageWriter writer = ImageIO.getImageWritersByFormatName(getFormatName(format)).next();

        // Set compression parameters for JPEG
        ImageWriteParam param = null;
        if (format.equalsIgnoreCase("jpg") || format.equalsIgnoreCase("jpeg")) {
            param = new JPEGImageWriteParam(null);
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        } else {
            param = writer.getDefaultWriteParam();
        }

        float currentQuality = INITIAL_QUALITY;
        do {
            output.reset();

            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(currentQuality);
            }

            try (ImageOutputStream ios = ImageIO.createImageOutputStream(output)) {
                writer.setOutput(ios);
                writer.write(null, new IIOImage(image, null, null), param);
            }

            currentQuality = currentQuality * 0.7f;
        } while (output.size() > calculateMaxByteSize() && currentQuality > MIN_QUALITY);

        writer.dispose();
        String base64 = Base64.getEncoder().encodeToString(output.toByteArray());
        if (base64.length() <= MAX_BASE64_SIZE) {
            return base64;
        }
        return null;
    }

    private static String tryScaleCompression(BufferedImage original, String format) throws Exception {
        int width = original.getWidth();
        int height = original.getHeight();

        while (width > 100 && height > 100) {
            width = (int) (width * SCALE_FACTOR);
            height = (int) (height * SCALE_FACTOR);

            BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = resized.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                    RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(original, 0, 0, width, height, null);
            g.dispose();

            String result = tryQualityCompression(resized, new ByteArrayOutputStream(), format);
            if (result != null) return result;
        }

        // Fallback solution
        return fallbackCompression(original, format);
    }

    private static String fallbackCompression(BufferedImage image, String format) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        if (format.equalsIgnoreCase("jpg") || format.equalsIgnoreCase("jpeg")) {
            JPEGImageWriteParam jpegParams = new JPEGImageWriteParam(null);
            jpegParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            jpegParams.setCompressionQuality(MIN_QUALITY);

            try (ImageOutputStream ios = new MemoryCacheImageOutputStream(baos)) {
                ImageWriter writer = ImageIO.getImageWritersByFormatName("jpeg").next();
                writer.setOutput(ios);
                writer.write(null, new IIOImage(image, null, null), jpegParams);
                writer.dispose();
            }
        } else {
            try (ImageOutputStream ios = new MemoryCacheImageOutputStream(baos)) {
                ImageWriter writer = ImageIO.getImageWritersByFormatName("png").next();
                writer.setOutput(ios);
                writer.write(null, new IIOImage(image, null, null), writer.getDefaultWriteParam());
                writer.dispose();
            }
        }

        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }

    private static String getFormatName(String format) {
        if (format.equalsIgnoreCase("jpg")) {
            return "jpeg";
        }
        return format.toLowerCase();
    }

    private static int calculateMaxByteSize() {
        return (int) (MAX_BASE64_SIZE * BASE64_RATIO);
    }

    public static void convertToImage(String base64String, String outputFormat, String outputPath)
            throws IllegalArgumentException, IOException {
        // 参数验证
        if (base64String == null || base64String.isEmpty()) {
            throw new IllegalArgumentException("Base64字符串不能为空");
        }
        if (!isSupportedFormat(outputFormat)) {
            throw new IllegalArgumentException("只支持jpg,jpeg和png格式输出");
        }
        // 清理Base64头部信息
        String cleanBase64 = cleanBase64Header(base64String);

        // Base64解码
        byte[] imageBytes = Base64.getDecoder().decode(cleanBase64);

        // 创建缓存流
        try (InputStream inputStream = new ByteArrayInputStream(imageBytes)) {
            BufferedImage image = ImageIO.read(inputStream);

            if (image == null) {
                throw new IOException("无法解码图片数据，可能不是有效的图片格式");
            }

            // 创建输出目录
            File outputFile = new File(outputPath);
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 保存图片
            if (!ImageIO.write(image, getFormatName(outputFormat), outputFile)) {
                throw new IOException("不支持的输出格式: " + outputFormat);
            }

            System.out.println("图片保存成功: " + outputFile.getAbsolutePath());
        }
    }

    private static String cleanBase64Header(String base64String) {
        // 处理包含头部的情况（如：data:image/png;base64,...）
        int base64Start = base64String.indexOf("base64,");
        if (base64Start != -1) {
            return base64String.substring(base64Start + 7);
        }
        return base64String;
    }

    public static void main(String[] args) throws Exception {
        String imageUrl = "https://uat-i0.hdslb.com/bfs/miniappopen/open_resource/202505/15ddadfd01c39e4315e78a57e0806d09.jpg";

        // Test with different formats
        String jpegResult = optimizeImage(imageUrl, "jpeg");
        convertToImage(jpegResult, "jpeg", "output.jpeg");

        String jpgResult = optimizeImage(imageUrl, "jpg");
        convertToImage(jpgResult, "jpg", "output.jpg");

        String pngResult = optimizeImage(imageUrl, "png");
        convertToImage(pngResult, "png", "output.png");
    }
}
