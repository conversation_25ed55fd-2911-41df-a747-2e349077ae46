package com.bilibili.miniapp.open.common.util;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Slf4j
public class ImageUtil {

    public static String convertImageToBase64(String imageUrl) {
        String base64Image = null;
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;

        try {
            // Create a connection to the URL
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setDoInput(true);
            connection.connect();

            // Check for a successful response code
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                inputStream = connection.getInputStream();
                outputStream = new ByteArrayOutputStream();

                // Read the input stream and write to the output stream
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                // Convert the output stream to a Base64 encoded string
                base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());
            }
        } catch (Exception e) {
            log.error("Error converting image to Base64: {}", e.getMessage());
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR.getCode(), "图片转换base64失败");
        } finally {
            // Clean-up: close streams and disconnect
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
                if (connection != null) {
                    connection.disconnect();
                }
            } catch (Exception e) {
                log.error("Error closing streams: {}", e.getMessage());
            }
        }
        return base64Image;
    }

    public static void main(String[] args) {
        // Example usage
        String imageUrl = "https://boss.hdslb.com/miniapp/icp/2232_1_1746784774650.jpg"; // Replace with your URL
        String base64Image = convertImageToBase64(imageUrl);

        if (base64Image != null) {
            System.out.println("Base64 Encoded Image: " + base64Image);
        } else {
            System.out.println("Failed to encode image");
        }
    }
}
