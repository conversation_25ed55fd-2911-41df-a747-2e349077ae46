package com.bilibili.miniapp.open.common.util;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
public class MemoryPagination {

    /**
     * 内存分页方法
     *
     * @param list 原始列表
     * @param page 当前页码
     * @param size 每页大小
     * @param <T>  列表元素类型
     * @return 分页后的子列表
     */
    public static <T> List<T> pageList(List<T> list, int page, int size) {
        if (list == null || list.isEmpty()) {
            return list;
        }
        int totalItems = list.size();
        int fromIndex = (page - 1) * size;
        if (fromIndex >= totalItems) {
            return List.of();
        }
        int toIndex = Math.min(fromIndex + size, totalItems);
        return list.subList(fromIndex, toIndex);
    }
}
