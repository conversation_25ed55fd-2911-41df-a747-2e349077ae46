package com.bilibili.miniapp.open.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * B站支付中台的签名工具类
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957956">支付中台签名</a>
 * value为null则不需要签名
 *
 * <AUTHOR>
 * @date 2025/1/16 22:23
 */
@Slf4j
public final class PaySignUtil {

    /**
     * 根据Bean构建签名
     * <p>
     * 适用于有明确签名字段时调用，比如开平调支付中台接口时，具体要签名哪些字段，开平是预知的
     */
    public static String sign(Object data, String token) {
        log.info("[PaySignUtil] sign data={}", JSON.toJSONString(data));
        List<Pair<String, String>> parameters = Lists.newArrayList();
        try {
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.isAnnotationPresent(Sign.class)) {
                    Object value = field.get(data);
                    if (Objects.isNull(value)) {
                        continue;
                    }
                    parameters.add(Pair.of(field.getName(), value.toString()));
                }
            }
        } catch (Exception e) {
            log.error("[PaySignUtil] sign error", e);
            throw new ServiceException(ErrorCodeType.PARSE_SIGN_PARAMETER_FAILED);
        }
        return doSign(parameters, token);
    }

    /**
     * 根据JSON构建签名
     * <p>
     * 适用于签名字段不明确时调用，比如支付中台调开平接口时，消息体中具体签名了哪些字段，开平是未知的，因为支付中台很有可能在后期迭代新增了一些字段也参与了签名
     */
    public static String sign(JSONObject data, String token) {
        log.info("[PaySignUtil] sign data={}", data.toJSONString());
        List<Pair<String, String>> parameters = Lists.newArrayList();
        for (String key : data.keySet()) {
            String value = data.getString(key);
            if (value != null) {
                parameters.add(Pair.of(key, value));
            }
        }
        return doSign(parameters, token);
    }

    private static String doSign(List<Pair<String, String>> parameters, String token) {
        //按照key升序
        parameters.sort(Comparator.comparing(Pair::getLeft));
        //追加token
        parameters.add(Pair.of("token", token));
        String data = parameters.stream()
                .map(pair -> String.format("%s=%s", pair.getLeft(), pair.getRight()))
                .collect(Collectors.joining("&"));
        String md5 = Md5Util.md5(data);
        log.info("[PaySignUtil] doSign data={}, sign={}", data, md5);
        return md5;
    }
}
