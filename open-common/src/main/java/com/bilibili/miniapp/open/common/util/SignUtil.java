package com.bilibili.miniapp.open.common.util;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.entity.SignParameterEntity;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/10 09:57
 */
@Slf4j
public final class SignUtil {
    /**
     * 参数对之间的连接符
     * <p>
     * 1、复合属性参数对
     * <p>
     * 2、Map元素参数对
     */
    private static final String PARAMETER_PAIR_DELIMITER = "&";
    /**
     * 参数key和value之间的连接符
     * <p>
     * 1、复合属性
     * <p>
     * 2、Map元素
     */
    private static final String KEY_VALUE_DELIMITER = "=";
    /**
     * 集合元素之间的连接符
     * <p>
     * 1、适用于所有Collection，常见的如List、Set、Array等
     */
    private static final String COLLECTION_ELEMENT_DELIMITER = ",";

    /**
     * 签名算法
     */
    private static final String ALGORITHM = "HmacSHA256";

    /**
     * 将目标对象解析为签名必需的参数串
     * <p>
     * 1、不保证所有类型，只处理常见的签名参数类型
     *
     * @param obj 待解析的签名实体，通常是一个Map或者复合对象
     */
    public static String serialize(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        Class<?> clazz = obj.getClass();
        if (CharSequence.class.isAssignableFrom(clazz) || ClassUtils.isPrimitiveOrWrapper(clazz)) {
            return Objects.toString(obj);
        }

        if (clazz.isArray()) {
            Object[] objs = (Object[]) obj;
            //保持原有顺序
            return Arrays.stream(objs).map(SignUtil::serialize).collect(Collectors.joining(COLLECTION_ELEMENT_DELIMITER));
        }

        if (Collection.class.isAssignableFrom(clazz)) {
            Collection<?> objs = (Collection<?>) obj;
            //Collection可能是无序的，顾将每层的序列化结果都排序（升降无所谓，只要一致即可）
            return objs.stream()
                    .map(SignUtil::serialize)
                    .sorted(Comparator.comparing(Function.identity()))
                    .collect(Collectors.joining(COLLECTION_ELEMENT_DELIMITER));
        }

        if (Map.class.isAssignableFrom(clazz)) {
            //Map是无序的，顾将每层的序列化结果都排序（升降无所谓，只要一致即可）
            return parseMapSignParameter((Map<?, ?>) obj)
                    .stream()
                    .sorted(Comparator.comparing(Function.identity()))
                    .collect(Collectors.joining(PARAMETER_PAIR_DELIMITER));
        }

        //将复合对象解析为参数对，正常是自然有序的，但可能存在继承等场景（虽然也是有序的），为了清晰以及一致，顾按照字典序升序排列
        return parseCompositeSignParameter(obj.getClass(), obj).stream()
                .sorted(Comparator.comparing(Function.identity()))
                .collect(Collectors.joining(PARAMETER_PAIR_DELIMITER));
    }

    /**
     * 尝试将复合对象参数解析为参数对
     * <p>
     * 1、不包含参数对key或value等于空的数据
     */
    private static List<String> parseMapSignParameter(Map<?, ?> obj) {
        return obj.entrySet().stream()
                .map(entry -> Pair.of(serialize(entry.getKey()), serialize(entry.getValue())))
                .filter(pair -> StringUtils.isNotBlank(pair.getKey()) && StringUtils.isNotBlank(pair.getValue()))
                .map(pair -> String.format("%s%s%s", pair.getKey(), KEY_VALUE_DELIMITER, pair.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 尝试将复合对象参数解析为参数对
     * <p>
     * 1、 如果是非复合对象，或者是复合对象但没有任何参与签名的参数则返回空
     * <p>
     * 2、不包含参数对value等于空的数据
     *
     * @see Sign
     */
    private static List<String> parseCompositeSignParameter(Class<?> clazz, Object obj) {
        List<String> parameters = Lists.newArrayList();
        if (Objects.isNull(obj) || Objects.equals(clazz, Object.class)) {
            return parameters;
        }
        Field[] fields = clazz.getDeclaredFields();
        try {
            Sign sign;
            String value;
            for (Field field : fields) {
                field.setAccessible(true);
                if (Objects.nonNull(sign = field.getAnnotation(Sign.class))
                        && StringUtils.isNotBlank(value = serialize(field.get(obj)))) {
                    parameters.add(String.format("%s%s%s",
                            StringUtils.isNotBlank(sign.key()) ? sign.key() : field.getName(),
                            KEY_VALUE_DELIMITER, value));
                }
            }
            //parse super class
            parameters.addAll(parseCompositeSignParameter(clazz.getSuperclass(), obj));
        } catch (Exception e) {
            log.error("parseCompositeSignParameter error", e);
            throw new ServiceException(ErrorCodeType.PARSE_SIGN_PARAMETER_FAILED);
        }
        return parameters;
    }

    /**
     * 将目标对象解析为签名必需的参数对，将最顶层数据结构平铺为参数对
     * <p>
     * 1、不包含参数对value等于空的数据
     *
     * @param obj 待解析的签名实体，通常是一个Map或者复合对象
     */
    private static List<String> parseSignParameter(Object obj) {
        List<String> parameters = Lists.newArrayList();
        if (Objects.isNull(obj)) {
            return parameters;
        }
        Class<?> clazz = obj.getClass();
        if (Map.class.isAssignableFrom(clazz)) {
            return parseMapSignParameter((Map<?, ?>) obj);
        }
        return parseCompositeSignParameter(clazz, obj);
    }

    /**
     * @param data      待签名的JSON，适用于存在增量字段签名的场景
     * @param secretKey 签名密钥
     */
    public static String sign(JSONObject data, String secretKey) throws Exception {
        List<String> parameters = Lists.newArrayList();
        for (String key : data.keySet()) {
            String value = data.getString(key);
            if (StringUtils.isNotEmpty(value)) {
                parameters.add(String.format("%s%s%s", key, KEY_VALUE_DELIMITER, value));
            }
        }
        return sign(parameters, secretKey);
    }

    /**
     * @param data      待签名的实体
     * @param secretKey 签名密钥
     */
    public static String sign(SignParameterEntity data, String secretKey) throws Exception {
        List<String> paramPairList = Lists.newArrayList(String.format("ts%s%d", KEY_VALUE_DELIMITER, data.getTs()));
        paramPairList.addAll(parseSignParameter(data.getParam1()));
        paramPairList.addAll(parseSignParameter(data.getParam2()));
        return sign(paramPairList, secretKey);
    }

    /**
     * @param data      待签名的实体，通常是Map或者复合对象
     * @param secretKey 签名密钥
     */
    public static String sign(Object data, String secretKey) throws Exception {
        return sign(parseSignParameter(data), secretKey);
    }

    /**
     * @param parameterPairList 待签名的参数对
     * @param secretKey         签名密钥
     */
    public static String sign(@NotNull List<String> parameterPairList, @NotNull String secretKey) throws Exception {
        //将参数对按照字典序升序排列并聚合成单一字符串
        String rawParameter = parameterPairList.stream()
                .sorted(Comparator.comparing(Function.identity()))
                .collect(Collectors.joining(PARAMETER_PAIR_DELIMITER));
        return sign(rawParameter, secretKey);
    }


    /**
     * @param data      待签名的参数
     * @param secretKey 签名密钥
     */
    public static String sign(@NotNull String data, @NotNull String secretKey) throws Exception {
        Mac mac = Mac.getInstance(ALGORITHM);
        SecretKeySpec signingKey = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        mac.init(signingKey);
        //HmacSHA256产生的摘要是256bits即32B，rawHmac一定是32B
        byte[] rawHmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        //base64length = ⌈原字节长度/3⌉*4，因此rawSign一定是44B
        String rawSign = Base64.getEncoder().encodeToString(rawHmac);
        //将可能导致URL安全隐患的特殊字符替换为B
        String sign = rawSign.replaceAll("[+/=]", "B");
        log.info("sign result, rawParameter={}, rawSign={}, sign={}", data, rawSign, sign);
        return sign;
    }



    public static void main(String[] args) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("order_id", "1000351");
        paramMap.put("app_id", "bili70dae91e9c7815a2");
        String bodyJson = "";

        //预发测试
        // key:6694c0970eee4c1bacc408abd7e4324d
        // token:YjU2OTRjNGU4ZWJkNDBiMDgxNjkwODM3

        String secretKey = "YjU2OTRjNGU4ZWJkNDBiMDgxNjkwODM3";
        //a9482f1e78514516a2dedb42d68d64e2:
        SignParameterEntity signParameterEntity = new SignParameterEntity();
        long ts = System.currentTimeMillis();
        signParameterEntity.setParam1(paramMap);
        signParameterEntity.setTs(ts);
        signParameterEntity.setParam2(JSONObject.parseObject(bodyJson));
        System.out.println(sign(signParameterEntity, secretKey));
        System.out.println(ts);
    }
}
