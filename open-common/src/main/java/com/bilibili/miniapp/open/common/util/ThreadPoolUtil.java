package com.bilibili.miniapp.open.common.util;

import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.concurrent.metrics.ExecutorMetricsCollectorCache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
public final class ThreadPoolUtil {
    private static final Map<ThreadPoolType, ThreadPoolExecutor> EXECUTOR_MAP = new ConcurrentHashMap<>();

    static {
        //默认线程池
        ThreadPoolExecutor defaultExecutor = new ThreadPoolExecutor(
                16,
                128,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), ThreadPoolType.MINI_APP_OPEN_PLATFORM_DEFAULT.name()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR_MAP.put(ThreadPoolType.MINI_APP_OPEN_PLATFORM_DEFAULT, defaultExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_DEFAULT.name(), defaultExecutor);

        //ogv线程池
        //这个线程池主要负责并行查询OGV剧集、作者信息等，大多都是IO操作，因此池子可以设置大点
        ThreadPoolExecutor ogvExecutor = new ThreadPoolExecutor(
                128,
                500,
                30,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), ThreadPoolType.MINI_APP_OPEN_PLATFORM_OGV.name()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR_MAP.put(ThreadPoolType.MINI_APP_OPEN_PLATFORM_OGV, ogvExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_OGV.name(), ogvExecutor);


        //事件线程池
        //这个线程池主要负责发布事件
        ThreadPoolExecutor eventExecutor = new ThreadPoolExecutor(
                8,
                100,
                10,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), ThreadPoolType.MINI_APP_OPEN_PLATFORM_EVENT.name()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR_MAP.put(ThreadPoolType.MINI_APP_OPEN_PLATFORM_EVENT, eventExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_EVENT.name(), eventExecutor);

        //大json处理
        // 主要是IO(目前优酷数据一批是8个json, 每个json处理时间又过长
        ThreadPoolExecutor largeJsonExecutor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 4,
                Runtime.getRuntime().availableProcessors() * 4 * 2,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), ThreadPoolType.LARGE_JSON.name()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR_MAP.put(ThreadPoolType.LARGE_JSON, largeJsonExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.LARGE_JSON.name(), largeJsonExecutor);

        //优酷图片处理(主要下载优酷图片并上传到BOSS)理论上处理很快
        ThreadPoolExecutor youkuImageExecutor = new ThreadPoolExecutor(
                20,
                100,
                10,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), ThreadPoolType.YOU_KU_IMAGE.name()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR_MAP.put(ThreadPoolType.YOU_KU_IMAGE, youkuImageExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.YOU_KU_IMAGE.name(), youkuImageExecutor);

        //boss线程池
        ThreadPoolExecutor bossExecutor = new ThreadPoolExecutor(
                1,
                10,
                0,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                r -> new Thread(r, ThreadPoolType.BOSS.name()),
                new ThreadPoolExecutor.CallerRunsPolicy());
        EXECUTOR_MAP.put(ThreadPoolType.BOSS, bossExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.BOSS.name(), bossExecutor);


        //短剧小程序查询线程池
        ThreadPoolExecutor shortPlayExecutor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 2,
                Runtime.getRuntime().availableProcessors() * 2 * 2,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), ThreadPoolType.MINI_APP_OPEN_PLATFORM_SHORT_PLAY.name()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        EXECUTOR_MAP.put(ThreadPoolType.MINI_APP_OPEN_PLATFORM_SHORT_PLAY, shortPlayExecutor);
        ExecutorMetricsCollectorCache.addThreadPoolExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_SHORT_PLAY.name(), shortPlayExecutor);


    }

    public static ThreadPoolExecutor getExecutor(ThreadPoolType threadPoolType) {
        return EXECUTOR_MAP.get(threadPoolType);
    }
}
