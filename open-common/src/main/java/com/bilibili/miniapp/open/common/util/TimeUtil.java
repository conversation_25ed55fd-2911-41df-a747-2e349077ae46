package com.bilibili.miniapp.open.common.util;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;


public class TimeUtil {

    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.S";

    public static final Integer SECOND = 1000;

    public static final Integer MINUTE = 60 * SECOND;

    public static final Integer HOUR = 60 * MINUTE;

    /**
     * 把long类型时间格式化成字符串。
     */
    public static String longFormat(Long time) {
        if (time == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_TIME_FORMAT).format(time);
    }

    /**
     * 字符串转时间
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static Date convertDateByStr(String dateStr, String format) {
        try {
            return new SimpleDateFormat(format).parse(dateStr);
        } catch (ParseException e) {
            throw new IllegalArgumentException("date parse error");
        }
    }

    /**
     * 字符串转时间戳
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static Timestamp getTimestampByStr(String dateStr, String format) {
        try {
            Date date = new SimpleDateFormat(format).parse(dateStr);
            return new Timestamp(date.getTime());
        } catch (ParseException e) {
            throw new IllegalArgumentException("timestamp parse error");
        }
    }

    public static Timestamp getTimestampByDateTimeStr(String dateStr) {
        try {
            Date date = new SimpleDateFormat(DATE_TIME_FORMAT).parse(dateStr);
            return new Timestamp(date.getTime());
        } catch (ParseException e) {
            throw new IllegalArgumentException("timestamp parse error");
        }
    }

    /**
     * 字符串转时间戳（单位：毫秒）
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static Long getTimeByStr(String dateStr, String format) {
        try {
            Date date = new SimpleDateFormat(format).parse(dateStr);
            return date.getTime();
        } catch (ParseException e) {
            throw new IllegalArgumentException("timestamp parse error");
        }
    }

    /**
     * 获取今天的日期字符串。
     */
    public static String getDayString() {
        return new java.sql.Date(System.currentTimeMillis()).toString();
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String format(Date date, String format) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_FORMAT).format(date);
    }

    public static String timestampToString(Timestamp time) {
        if (time == null) {
            return null;
        }
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(time);
    }

    public static String timestampToString(Timestamp time, String format) {
        if (time == null) {
            return null;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(time);
    }

    public static String timestampToDateTimeString(Timestamp time) {
        if (time == null) {
            return null;
        }
        DateFormat formatter = new SimpleDateFormat(DATE_TIME_FORMAT);
        return formatter.format(time);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String formatDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_TIME_FORMAT).format(date);
    }

    /**
     * 把日期类型格式化成字符串。
     */
    public static String formatTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(TIMESTAMP_FORMAT).format(date);
    }

    /**
     * 转sql的time格式。
     */
    public static Timestamp toTimestamp(Date date) {
        if (date == null) {
            return null;
        }
        return new Timestamp(date.getTime());
    }

    /**
     * 转sql的time格式。
     */
    public static Date getDateByTimestamp(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp.getTime());
    }

    /**
     * 获取当前Timestamp。
     */
    public static Timestamp nowTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    /**
     * 获取当前Timestamp。
     */
    public static java.sql.Date nowSqlDate() {
        return new java.sql.Date(System.currentTimeMillis());
    }

    /**
     * 获取今天开始的 Timestamp。
     */
    public static Timestamp todayStart() {
        Calendar calendar = Calendar.getInstance();
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 获取今天开始的 Sql Date。
     */
    public static java.sql.Date todayStartSqlDate() {
        Calendar calendar = Calendar.getInstance();
        setDayStart(calendar);
        return new java.sql.Date(calendar.getTimeInMillis());
    }

    /**
     * 获取今天结束的 Timestamp。
     */
    public static Timestamp todayEnd() {
        Calendar calendar = Calendar.getInstance();
        setDayEnd(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 获取今天结束的 Sql Date。
     */
    public static java.sql.Date todayEndSqlDate() {
        Calendar calendar = Calendar.getInstance();
        setDayEnd(calendar);
        return new java.sql.Date(calendar.getTimeInMillis());
    }

    /**
     * 设置23:59:59.999。
     */
    private static void setDayEnd(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }

    /**
     * 设置00:00:00.0。
     */
    private static void setDayStart(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 设置23:59:59.999。
     */
    public static Timestamp getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        setDayEnd(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置00:00:00.000。
     */
    public static Timestamp getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置昨天的00:00:00.0。
     */
    public static Timestamp afterDayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 1);
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置昨天的00:00:00.0。
     */
    public static Timestamp yesterdayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置昨天的23:59:59.999。
     */
    public static Timestamp yesterdayEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        setDayEnd(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置上月开始的00:00:00.0。
     */
    public static Timestamp lastMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置上月结束的23:59:59.999。
     */
    public static Timestamp lastMonthEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        setDayEnd(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置本月开始的00:00:00.0。
     */
    public static Timestamp thisMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置本月结束的23:59:59.999。
     */
    public static Timestamp thisMonthEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        setDayEnd(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置日期所在月开始的00:00:00.0。
     */
    public static Timestamp monthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        setDayStart(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 设置日期所在月结束的23:59:59.999。
     */
    public static Timestamp monthEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        setDayEnd(calendar);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 获取日期的日。
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获取日期的月。
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取日期的年。
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取日期的时。
     */
    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取日期的分种。
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 获取日期的秒。
     */
    public static int getSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.SECOND);
    }

    /**
     * 获取星期几。
     */
    public static int getWeekDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek - 1;
    }

    /**
     * 获取月份的天数。
     */
    public static int getDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取哪一年共有多少周。
     */
    public static int getMaxWeekNumOfYear(int year) {
        Calendar c = new GregorianCalendar();
        c.set(year, Calendar.DECEMBER, 31, 23, 59, 59);
        return getWeekNumOfYear(c.getTime());
    }

    /**
     * 取得某天是一年中的多少周。
     */
    public static int getWeekNumOfYear(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setMinimalDaysInFirstWeek(7);
        c.setTime(date);
        return c.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 取得某天所在周的第一天。
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
        return c.getTime();
    }

    /**
     * 取得某天所在周的最后一天。
     */
    public static Date getLastDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6);
        return c.getTime();
    }

    /**
     * 取得某年某周的第一天（周属于周一所在的年）。
     */
    public static Date getFirstDayOfWeek(int year, int week) {
        Calendar calFirst = Calendar.getInstance();
        calFirst.set(year, 0, 7);
        Date firstDate = getFirstDayOfWeek(calFirst.getTime());

        Calendar firstDateCal = Calendar.getInstance();
        firstDateCal.setTime(firstDate);

        Calendar c = new GregorianCalendar();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DATE, firstDateCal.get(Calendar.DATE));

        Calendar cal = (GregorianCalendar) c.clone();
        cal.add(Calendar.DATE, (week - 1) * 7);
        return getFirstDayOfWeek(cal.getTime());
    }

    /**
     * 取得某年某周的最后一天 （周属于周一所在的年）。
     */
    public static Date getLastDayOfWeek(int year, int week) {
        Calendar calLast = Calendar.getInstance();
        calLast.set(year, 0, 7);
        Date firstDate = getLastDayOfWeek(calLast.getTime());

        Calendar firstDateCal = Calendar.getInstance();
        firstDateCal.setTime(firstDate);

        Calendar c = new GregorianCalendar();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DATE, firstDateCal.get(Calendar.DATE));

        Calendar cal = (GregorianCalendar) c.clone();
        cal.add(Calendar.DATE, (week - 1) * 7);
        return getLastDayOfWeek(cal.getTime());
    }

    /**
     * 日期字段操作，见Calendar中的常量。
     */
    private static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(calendarField, amount);
            return c.getTime();
        }
    }

    /**
     * 增加年。
     */
    public static Date addYears(Date date, int amount) {
        return add(date, Calendar.YEAR, amount);
    }

    /**
     * 增加月。
     */
    public static Date addMonths(Date date, int amount) {
        return add(date, Calendar.MONTH, amount);
    }

    /**
     * 增加周。
     */
    public static Date addWeeks(Date date, int amount) {
        return add(date, Calendar.WEEK_OF_YEAR, amount);
    }

    /**
     * 增加天。
     */
    public static Date addDays(Date date, int amount) {
        return add(date, Calendar.DATE, amount);
    }

    /**
     * 增加时。
     */
    public static Date addHours(Date date, int amount) {
        return add(date, Calendar.HOUR, amount);
    }

    /**
     * 增加分。
     */
    public static Date addMinutes(Date date, int amount) {
        return add(date, Calendar.MINUTE, amount);
    }

    /**
     * 增加秒。
     */
    public static Date addSeconds(Date date, int amount) {
        return add(date, Calendar.SECOND, amount);
    }

    /**
     * 增加毫秒。
     */
    public static Date addMilliseconds(Date date, int amount) {
        return add(date, Calendar.MILLISECOND, amount);
    }

    /**
     * time差。
     */
    public static long diffTimes(Date before, Date after) {
        return after.getTime() - before.getTime();
    }

    /**
     * 秒差。
     */
    public static long diffSecond(Date before, Date after) {
        return (after.getTime() - before.getTime()) / 1000;
    }

    /**
     * 分种差。
     */
    public static int diffMinute(Date before, Date after) {
        return (int) (after.getTime() - before.getTime()) / 60000;
    }

    /**
     * 时差。
     */
    public static int diffHour(Date before, Date after) {
        return (int) (after.getTime() - before.getTime()) / 3600000;
    }

    /**
     * 天数差。
     */
    public static int diffDay(Date before, Date after) {
        return (int) ((after.getTime() - before.getTime()) / 86400000);
    }

    /**
     * 年差。
     */
    public static int diffYear(Date before, Date after) {
        return getYear(after) - getYear(before);
    }

    /**
     * 月差。
     */
    public static int diffMonth(Date before, Date after) {
        int yearsX = diffYear(before, after);
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(before);
        c2.setTime(after);
        int monthsX = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH);
        int monthAll = yearsX * 12 + monthsX;
        int daysX = c2.get(Calendar.DATE) - c1.get(Calendar.DATE);
        if (daysX > 0) {
            monthAll = monthAll + 1;
        }
        return monthAll;
    }

    /**
     * 休眠一毫秒。
     */
    public static void sleepAMilli() {
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将毫秒数转换为格式 00:12:30
     *
     * @param millSecond
     * @return
     */
    public static String buildBarrageShowTime(Long millSecond) {
        long hour = 0;
        long minute = 0;
        long second = 0;
        if (millSecond >= HOUR) {
            hour = millSecond / HOUR;
            millSecond = millSecond % HOUR;
        }
        if (millSecond >= MINUTE) {
            minute = millSecond / MINUTE;
            millSecond = millSecond % MINUTE;
        }
        if (millSecond >= SECOND) {
            second = millSecond / SECOND;
        }
        return int2FormatStr(hour) + ":" + int2FormatStr(minute) + ":" + int2FormatStr(second);
    }

    public static String int2FormatStr(long i) {
        if (i == 0) {
            return "00";
        } else if (i < 10) {
            return "0" + i;
        } else {
            return i + "";
        }
    }

    /**
     * 判断时间是否相同
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean isEquals(Timestamp a, Timestamp b) {
        if ((a == null && b == null) || (a != null && b != null && a.equals(b))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 设置日期最近N天的开始日期。
     */
    public static Timestamp rencentNdaysFirst(Integer days) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -days);
        return new Timestamp(calendar.getTimeInMillis());
    }

    /**
     * 把字符串转化为时间。
     */
    public static Date parseStringToDate(String stringTime) throws ParseException {
        if (stringTime == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_FORMAT).parse(stringTime);
    }

    /**
     * 格式化年月yyyy-MM
     */
    public static String getMonthAndYearString() {
        return new SimpleDateFormat("yyyy-MM").format(new Date());
    }

    public static String getMonthAndYearStringByTimeStamp(Date date) {
        return new SimpleDateFormat("yyyy-MM").format(date);
    }

    public static Timestamp getMaxTime(Timestamp timestamp1, Timestamp timestamp2) {
        return timestamp1.after(timestamp2) ? timestamp1 : timestamp2;
    }


    public static Timestamp curTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }


}
