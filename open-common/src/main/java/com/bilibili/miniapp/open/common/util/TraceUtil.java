package com.bilibili.miniapp.open.common.util;


import org.slf4j.MDC;

import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2022/12/9 11:23
 */
public final class TraceUtil {
    //没有采用Under Score Case风格的trace_id，主要是和ops_log已有的traceid命名保持一致
    public final static String TRACE_ID = "traceid";

    public static String genTraceId() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    public static Runnable decorate(Runnable runnable) {
        String traceId = MDC.get(TraceUtil.TRACE_ID);
        if (traceId == null || traceId.isEmpty()) {
            traceId = genTraceId();
        }
        final String fTraceId = traceId;
        return () -> {
            try {
                MDC.put(TraceUtil.TRACE_ID, fTraceId);
                runnable.run();
            } finally {
                MDC.remove(TraceUtil.TRACE_ID);
            }
        };
    }


    public static <T> Callable<T> decorate(Callable<T> callable) {
        String traceId = MDC.get(TraceUtil.TRACE_ID);
        if (traceId == null || traceId.isEmpty()) {
            traceId = genTraceId();
        }
        final String fTraceId = traceId;
        return () -> {
            try {
                MDC.put(TraceUtil.TRACE_ID, fTraceId);
                return callable.call();
            } finally {
                MDC.remove(TraceUtil.TRACE_ID);
            }
        };
    }


    public static <T> Supplier<T> decorateSupplier(Supplier<T> supplier) {
        String traceId = MDC.get(TraceUtil.TRACE_ID);
        if (traceId == null || traceId.isEmpty()) {
            traceId = genTraceId();
        }
        final String fTraceId = traceId;
        return () -> {
            try {
                MDC.put(TraceUtil.TRACE_ID, fTraceId);
                return supplier.get();
            } finally {
                MDC.remove(TraceUtil.TRACE_ID);
            }
        };
    }
}
