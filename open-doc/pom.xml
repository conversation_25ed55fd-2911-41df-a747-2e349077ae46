<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bilibili.miniapp</groupId>
        <artifactId>open-platform</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>open-platform-doc</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>open-doc</name>
    <description>open-doc</description>
    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.miniapp</groupId>
            <artifactId>open-platform-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.yaml</groupId>-->
        <!--            <artifactId>snakeyaml</artifactId>-->
        <!--        </dependency>-->
    </dependencies>


</project>
