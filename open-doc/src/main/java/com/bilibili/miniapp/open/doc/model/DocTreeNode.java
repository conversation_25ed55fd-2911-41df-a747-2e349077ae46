package com.bilibili.miniapp.open.doc.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/12 14:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocTreeNode implements Serializable {
    private static final long serialVersionUID = 111525764196241713L;
    //节点
    private String node;
    //节点名称，目录名称或者API名称
    private String name;
    //0：非叶子节点，1：叶子节点
    private Integer type;
    //顺序，从0开始，作用于同级才有意义
    private Integer seq;
    //doc的路径
    private String location;
    //版本，采用二阶版本，xx.yy
    private String version;
    //子节点
    private List<DocTreeNode> children;
}
