package com.bilibili.miniapp.open.doc.service.impl;

import com.bilibili.miniapp.open.common.entity.BFSKey;
import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.enums.OpenDocStatus;
import com.bilibili.miniapp.open.doc.model.DocTreeNode;
import com.bilibili.miniapp.open.doc.model.DocTreeBo;
import com.bilibili.miniapp.open.doc.service.IOpenDocService;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenDocDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenDocPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenDocPoExample;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.nodes.Node;
import org.yaml.snakeyaml.nodes.SequenceNode;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:36
 */
@Slf4j
@Service
public class OpenDocServiceImpl implements IOpenDocService {
    @Autowired
    private MiniAppOpenDocDao miniAppOpenDocDao;
    @Autowired
    private IBFSService bfsService;

    /**
     * 一定在事务内执行，防止已提交的部分数据在线上可见
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish() throws Exception {
        List<DocTreeNode> docTrees;
        DefaultResourceLoader resolver = new DefaultResourceLoader();
        Resource resource = resolver.getResource("classpath:open_doc/miniapp_open_doc.yaml");
        try (InputStream in = resource.getInputStream()) {
            docTrees = new Yaml(new ListConstructor<>(DocTreeNode.class)).load(in);
        } catch (Exception ex) {
            log.error("[OpenDocServiceImpl] publish open doc error", ex);
            throw ex;
        }
        Assert.notEmpty(docTrees, "docs must be exist.");
        Map<String, String> nodeMap = Maps.newHashMap();
        List<Long> docIdList = Lists.newArrayList();
        //step1：上传markdown至bfs
        upload(docTrees, nodeMap);
        List<Long> oldDocIdList = selectCurOpenDocList().stream().map(MiniAppOpenDocPo::getId).collect(Collectors.toList());
        //step2：刷新最新版本文档
        refresh(docTrees, 0L, nodeMap, docIdList);
        //step3：启用最新版本文档，同时禁用旧版本文档
        start(docIdList, oldDocIdList);
    }

    @Override
    public List<DocTreeBo> getCurDocTree() {
        List<MiniAppOpenDocPo> miniAppOpenDocPos = selectCurOpenDocList();
        return toDocTree(miniAppOpenDocPos, 0L);
    }

    private void upload(List<DocTreeNode> docTrees, Map<String, String> result) throws IOException {
        if (CollectionUtils.isEmpty(docTrees)) {
            return;
        }
        for (DocTreeNode docTree : docTrees) {
            if (StringUtils.hasText(docTree.getLocation())) {
                DefaultResourceLoader resolver = new DefaultResourceLoader();
                Resource resource = resolver.getResource("classpath:" + docTree.getLocation());
                Path tempPath = Files.createTempFile("", ".md");
                try (InputStream inputStream = resource.getInputStream()) {
                    Files.copy(inputStream, tempPath, StandardCopyOption.REPLACE_EXISTING);
                }
                /**
                 * 不能直接使用resource.getFile()，在容器上可能会抛出如下异常
                 * java.io.FileNotFoundException: class path resource [xxx] cannot be resolved to absolute file path
                 * because it does not reside in the file system: jar:file
                 */
                BFSUploadResult upload = bfsService.upload(BFSKey.CATEGORY_MINIAPP_OPEN_DOC, tempPath.toFile());
                result.put(docTree.getNode(), upload.getUrl());
            }
            upload(docTree.getChildren(), result);
        }
    }

    private void refresh(List<DocTreeNode> docTrees, Long parentId, Map<String, String> nodeMap, List<Long> docIdList) {
        if (CollectionUtils.isEmpty(docTrees)) {
            return;
        }
        for (DocTreeNode docTree : docTrees) {
            MiniAppOpenDocPo openDocPo = MiniAppOpenDocPo.builder()
                    .node(docTree.getNode())
                    .name(docTree.getName())
                    .type(docTree.getType())
                    .seq(docTree.getSeq())
                    .status(OpenDocStatus.INVALID.getCode())
                    .location(nodeMap.getOrDefault(docTree.getNode(), ""))
                    .version(docTree.getVersion())
                    .parentId(parentId)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .build();
            miniAppOpenDocDao.insertSelective(openDocPo);
            docIdList.add(openDocPo.getId());
            refresh(docTree.getChildren(), openDocPo.getId(), nodeMap, docIdList);
        }
    }

    private void start(List<Long> docIdList, List<Long> oldDocIdList) {
        if (!CollectionUtils.isEmpty(oldDocIdList)) {
            MiniAppOpenDocPoExample example = new MiniAppOpenDocPoExample();
            MiniAppOpenDocPoExample.Criteria criteria = example.createCriteria();
            criteria.andIdIn(oldDocIdList);
            miniAppOpenDocDao.updateByExampleSelective(MiniAppOpenDocPo.builder()
                    .status(OpenDocStatus.INVALID.getCode())
                    .build(), example);
        }
        MiniAppOpenDocPoExample example = new MiniAppOpenDocPoExample();
        MiniAppOpenDocPoExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(docIdList);
        miniAppOpenDocDao.updateByExampleSelective(MiniAppOpenDocPo.builder()
                .status(OpenDocStatus.VALID.getCode())
                .build(), example);
    }

    private List<DocTreeBo> toDocTree(List<MiniAppOpenDocPo> docPoList, Long parentId) {
        return docPoList.stream()
                .filter(doc -> Objects.equals(doc.getParentId(), parentId))
                .map(doc -> DocTreeBo.builder()
                        .id(doc.getId())
                        .parentId(doc.getParentId())
                        .node(doc.getNode())
                        .name(doc.getName())
                        .type(doc.getType())
                        .seq(doc.getSeq())
                        .location(doc.getLocation())
                        .children(toDocTree(docPoList, doc.getId()))
                        .build())
                .sorted(Comparator.comparing(DocTreeBo::getSeq))
                .collect(Collectors.toList());
    }

    private List<MiniAppOpenDocPo> selectCurOpenDocList() {
        MiniAppOpenDocPoExample example = new MiniAppOpenDocPoExample();
        MiniAppOpenDocPoExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(OpenDocStatus.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return miniAppOpenDocDao.selectByExample(example);
    }

    static class ListConstructor<T> extends Constructor {
        private final Class<T> clazz;

        public ListConstructor(final Class<T> clazz) {
            this.clazz = clazz;
        }

        @Override
        protected Object constructObject(final Node node) {
            if (node instanceof SequenceNode && isRootNode(node)) {
                ((SequenceNode) node).setListType(clazz);
            }
            return super.constructObject(node);
        }

        private boolean isRootNode(final Node node) {
            return node.getStartMark().getIndex() == 0;
        }
    }
}
