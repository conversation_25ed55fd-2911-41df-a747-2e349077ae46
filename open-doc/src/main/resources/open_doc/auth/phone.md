# 手机号授权

## 1 接口说明
该接口用于小程序手机号授权登录，获取用户手机号。

## 2 请求说明

### 2.1 基础信息

|     属性      |                    说明                     |
|:-----------:|:-----------------------------------------:|
|     域名      |       https://miniapp.bilibili.com        |
|     路径      | /open/open_api/v1/platform/phone/exchange |
|   Method    |                   POST                    |
| ContentType |             application/json              |

### 2.2 请求参数

|      参数      |   类型   | 必填 | 位置（query、header、body） | 是否参与签名 |                      示例                      |        说明        |
|:------------:|:------:|:--:|:---------------------:|:------:|:--------------------------------------------:|:----------------:|
|  access_key  | String | 是  |         query         |   否    |       b6dj2f1e785149fjp2dedbiad68dwl9y       | access_key，接入前申请 |
|     sign     | String | 是  |         query         |   否    | WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B |  签名，参考【签名计算准则】   |
|      ts      |  Long  | 是  |         query         |   是    |                1736739534331                 |     时间戳，单位毫秒     |
|    app_id    | String | 是  |         body          |   是    |              bili388fh0g748hdj               |    小程序app_id     |
|   open_id    | String | 是  |         body          |   是    |                1736739534331                 |    小程序open_id    |
| preauth_code | String | 是  |         body          |   是    |         PRE_20231010123456_abcd1234          |   小程序获取手机号的授权码   |

## 3 响应说明

### 3.1 响应信息

|                    字段                     |           类型            |   示例    |                        说明                        |
|:-----------------------------------------:|:-----------------------:|:-------:|:------------------------------------------------:|
|                   code                    |          Long           |    0    | 错误码，0表示成功，其他表示异常，具体异常信息见message字段，详细错误码参考【状态码】介绍 |
|                  message                  |         String          | success |                       错误信息                       |
|                   data                    | PhoneExchangeDetailResp |  见完整示例  |                  接口返回值，包含订单详情信息                  |
| `PhoneExchangeDetailResp`.encrypted_phone |         String          |    -    |              加密后的手机号，参考【开放数据校验与解密】               |
|       `PhoneExchangeDetailResp`.iv        |         String          |    -    |                解密向量，参考【开放数据校验与解密】                |

### 3.2 错误码

| code |    说明（具体错误信息见message字段）    |
|:----:|:--------------------------:|
|  0   |            请求成功            |
| 100  |         系统繁忙，请稍后再试         |
| 102  |     程序内部错误，可联系B站相关同学排查     |
| 104  |      参数异常，如接口参数不符合规范等      |
| 105  | unauthorized，如小程序访问未授权的剧集等 |
| 106  |  invalid sign，如接口参数签名不正确等  |
| 107  |   解析签名参数失败，如解析接口签名参数异常等    |
| 200  |     数据不存在，如未查询到有效的小程序等     |
| 201  |    非法数据，如不符合业务逻辑安全性检查等     |
| 402  |           凭证已失效            |

备注：后续有新增或者变化，会持续新增

### 3.3 完整示例

#### 3.3.1 请求示例

URL：  
{API}?access_key=b6dj2f1e785149fjp2dedbiad68dwl9y&ts=1736739534331&sign=WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B

Body:
```json
{
    "app_id": "bili4a144dc9519e38b8",
    "open_id": "fba20827e95da8ada251be6bab2bb679",
    "preauth_code": "PRE_AUTH_1741958522488_fe11657247b54a75a855a4d9a6d38cb6"
}
```

#### 3.3.2 响应示例
```json
{
  "data": {
    "encrypted_phone": "uXkJiU7eAHbDcHKLTrbJ4Q==",
    "iv": "$YOCLvODjA7PbkQB"
  },
  "code": 0,
  "message": "success",
  "current_time": 1739871452085
}
```

## 4 解密说明

参考【开放数据校验与解密】

示例：
```java
public class AESDecryptor {
    public static String decrypt(String encryptedData, String secretKey, String iv) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes());
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decodedEncryptedData = Base64.getUrlDecoder().decode(encryptedData);
        byte[] original = cipher.doFinal(decodedEncryptedData);
        return new String(original);
    }

    public static void main(String[] args) {
        try {
            String encryptedData = "加密手机号";
            String secretKey = "B站小程序团队获取";
            String iv = "接口返回";
            String decryptedData = decrypt(encryptedData, secretKey, iv);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

## 5 版本变更

| 版本  |      说明      |         时间          |
|:---:|:------------:|:-------------------:|
| 1.0 | 新增手机号授权相关API | 2025-03-12 17:45:14 |