# 支付与回调

## 1 接口说明
开发者根据自身业务，定制多样的支付场景，本接口用于支付场景之前的创单行为。

## 2 请求说明

### 2.1 基础信息

|     属性      |                   说明                    |
|:-----------:|:---------------------------------------:|
|     域名      |      https://miniapp.bilibili.com       |
|     路径      | /open/open_api/v1/platform/order/create |
|   Method    |                  POST                   |
| ContentType |            application/json             |

### 2.2 请求参数

|      参数      |   类型    | 必填  | 位置（query、header、body） | 是否参与签名 |                      示例                      |                               说明                               |
|:------------:|:-------:|:---:|:---------------------:|:------:|:--------------------------------------------:|:--------------------------------------------------------------:|
|  access_key  | String  |  是  |         query         |   否    |       b6dj2f1e785149fjp2dedbiad68dwl9y       |                        access_key，接入前申请                        |
|     sign     | String  |  是  |         query         |   否    | WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B |                         签名，参考【签名计算准则】                          |
|      ts      |  Long   |  是  |         query         |   是    |                1736739534331                 |                            时间戳，单位毫秒                            |
|    app_id    | String  |  是  |         body          |   是    |              bili388fh0g748hdj               |                           小程序app_id                            |
|   open_id    | String  |  是  |         body          |   是    |                1736739534331                 |                           小程序open_id                           |
| dev_order_id | String  |  是  |         body          |   是    |                  m123456789                  | 开发者业务订单号（<=32字符），<br/>【app_id，dev_order_id】二元组每次请求必须唯一，业务方自行保证 |
|    amount    |  Long   |  是  |         body          |   是    |                     100                      |           订单金额，单位分（>0），如果是B币支付，则一定是100倍数即整元，否则无法拉取支付           |
| product_type | Integer |  是  |         body          |   是    |                      1                       |                              商品类型                              |
|  product_id  | String  |  是  |         body          |   是    |                  p123456789                  |                          商品id（<=32字符）                          |
| product_name | String  |  是  |         body          |   是    |                     天龙八部                     |                   商品名称，不要包含如%&等特殊字符（<=32字符）                    |
| product_desc | String  |  否  |         body          |   是    |    以宋哲宗时代为背景，各国之间的武林恩怨和民族矛盾，对人生和社会进行审视和描写    |                   商品描述，不要包含如%&等特殊字符（<=64字符）                    |
|  notify_url  | String  |  否  |         body          |   是    |                   支付状态回调地址                   |            不要携带任何参数，系统回调时会自动拼接相关签名参数，建议接入（<=128字符）             |
|  extra_data  | String  |  否  |         body          |   是    |             {"a":1,"b":"4567dd"}             |                 拓展信息，开放平台不感知，回调时会原样返回（<=512字符）                 |

商品类型目前主要有以下几种，开发者根据自己的业务场景，选择对应的`product_type`以及`product_id`，建议开发者合理、准确传值，促进双方系统精准化运营和归因。

| product_type |           product_id           |                      说明                      |
|:------------:|:------------------------------:|:--------------------------------------------:|
|      0       |               0                |                      未知                      |
|      1       | B站剧id<br/>（业务方根据自身系统保存的映射关系获取） | 解锁剧集<br/>如果是某具体的集，可以将B站集id透传在`extra_data`字段中 |
|      2       |            业务方相关id             |                  购买优惠券/储值卡                   |
|      3       |            业务方相关id             |                     购买会员                     |
|      4       |            业务方相关id             |                    购买虚拟货币                    |


## 3 响应说明

### 3.1 响应信息

|            字段             |       类型       |   示例    |                                               说明                                               |
|:-------------------------:|:--------------:|:-------:|:----------------------------------------------------------------------------------------------:|
|           code            |      Long      |    0    |                        错误码，0表示成功，其他表示异常，具体异常信息见message字段，详细错误码参考【状态码】介绍                        |
|          message          |     String     | success |                                              错误信息                                              |
|           data            | OrderCreateRes |  见完整示例  | 接口返回值，将该字段完全透传给支付SDK即可。<br/>为了解决用户不能及时支付但后续重新支付的问题，业务方可以留存在自己系统中，根据自身业务逻辑选择重新下单还是重新支付（15分钟内有效） |
| `OrderCreateRes`.order_id |     String     |  10001  |                                    小程序开放平台订单号，建议留存，退款流程会使用                                     |
| `OrderCreateRes`.pay_info |     String     |    -    |                                    支付信息（15分钟内有效），开发者无需关心内容                                     |

### 3.2 错误码

| code |    说明（具体错误信息见message字段）    |
|:----:|:--------------------------:|
|  0   |            请求成功            |
| 100  |         系统繁忙，请稍后再试         |
| 102  |     程序内部错误，可联系B站相关同学排查     |
| 104  |      参数异常，如接口参数不符合规范等      |
| 105  | unauthorized，如小程序访问未授权的剧集等 |
| 106  |  invalid sign，如接口参数签名不正确等  |
| 107  |   解析签名参数失败，如解析接口签名参数异常等    |
| 200  |     数据不存在，如未查询到有效的小程序等     |
| 201  |    非法数据，如不符合业务逻辑安全性检查等     |

备注：后续有新增或者变化，会持续新增

### 3.3 完整示例

#### 3.3.1 请求示例

URL：  
{API}?access_key=b6dj2f1e785149fjp2dedbiad68dwl9y&ts=1736739534331&sign=WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B

Body：
```json
{
  "open_id": "12345678900101",
  "dev_order_id": "m123456789",
  "app_id": "bili388fh0g748hdj",
  "amount": 100,
  "product_type": 1,
  "product_id": "1234567",
  "product_name": "天龙八部",
  "product_desc": "以宋哲宗时代为背景，各国之间的武林恩怨和民族矛盾，对人生和社会进行审视和描写",
  "notify_url": "https://demo.com/order/callback",
  "extra_data": "{\"a\":1,\"b\":\"4567dd\"}"
}
```
#### 3.3.2 响应示例
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "order_id": "123456789",
    "pay_info": "57Gz5YiH5bCU5bCx6KW6JeP5a6a939fjd3KudH5pel6YGt5YX5Zyw6ZyH5ZCR5Lit5Zu95pS5bqc"
  }
}
```

## 4 回调通知
回调通知通常发生在支付成功等交易场景，建议开发者接入。


### 4.1 回调参数

- 回调形式

|     属性      |                   说明                    |
|:-----------:|:---------------------------------------:|
|   Method    |                  POST                   |
| ContentType |            application/json             |

- 回调地址
  开放平台会根据开发者创单时使用的`access_token`进行数据签名，比如创单时的`notify_url=https:\//demo.com/order/callback`，
  则实际开放平台回调的URL是`https:\//demo.com/order/callback?ts=1736750625059&sign=11GNoWSnBtgpK59lnip06iPYdJgiTc2w6T2BI7Bcpo4B`，开发者根据相关参数进行参数签名校验。

- 详细回调请求

|      字段      |   类型    | 位置（query、header、body） | 是否参与签名 |                      示例                      |           说明           |
|:------------:|:-------:|:---------------------:|:------:|:--------------------------------------------:|:----------------------:|
|     sign     | String  |         query         |   否    | 11GNoWSnBtgpK59lnip06iPYdJgiTc2w6T2BI7Bcpo4B |           签名           |
|      ts      |  Long   |         query         |   是    |                1736750625059                 |        时间戳（毫秒）         |
|   order_id   | String  |         body          |   是    |                  123456789                   |       小程序开放平台订单号       |
| dev_order_id | String  |         body          |   是    |                  m123456789                  |       开发者系统业务订单号       |
|    amount    |  Long   |         body          |   是    |                     100                      |        订单金额，单位分        |
|  pay_amount  |  Long   |         body          |   是    |                     100                      |   实付金额，单位分，目前等于订单金额    |
|   pay_time   |  Long   |         body          |   是    |                1736752136959                 |          支付时间          |
|  pay_status  | Integer |         body          |   是    |                      1                       | 支付状态，1：成功，目前仅在支付成功后回调  |
| pay_channel  | Integer |         body          |   是    |                      1                       | 支付渠道，1：支付宝，2：微信，3：B币支付 |
|  extra_data  | String  |         body          |   是    |             {"a":1,"b":"4567dd"}             |      创单时的自定义拓展参数       |  

- 确保响应体的结构如下，其中`code=0`则认为回调成功，否则认为回调失败

```json
{
  "code": 0,
  "message": "success"
}
```

- 请尽可能保证回调服务的稳定性和幂等性，开放平台保证在支付成功后及时回调，如果该接口回调失败，则依次按照5s, 10s, 20s, 1min, 3min, 5min, 30min, 1hour的梯度重试，所有重试梯度使用完之后，放弃回调

- 业务方在校验签名时，要兼容后续开放平台返回增量字段的能力，因此建议使用`JSON`接收回调信息，然后生成签名和校验签名，签名验证通过后，再反序列化自身关注的字段。

### 4.2 完整示例

#### 4.2.1 请求示例

```json
{
  "order_id": "123456789",
  "dev_order_id": "m123456789",
  "amount": 100,
  "pay_amount": 100,
  "pay_time": 1736752136959,
  "pay_status": 1,
  "extra_data": "{\"a\":1,\"b\":\"4567dd\"}"
}
```

#### 4.2.2 响应示例

```json
{
  "code": 0,
  "message": "success"
}
```

## 5 版本变更

| 版本  |                     说明                     |         时间          |
|:---:|:------------------------------------------:|:-------------------:|
| 1.0 |                 补充支付相关API                  | 2025-01-13 15:30:00 |
| 1.1 | 补充相关API使用细节，如支付有效期、extra_data参与签名、回调签名兼容性等 | 2025-01-18 11:34:00 |
| 1.2 |        补充回调请求的细节，将ts、sign参数在回调请求中明确        | 2025-02-11 15:05:00 |
| 1.3 |  支付接口中的`product_name`,`product_desc`加入签名   | 2025-02-14 14:49:24 |
| 1.4 |          回调接口参数新增`pay_channel`字段           | 2025-02-24 12:06:47 |
