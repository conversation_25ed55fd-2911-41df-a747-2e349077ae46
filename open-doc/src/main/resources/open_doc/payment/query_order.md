# 订单查询

## 1 接口说明
该接口用于查询已创建的订单详情。

## 2 请求说明

### 2.1 基础信息

|     属性      |                   说明                    |
|:-----------:|:---------------------------------------:|
|     域名      |      https://miniapp.bilibili.com       |
|     路径      | /open/open_api/v1/platform/order/query  |
|   Method    |                   GET                   |
| ContentType |            application/json             |

### 2.2 请求参数

|     参数     |   类型   | 必填 | 位置（query、header、body） | 是否参与签名 |                      示例                      |        说明        |
|:----------:|:------:|:--:|:---------------------:|:------:|:--------------------------------------------:|:----------------:|
| access_key | String | 是  |         query         |   否    |       b6dj2f1e785149fjp2dedbiad68dwl9y       | access_key，接入前申请 |
|    sign    | String | 是  |         query         |   否    | WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B |  签名，参考【签名计算准则】   |
|     ts     |  Long  | 是  |         query         |   是    |                1736739534331                 |     时间戳，单位毫秒     |
|   app_id   | String | 是  |         query         |   是    |              bili388fh0g748hdj               |    小程序app_id     |
|  order_id  | String | 是  |         query         |   是    |                  123456789                   |      小程序订单号      |

## 3 响应说明

### 3.1 响应信息

|              字段               |       类型       |          示例          |                        说明                        |
|:-----------------------------:|:--------------:|:--------------------:|:------------------------------------------------:|
|             code              |      Long      |          0           | 错误码，0表示成功，其他表示异常，具体异常信息见message字段，详细错误码参考【状态码】介绍 |
|            message            |     String     |       success        |                       错误信息                       |
|             data              | OrderDetailRes |        见完整示例         |                  接口返回值，包含订单详情信息                  |
|   `OrderDetailRes`.open_id    |     String     |    12345678900101    |                    小程序open_id                    |
| `OrderDetailRes`.dev_order_id |     String     |      m123456789      |                     开发者业务订单号                     |
|  `OrderDetailRes`.pay_status  |    Integer     |          1           |                  支付状态，1：成功，2：失败                  |
|    `OrderDetailRes`.amount    |      Long      |         100          |                     订单金额，单位分                     |
|  `OrderDetailRes`.pay_amount  |      Long      |         100          |                     实付金额，单位分                     |
|   `OrderDetailRes`.pay_time   |      Long      |    1736752136959     |                       支付时间                       |
| `OrderDetailRes`.product_type |    Integer     |          1           |                       商品类型                       |
|  `OrderDetailRes`.product_id  |     String     |       1234567        |                       商品id                       |
| `OrderDetailRes`.product_name |     String     |         天龙八部         |                       商品名称                       |
|  `OrderDetailRes`.extra_data  |     String     | {"a":1,"b":"4567dd"} |                       拓展信息                       |
| `OrderDetailRes`.pay_channel  |    Integer     |          1           |              支付渠道，1：支付宝，2：微信，3：B币支付              |

### 3.2 错误码

| code |    说明（具体错误信息见message字段）    |
|:----:|:--------------------------:|
|  0   |            请求成功            |
| 100  |         系统繁忙，请稍后再试         |
| 102  |     程序内部错误，可联系B站相关同学排查     |
| 104  |      参数异常，如接口参数不符合规范等      |
| 105  | unauthorized，如小程序访问未授权的剧集等 |
| 106  |  invalid sign，如接口参数签名不正确等  |
| 107  |   解析签名参数失败，如解析接口签名参数异常等    |
| 200  |     数据不存在，如未查询到有效的小程序等     |
| 201  |    非法数据，如不符合业务逻辑安全性检查等     |

备注：后续有新增或者变化，会持续新增

### 3.3 完整示例

#### 3.3.1 请求示例

URL：  
{API}?access_key=b6dj2f1e785149fjp2dedbiad68dwl9y&ts=1736739534331&sign=WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B&app_id=bili388fh0g748hdj&order_id=123456789

#### 3.3.2 响应示例
```json
{
  "data": {
    "open_id": "df4e9eec68e41e2b0165eb004af65555",
    "dev_order_id": "8153013733091739780840044",
    "pay_status": 0,
    "amount": 10,
    "pay_amount": 0,
    "product_type": 1,
    "product_id": "81530",
    "product_name": "测试剧集名称",
    "extra_data": "{\"alb_id\":\"123\",\"epe_id\":\"123\"}"
  },
  "code": 0,
  "message": "success",
  "current_time": 1739871452085
}
```

## 4 版本变更

| 版本  |          说明           |         时间          |
|:---:|:---------------------:|:-------------------:|
| 1.0 |       新增查单相关API       | 2025-02-18 19:49:14 |
| 1.1 |     修改订单id为string     | 2025-02-19 18:42:47 |
| 1.2 |      移除「取消」支付状态       | 2025-02-24 11:10:47 |
| 1.3 | 订单返回信息新增`pay_channel` | 2025-02-24 12:05:27 |