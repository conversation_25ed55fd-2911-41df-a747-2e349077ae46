# 退款与回调

## 1 接口说明
针对已完成支付的订单，发起退款。

## 2 请求说明

### 2.1 基础信息

|     属性      |                   说明                    |
|:-----------:|:---------------------------------------:|
|     域名      |      https://miniapp.bilibili.com       |
|     路径      | /open/open_api/v1/platform/order/refund |
|   Method    |                  POST                   |
| ContentType |            application/json             |

### 2.2 请求参数

|      参数       |   类型   | 必填 | 位置（query、header、body） | 是否参与签名 |                      示例                      |                                  说明                                   |
|:-------------:|:------:|:--:|:---------------------:|:------:|:--------------------------------------------:|:---------------------------------------------------------------------:|
|  access_key   | String | 是  |         query         |   否    |       b6dj2f1e785149fjp2dedbiad68dwl9y       |                           access_key，接入前申请                            |
|     sign      | String | 是  |         query         |   否    | WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B |                             签名，参考【签名计算准则】                             |
|      ts       |  Long  | 是  |         query         |   是    |                1736739534331                 |                               时间戳，单位毫秒                                |
|   order_id    | String | 是  |         body          |   是    |                    100001                    |                              小程序开放平台订单号                               |
|    app_id     | String | 是  |         body          |   是    |              bili388fh0g748hdj               |                               小程序app_id                               |
| dev_order_id  | String | 是  |         body          |   是    |                  m123456789                  | 开发者业务订单号（<=32字符），<br/>【dev_order_id，dev_refund_id】二元组每次请求必须唯一，业务方自行保证 |
| dev_refund_id | String | 是  |         body          |   是    |               d096544567ghd08                |   退款批次（<=32字符），<br/>【dev_order_id，dev_refund_id】二元组每次请求必须唯一，业务方自行保证   |
| refund_amount |  Long  | 是  |         body          |   是    |                     100                      |            退款金额，单位分（>0 && <=订单总支付金额），如果是B币支付，则一定是100倍数即整元             |
|  refund_desc  | String | 是  |         body          |   是    |              用户对产品产生异议，申诉退款50%               |                             退款描述（<=32字符）                              |
|  notify_url   | String | 否  |         body          |   是    |                   退款状态回调地址                   |                不要携带任何参数，系统回调时会自动拼接相关签名参数，建议接入（<=128字符）                |
|  extra_data   | String | 否  |         body          |   是    |             {"a":1,"b":"4567dd"}             |                    拓展信息，开放平台不感知，回调时会原样返回（<=512字符）                     |


## 3 响应说明

### 3.1 响应信息

|               字段               |       类型       |   示例    |                        说明                        |
|:------------------------------:|:--------------:|:-------:|:------------------------------------------------:|
|              code              |      Long      |    0    | 错误码，0表示成功，其他表示异常，具体异常信息见message字段，详细错误码参考【状态码】介绍 |
|            message             |     String     | success |                       错误信息                       |
|              data              | OrderRefundRes |  见完整示例  |                      接口返回值                       |
| `OrderRefundRes`.refund_status |    Integer     |    1    |                 退款状态，目前只会返回0：待退款                 |

### 3.2 错误码

| code |    说明（具体错误信息见message字段）    |
|:----:|:--------------------------:|
|  0   |            请求成功            |
| 100  |         系统繁忙，请稍后再试         |
| 102  |     程序内部错误，可联系B站相关同学排查     |
| 104  |      参数异常，如接口参数不符合规范等      |
| 105  | unauthorized，如小程序访问未授权的剧集等 |
| 106  |  invalid sign，如接口参数签名不正确等  |
| 107  |   解析签名参数失败，如解析接口签名参数异常等    |
| 200  |     数据不存在，如未查询到有效的小程序等     |
| 201  |    非法数据，如不符合业务逻辑安全性检查等     |

备注：后续有新增或者变化，会持续新增

### 3.3 完整示例

#### 3.3.1 请求示例

URL：  
{API}?access_key=b6dj2f1e785149fjp2dedbiad68dwl9y&ts=1736739534331&sign=WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B

Body：
```json
{
  "order_id": "100001",
  "dev_order_id": "m123456789",
  "app_id": "bili388fh0g748hdj",
  "dev_refund_id": "d096544567ghd08",
  "refund_amount": 100,
  "refund_desc": "用户对产品产生异议，申诉退款50%",
  "notify_url": "https://demo.com/order/refund/callback",
  "extra_data": "{\"a\":1,\"b\":\"4567dd\"}"
}
```
#### 3.3.2 响应示例
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "refund_status": 0
  }
}
```

## 4 回调通知
回调通知通常发生在退款成功/失败等交易场景（如果是向金融结算机构发起退款请求失败，则不会通知），建议开发者接入。

### 4.1 回调参数

- 回调形式

|     属性      |                   说明                    |
|:-----------:|:---------------------------------------:|
|   Method    |                  POST                   |
| ContentType |            application/json             |

- 回调地址  
  开放平台会根据开发者创单时使用的`access_token`进行数据签名，比如退款时的回调通知地址是`notify_url=https:\//demo.com/order/refund/callback`，
  则实际开放平台回调的URL是`https:\//demo.com/order/refund/callback?ts=1736750625059&sign=11GNoWSnBtgpK59lnip06iPYdJgiTc2w6T2BI7Bcpo4B`，开发者根据相关参数进行参数签名校验。

- 详细回调请求

|      字段       |   类型    | 位置（query、header、body） | 是否参与签名 |                      示例                      |       说明       |
|:-------------:|:-------:|:---------------------:|:------:|:--------------------------------------------:|:--------------:|
|     sign      | String  |         query         |   否    | 11GNoWSnBtgpK59lnip06iPYdJgiTc2w6T2BI7Bcpo4B |       签名       |
|      ts       |  Long   |         query         |   是    |                1736750625059                 |    时间戳（毫秒）     |
|   order_id    | String  |         body          |   是    |                  123456789                   |   小程序开放平台订单号   |
| dev_order_id  | String  |         body          |   是    |                  m123456789                  |   开发者系统业务订单号   |
| dev_refund_id | String  |         body          |   是    |               d096544567ghd08                |      退款批次      |
| refund_amount |  Long   |         body          |   是    |                     100                      |    退款金额，单位分    |
|  refund_time  |  Long   |         body          |   是    |                1736752136959                 |   退款时间 （毫秒）    |
| refund_status | Integer |         body          |   是    |                      3                       | 退款状态，3：成功，4：失败 |
|  extra_data   | String  |         body          |   是    |             {"a":1,"b":"4567dd"}             |  退款时的自定义拓展参数   |  

- 确保响应体的结构如下，其中`code=0`则认为回调成功，否则认为回调失败

```json
{
  "code": 0,
  "message": "success"
}
```

- 请尽可能保证回调服务的稳定性和幂等性，开放平台保证在退款成功或者失败后及时回调，如果该接口回调失败，则依次按照5s, 10s, 20s, 1min, 3min, 5min, 30min, 1hour的梯度重试，所有重试梯度使用完之后，放弃回调

- 业务方在校验签名时，要兼容后续开放平台返回增量字段的能力，因此建议使用`JSON`接收回调信息，然后生成签名和校验签名，签名验证通过后，再反序列化自身关注的字段。

### 4.2 完整示例

#### 4.2.1 请求示例

```json
{
  "order_id": "123456789",
  "dev_order_id": "m123456789",
  "dev_refund_id": "d096544567ghd08",
  "refund_amount": 100,
  "refund_time": 1736752136959,
  "refund_status": 3,
  "extra_data": "{\"a\":1,\"b\":\"4567dd\"}"
}
```

#### 4.2.2 响应示例

```json
{
  "code": 0,
  "message": "success"
}
```

## 5 版本变更

| 版本  |         说明         |         时间          |
|:---:|:------------------:|:-------------------:|
| 1.0 |     补充支付相关API      | 2025-02-11 16:06:00 |
| 1.1 | 将`refund_desc`加入签名 | 2025-02-24 11:30:59 |
| 1.2 | 修改`refund_desc`必填  | 2025-05-08 19:28:30 |
