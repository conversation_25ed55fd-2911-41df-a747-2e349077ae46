# 优酷订单数据接入

## 1 接口说明
该接口用于接收优酷小程序的订单回传数据。

## 2 请求说明

### 2.1 基础信息

|     属性      |                      说明                      |
|:-----------:|:--------------------------------------------:|
|     域名      |         https://miniapp.bilibili.com         |
|     路径      | /open/open_api/v1/platform/yk/callback/order |
|   Method    |                     POST                     |
| ContentType |               application/json               |

### 2.2 请求参数

|      参数       |   类型    | 必填 | 位置（query、header、body） | 是否参与签名 |                      示例                      |         说明         |
|:-------------:|:-------:|:--:|:---------------------:|:------:|:--------------------------------------------:|:------------------:|
|  access_key   | String  | 是  |         query         |   否    |       b6dj2f1e785149fjp2dedbiad68dwl9y       |  access_key，接入前申请  |
|     sign      | String  | 是  |         query         |   否    | WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B |   签名，参考【签名计算准则】    |
|      ts       |  Long   | 是  |         query         |   是    |                1736739534331                 |      时间戳，单位毫秒      |
|    user_id    | String  | 是  |         body          |   是    |                  abcdesgsgs                  |       ⽤户唯⼀id       |
|   order_id    |  Long   | 是  |         body          |   是    |                 202311080001                 |       订单唯⼀id       |
|  order_code   | String  | 是  |         body          |   是    |                      -                       |    订单唯一码，一般不使用     |
|  order_type   | String  | 是  |         body          |   是    |             订单类型，签约订单、续费订单、普通订单              |        订单类型        |
|    pay_amt    | String  | 是  |         body          |   是    |                     36.8                     |      订单⾦额（元）       |
|   pay_time    | String  | 是  |         body          |   是    |             2024-11-06 13:12:58              |       订单付费时间       |
|  product_id   |  Long   | 是  |         body          |   是    |                      -                       |        商品id        |
| product_name  | String  | 是  |         body          |   是    |                      -                       |        商品名称        |
|   sku_name    | String  | 是  |         body          |   是    |                      -                       |       会员权益名称       |
|   pay_type    | String  | 是  |         body          |   是    |                      -                       |        支付方式        |
| refund_state  | Integer | 是  |         body          |   是    |                 0：未退款，1：已退款                  |        退款状态        |
|  refund_time  | String  | 否  |         body          |   是    |             2024-11-06 13:12:58              |        退款时间        |
|  refund_amt   | String  | 否  |         body          |   是    |                     19.5                     |      退款⾦额（元）       |
| sign_order_id |  Long   | 否  |         body          |   是    |             2025-04-03 15:34:32              |  签约订单id，针对续费订单提供   |
|  sign_state   | Integer | 否  |         body          |   是    |            1：签约，2：解约，0：⾮签约单或签约未⽣效            |        签约状态        |
| sign_off_date | String  | 否  |         body          |   是    |                   20241212                   |        解约⽇期        |
|     args      | String  | 是  |         body          |   是    | sourcefrom=100102&from_avid=67890&platform=1 | 扩展参数，格式k1=v1&k2=v2 |
|      app      | String  | 是  |         body          |   是    |                    抖⾳、快⼿                     |        订单应⽤        |
|     time      |  Long   | 是  |         body          |   是    |                      -                       |       当前时间戳        |

## 3 响应说明

### 3.1 响应信息

|                    字段                     |           类型            |   示例    |                        说明                        |
|:-----------------------------------------:|:-----------------------:|:-------:|:------------------------------------------------:|
|                   code                    |          Long           |    0    | 错误码，0表示成功，其他表示异常，具体异常信息见message字段，详细错误码参考【状态码】介绍 |
|                  message                  |         String          | success |                       错误信息                       |

### 3.2 错误码

| code |    说明（具体错误信息见message字段）    |
|:----:|:--------------------------:|
|  0   |            请求成功            |
| 100  |         系统繁忙，请稍后再试         |
| 102  |     程序内部错误，可联系B站相关同学排查     |
| 104  |      参数异常，如接口参数不符合规范等      |
| 105  | unauthorized，如小程序访问未授权的剧集等 |
| 106  |  invalid sign，如接口参数签名不正确等  |
| 107  |   解析签名参数失败，如解析接口签名参数异常等    |
| 200  |     数据不存在，如未查询到有效的小程序等     |
| 201  |    非法数据，如不符合业务逻辑安全性检查等     |

备注：后续有新增或者变化，会持续新增

### 3.3 完整示例

#### 3.3.1 请求示例

URL：  
{API}?access_key=b6dj2f1e785149fjp2dedbiad68dwl9y&ts=1736739534331&sign=WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B

Body:
```json
{
  "order_id": 100,
  "order_code": "97",
  "order_type": "普通订单",
  "user_id": "44",
  "pay_amt": "39.8",
  "pay_time": "2024-09-22 06:25:48",
  "product_id": 57,
  "product_name": "测试数据1",
  "sku_name": "测试数据1",
  "pay_type": "微信",
  "refund_state": 36,
  "refund_time": "2025-12-12 01:57:44",
  "refund_amt": "15.8",
  "args": "platform=android&appname=YOUKU_BILIBILI_MINI_APP&scene=0&ver=0.0.3&eid=&mid=&ctime=&dev=OPPO&ug_from=bli&sourcefrom=10041&from_avid=&from_trackid=&vid=&sid=&p_vid=XMzcxNDY5ODQ4&p_sid=cbff984c962411de83b1",
  "app": "b站",
  "time": 1758323118134
}

```

#### 3.3.2 响应示例
```json
{
  "code": 0,
  "message": "success",
  "current_time": 1739871452085
}
```

## 5 版本变更

| 版本  |     说明      |         时间          |
|:---:|:-----------:|:-------------------:|
| 1.0 | 新增优酷订单回传API | 2025-05-09 15:50:14 |