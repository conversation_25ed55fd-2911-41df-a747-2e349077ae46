# 获取用户变现ECPM

## 1 接口说明
获取用户在B站小程序的曝光事件ecpm明细

## 2 请求说明

### 2.1 基础信息

|    属性     |                    说明                    |
| :---------: | :----------------------------------------: |
|    域名     |        https://miniapp.bilibili.com        |
|    路径     | /open/open_api/v1/platform/data/query_ecpm |
|   Method    |                    GET                     |
| ContentType |              application/json              |

### 2.2 请求参数

|    参数    |  类型  |   必填    | 位置（query、header、body） | 是否参与签名 |                     示例                     |                             说明                             |
| :--------: | :----: |:-------:| :-------------------------: | :----------: | :------------------------------------------: | :----------------------------------------------------------: |
| access_key | String |    是    |            query            |      否      |       b6dj2f1e785149fjp2dedbiad68dwl9y       |                    access_key，接入前申请                    |
|    sign    | String |    是    |            query            |      否      | WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B |                  签名，参考【签名计算准则】                  |
|     ts     |  Long  |    是    |            query            |      是      |                1736739534331                 |                       时间戳，单位毫秒                       |
|   app_id   | String |    是    |            query            |      是      |              bili388fh0g748hdj               |                         小程序app_id                         |
|  open_id   | String |    是    |            query            |      是      |       df4e9eec68e41e2b0165eb004af65555       |        用户的 open_id，传入为空字符串时，查询所有用户        |
| date_hour  | String |    是    |            query            |      是      |             2025-04-25 14:28:03              | 根据传参，有两种时间范围可查：<br/>- 若传 YYYY-MM-DD，则查天级范围，即 YYYY-MM-DD 00:00:00 ～ YYYY-MM-DD 23:59:59<br/>- 若传 YYYY-MM-DD hh[:mm:ss]，则查小时级范围，即 YYYY-MM-DD hh:00:00 ～ YYYY-MM-DD hh:59:59<br/>举例：<br/>查 13 号：“2024-07-13”<br/>查 13 号 14 点：“2024-07-13 14” 或 “2024-07-13 14:00:00” 或 “2024-07-13 14:13:24”<br/>PS：数据源仅保存近 3 天时间范围内的数据，建议明确时间范围的情况下，指定小时级别 |
|  page_no   | Integer | 是 |            query            |      是      |                      1                       | 查询的页码，页码从1开始 |
| page_size  | Integer | 是 |            query            |      是      |                     500                      |                   单页的大小，最大支持500                    |

## 3 响应说明

### 3.1 响应信息

|        字段         |  类型  |               示例               |                             说明                             |
| :-----------------: | :----: | :------------------------------: | :----------------------------------------------------------: |
|        code         |  Long  |                0                 | 错误码，0表示成功，其他表示异常，具体异常信息见message字段，详细错误码参考【状态码】介绍 |
|       message       | String |             success              |                           错误信息                           |
|        data         | Record |            见完整示例            |                 接口返回值，包含订单详情信息                 |
|     `Record`.id     | String | f5a677bff8ff5146ff06b11622e5c8df |                            唯一id                            |
| `Record`.event_time | String |       2025-04-25 17:01:20        |                       计费事件发生时间                       |
| `Record`.event_type | String |               show               |                          计费事件名                          |
|  `Record`.open_id   | String | 204fcdbd74b82a0c3319bf187817595a |                          用户openID                          |
|    `Record`.cost    | String |          2066.39999999           |                          单位：千分                          |

### 3.2 错误码

| code |    说明（具体错误信息见message字段）    |
|:----:|:--------------------------:|
|  0   |            请求成功            |
| 100  |         系统繁忙，请稍后再试         |
| 102  |     程序内部错误，可联系B站相关同学排查     |
| 104  |      参数异常，如接口参数不符合规范等      |
| 105  | unauthorized，如小程序访问未授权的剧集等 |
| 106  |  invalid sign，如接口参数签名不正确等  |
| 107  |   解析签名参数失败，如解析接口签名参数异常等    |
| 200  |     数据不存在，如未查询到有效的小程序等     |
| 201  |    非法数据，如不符合业务逻辑安全性检查等     |
| 404  |      请按顺序查询或已无更多数据  |

备注：后续有新增或者变化，会持续新增

### 3.3 完整示例

#### 3.3.1 请求示例

URL：  
{API}?access_key=b6dj2f1e785149fjp2dedbiad68dwl9y&ts=1736739534331&sign=WbGNoWSnhogpKzilnQfPciPYdJgiTc2w6T2BI7Bcpo4B&app_id=bili388fh0g748hdj&open_id=&date_hour=2025-04-25 14&page_no=1&page_size=100

#### 3.3.2 响应示例
```json
{
  "data": {
    "total_count": 1,
    "records": [
      {
        "event_id": "1234567890",
        "event_time": "2025-01-01 00:00:00",
        "event_type": "show",
        "open_id": "abcdef",
        "cost": "10.5"
      }
    ]
  },
  "code": 0,
  "message": "success",
  "current_time": 1739871452085
}
```

## 4 版本变更

| 版本 |       说明       |        时间         |
| :--: | :--------------: | :-----------------: |
| 1.0  | 新增查询ecpm接口 | 2025-04-25 16:43:20 |