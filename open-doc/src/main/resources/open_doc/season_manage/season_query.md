# 剧集信息查询

## 1 接口说明

通常用在信息流分发场景，根据对应的剧id和集id查询完整的剧集信息
[Heading IDs](#heading-ids)

- [x] dd
- [ ] xx

## 2 请求说明

### 2.1 基础信息

|     属性      |                   说明                   |
|:-----------:|:--------------------------------------:|
|     域名      |               参考【通用逻辑说明】               |
|     路径      | /open/open_api/v1/miniapp/season/query |
|   Method    |                  GET                   |
| ContentType |            application/json            |

### 2.2 请求参数

|     参数      |   类型   | 必填  | 位置（query、header、body） |         示例          |    说明     |
|:-----------:|:------:|:---:|:---------------------:|:-------------------:|:---------:|
|  season_id  |  Long  |  是  |         query         |         100         |    剧id    |
| episode_id  |  Long  |  是  |         query         |         200         |    集id    |
|  applet_id  | String |  是  |        header         | dk73hf6sl037rd9e0gn | 小程序app_id |

## 3 响应说明

### 3.1 data信息

|        字段        |   类型   |  示例   |   说明   |
|:----------------:|:------:|:-----:|:------:|
|    season_id     |  Long  |  100  |  剧id   |
|      author      | Author | 见完整示例 | 剧的作者信息 |
| **`author`**.mid |  Long  | 1003  |  作者id  |

### 3.2 状态码

| code |      说明（具体错误信息见message字段）      |
|:----:|:------------------------------:|
| 100  | 参数非法，比如season_id未传，或者不符合long要求 |
| 200  |    请求失败，如内部业务约束或者依赖失败导致的错误     |
| 500  |             程序内部错误             |

### 3.3 完整实例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "season_id": 345,
    "author": {
      "mid": 1000,
      "face": "https://i0.hdslb.com/bfs/face/049b47e0e73fc5cc1564343bb0aeacce8ae8e6f8.jpg",
      "name": "大桥短剧"
    },
    "cover": "https://i0.hdslb.com/bfs/face/049b47e0e73fc5cc1564343bb0aeacce8ae8e6f8.jpg",
    "title": "闪婚来袭：契约夫人是豪门千金",
    "sub_title": "闪婚来袭：契约夫人是豪门千金",
    "styles": [
      "爱情",
      "古装"
    ],
    "is_finish": 1,
    "episodes": [
      {
        "season_id": 345,
        "episode_id": 1,
        "ord": 1,
        "title": "第1集",
        "long_title": "纪然在民政局门口被相亲男放鸽子并羞辱，而白君奕受爷爷之托，阻止纪然与他",
        "aid": 113404702367522,
        "cid": 26555911281,
        "cover": "http://i0.hdslb.com/bfs/storyff/n241101sa2nfu8xf24be3w2wcvegkfig_firsti.jpg"
      },
      {
        "season_id": 345,
        "episode_id": 2,
        "ord": 2,
        "title": "第2集",
        "long_title": "纪然在民政局门口被相亲男放鸽子并羞辱，而白君奕受爷爷之托，阻止纪然与他",
        "aid": 1106466960,
        "cid": 1651137046,
        "cover": "http://i0.hdslb.com/bfs/storyff/n240816er1znvy8ta30lh2exib5c6tqw_firsti.jpg"
      }
    ]
  }
}
```

## 4 版本变更

| 版本  |          说明          |         时间          |
|:---:|:--------------------:|:-------------------:|
| 1.0 |         初始化          | 2024-12-13 12:00:01 |
| 1.1 | 新增applet_id等header参数 | 2024-12-25 22:30:56 |
