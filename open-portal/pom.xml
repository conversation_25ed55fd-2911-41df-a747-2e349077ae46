<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bilibili.miniapp</groupId>
        <artifactId>open-platform</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>open-platform-portal</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>open-portal</name>
    <description>open-portal</description>
    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.bilibili.miniapp</groupId>
            <artifactId>open-platform-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.miniapp</groupId>
            <artifactId>open-platform-doc</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili.miniapp</groupId>
            <artifactId>open-api</artifactId>
            <version>${miniapp.open.api.version}</version>
        </dependency>
    </dependencies>

    <build>
        <!--nyx会copy这个包，caster会读取这个包，不要动-->
        <finalName>open-platform</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.bilibili.miniapp.open.portal.Application</mainClass>
                    <!-- 不要设置为true，否则打包的manifest无法包含启动类，导致出现no main manifest attribute-->
                    <!-- <skip>true</skip>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
