package com.bilibili.miniapp.open.portal;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import pleiades.venus.starter.http.client.RESTClientScan;


@RESTClientScan(basePackages = {"com.bilibili.miniapp.open"})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@SpringBootApplication(scanBasePackages = {"com.bilibili.miniapp.open"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
