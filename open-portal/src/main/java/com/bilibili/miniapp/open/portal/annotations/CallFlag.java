package com.bilibili.miniapp.open.portal.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025/01/06 11:35
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CallFlag {
    F[] value() default {};

    enum F {
        //开放接口
        OPEN_API,
        //开平
        OPEN_PLATFORM,
        //小程序
        MINI_APP
    }
}
