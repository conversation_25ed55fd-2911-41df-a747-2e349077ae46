package com.bilibili.miniapp.open.portal.annotations;

import com.bilibili.miniapp.open.portal.validator.ConditionalValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证可选的参数
 *
 * <AUTHOR>
 * @date 2025/1/18 16:24
 */
@Target(value = {ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ConditionalValidator.class)
public @interface Conditional {
    String message() default "参数不符合规范";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    //如果作用在String上，则表示的字符串的长度
    long min() default 0;

    //如果作用在String上，则表示的字符串的长度
    long max() default Long.MAX_VALUE;
}
