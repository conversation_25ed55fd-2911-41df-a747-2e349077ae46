package com.bilibili.miniapp.open.portal.annotations;

import com.bilibili.miniapp.open.portal.validator.NotXssValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * XSS攻击校验：用于检查参数是否包含可能的XSS攻击脚本
 * <p>
 * 1、如果用在Vo中的字段上，则接口参数中需要结合@Validated注解使用
 * <code>
 *  public class XXXVo{
 *      @Xss
 *      private String url;
 *  }
 *
 *  @RestController
 *  public class XXXController{
 *      @RequestMapping(value = "/post", method = RequestMethod.POST)
 *      public Response<String> post(@Validated @RequestBody XXXVo vo) {
 *          return new Response("ok");
 *      }
 * }
 * </code>
 * <p>
 * 2、如果用在基本参数上，尤其是GET接口，则接口参数中除了使用该注解之外，还要在Controller中搭配@Validated注解
 * <code>
 *  @Validated
 *  @RestController
 *  public class XXXController{
 *     @RequestMapping(value = "/get", method = RequestMethod.GET)
 *     public Response<String> get(@Xss @RequestParam(value = "url") String url) {
 *         return new Response("ok");
 *     }
 *  }
 * </code>
 * <p>
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */

@Target(value = {ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = NotXssValidator.class)
public @interface NotXss {
    String message() default "可能包含潜在的XSS攻击";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
