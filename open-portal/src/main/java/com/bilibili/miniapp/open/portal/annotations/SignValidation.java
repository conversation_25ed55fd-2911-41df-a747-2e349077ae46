package com.bilibili.miniapp.open.portal.annotations;

import com.bilibili.miniapp.open.common.annotations.Sign;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标识某接口是否需要验证签名，若使用了该注解，则默认该接口已经具备签名的能力，其接口请求参数中至少包含以下三种参数（如果接口没有明确使用逻辑，
 * 则可以不主动接收，此时只会在签名验证时自动读取）
 * <p>1、access_key：表示开发者在开平申请的调用凭证包括access_key和access_token，其中access_token是开平和开发者约定的签名密钥
 * <p>2、ts：请求调用时的时间戳，单位毫秒，该参数会自动作为签名算法的一部分，注意该参数具备有效期，比如10s内的调用方可有效
 * <p>3、sign：根据接口约定的签名参数以及access_token算出的签名
 *
 * <AUTHOR>
 * @date 2025/01/06 11:35
 * @see Sign 和该注解搭配使用
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SignValidation {
}
