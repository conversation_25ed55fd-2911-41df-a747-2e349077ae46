package com.bilibili.miniapp.open.portal.aspect;

import com.bilibili.miniapp.open.service.common.CustomGrpcException;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class AspectService {


    @Around("@within(com.bilibili.miniapp.open.portal.annotations.RpcServiceAspect)")
    public Object rpcServiceAspect(ProceedingJoinPoint joinPoint) {
        final var args = joinPoint.getArgs();
        final var streamObserver = (StreamObserver<?>) args[1];
        try {
            final var obj = joinPoint.proceed();
            streamObserver.onCompleted();
            return obj;
        } catch (IllegalArgumentException | StatusRuntimeException err) {
            log.error(joinPoint.getTarget().getClass().getName(), err);
            streamObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(err.getMessage())
                    .asRuntimeException());
            return null;
        } catch (CustomGrpcException e) {
            Metadata metadata = new Metadata();
            Metadata.Key<String> codeKey = Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);
            Metadata.Key<String> descKey = Metadata.Key.of("error-description", Metadata.ASCII_STRING_MARSHALLER);

            metadata.put(codeKey, String.valueOf(e.getCode()));
            metadata.put(descKey, e.getDesc());

            streamObserver.onError(Status.INTERNAL
                    .withDescription("错误代码: " + e.getCode() + ", 描述: " + e.getDesc())
                    .asRuntimeException(metadata));
            return null;
        } catch (Throwable t) {
            log.error(joinPoint.getTarget().getClass().getName(), t);
            streamObserver.onError(Status.UNKNOWN
                    .withDescription(t.getMessage())
                    .withCause(t)
                    .asRuntimeException());
            return null;
        }
    }

}
