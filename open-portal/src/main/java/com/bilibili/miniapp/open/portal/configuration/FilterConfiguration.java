package com.bilibili.miniapp.open.portal.configuration;

import com.bilibili.miniapp.open.portal.filter.WebApiSecurityFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 * @date 2024/12/6 22:54
 */
@Configuration
public class FilterConfiguration {

    @Bean
    public FilterRegistrationBean<WebApiSecurityFilter> registerWebApiSecurityFilter() {
        FilterRegistrationBean<WebApiSecurityFilter> registrationBean = new FilterRegistrationBean<>();
        WebApiSecurityFilter filter = new WebApiSecurityFilter();
        registrationBean.setFilter(filter);
        registrationBean.setName("webapi_security");
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        return registrationBean;
    }
}
