package com.bilibili.miniapp.open.portal.configuration;

import com.bilibili.miniapp.open.common.entity.RequestAttributeKey;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.interceptor.ContextAppenderInterceptor;
import com.bilibili.miniapp.open.portal.interceptor.MainSitePassportInterceptor;
import com.bilibili.miniapp.open.portal.interceptor.TraceInterceptor;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.ServletWebArgumentResolverAdapter;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/9/3 21:16
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer, ApplicationContextAware {
    private ApplicationContext ctx;
    private static final String CONTEXT_PATH = "/open";

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new TraceInterceptor())
                .addPathPatterns("/**")
                .order(0);
        registry.addInterceptor(new ContextAppenderInterceptor())
                .addPathPatterns("/**")
                .order(1);
        registry.addInterceptor(ctx.getBean(MainSitePassportInterceptor.class))
                .addPathPatterns("/**")
                .order(2);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new ServletWebArgumentResolverAdapter((methodParameter, webRequest) -> {
            if (methodParameter.getParameterType().equals(Context.class)) {
                Object request = webRequest.getNativeRequest();
                if (request instanceof HttpServletRequest) {
                    HttpServletRequest httpRequest = (HttpServletRequest) request;
                    return httpRequest.getAttribute(RequestAttributeKey.ATTRIBUTE_CONTEXT_KEY);
                }
            }
            return new Context();
        }));
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix(CONTEXT_PATH, AbstractController.class::isAssignableFrom);
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }
}
