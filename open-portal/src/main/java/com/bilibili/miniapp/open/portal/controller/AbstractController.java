package com.bilibili.miniapp.open.portal.controller;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.entity.Response;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.engine.ConstraintViolationImpl;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */

@Slf4j
@RestController
public abstract class AbstractController {

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public Response<String> handleMethodArgumentTypeMismatchException(HttpServletRequest request, HandlerMethod handlerMethod,
                                                                      MethodArgumentTypeMismatchException exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":MethodArgumentTypeMismatchException", exception);
        return Response.FAIL(ErrorCodeType.BAD_PARAMETER, String.format("'%s' parameter is not matched", exception.getName()));
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public Response<String> handleMissingServletRequestParameterException(HttpServletRequest request, HandlerMethod handlerMethod,
                                                                          MissingServletRequestParameterException exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":MissingServletRequestParameterException", exception);
        return Response.FAIL(ErrorCodeType.BAD_PARAMETER, exception.getMessage());
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Response<String> handleMethodArgumentNotValidException(HttpServletRequest request, HandlerMethod handlerMethod,
                                                                  MethodArgumentNotValidException exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":MethodArgumentNotValidException", exception);
        List<FieldError> fieldErrors = exception.getFieldErrors();
        StringBuilder errorBuilder = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            errorBuilder.append(fieldError.getField())
                    .append("：")
                    .append(fieldError.getDefaultMessage())
                    .append("，");
        }
        return Response.FAIL(ErrorCodeType.BAD_PARAMETER, String.format("validate failed, %s", errorBuilder));
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public Response<String> handleConstraintViolationException(HttpServletRequest request, HandlerMethod handlerMethod,
                                                               ConstraintViolationException exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":ConstraintViolationException", exception);
        Set<ConstraintViolation<?>> constraintViolations = exception.getConstraintViolations();
        StringBuilder errorBuilder = new StringBuilder();
        for (ConstraintViolation<?> constraintViolation : constraintViolations) {
            if (constraintViolation instanceof ConstraintViolationImpl) {
                ConstraintViolationImpl<?> constraint = (ConstraintViolationImpl<?>) constraintViolation;
                errorBuilder.append(constraint.getPropertyPath())
                        .append("：")
                        .append(constraint.getMessage())
                        .append("，");
            }
        }
        if (errorBuilder.length() == 0) {
            errorBuilder.append("参数不合法");
        }
        return Response.FAIL(ErrorCodeType.BAD_PARAMETER, String.format("validate failed, %s", errorBuilder));
    }


    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public Response<String> handleServiceException(HttpServletRequest request, HandlerMethod handlerMethod,
                                                   ServiceException exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":ServiceException", exception);
        return Response.FAIL(exception.getCode(), exception.getMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    public Response<String> handleIllegalArgumentException(HttpServletRequest request, HandlerMethod handlerMethod,
                                                           IllegalArgumentException exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":IllegalArgumentException", exception);
        return Response.FAIL(ErrorCodeType.BAD_DATA.getCode(), exception.getMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Response<String> handleException(HttpServletRequest request, HandlerMethod handlerMethod,
                                            Exception exception) {
        Cat.logEvent("Exception_WebApi", request.getRequestURI());
        log.error(request.getRequestURI() + ":Exception", exception);
        return Response.FAIL("internal error");
    }
}

