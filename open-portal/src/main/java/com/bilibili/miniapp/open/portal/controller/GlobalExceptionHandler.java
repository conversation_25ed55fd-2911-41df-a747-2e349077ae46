package com.bilibili.miniapp.open.portal.controller;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {


    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Response<Void> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex) {
        log.error("不支持的请求方法: {}", ex.getMethod());

        String message = String.format("不支持的请求方法: %s", ex.getMethod());

        return Response.FAIL(ErrorCodeType.BAD_REQUEST, message);
    }
}
