package com.bilibili.miniapp.open.portal.controller.open_api;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.SignValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AuthLoginMapper;
import com.bilibili.miniapp.open.portal.vo.auth.PhoneExchangeReqVo;
import com.bilibili.miniapp.open.portal.vo.auth.PhoneExchangeRespVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.order.OrderCreateReqVo;
import com.bilibili.miniapp.open.portal.vo.order.OrderCreateResVo;
import com.bilibili.miniapp.open.service.biz.auth.IMiniAppAuthService;
import com.bilibili.miniapp.open.service.biz.auth.impl.MiniAppAuthService;
import com.bilibili.miniapp.open.service.bo.auth.EncryptedEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/14
 **/

@Slf4j
@RestController
@RequestMapping("/open_api/v1/platform")
@Tag(name = "/open_api/v1/platform")
@RequiredArgsConstructor
public class OpenPlatformAuthExchangeController extends AbstractController {

    @Resource
    private IMiniAppAuthService authService;

    @SignValidation
    @Operation(description = "获取加密手机号")
    @PostMapping(value = "/phone/exchange")
    public Response<PhoneExchangeRespVo> exchangePhone(Context context,
                                                       @Validated
                                                    @Sign @RequestBody PhoneExchangeReqVo vo) throws Exception {
        EncryptedEntity result = authService.authLogin(vo.getAppId(), vo.getOpenId(), vo.getPreauthCode());
        return Response.SUCCESS(AuthLoginMapper.MAPPER.toPhoneExchangeRespVo(result));
    }
}
