package com.bilibili.miniapp.open.portal.controller.open_api;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.portal.annotations.SignValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.DataMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.data.EcpmEventLogVo;
import com.bilibili.miniapp.open.service.biz.data.EcpmEventLogService;
import com.bilibili.miniapp.open.service.bo.data.EcpmEventLogBo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/open_api/v1/platform/data")
@Tag(name = "/open_api/v1/platform/data")
@RequiredArgsConstructor
public class OpenPlatformDataController extends AbstractController {

    @Autowired
    private EcpmEventLogService ecpmEventLogService;

    @SignValidation
    @GetMapping(value = "/query_ecpm")
    public Response<PageResult<EcpmEventLogVo>> exchangePhone(Context context,
                                                              @Sign @RequestParam("app_id") String appId,
                                                              @Sign @RequestParam("open_id") String openId,
                                                              @Sign @RequestParam("date_hour") String dateHour,
                                                              @Sign @RequestParam("page_no") Integer pageNo,
                                                              @Sign @RequestParam("page_size") Integer pageSize) {

        AssertUtil.isTrue(pageNo > 0, ErrorCodeType.BAD_PARAMETER.getCode(), "page_no必须大于0");
        AssertUtil.isTrue(pageSize > 0, ErrorCodeType.BAD_PARAMETER.getCode(), "page_size必须大于0");

        PageResult<EcpmEventLogBo> ecpmEventLogBoPageResult = ecpmEventLogService.queryEventLog(appId, openId, dateHour, Page.valueOf(pageNo, pageSize));
        return Response.SUCCESS(PageResult.<EcpmEventLogVo>builder()
                .total(ecpmEventLogBoPageResult.getTotal())
                .records(ecpmEventLogBoPageResult.getRecords().stream()
                        .map(DataMapper.MAPPER::toVo)
                        .collect(Collectors.toList()))
                .build());
    }
}
