package com.bilibili.miniapp.open.portal.controller.open_api;

import com.bilibili.miniapp.open.common.entity.LocalCacheKey;
import com.bilibili.miniapp.open.doc.model.DocTreeBo;
import com.bilibili.miniapp.open.doc.service.IOpenDocService;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.vo.doc.DocTreeVo;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 文档
 *
 * <AUTHOR>
 * @date 2024/12/12 17:52
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/platform/doc")
@Tag(name = "/open_api/v1/platform/doc")
public class OpenPlatformDocController extends AbstractController {
    @Autowired
    private IOpenDocService openDocService;
    private LoadingCache<Object, List<DocTreeVo>> cache;

    @PostConstruct
    public void init() {
        cache = Caffeine.newBuilder()
                .maximumSize(1)
                .refreshAfterWrite(Duration.ofDays(1))//变更频率极低，每天更新一次即可
                .build(key -> toDocTreeVo(openDocService.getCurDocTree()));
        cache.refresh(LocalCacheKey.KEY_MINIAPP_OPEN_DOC);//refresh or get 操作进行预加载
    }

    private List<DocTreeVo> toDocTreeVo(List<DocTreeBo> docTreeList) {
        if (CollectionUtils.isEmpty(docTreeList)) {
            return Collections.emptyList();
        }
        return docTreeList.stream()
                .map(bo -> DocTreeVo.builder()
                        .node(bo.getNode())
                        .location(bo.getLocation())
                        .name(bo.getName())
                        .seq(bo.getSeq())
                        .type(bo.getType())
                        .children(toDocTreeVo(bo.getChildren()))
                        .build())
                .collect(Collectors.toList());
    }

    @Operation(description = "发布文档")
    @RequestMapping(value = "/publish", method = RequestMethod.POST)
    public Response<String> publish(@RequestParam("token") String token) throws Exception {
        Assert.isTrue(Objects.equals(token, "7c3cbfc5eb8062bdf7783d0a1ea8efbb"), "FORBIDDEN");
        openDocService.publish();
        return Response.SUCCESS("success");
    }

    @Operation(description = "刷新文档")
    @RequestMapping(value = "/refresh", method = RequestMethod.POST)
    public Response<String> refresh(@RequestParam("token") String token) throws Exception {
        Assert.isTrue(Objects.equals(token, "7c3cbfc5eb8062bdf7783d0a1ea8efbb"), "FORBIDDEN");
        cache.refresh(LocalCacheKey.KEY_MINIAPP_OPEN_DOC);
        return Response.SUCCESS("success");
    }

    @Operation(description = "查询文档")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public Response<List<DocTreeVo>> get() throws Exception {
        List<DocTreeVo> docTreeVos = cache.get(LocalCacheKey.KEY_MINIAPP_OPEN_DOC);
        return Response.SUCCESS(docTreeVos);
    }
}
