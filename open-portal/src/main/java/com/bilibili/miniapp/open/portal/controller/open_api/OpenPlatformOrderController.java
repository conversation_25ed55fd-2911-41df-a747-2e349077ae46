package com.bilibili.miniapp.open.portal.controller.open_api;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.entity.SecretKeyPair;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.EncryptUtil;
import com.bilibili.miniapp.open.portal.annotations.SignValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.OrderMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.order.*;
import com.bilibili.miniapp.open.service.biz.order.IOrderService;
import com.bilibili.miniapp.open.service.biz.payment.IPaymentService;
import com.bilibili.miniapp.open.service.bo.order.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.PaymentConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小程序订单（开放）
 *
 * <AUTHOR>
 * @date 2025/01/16 15:44
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/platform/order")
@Tag(name = "/open_api/v1/platform/order")
@RequiredArgsConstructor
public class OpenPlatformOrderController extends AbstractController {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private IPaymentService paymentService;

    /**
     * 创建订单
     */
    @SignValidation
    @Operation(description = "创建订单")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public Response<OrderCreateResVo> create(Context context,
                                             @Validated
                                             @Sign @RequestBody OrderCreateReqVo vo) throws Exception {
        log.info("[MiniAppOrderController] create order request={}, accessKey={}, ts={}, sign={}", JSON.toJSONString(vo),
                context.getAccessKey(), context.getTs(), context.getSign());
        OrderCreateReq createReq = OrderMapper.MAPPER.toOrderCreateReq(vo);
        AssertUtil.notNull(createReq, ErrorCodeType.BAD_PARAMETER);
        createReq.setAccessKey(context.getAccessKey());
        OrderCreateRes orderRes = orderService.createOrder(createReq);
        PaymentConfig paymentConfig = configCenter.getPayment();
        String orderResStr = JSON.toJSONString(orderRes);
        String payInfo = EncryptUtil.encrypt(orderResStr, SecretKeyPair.builder()
                .key(paymentConfig.getOpenPayEncryptKey())
                .iv(paymentConfig.getOpenPayEncryptIv())
                .build());
        log.info("[MiniAppOrderController] create order response={}, payInfo={}, accessKey={}, ts={}, sign={}",
                JSON.toJSONString(orderRes), payInfo, context.getAccessKey(), context.getTs(), context.getSign());
        OrderCreateResVo orderCreateResVo = OrderCreateResVo.builder()
                .orderId(Objects.toString(orderRes.getOrderId()))
                .devOrderId(orderRes.getDevOrderId())
                .payInfo(payInfo)
                .build();
        return Response.SUCCESS(orderCreateResVo);
    }

    /**
     * 发起订单退款
     */
    @SignValidation
    @Operation(description = "订单退款")
    @RequestMapping(value = "/refund", method = RequestMethod.POST)
    public Response<OrderRefundResVo> refund(Context context,
                                             @Validated
                                             @Sign @RequestBody OrderRefundReqVo vo) throws Exception {
        log.info("[MiniAppOrderController] refund order request={}, accessKey={}, ts={}, sign={}", JSON.toJSONString(vo),
                context.getAccessKey(), context.getTs(), context.getSign());
        RefundReq refundReq = OrderMapper.MAPPER.toRefundReq(vo);
        AssertUtil.notNull(refundReq, ErrorCodeType.BAD_PARAMETER);
        refundReq.setAccessKey(context.getAccessKey());
        Refund refund = paymentService.refund(refundReq);
        log.info("[MiniAppOrderController] refund order response={}, accessKey={}, ts={}, sign={}",
                JSON.toJSONString(refund), context.getAccessKey(), context.getTs(), context.getSign());
        OrderRefundResVo orderRefundResVo = OrderRefundResVo.builder()
                .refundStatus(refund.getRefundStatus())
                .build();
        return Response.SUCCESS(orderRefundResVo);
    }

    @SignValidation
    @Operation(description = "查询订单")
    @GetMapping(value = "/query")
    public Response<OrderDetailResVo> query(Context context,
                                            @Sign @RequestParam("order_id") Long orderId,
                                            @Sign @RequestParam("app_id") String appId
                                            ) {

        log.info("[MiniAppOrderController] query order request, appId={}, accessKey={}, ts={}, sign={}",
                context.getMiniAppId(), context.getAccessKey(), context.getTs(), context.getSign());
        Order order = orderService.getOrderByOrderId(context.getMiniAppId(), orderId);

        OrderDetailResVo res = OrderMapper.MAPPER.toOrderDetailResVo(order);

        return Response.SUCCESS(res);
    }

}
