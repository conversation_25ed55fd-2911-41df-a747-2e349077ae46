package com.bilibili.miniapp.open.portal.controller.open_api;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.SignUtil;
import com.bilibili.miniapp.open.portal.annotations.SignValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.ogv.OpenApiSignTestVo;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.order.IOrderService;
import com.bilibili.miniapp.open.service.bo.order.Order;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文档
 *
 * <AUTHOR>
 * @date 2024/12/12 17:52
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/platform/test")
@Tag(name = "/open_api/v1/platform/test")
public class OpenPlatformTestController extends AbstractController {

    @Autowired
    private IOrderService orderService;
    @Autowired
    private IOpenAccessService openAccessService;

    @SignValidation
    @Operation(description = "测试body")
    @RequestMapping(value = "/post", method = RequestMethod.POST)
    public Response<String> post(Context context,
                                 @Sign @RequestBody @Validated OpenApiSignTestVo vo,
                                 @Sign @RequestParam("q1") String q1) throws Exception {
        return Response.SUCCESS("success");
    }

    @SignValidation
    @Operation(description = "测试参数")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public Response<String> get(Context context,
                                @Sign @RequestParam("app_id") String appId,
                                @Sign @RequestParam("q1") String q1,
                                @RequestParam("q2") Integer q2,
                                @Sign @RequestParam("q3") List<String> q3) throws Exception {
        return Response.SUCCESS("success");
    }


    @Operation(description = "mock开发者回调接口")
    @RequestMapping(value = "/dev/callback", method = RequestMethod.POST)
    public Response<String> mockDevCallback(Context context,
                                            @RequestParam("ts") Long ts,
                                            @RequestParam("sign") String sign,
                                            @RequestBody JSONObject body) throws Exception {
        log.info("[OpenPlatformTestController] mockDevCallback, request={}", body.toJSONString());
        String order_id = body.getString("order_id");
        Order order = orderService.getOrder(Long.parseLong(order_id));
        AssertUtil.notNull(order, ErrorCodeType.NO_DATA.getCode(), "订单不存在");
        body.put("ts", ts);
        String sign1 = SignUtil.sign(body, openAccessService.getAccessToken(order.getAccessKey()));
        AssertUtil.isTrue(sign1.equalsIgnoreCase(sign), ErrorCodeType.BAD_SIGN);
        return Response.SUCCESS("success");
    }


    @Operation(description = "mock开发者退款回调接口")
    @RequestMapping(value = "/dev/refund/callback", method = RequestMethod.POST)
    public Response<String> mockDevRefundCallback(Context context,
                                            @RequestParam("ts") Long ts,
                                            @RequestParam("sign") String sign,
                                            @RequestBody JSONObject body) throws Exception {
        log.info("[OpenPlatformTestController] mockDevRefundCallback, request={}", body.toJSONString());
        String order_id = body.getString("order_id");
        Order order = orderService.getOrder(Long.parseLong(order_id));
        AssertUtil.notNull(order, ErrorCodeType.NO_DATA.getCode(), "订单不存在");
        body.put("ts", ts);
        String sign1 = SignUtil.sign(body, openAccessService.getAccessToken(order.getAccessKey()));
        AssertUtil.isTrue(sign1.equalsIgnoreCase(sign), ErrorCodeType.BAD_SIGN);
        return Response.SUCCESS("success");
    }
}
