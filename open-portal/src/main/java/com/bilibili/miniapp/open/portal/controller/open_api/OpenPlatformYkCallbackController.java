package com.bilibili.miniapp.open.portal.controller.open_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.YkCallbackControllerMapper;
import com.bilibili.miniapp.open.portal.vo.yk.YkOrderCallbackReqVo;
import com.bilibili.miniapp.open.service.biz.youku.YouKuCallbackService;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.bilibili.miniapp.open.service.util.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/7
 **/

@Slf4j
@RestController
@RequestMapping("/open_api/v1/platform/yk/callback")
public class OpenPlatformYkCallbackController extends AbstractController {

    @Resource
    private YouKuCallbackService youKuCallbackService;

    @Resource
    private ConfigCenter configCenter;

    @PostMapping("/order")
    public Response<Void> orderCallback(@RequestParam("sign") String sign, @RequestBody String reqVo) {
        String secret = configCenter.getYouKu().getOrderSecretKey();
        if (!SignUtil.verifySign(reqVo, sign, secret)) {
            log.error("Youku callback sign verification failed. reqVo: {}, sign: {}", reqVo, sign);
            return Response.FAIL(ErrorCodeType.BAD_SIGN.getCode(), "签名验证失败");
        }
        youKuCallbackService.saveOrderCallback(YkCallbackControllerMapper.MAPPER.toYkOrderCallbackReq(JsonUtil.readValue(reqVo, YkOrderCallbackReqVo.class)));
        return Response.SUCCESS();
    }
}
