package com.bilibili.miniapp.open.portal.controller.open_api.app;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.ClientAppletControllerMapper;
import com.bilibili.miniapp.open.portal.vo.client.applet.ClientAppletInfoVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.client.applet.IClientAppletService;
import com.bilibili.miniapp.open.service.bo.client.applet.ClientAppletInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/client/applet")
public class MiniAppClientAppInfoController extends AbstractController {

    @Autowired
    private IClientAppletService appletService;

    @GetMapping("/info")
    @MiniAppValidation
    public Response<ClientAppletInfoVo> appInfo(Context context) {

        ClientAppletInfoBo clientAppletInfoBo = appletService.appInfo(context.getMiniAppId());

        return Response.SUCCESS(ClientAppletControllerMapper.MAPPER.toVo(clientAppletInfoBo));
    }
}
