package com.bilibili.miniapp.open.portal.controller.open_api.app;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.client.user.ClientUserVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.client.user.IClientUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/client/user")
public class MiniAppClientUserController extends AbstractController {

    @Autowired
    private IClientUserService clientUserService;

    @GetMapping("/jscode2session")
    @MiniAppValidation
    public Response<ClientUserVo> jscode2session(Context context,
                                                 @RequestParam("js_code") String jsCode) {

        String openId = clientUserService.getUserOpenId(context.getMiniAppId(), jsCode);

        return Response.SUCCESS(ClientUserVo.builder()
                .openId(openId)
                .build());
    }
}
