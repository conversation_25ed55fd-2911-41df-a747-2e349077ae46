package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.ad.AdPositionVo;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序短剧广告相关
 *
 * <AUTHOR>
 * @date 2024/12/10 17:40
 */

@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/ad")
@Tag(name = "/open_api/v1/miniapp/ad")
@RequiredArgsConstructor
public class MiniAppAdController extends AbstractController {

    @Autowired
    private ConfigCenter configCenter;

    /**
     * 短剧广告位
     */
    @Operation(description = "查询广告位")
    @RequestMapping(value = "/position/query", method = RequestMethod.GET)
    public Response<AdPositionVo> queryPosition(@Parameter(description = "虚拟AppId")
                                                @RequestParam("v_app_id")
                                                String vAppId,
                                                @Parameter(description = "单元id")
                                                @RequestParam("ad_unit_id")
                                                String adUnitId) {
        String adMock = configCenter.getAdMock();
        AdPositionVo adPositionVo = JSON.parseObject(adMock, AdPositionVo.class);
        //todo: 需要从header中提取applet-id，作为app_id
        return Response.SUCCESS(adPositionVo);
    }

}
