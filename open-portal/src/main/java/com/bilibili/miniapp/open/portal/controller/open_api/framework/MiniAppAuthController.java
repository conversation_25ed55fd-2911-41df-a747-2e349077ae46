package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AuthLoginMapper;
import com.bilibili.miniapp.open.portal.vo.auth.PreAuthRespVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.auth.IMiniAppAuthService;
import com.bilibili.miniapp.open.service.bo.auth.MiniAppAuthBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 小程序授权登录
 * @Date 2025/3/13
 **/

@RestController
@RequestMapping(value = "/open_api/v1/miniapp")
@Slf4j
@Tag(name = "/open_api/v1/miniapp")
@RequiredArgsConstructor
public class MiniAppAuthController extends AbstractController {

    @Resource
    private IMiniAppAuthService authService;


    /**
     * 小程序手机号授权预登录（获取打码手机号）
     * @return
     */
    @MiniAppValidation
    @MainSiteLoginValidation
    @Operation(description = "获取打码手机号")
    @GetMapping(value = "/phone/preauth")
    public Response<PreAuthRespVo> preAuthLogin(Context context) {
        String appId = context.getMiniAppId();
        MiniAppAuthBo preAuthCode = authService.getPreAuthCode(appId, context.getMid());
        log.info("[MiniAppAuthController] preAuthLogin appId={}, mid={}, preAuthCode={}", appId, context.getMid(), preAuthCode);
        return Response.SUCCESS(AuthLoginMapper.MAPPER.toPreAuthRespVo(preAuthCode));
    }
}
