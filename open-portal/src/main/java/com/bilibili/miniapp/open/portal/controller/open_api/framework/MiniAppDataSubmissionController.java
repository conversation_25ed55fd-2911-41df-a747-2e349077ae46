package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.ActivationControllerMapper;
import com.bilibili.miniapp.open.portal.vo.client.activation.MiniAppActivationReportVo;
import com.bilibili.miniapp.open.service.biz.activation.IMiniAppDataSubmitService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序数据上报
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/data/submit")
public class MiniAppDataSubmissionController extends AbstractController {

    @Autowired
    private IMiniAppDataSubmitService miniAppDataSubmitService;

    /**
     * 上报小程序激活行为
     */
    @PostMapping("/activation")
    @Operation(description = "上报小程序激活行为")
    @MiniAppValidation
    public Response<Void> reportActivation(@RequestBody MiniAppActivationReportVo reportVo) {

        miniAppDataSubmitService.reportActivation(ActivationControllerMapper.MAPPER.toMsg(reportVo));

        return Response.SUCCESS();
    }
}
