package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.entity.SecretKeyPair;
import com.bilibili.miniapp.open.common.util.EncryptUtil;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.payment.ExchangePayInfoReqVo;
import com.bilibili.miniapp.open.portal.vo.payment.ExchangePayInfoResVo;
import com.bilibili.miniapp.open.portal.vo.payment.ExchangePayOrderInfoVo;
import com.bilibili.miniapp.open.portal.vo.payment.OpenPayInfoVo;
import com.bilibili.miniapp.open.service.biz.payment.IPaymentService;
import com.bilibili.miniapp.open.service.bo.order.OrderCreateRes;
import com.bilibili.miniapp.open.service.bo.payment.ExchangePayInfo;
import com.bilibili.miniapp.open.service.bo.payment.ExchangePayInfoReq;
import com.bilibili.miniapp.open.service.bo.payment.OpenPayConfirmInfo;
import com.bilibili.miniapp.open.service.bo.payment.OpenPayInfo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.PaymentConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 小程序端使用的支付接口
 *
 * <AUTHOR>
 * @date 2025/1/16 16:12
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/payment")
@Tag(name = "/open_api/v1/miniapp/payment")
@RequiredArgsConstructor
public class MiniAppPaymentController extends AbstractController {
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private ConfigCenter configCenter;

    /**
     * 交换支付信息
     */
    @MiniAppValidation
    @Operation(description = "交换支付信息")
    @RequestMapping(value = "/exchange_pay_info", method = RequestMethod.POST)
    public Response<ExchangePayInfoResVo> exchangePayInfo(Context context,
                                                          @Validated @RequestBody ExchangePayInfoReqVo vo) throws Exception {
        log.info("[MiniAppPaymentController] exchangePayInfo request={}", JSON.toJSONString(vo));
        Assert.notNull(vo.getOrderInfo(), "订单信息不能为空");
        Assert.hasText(vo.getOrderInfo().getPayInfo(), "订单信息不能为空");

        //decrypt pay info
        ExchangePayOrderInfoVo orderInfo = vo.getOrderInfo();
        PaymentConfig paymentConfig = configCenter.getPayment();
        String decrypt = EncryptUtil.decrypt(orderInfo.getPayInfo(), SecretKeyPair.builder()
                .key(paymentConfig.getOpenPayEncryptKey())
                .iv(paymentConfig.getOpenPayEncryptIv())
                .build());
        OrderCreateRes orderCreateRes = JSON.parseObject(decrypt, OrderCreateRes.class);

        //build exchange request
        ExchangePayInfoReq exchangePayInfoReq = ExchangePayInfoReq.builder()
                .order(orderCreateRes)
                .platform(context.getMiniAppPlatform())
                .userAgent(context.getMiniAppUserAgent())
                .traceInfo(vo.getTraceInfo())
                .build();

        //do exchange
        ExchangePayInfo exchangePayInfo = paymentService.exchangePayInfo(exchangePayInfoReq);


        //encrypt confirm info
        OpenPayInfo payInfo = exchangePayInfo.getPayInfo();
        String confirmInfo = null;
        if (Objects.nonNull(payInfo.getConfirmInfo())) {
            //如果不为null，则意味着需要走开放平支付（含先充值后支付），因此需要加密处理
            confirmInfo = EncryptUtil.encrypt(JSON.toJSONString(payInfo.getConfirmInfo()), SecretKeyPair.builder()
                    .key(paymentConfig.getOpenPayEncryptKey())
                    .iv(paymentConfig.getOpenPayEncryptIv())
                    .build());
        }

        //response
        ExchangePayInfoResVo exchangePayInfoResVo = ExchangePayInfoResVo.builder()
                .payParam(exchangePayInfo.getPayParam())
                .payType(exchangePayInfo.getPayType())
                .payInfo(OpenPayInfoVo.builder()
                        .showTitle(payInfo.getShowTitle())
                        .showContent(payInfo.getShowContent())
                        .confirmInfo(confirmInfo)
                        .build())
                .build();
        log.info("[MiniAppPaymentController] exchangePayInfo response={}", JSON.toJSONString(exchangePayInfoResVo));
        return Response.SUCCESS(exchangePayInfoResVo);
    }


    /**
     * 确认支付信息
     */
    @MiniAppValidation
    @Operation(description = "确认支付信息")
    @RequestMapping(value = "/confirm_pay_info", method = RequestMethod.POST)
    public Response<String> confirmPayInfo(Context context,
                                           @Validated @RequestBody OpenPayInfoVo vo) throws Exception {
        log.info("[MiniAppPaymentController] confirmPayInfo request={}", JSON.toJSONString(vo));
        Assert.hasText(vo.getConfirmInfo(), "支付确认信息不能为空");

        //decrypt confirm info
        PaymentConfig paymentConfig = configCenter.getPayment();
        String decrypt = EncryptUtil.decrypt(vo.getConfirmInfo(), SecretKeyPair.builder()
                .key(paymentConfig.getOpenPayEncryptKey())
                .iv(paymentConfig.getOpenPayEncryptIv())
                .build());
        OpenPayConfirmInfo openPayConfirmInfo = JSON.parseObject(decrypt, OpenPayConfirmInfo.class);
        openPayConfirmInfo.setBuvid(context.getBuvid());
        //do confirm
        paymentService.confirmPayInfo(openPayConfirmInfo);
        return Response.SUCCESS("");
    }
}
