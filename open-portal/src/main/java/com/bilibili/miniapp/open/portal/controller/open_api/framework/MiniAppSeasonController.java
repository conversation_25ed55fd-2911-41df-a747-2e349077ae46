package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.OgvSeasonMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.vo.ogv.SeasonVo;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationService;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * 小程序剧集管理相关
 *
 * <AUTHOR>
 * @date 2024/12/10 17:36
 */
@Slf4j
@RestController
@RequestMapping("/open_api/v1/miniapp/season")
@Tag(name = "/open_api/v1/miniapp/season")
@RequiredArgsConstructor
public class MiniAppSeasonController extends AbstractController {
    @Autowired
    private IOgvSeasonService ogvSeasonService;
    @Autowired
    private ISeasonAuthorizationService seasonAuthorizationService;

    /**
     * 短剧剧集
     */
//    @MainSiteLogin
    @MiniAppValidation
    @Operation(description = "查询剧集信息")
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public Response<SeasonVo> querySeason(Context context,
                                          @Parameter(description = "剧id")
                                          @RequestParam("season_id")
                                          Long seasonId,
                                          @Parameter(description = "集id")
                                          @RequestParam(value = "episode_id", required = false)
                                          Long episodeId) throws Exception {

        Assert.isTrue(seasonAuthorizationService.isAuthorized(context.getMiniAppId(), seasonId), "no authorization");
        Map<Long, SeasonBo> seasonMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(Lists.newArrayList(seasonId))
                .appendEpisode(true)
                .appendVideoDimension(true)
                .appendAuthorDetail(true)
                .build());
        SeasonBo seasonBo = seasonMap.get(seasonId);
        Assert.notNull(seasonBo, "剧集不存在");
        SeasonVo seasonVo = OgvSeasonMapper.MAPPER.toSeasonVo(seasonBo);
        if (Objects.equals(context.getMiniAppPlatform(), "ios") && context.getMiniAppVersion() < 82800000L) {
            //ios早些版本，把is_finish当成了更新的最新集数，因此需要兼容
            seasonVo.setIsFinish(seasonVo.getEpisodes().size());
        }
        return Response.SUCCESS(seasonVo);
    }
}
