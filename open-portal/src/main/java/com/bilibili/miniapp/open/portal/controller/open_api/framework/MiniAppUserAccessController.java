package com.bilibili.miniapp.open.portal.controller.open_api.framework;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.user.impl.UserAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@RestController
@RequestMapping(value = "/open_api/v1/miniapp/user/access")
@Slf4j
public class MiniAppUserAccessController extends AbstractController {

    @Resource
    private UserAccessService userAccessService;

    @MiniAppValidation
    @MainSiteLoginValidation
    @PostMapping("")
    public Response<Void> userAccess(Context context, @RequestParam("app_id") String appId) {
        Long mid = context.getMid();
        Assert.isTrue(Objects.nonNull(mid) && mid > 0, "mid非法");
        if (appId.startsWith("bilinternal")) {
            // 内部小程序访问暂不记录
            log.info("[MiniAppUserAccessController] 内部小程序访问暂不记录: {}", appId);
            return Response.SUCCESS();
        }
        userAccessService.recordUserAccess(mid, appId);
        return Response.SUCCESS();
    }
}
