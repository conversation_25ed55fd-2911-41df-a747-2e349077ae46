package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AccountMapper;
import com.bilibili.miniapp.open.portal.vo.account.AccountInfoResVo;
import com.bilibili.miniapp.open.portal.vo.account.AccountRoleVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@RestController
@RequestMapping("/web_api/v1/platform/account")
public class AccountController extends AbstractController {

    @Autowired
    private IAccountService accountService;

    @GetMapping("/info")
    @MainSiteLoginValidation
    public Response<AccountInfoResVo> getAccountInfo(Context context) {

        AccountInfoBo accountInfo = accountService.getAccountInfo(context.getMid());

        return Response.SUCCESS(AccountMapper.MAPPER.toVo(accountInfo));
    }

    @GetMapping("/role")
    @MainSiteLoginValidation
    public Response<AccountRoleVo> getAccountRole(Context context,
                                                  @RequestParam(value = "app_id", required = false) String appId) {

        Integer role = accountService.getUserRole(context.getMid(), appId);

        return Response.SUCCESS(AccountRoleVo.builder()
                .role(role)
                .build());
    }
}