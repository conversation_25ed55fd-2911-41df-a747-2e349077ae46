package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.util.GsonUtil;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/11/6 21:09
 */
@Slf4j
@Controller
@RequestMapping("/web_api/v1/backdoor")
@Tag(name = "/backdoor", description = "在线调试接口")
public class BackdoorController extends AbstractController {

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ConfigCenter configCenter;

    private static final Pattern BEAN_METHOD = Pattern.compile("([a-zA-Z0-9_$.]+)[.#]([a-zA-Z0-9_$]+)\\((.*)\\)", Pattern.DOTALL);

    @RequestMapping
    @Operation(description = "在线调试")
    public ResponseEntity<String> home(@RequestParam(value = "expression", required = false) String expression) throws Exception {
        if (!configCenter.getBackdoor().isEnable()) {
            return ResponseEntity.ok().body("Forbidden!");
        }
        String page = "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>在线调试</title>\n" +
                "</head>\n" +
                "<body>\n" +
                "\n" +
                "<form method=\"post\" action=\"\">\n" +
                "    <label>在线调试</label>\n" +
                "    <br/>\n" +
                "    <textarea rows=\"10\" cols=\"100\" name=\"expression\" placeholder=\"请输入表达式\">";

        // 写入参数
        page += expression == null ? "" : expression.trim();

        page += "</textarea>\n" +
                "    <br/>\n" +
                "    <input type=\"submit\"  value=\"　提　交　\"/>\n" +
                "</form>\n" +
                "<br/>\n" +
                "<textarea rows=\"20\" cols=\"100\" id=\"result\" placeholder=\"调用返回的结果\">";

        // 写入返回值
        try {
            if (StringUtils.hasText(expression)) {
                page += methodExecute(expression);
            }
        } catch (Exception e) {
            page += "调用异常：" + e.getMessage();
        }

        page += "</textarea>\n" +
                "</body>\n" +
                "</html>";

        return ResponseEntity.ok()
                .contentType(MediaType.valueOf("text/html;charset=UTF-8"))
                .body(page);
    }

    /***
     * 可执行项目中的任意方法。
     * @param expression Spring Bean 方法调用：userServiceImpl.findUser({name:"元宝"})
     *                   或直接复制方法引用：com.ewing.UserServiceImpl#findUser({name:"元宝"})
     *                   静态方法或new一个新对象调用：ewing.common.TimeUtils.getDaysOfMonth(2018,5)
     *                   注意：如果方法重载的，参数Json也是兼容的，将无法确定调用哪个方法。
     */
    public String methodExecute(String expression) {
        Assert.hasText(expression, "表达式不能为空！");
        Matcher matcher = BEAN_METHOD.matcher(expression);
        Assert.isTrue(matcher.find(), "表达式格式不正确！");

        // 根据名称获取Bean
        String classOrBeanName = matcher.group(1);
        Object springBean = getSpringBean(classOrBeanName);
        Class clazz;
        try {
            clazz = springBean == null ? Class.forName(classOrBeanName) : AopProxyUtils.ultimateTargetClass(springBean);
        } catch (Exception e) {
            throw new RuntimeException("初始化类失败！", e);
        }
        Assert.notNull(clazz, "调用Class不能为空！");

        // 根据名称获取方法列表
        List<Method> mayMethods = getMethods(clazz, matcher.group(2));

        // 转换方法参数
        JsonArray params = getJsonArray("[" + matcher.group(3) + "]");
        return GsonUtil.toJson(executeFoundMethod(clazz, springBean, mayMethods, params));
    }

    private Object executeFoundMethod(Class clazz, Object bean, List<Method> mayMethods, JsonArray params) {
        // 根据参数锁定方法
        List<Object> args = new ArrayList<>();
        Method foundMethod = null;
        for (Method method : mayMethods) {
            if (!args.isEmpty()) {
                args.clear();
            }
            Type[] types = method.getGenericParameterTypes();
            if (types.length != params.size()) {
                continue;
            }
            // 参数转换，无异常表示匹配
            Iterator<JsonElement> paramIterator = params.iterator();
            try {
                for (Type type : types) {
                    Object arg = GsonUtil.getGson().fromJson(paramIterator.next(), type);
                    args.add(arg);
                }
            } catch (Exception e) {
                continue;
            }
            Assert.isNull(foundMethod, "方法调用重复：" + foundMethod + " 和 " + method);
            foundMethod = method;
            break;//既然都已经找到方法了，就不要再循环处理重载方法了，否则把正常的args都清空了
        }

        // 调用方法并返回
        Assert.notNull(foundMethod, "未找到满足参数的方法！");
        try {
            foundMethod.setAccessible(true);
            if (Modifier.isStatic(foundMethod.getModifiers())) {
                return foundMethod.invoke(clazz, args.toArray());
            } else {
                if (bean != null) {
                    Class<?> methodClass = foundMethod.getDeclaringClass();
//                    if (!methodClass.equals(bean.getClass())) {
//                        foundMethod = bean.getClass().getDeclaredMethod(foundMethod.getName(), foundMethod.getParameterTypes());
//                    }
                    return foundMethod.invoke(bean, args.toArray());
                } else {
                    return foundMethod.invoke(clazz.newInstance(), args.toArray());
                }
            }
        } catch (InvocationTargetException e) {
            String msg = "调用方法失败：" + ExceptionUtils.getMessage(e.getTargetException());
            log.error(msg, e);
            throw new RuntimeException(msg, e);
        } catch (Exception e) {
            log.error("调用方法失败：" + e.getMessage(), e);
            throw new RuntimeException("调用方法失败：" + e.getMessage(), e);
        }
    }

    private JsonArray getJsonArray(String jsonParams) {
        try {
            return GsonUtil.toObject(jsonParams, JsonArray.class);
        } catch (Exception e) {
            throw new RuntimeException("参数格式不正确！");
        }
    }

    private List<Method> getMethods(Class clazz, String methodName) {
        List<Method> mayMethods = Stream.of(clazz.getDeclaredMethods())
                .filter(m -> methodName.equals(m.getName()))
                .collect(Collectors.toList());
        Assert.notEmpty(mayMethods, "未找到方法：" + methodName);
        return mayMethods;
    }

    private Object getSpringBean(String beanName) {
        try {
            return applicationContext.getBean(beanName);
        } catch (Exception e) {
            try {
                return applicationContext.getBean(Class.forName(beanName));
            } catch (Exception ex) {
                return null;
            }
        }
    }
}

