package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import org.elasticsearch.common.inject.spi.PrivateElements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
@RestController
@RequestMapping("/web_api/v1/berserker/callback")
public class BerserkerCallBackController extends AbstractController {


    @Autowired
    private IIncomeService incomeService;

    @GetMapping("/daily/income/process")
    public Response<Void> processDailyIncomeDetails(@RequestParam("log_date") String logDate) {
        incomeService.processDailyIncomeDetails(logDate);
    }

}
