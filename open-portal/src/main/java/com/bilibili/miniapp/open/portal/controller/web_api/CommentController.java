package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.CommentMapper;
import com.bilibili.miniapp.open.portal.vo.comment.BatchGetReplyBlueLinkInfoReqVo;
import com.bilibili.miniapp.open.portal.vo.comment.BatchGetReplyBlueLinkInfoRespVo;
import com.bilibili.miniapp.open.common.entity.Response;

import com.bilibili.miniapp.open.service.biz.comment.CommentService;
import com.bilibili.miniapp.open.service.bo.comment.BatchGetReplyBlueLinkInfoRespBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/comment")
@Tag(name = "/comment", description = "评论操作接口")
public class CommentController extends AbstractController {

    @Autowired
    private CommentService commentService;

    @PostMapping("/blue_link")
    @Operation(description = "转换评论蓝链")
    public Response<BatchGetReplyBlueLinkInfoRespVo> convertCommentBlueLinks(@RequestBody BatchGetReplyBlueLinkInfoReqVo request) {

        BatchGetReplyBlueLinkInfoRespBo respBo = commentService.convertCommentBlueLinks(CommentMapper.MAPPER.voToBo(request));
        BatchGetReplyBlueLinkInfoRespVo response = CommentMapper.MAPPER.boToVo(respBo);

        return Response.SUCCESS(response);
    }
}