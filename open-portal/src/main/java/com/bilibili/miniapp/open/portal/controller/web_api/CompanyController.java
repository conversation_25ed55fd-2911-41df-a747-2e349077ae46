package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.CompanyControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.company.CompanyDetailResVo;
import com.bilibili.miniapp.open.portal.vo.company.CompanyInfoVo;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@RestController
@RequestMapping("/web_api/v1/platform/company")
public class CompanyController extends AbstractController {

    @Autowired
    private ICompanyService companyService;

    @PostMapping("/admission/save")
    @MainSiteLoginValidation
    public Response<Void> saveCompanyAdmission(Context context,
                                               @RequestBody CompanyInfoVo companyInfoVo) {

        companyService.saveCompanyAdmission(CompanyControllerMapper.MAPPER.toBo(companyInfoVo, context.getMid()));

        return Response.SUCCESS();
    }

    @GetMapping("/detail")
    @MainSiteLoginValidation
    public Response<CompanyDetailResVo> getCompanyDetail(Context context) {
        CompanyDetailBo detail = companyService.getDetail(context.getMid());
        return Response.SUCCESS(CompanyControllerMapper.MAPPER.toVo(detail));
    }

}