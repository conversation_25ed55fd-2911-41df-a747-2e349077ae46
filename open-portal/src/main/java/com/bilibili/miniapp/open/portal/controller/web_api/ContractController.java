package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.ContractControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.contract.ContractSettlementDetailRespVo;
import com.bilibili.miniapp.open.portal.vo.contract.ContractSignUrlRespVo;
import com.bilibili.miniapp.open.portal.vo.contract.SettlementContractVo;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.bilibili.miniapp.open.service.bo.contract.ContractSignUrlBo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 合同相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/contract")
public class ContractController extends AbstractController {

    @Autowired
    private IContractService contractService;

    /**
     * 创建合同结算
     */
    @PostMapping("/settlement/save")
    @MainSiteLoginValidation
    public Response<Void> createContractSettlement(Context context,
                                                   @Valid @RequestBody SettlementContractVo request) {

        SettlementContractBo settlementContractBo = ContractControllerMapper.MAPPER.voToBo(request);
        contractService.saveContractSettlement(context.getMid(), settlementContractBo);
        return Response.SUCCESS();
    }

    /**
     * 获取合同详情
     */
    @GetMapping("/settlement/detail")
    @MainSiteLoginValidation
    public Response<ContractSettlementDetailRespVo> getContractDetail(Context context,
                                                                      @RequestParam("app_id") @NotBlank String appId) {
        return Response.SUCCESS(ContractControllerMapper.MAPPER.boToVo(
                contractService.getContractDetail(context.getMid(), appId)));
    }

    /**
     * 获取合同签署链接
     */
    @GetMapping("/settlement/sign_url")
    @MainSiteLoginValidation
    public Response<ContractSignUrlRespVo> getContractSignUrl(Context context,
                                                              @RequestParam("app_id") String appId) {

        ContractSignUrlBo contractSignUrl = contractService.getContractSignUrl(context.getMid(), appId);

        return Response.SUCCESS(ContractControllerMapper.MAPPER.boToVo(contractSignUrl));
    }

}
