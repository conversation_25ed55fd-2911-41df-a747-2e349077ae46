package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.IcpControllerMapper;
import com.bilibili.miniapp.open.portal.vo.icp.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportInfo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.service.biz.icp.IcpService;
import com.bilibili.miniapp.open.service.bo.icp.IcpConfigBo;
import com.bilibili.miniapp.open.service.bo.icp.IdentityAuthResp;
import com.bilibili.miniapp.open.service.bo.icp.IdentityResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/19
 **/

@RestController
@RequestMapping("/web_api/v1/platform/icp")
public class IcpController extends AbstractController {

    @Resource
    private IcpService icpService;

    @GetMapping("/status")
    @MainSiteLoginValidation
    public Response<IcpStatusRespVo> queryIcpStatus(@RequestParam("app_id") String appId) {
        IcpReportState icpReportState = icpService.getIcpReportState(appId);
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIcpStatusRespVo(icpReportState));
    }

    @GetMapping("/config")
    @MainSiteLoginValidation
    public Response<IcpConfigVo> getIcpConfig() {
        IcpConfigBo icpConfigBo = icpService.getIcpConfigBo();
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIcpConfigVo(icpConfigBo));
    }

    @PostMapping("/report")
    @MainSiteLoginValidation
    public Response<Void> reportIcpInfo(@RequestBody IcpReportVo vo) {
        icpService.saveIcpReportInfo(IcpControllerMapper.MAPPER.toIcpReportInfo(vo));
        return Response.SUCCESS();
    }

    @GetMapping("/{flow_id}")
    @MainSiteLoginValidation
    public Response<IcpReportVo> getIcpReportInfo(@PathVariable("flow_id") Long flowId) {
        IcpReportInfo icpReportInfo = icpService.getIcpReportInfo(flowId);
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIcpReportReqVo(icpReportInfo));
    }

    @PostMapping("/identity/qrcode")
    @MainSiteLoginValidation
    public Response<IdentityAuthRespVo> identityAuth(@RequestBody IcpReportVo vo) {
        IdentityAuthResp identityAuthResp = icpService.identityAuth(IcpControllerMapper.MAPPER.toIcpReportInfo(vo));
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIdentityAuthRespVo(identityAuthResp));
    }

    @GetMapping("/identity/result")
    @MainSiteLoginValidation
    public Response<IcpIdentityResultVo> getIdentityResult(@RequestParam("identity_id") Long identityId) {
        IdentityResult identityResult = icpService.syncIdentityAuthInfo(identityId);
        if (identityResult == null) {
            return Response.SUCCESS();
        }
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIdentityAuthRespVo(identityResult));
    }

    @PostMapping("/report/retry")
    @MainSiteLoginValidation
    public Response<Void> retryIcpReport(@RequestBody IcpRetryReportVo vo) {
        icpService.retry(vo.getFlowId());
        return Response.SUCCESS();
    }
}
