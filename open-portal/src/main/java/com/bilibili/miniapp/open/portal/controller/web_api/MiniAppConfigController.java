package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.MiniAppConfigControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.miniapp_config.MiniAppCustomLinkVo;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@RestController
@RequestMapping("/web_api/v1/platform/mini_app/config")
public class MiniAppConfigController extends AbstractController {

    @Autowired
    private IMiniAppCustomLinkService customLinkService;

    @PostMapping("/custom_link")
    @MainSiteLoginValidation
    public Response<Void> saveCustomLink(Context context,
                                         @RequestBody MiniAppCustomLinkVo customLinkVo) {

        AssertUtil.isTrue(StringUtils.isNotBlank(customLinkVo.getCustomPath()), ErrorCodeType.BAD_PARAMETER.getCode(), "custom_path不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(customLinkVo.getAppId()), ErrorCodeType.BAD_PARAMETER.getCode(), "app_id不能为空");

        MiniAppCustomLinkBo bo = MiniAppConfigControllerMapper.MAPPER.toBo(customLinkVo);
        customLinkService.saveCustomLink(bo);

        return Response.SUCCESS();
    }

    @GetMapping("/custom_link")
    @MainSiteLoginValidation
    public Response<MiniAppCustomLinkVo> getCustomLink(Context context,
                                                       @RequestParam("app_id") String appId) {

        MiniAppCustomLinkBo customLinkBo = customLinkService.getCustomLinkFromDb(appId);

        return Response.SUCCESS(MiniAppConfigControllerMapper.MAPPER.toVo(customLinkBo));
    }

}