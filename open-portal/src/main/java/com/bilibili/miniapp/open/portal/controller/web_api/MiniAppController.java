package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AccountMapper;
import com.bilibili.miniapp.open.portal.mapper.MiniAppControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.miniapp.*;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCategoryTree;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppDetailBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppListBo;
import com.bilibili.miniapp.open.service.bo.ogv.AuthorAuthorizationBo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@RestController
@RequestMapping("/web_api/v1/platform/mini_app")
public class MiniAppController extends AbstractController {

    @Autowired
    private IMiniAppService miniAppService;
    @Autowired
    private IAuthorAuthorizationService authorizationService;

    @PostMapping("/account/auth")
    @MainSiteLoginValidation
    public Response<Void> auth(Context context,
                               @RequestBody MiniAppAuthVo miniAppAuthVo) {

        Assert.isTrue(miniAppAuthVo.getAppId() != null, "app_id不能为空");

        authorizationService.authorize(AuthorAuthorizationBo.builder()
                .appId(miniAppAuthVo.getAppId())
                .midList(List.of(context.getMid()))
                .build());

        return Response.SUCCESS();
    }

    @DeleteMapping("/account/auth")
    @MainSiteLoginValidation
    public Response<Void> cancelAuth(Context context,
                                     @RequestBody MiniAppCancelAuthVo cancelAuthVo) {

        authorizationService.cancel(AuthorAuthorizationBo.builder()
                .appId(cancelAuthVo.getAppId())
                .midList(List.of(Long.valueOf(cancelAuthVo.getUpMid())))
                .build());

        return Response.SUCCESS();
    }

    @GetMapping("/account/auth/list")
    @MainSiteLoginValidation
    public Response<MiniAppAuthListVo> authList(Context context,
                                                @RequestParam("app_id") String appId) {

        AuthorAuthorizationBo authorization = authorizationService.getAuthorizationWithMidInfo(appId);

        return Response.SUCCESS(MiniAppAuthListVo.builder()
                .accounts(authorization.getAccounts().stream()
                        .map(AccountMapper.MAPPER::toVo)
                        .collect(Collectors.toList()))
                .build());
    }

    @MainSiteLoginValidation
    @PostMapping("/admission/save")
    public Response<Void> saveMiniAppAdmission(Context context,
                                               @RequestBody MiniAppVo miniAppVo) {

        MiniAppBo miniAppBo = MiniAppControllerMapper.MAPPER.toBo(miniAppVo);
        miniAppService.saveMiniAppAdmission(context.getMid(), miniAppBo);

        return Response.SUCCESS();
    }

    @MainSiteLoginValidation
    @GetMapping("/list")
    public Response<PageResult<MiniAppListVo>> getMiniAppList(Context context,
                                                              @RequestParam(value = "name", required = false) String name,
                                                              @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                              @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {

        PageResult<MiniAppListBo> pageResult = miniAppService.queryMiniAppList(context.getMid(), name, page, size);

        return Response.SUCCESS(new PageResult<>(pageResult.getTotal(),
                pageResult.getRecords().stream()
                        .map(MiniAppControllerMapper.MAPPER::toVo)
                        .collect(Collectors.toList())));
    }


    @MainSiteLoginValidation
    @GetMapping("/detail")
    public Response<MiniAppDetailVo> getMiniAppDetail(Context context,
                                                      @RequestParam(value = "app_id", required = false) String appId,
                                                      @RequestParam(value = "admission_id", required = false) Long admissionId) {

        Assert.isTrue(!(admissionId == null && appId == null), "admission_id和app_id不能都为空");
        MiniAppDetailBo miniAppDetailBo = miniAppService.getMiniAppDetail(context.getMid(), appId, admissionId);

        return Response.SUCCESS(MiniAppControllerMapper.MAPPER.toVo(miniAppDetailBo));
    }

    @MainSiteLoginValidation
    @GetMapping("/name/check")
    public Response<MiniAppNameCheckVo> checkName(Context context,
                                      @RequestParam(value = "app_id", required = false) String appId,
                                      @RequestParam(value = "name") String name) {
        String nameCheckResult = miniAppService.checkName(appId, name);
        return Response.SUCCESS(MiniAppNameCheckVo.builder()
                .validate(StringUtils.isBlank(nameCheckResult))
                .msg(nameCheckResult)
                .build());
    }

    @MainSiteLoginValidation
    @GetMapping("/service_type")
    public Response<List<MiniAppCategoryTreeVo>> getCategoryTree(Context context) {
        List<MiniAppCategoryTree> categoryTree = miniAppService.getCategoryTree();
        if (CollectionUtils.isEmpty(categoryTree)) {
            return Response.SUCCESS(List.of());
        }
        List<MiniAppCategoryTreeVo> categoryTreeVo = categoryTree.stream()
                .map(MiniAppControllerMapper.MAPPER::toVo)
                .collect(Collectors.toList());
        return Response.SUCCESS(categoryTreeVo);
    }

    @MainSiteLoginValidation
    @PostMapping("/name/update")
    public Response<Boolean> updateName(@RequestParam(value = "app_id") String appId,
                                     @RequestParam(value = "name") String name) {
        return Response.SUCCESS(miniAppService.updateName(appId, name));
    }
}
