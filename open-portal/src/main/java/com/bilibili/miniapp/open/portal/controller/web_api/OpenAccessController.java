package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.AccessControllerMapper;
import com.bilibili.miniapp.open.portal.vo.access.OpenAccessResVo;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/13
 */
@RestController
@RequestMapping("/web_api/v1/platform/access")
public class OpenAccessController extends AbstractController {

    @Autowired
    private IOpenAccessService openAccessService;

    @GetMapping("/info")
    @MainSiteLoginValidation
    public Response<OpenAccessResVo> getAccessInfo(Context context) {
        OpenAccessBo access = openAccessService.getAccess(context.getMid());
        return Response.SUCCESS(AccessControllerMapper.MAPPER.toVo(access));
    }
}
