package com.bilibili.miniapp.open.portal.controller.web_api;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.PaySignUtil;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.payment.IPaymentService;
import com.bilibili.miniapp.open.service.bo.payment.PayNotifyInfo;
import com.bilibili.miniapp.open.service.bo.payment.RefundNotifyInfo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.PaymentConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 开放平台支付回调接口
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957975">支付回调文档</a>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957983">退款回调文档</a>
 * <a href="https://miniapp.bilibili.co/open/open_api/v1/platform/payment/notify">开平支付回调地址</a>
 * <a href="https://miniapp.bilibili.co/open/open_api/v1/platform/payment/refund/notify">开平退款回调地址</a>
 *
 * <AUTHOR>
 * @date 2025/1/16 22:17
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/payment")
@Tag(name = "/web_api/v1/platform/payment")
@RequiredArgsConstructor
public class OpenPlatformPaymentController extends AbstractController {
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private ConfigCenter configCenter;

    private static final String SUCCESS = "SUCCESS";
    private static final String FAIL = "FAIL";
    private static final String REPUBLISH = "REPUBLISH";

    /**
     * 支付回调
     * <p>
     * SUCCESS：表示成功
     * <p>
     * FAIL：表示失败，立即重试
     * <p>
     * REPUBLISH：表示稍后重试
     */
    @Operation(description = "支付回调")
    @RequestMapping(value = "/notify", method = RequestMethod.GET)
    public String notify(Context context,
                         @RequestParam("msgId") String msgId,
                         @RequestParam("msgContent") String msgContent) throws Exception {
        log.info("[OpenPlatformPaymentController] notify msgId={}, msgContent={}", msgId, msgContent);
        String res = SUCCESS;
        try {
            //使用OrderedField来保证JSONObject在toString时和定义的顺序一致，否则对于复合类型的toString顺序错乱导致签名失败
            JSONObject content = JSONObject.parseObject(msgContent, Feature.OrderedField);
            PayNotifyInfo payNotifyInfo = content.toJavaObject(PayNotifyInfo.class);

            AssertUtil.hasText(msgId, ErrorCodeType.BAD_PARAMETER.getCode(), "msgId不能为空");
            AssertUtil.notNull(content, ErrorCodeType.BAD_PARAMETER.getCode(), "msgContent不能为空");
            AssertUtil.notNull(payNotifyInfo, ErrorCodeType.BAD_PARAMETER.getCode(), "回调消息格式错误");

            PaymentConfig paymentConfig = configCenter.getPayment();
            content.remove("sign");//移除签名key之后再签名
            String sign = PaySignUtil.sign(content, paymentConfig.getPayToken());
            AssertUtil.isTrue(Objects.equals(sign, payNotifyInfo.getSign()), ErrorCodeType.BAD_SIGN);
            paymentService.notify(payNotifyInfo);
        } catch (ServiceException e) {
            res = FAIL;
            if (Objects.equals(ErrorCodeType.FAILED_AND_RETRY_WAIT_MOMENT.getCode(), e.getCode())) {
                res = REPUBLISH;
            }
            log.error("[OpenPlatformPaymentController] notify process service error, msgId={}, msgContent={}", msgId, msgContent, e);
        } catch (Throwable e) {
            res = FAIL;
            log.error("[OpenPlatformPaymentController] notify process error, msgId={}, msgContent={}", msgId, msgContent, e);
        }
        return res;
    }

    //public static void main(String[] args) {
    //    String correctMsg = "{\"customerId\":10082,\"serviceType\":0,\"txId\":3900640623332598133,\"orderId\":\"1024115\",\"feeType\":\"CNY\",\"payStatus\":\"SUCCESS\",\"payChannel\":\"wechat\",\"payChannelName\":\"微信\",\"payChannelId\":10003,\"payAmount\":2990,\"payMsgContent\":\"{\\\"payCounponAmount\\\":0,\\\"payBpAmount\\\":0,\\\"defaultBpAmount\\\":0,\\\"iosBpAmount\\\":0,\\\"productId\\\":\\\"2194\\\",\\\"uid\\\":\\\"*********\\\",\\\"failReason\\\":\\\"\\\"}\",\"payAccountId\":\"oMfZ_t9HMHANn1sOoaZNZ8I1F5zs\",\"payBank\":\"CCB_DEBIT\",\"deviceType\":3,\"orderPayTime\":\"2025-04-20 11:26:39\",\"timestamp\":\"*************\",\"traceId\":\"3900642493992214984\",\"extData\":\"{\\\"openId\\\":\\\"d216ba1533383d5baf2368c0f74f380c\\\",\\\"appId\\\":\\\"bilic7e66d6df849d4f3\\\"}\",\"signType\":\"MD5\",\"sign\":\"4d43022d912914a79d56cdbee129768b\",\"expiredTime\":0}";
    //    String errorMsg = "{\"customerId\":10082,\"serviceType\":0,\"txId\":3900640619138295307,\"orderId\":\"1024102\",\"feeType\":\"CNY\",\"payStatus\":\"SUCCESS\",\"payChannel\":\"wechat\",\"payChannelName\":\"微信\",\"payChannelId\":10003,\"payAmount\":2990,\"payMsgContent\":\"{\\\"payCounponAmount\\\":0,\\\"payBpAmount\\\":0,\\\"defaultBpAmount\\\":0,\\\"iosBpAmount\\\":0,\\\"productId\\\":\\\"2194\\\",\\\"uid\\\":\\\"*********\\\",\\\"failReason\\\":\\\"\\\"}\",\"payAccountId\":\"\",\"payAccount\":\"\",\"payBank\":\"CCB_DEBIT\",\"deviceType\":3,\"orderPayTime\":\"2025-04-20 11:11:01\",\"timestamp\":\"*************\",\"traceId\":\"3900647992724989605\",\"extData\":\"{\\\"openId\\\":\\\"d216ba1533383d5baf2368c0f74f380c\\\",\\\"appId\\\":\\\"bilic7e66d6df849d4f3\\\"}\",\"signType\":\"MD5\",\"sign\":\"517ec1a4b82e13c3e9e8a146f17d259b\",\"expiredTime\":0}";
    //    JSONObject content = JSONObject.parseObject(errorMsg, Feature.OrderedField);
    //    PayNotifyInfo payNotifyInfo = content.toJavaObject(PayNotifyInfo.class);
    //    content.remove("sign");//移除签名key之后再签名
    //    String sign = PaySignUtil.sign(content, "58ad6387c0867c766c9993dbc948961d");
    //    System.out.println(sign);
    //    System.out.println(payNotifyInfo.getSign());
    //}

    /**
     * 退款回调
     * SUCCESS   表示成功
     * <p>
     * FAIL 表示失败，立即重试
     * <p>
     * REPUBLISH 表示稍后重试
     * <p>
     * REFUND_CONFIRM 退款确认
     * <p>
     * REFUND_CANCEL 退款取消
     */
    @Operation(description = "退款回调")
    @RequestMapping(value = "/refund/notify", method = RequestMethod.GET)
    public String refundNotify(Context context,
                               @RequestParam("msgId") String msgId,
                               @RequestParam("msgContent") String msgContent) throws Exception {
        log.info("[OpenPlatformPaymentController] refundNotify msgId={}, msgContent={}", msgId, msgContent);
        String res = SUCCESS;
        try {
            //使用OrderedField来保证JSONObject在toString时和定义的顺序一致，否则batchRefundList字段toString的字段顺序和原串中不一致，导致签名失败
            JSONObject content = JSONObject.parseObject(msgContent, Feature.OrderedField);
            RefundNotifyInfo refundNotifyInfo = content.toJavaObject(RefundNotifyInfo.class);

            AssertUtil.hasText(msgId, ErrorCodeType.BAD_PARAMETER.getCode(), "msgId不能为空");
            AssertUtil.notNull(content, ErrorCodeType.BAD_PARAMETER.getCode(), "msgContent不能为空");
            AssertUtil.notNull(refundNotifyInfo, ErrorCodeType.BAD_PARAMETER.getCode(), "回调消息格式错误");

            PaymentConfig paymentConfig = configCenter.getPayment();
            content.remove("sign");//移除签名key之后再签名
            String sign = PaySignUtil.sign(content, paymentConfig.getPayToken());
            AssertUtil.isTrue(Objects.equals(sign, refundNotifyInfo.getSign()), ErrorCodeType.BAD_SIGN);
            paymentService.refundNotify(refundNotifyInfo);
        } catch (ServiceException e) {
            res = FAIL;
            if (Objects.equals(ErrorCodeType.FAILED_AND_RETRY_WAIT_MOMENT.getCode(), e.getCode())) {
                res = REPUBLISH;
            }
            log.error("[OpenPlatformPaymentController] refundNotify process service error, msgId={}, msgContent={}", msgId, msgContent, e);
        } catch (Throwable e) {
            res = FAIL;
            log.error("[OpenPlatformPaymentController] refundNotify process error, msgId={}, msgContent={}", msgId, msgContent, e);
        }
        return res;
    }
}
