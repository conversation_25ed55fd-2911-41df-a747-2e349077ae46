package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.vo.resource.PicUploadResVo;
import com.bilibili.miniapp.open.service.biz.resource.impl.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@RestController
@RequestMapping("/web_api/v1/platform/resource")
public class ResourceController extends AbstractController {

    @Autowired
    private ResourceService resourceService;

    @PostMapping("/upload_pic")
    @MainSiteLoginValidation
    public Response<PicUploadResVo> uploadPic(@RequestPart("file") MultipartFile pic,
                                              @RequestParam(value = "width", required = false) Integer width,
                                              @RequestParam(value = "height", required = false) Integer height) {

        String url = resourceService.uploadPic(pic, width, height);

        return Response.SUCCESS(PicUploadResVo.builder()
                .url(url)
                .build());
    }
}
