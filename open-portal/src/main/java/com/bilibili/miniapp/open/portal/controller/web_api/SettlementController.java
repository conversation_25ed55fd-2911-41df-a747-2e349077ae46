package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.SettlementControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDateListVo;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDateListBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/settlement")
public class SettlementController extends AbstractController {

    @Autowired
    private IIncomeService incomeService;

    /**
     * 获取结算日期列表
     */
    @GetMapping("/date")
    @MainSiteLoginValidation
    public Response<SettlementDateListVo> getSettlementDates(Context context,
                                                             @RequestParam("app_id") String appId) {

        SettlementDateListBo settlementDates = incomeService.getSettlementDates(context.getMid(), appId);

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.boToVo(settlementDates));
    }
}
