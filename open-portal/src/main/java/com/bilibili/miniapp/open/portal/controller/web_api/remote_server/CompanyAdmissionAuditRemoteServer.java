package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.CompanyAdmissionAuditInfoDto;
import com.bilibili.miniapp.api.dto.CompanyAdmissionAuditListDto;
import com.bilibili.miniapp.api.dto.AdmissionAuditReqDto;
import com.bilibili.miniapp.api.service.MiniAppCompanyAdmissionRemoteService;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.CompanyAdmissionAuditMapper;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditListBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@RestController
@RequestMapping("/web_api/v1/platform/audit/company/admission")
public class CompanyAdmissionAuditRemoteServer extends AbstractController implements MiniAppCompanyAdmissionRemoteService {

    @Autowired
    private ICompanyService companyService;

    @GetMapping("/list")
    @Override
    public Response<PageResult<CompanyAdmissionAuditListDto>> queryAdmissionAuditList(@RequestParam(value = "companyName", required = false) String companyName,
                                                                                      @RequestParam(value = "auditStatus", required = false) Integer auditStatus,
                                                                                      @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                                      @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {

        PageResult<CompanyAdmissionAuditListBo> result = companyService.queryAdmissionAuditList(companyName, AppCompanyAuditStatus.getByCode(auditStatus), Page.valueOf(page, size));

        return Response.SUCCESS(
                new PageResult<>(result.getTotal(),
                        result.getRecords().stream()
                                .map(CompanyAdmissionAuditMapper.MAPPER::toDto)
                                .collect(Collectors.toList()))
        );
    }

    @GetMapping("/detail")
    @Override
    public Response<CompanyAdmissionAuditInfoDto> queryAdmissionAuditDetail(@RequestParam("id") Long id) {
        CompanyAdmissionAuditInfoBo auditInfoBo = companyService.queryAdmissionAuditDetail(id);
        return Response.SUCCESS(CompanyAdmissionAuditMapper.MAPPER.toDto(auditInfoBo));
    }

    @PostMapping("")
    @Override
    public Response<Void> passOrRejectAdmission(@RequestBody AdmissionAuditReqDto auditReqDto) {
        AssertUtil.isTrue(auditReqDto.getAuditStatus() != null, 0, "审核状态不能为空");

        companyService.passOrRejectAdmission(auditReqDto.getId(), AppCompanyAuditStatus.getByCode(auditReqDto.getAuditStatus()), auditReqDto.getFailReason());
        return Response.SUCCESS();
    }
}