package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.*;
import com.bilibili.miniapp.api.service.MiniAppIcpAuditRemoteService;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.IcpControllerMapper;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportInfo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.service.biz.icp.IcpService;
import com.bilibili.miniapp.open.service.bo.icp.IcpConfigBo;
import com.bilibili.miniapp.open.service.bo.icp.IcpPlatformAuditListBo;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/

@RestController
@RequestMapping("/web_api/v1/platform/icp/remote")
public class IcpAuditRemoteServer extends AbstractController implements MiniAppIcpAuditRemoteService {

    @Resource
    private IcpService icpService;
    @Override
    @GetMapping("/audit/{flow_id}")
    public Response<IcpReportDto> getIcpReportInfo(@PathVariable("flow_id") Long flowId) {
        IcpReportInfo icpReportInfo = icpService.getIcpReportInfo(flowId);
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIcpReportDto(icpReportInfo));
    }

    @Override
    @PostMapping("/audit/commit")
    public Response<Void> handleIcpAuditResult(@RequestBody IcpPlatformAuditDto auditDto) {
        icpService.saveIcpPlatformAuditResult(IcpControllerMapper.MAPPER.toIcpPlatformAudit(auditDto));
        return Response.SUCCESS();
    }

    @Override
    @PostMapping("/audit/list")
    public Response<PageInfo<IcpPlatformAuditListDto>> queryIcpAuditList(@RequestBody IcpPlatformAuditQueryDto queryDto) {
        PageInfo<IcpPlatformAuditListBo> queryIcpAuditList = icpService.queryIcpAuditList(IcpControllerMapper.MAPPER.toIcpPlatformAuditQueryBo(queryDto));
        if (CollectionUtils.isEmpty(queryIcpAuditList.getList())) {
            return Response.SUCCESS(new PageInfo<>());
        }
        PageInfo<IcpPlatformAuditListDto> result = new PageInfo<>();
        BeanUtils.copyProperties(queryIcpAuditList, result);
        result.setList(queryIcpAuditList.getList().stream()
                .map(IcpControllerMapper.MAPPER::toIcpPlatformAuditListDto)
                .collect(Collectors.toList()));
        return Response.SUCCESS(result);
    }

    @Override
    @GetMapping("/state")
    public Response<IcpReportStatusDto> getIcpReportStatus(@RequestParam("app_id") String appId) {
        IcpReportState icpReportState = icpService.getIcpReportState(appId);
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIcpReportStatusDto(icpReportState));
    }

    @Override
    @GetMapping("/config")
    public Response<IcpRemoteConfigDto> getIcpConfig() {
        IcpConfigBo icpConfigBo = icpService.getIcpConfigBo();
        return Response.SUCCESS(IcpControllerMapper.MAPPER.toIcpConfigDto(icpConfigBo));
    }


}
