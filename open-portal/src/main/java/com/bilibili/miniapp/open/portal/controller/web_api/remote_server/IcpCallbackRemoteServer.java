package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.IcpBeiAnResultDto;
import com.bilibili.miniapp.api.dto.IcpConfigDto;
import com.bilibili.miniapp.api.service.MiniAppIcpCallbackRemoteService;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.IcpControllerMapper;
import com.bilibili.miniapp.open.service.biz.icp.IcpCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/icp/callback")
public class IcpCallbackRemoteServer extends AbstractController implements MiniAppIcpCallbackRemoteService {

    @Resource
    private IcpCallbackService icpCallbackService;

    @Override
    @PostMapping("/config")
    public Response<Void> handleIcpConfig(@RequestBody IcpConfigDto icpConfigDto) {
        // 处理基础代码
        icpCallbackService.handleIcpConfig(IcpControllerMapper.MAPPER.toIcpConfig(icpConfigDto));
        return Response.SUCCESS();
    }

    @Override
    @PostMapping("/result")
    public Response<Void> handleIcpBeiAnResult(@RequestBody IcpBeiAnResultDto icpBeiAnResultDto) {
        // 处理备案结果
        log.info("[handleIcpBeiAnResult] icpBeiAnResultDto={}", icpBeiAnResultDto);
        icpCallbackService.handleIcpBeiAnResult(IcpControllerMapper.MAPPER.toIcpBeiAnResult(icpBeiAnResultDto));
        return Response.SUCCESS();
    }
}
