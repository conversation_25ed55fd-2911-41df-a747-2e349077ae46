package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.*;
import com.bilibili.miniapp.api.service.MiniAppAppAdmissionRemoteService;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.common.enums.MiniAppModifyType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.MiniAppControllerMapper;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppAdmissionAuditDetailBo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppAdmissionAuditListBo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@RestController
@RequestMapping("/web_api/v1/platform/audit/mini_app/admission")
public class MiniAppAdmissionAuditRemoteServer extends AbstractController implements MiniAppAppAdmissionRemoteService {

    @Autowired
    private IMiniAppService appService;

    @GetMapping("/list")
    @Override
    public Response<PageResult<MiniAppAdmissionAuditListDto>> queryAdmissionAuditList(@RequestParam(value = "appId", required = false) String appId,
                                                                                      @RequestParam(value = "auditStatus", required = false) Integer auditStatus,
                                                                                      @RequestParam(value = "type", required = false) Integer type,
                                                                                      @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                                      @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {

        PageResult<MiniAppAdmissionAuditListBo> pageResult = appService.queryAdmissionAuditList(appId,
                AppCompanyAuditStatus.getByCode(auditStatus),
                MiniAppModifyType.getByCode(type),
                Page.valueOf(page, size));

        return Response.SUCCESS(
                new PageResult<>(pageResult.getTotal(),
                        pageResult.getRecords().stream()
                                .map(MiniAppControllerMapper.MAPPER::toDto)
                                .collect(Collectors.toList()))
        );
    }

    @GetMapping("/detail")
    @Override
    public Response<MiniAppAuditDetailDto> queryAdmissionAuditDetail(@RequestParam("id") Long id) {
        MiniAppAdmissionAuditDetailBo auditDetailBo = appService.queryAdmissionAuditDetail(id);

        return Response.SUCCESS(MiniAppControllerMapper.MAPPER.toDto(auditDetailBo));
    }

    @PostMapping("")
    @Override
    public Response<Void> passOrRejectAdmission(@RequestBody AdmissionAuditReqDto auditReqDto) {
        AssertUtil.isTrue(auditReqDto.getAuditStatus() != null, null, "审核状态不能为空");

        appService.passOrRejectAdmission(auditReqDto.getId(), AppCompanyAuditStatus.getByCode(auditReqDto.getAuditStatus()), auditReqDto.getFailReason());
        return Response.SUCCESS();
    }
}