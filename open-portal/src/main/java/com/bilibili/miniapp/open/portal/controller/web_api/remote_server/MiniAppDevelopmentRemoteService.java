package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.PublishTemplateDto;
import com.bilibili.miniapp.api.service.MiniAppDevelopmentService;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppDevelopmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/mini_app/development")
public class MiniAppDevelopmentRemoteService extends AbstractController implements MiniAppDevelopmentService {

    @Autowired
    private IMiniAppDevelopmentService miniAppPublishService;

    @Override
    @PostMapping("/template/publish")
    public Response<Void> publishTemplateMiniApp(@RequestBody PublishTemplateDto publishTemplate) {
        AssertUtil.isTrue(publishTemplate.getVersion() != null, ErrorCodeType.BAD_PARAMETER.getCode(), "版本号不能为空");
        miniAppPublishService.publishTemplateMiniApp(publishTemplate.getVersion());
        return Response.SUCCESS();
    }
}
