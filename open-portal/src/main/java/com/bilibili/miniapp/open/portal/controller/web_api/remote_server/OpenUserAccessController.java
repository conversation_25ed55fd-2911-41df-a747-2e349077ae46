package com.bilibili.miniapp.open.portal.controller.web_api.remote_server;

import com.bilibili.miniapp.api.dto.UserRecentAccessDto;
import com.bilibili.miniapp.api.service.MiniAppUserAccessRemoteService;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.UserRecentAccessMapper;
import com.bilibili.miniapp.open.service.biz.user.impl.UserAccessService;
import com.bilibili.miniapp.open.service.bo.user.UserRecentAccessDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/27
 **/

@Slf4j
@RestController
@RequestMapping("/web_api/v1/user/access")
public class OpenUserAccessController extends AbstractController implements MiniAppUserAccessRemoteService {

    @Resource
    private UserAccessService userAccessService;

    @GetMapping("/recent")
    public Response<UserRecentAccessDto> getRecentAccess(@RequestParam("mid") Long mid,
                                                         @RequestParam(value = "page_num", defaultValue = "1") Integer pageNum,
                                                         @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize) {
        List<UserRecentAccessDetail> userRecentAccesses = userAccessService.queryUserRecentAccess(mid, pageNum, pageSize);
        if (CollectionUtils.isEmpty(userRecentAccesses)) {
            return Response.SUCCESS(UserRecentAccessDto.builder()
                    .showMore(false)
                    .list(null)
                    .build());
        }

        return Response.SUCCESS(UserRecentAccessDto.builder()
                .list(userRecentAccesses.stream().map(UserRecentAccessMapper.MAPPER::toUserRecentAccessDto).collect(Collectors.toList()))
                .build());
    }
}
