package com.bilibili.miniapp.open.portal.filter;

import org.springframework.http.HttpMethod;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/12/6 22:50
 */
public class WebApiSecurityFilter implements Filter {
    private static final Pattern allowOriginPattern = Pattern.compile("(((http|https)://)?([\\w-_]*\\.)+bilibili\\.(co|com)(:\\d+)?)(/.*)?");

    private void corsResponse(HttpServletResponse response, String origin) {
        response.setHeader("Access-Control-Allow-Origin", origin);
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, HTTP-CONSUMER-KEY, HTTP-DEVICE-TYPE, HTTP-ACCESS-TOKEN, image_hash, Location, Cookie");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "X-My-Custom-Header, X-Another-Custom-Header, Date");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String origin = httpRequest.getHeader("Origin");
        if (origin == null) {
            origin = httpRequest.getHeader("Referer");
        }
        if (origin != null) {
            Matcher matcher = allowOriginPattern.matcher(origin);
            if (matcher.matches()) {
                this.corsResponse((HttpServletResponse) response, matcher.group(1));
            }
        }
        if (Objects.equals(HttpMethod.OPTIONS.name(), httpRequest.getMethod())) return;
        chain.doFilter(request, response);
    }
}
