package com.bilibili.miniapp.open.portal.grpc.server;

import com.bapis.ad.adp.miniapp.ApplyWithdrawReq;
import com.bapis.ad.adp.miniapp.ApplyWithdrawResp;
import com.bapis.ad.adp.miniapp.BatchIdReq;
import com.bapis.ad.adp.miniapp.BatchWithdrawBillResp;
import com.bapis.ad.adp.miniapp.IaaSettlementServiceGrpc.IaaSettlementServiceImplBase;
import com.bilibili.miniapp.open.portal.annotations.RpcServiceAspect;
import com.bilibili.miniapp.open.portal.grpc.server.model.GrpcModelConvertor;
import com.bilibili.miniapp.open.service.biz.settlement.IaaBillQueryService;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementService;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaAppType;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillRecordDTO;
import com.bilibili.miniapp.open.service.biz.settlement.vo.WithdrawApplyRequest;
import com.bilibili.miniapp.open.service.biz.settlement.vo.WithdrawApplyResult;
import io.grpc.stub.StreamObserver;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
@RPCService
@RpcServiceAspect
public class IaaSettlementGrpcService extends IaaSettlementServiceImplBase {

    @Resource
    private IaaSettlementService iaaSettlementService;

    @Resource
    private IaaBillQueryService iaaBillQueryService;


    @Override
    public void queryWithdrawBillByIds(BatchIdReq request, StreamObserver<BatchWithdrawBillResp> responseObserver) {

        List<IaaWithdrawBillRecordDTO> result = iaaBillQueryService.batchQueryById(
                request.getIdsList()
        );

        BatchWithdrawBillResp r = BatchWithdrawBillResp.newBuilder()
                .addAllWithdrawBills(
                        result.stream()
                                .map(GrpcModelConvertor.convertor::toGrpc)
                                .collect(Collectors.toList())
                )
                .build();

        responseObserver.onNext(r);

        // 切面已经onComplete

    }

    @Override
    public void applyWithdraw(ApplyWithdrawReq request, StreamObserver<ApplyWithdrawResp> responseObserver) {

        WithdrawApplyResult result = iaaSettlementService.applyWithdraw(
                new WithdrawApplyRequest()
                        .setAppId(request.getAppId())
                        .setAppType(IaaAppType.fromName(request.getAppType()))
                        .setApplyReason(request.getApplyReason())
                        .setBillIds(request.getBillIdsList())
                        .setInvoiceImgUrls(request.getInvoiceImgUrlsList())
        );

        ApplyWithdrawResp r = ApplyWithdrawResp.newBuilder()
                .addAllApplySuccessBillIds(result.getApplySuccessBillIds())
                .addAllSkipBillIds(result.getSkipBillIds())
                .build();
        responseObserver.onNext(r);

        // 切面已经onComplete
    }
}
