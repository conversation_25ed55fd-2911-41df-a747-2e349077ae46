package com.bilibili.miniapp.open.portal.grpc.server.model;

import com.bapis.ad.adp.miniapp.WithdrawBillInfo;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaAppType;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaCrmChargeBillStatus;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlementStatus;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaTrafficType;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBillStatus;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaCrmChargeBillDTO;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaSettlementDTO;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillDTO;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillRecordDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
@Mapper
public interface GrpcModelConvertor {


    GrpcModelConvertor convertor = Mappers.getMapper(GrpcModelConvertor.class);


    default WithdrawBillInfo toGrpc(IaaWithdrawBillRecordDTO dto) {

        return WithdrawBillInfo.newBuilder()
                .setBillId(dto.getBillId())
                .setBill(toGrpc(dto.getBill()))
                .addAllSettlementDetails(dto.getSettlementDetails()
                        .stream().map(this::toGrpc).collect(Collectors.toList()))
                .addAllCrmChargeBillDetails(dto.getCrmChargeBillDetails()
                        .stream().map(this::toGrpc).collect(Collectors.toList()))
                .build();

    }


    default com.bapis.ad.adp.miniapp.IaaWithdrawBillDTO toGrpc(IaaWithdrawBillDTO dto){

        return com.bapis.ad.adp.miniapp.IaaWithdrawBillDTO.newBuilder()
                .setId(dto.getId())
                .setTitle(dto.getTitle())
                .setBillStatus(dto.getBillStatus().name())
                .setFailReason(dto.getFailReason())
                .setAppType(dto.getAppType().name())
                .setAppId(dto.getAppId())
                .setBillStartTime(dto.getBillStartTime().getTime())
                .setBillEndTime(dto.getBillEndTime().getTime())
                .setWithdrawDate(dto.getWithdrawDate())
                .setLatestSettleTime(dto.getLatestSettleTime().getTime())
                .setWithdrawApplyTime(dto.getWithdrawApplyTime().getTime())
                .setWithdrawArrivalTime(dto.getWithdrawArrivalTime().getTime())
                .setIncomeAmt(dto.getIncomeAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setSettleTimes(dto.getSettleTimes())
                .setWithdrawAmt(dto.getWithdrawAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setCrmChargeAmt(dto.getCrmChargeAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setIncomeNaturalPartAmt(dto.getIncomeNaturalPartAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setIncomeBusinessPartAmt(dto.getIncomeBusinessPartAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setWithdrawNaturalPartAmt(dto.getWithdrawNaturalPartAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setWithdrawBusinessPartAmt(
                        dto.getWithdrawBusinessPartAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setBusinessEntityName(dto.getBusinessEntityName())
                .setWithdrawApplyAmt(dto.getWithdrawApplyAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setInvoiceImgUrl(dto.getInvoiceImgUrl())
                .setAccrualId(dto.getAccrualId())
                .setAccrualExtra(dto.getAccrualExtra())
                .setExpenseId(dto.getExpenseId())
                .setExpenseExtra(dto.getExpenseExtra())
                .setExpenseCode(dto.getExpenseCode())
                .setExpenseMessage(dto.getExpenseMessage())
                .setExtra(dto.getExtra())
                .setCtime(dto.getCtime().getTime())
                .setMtime(dto.getMtime().getTime())
                .build();



    }


    default com.bapis.ad.adp.miniapp.IaaSettlementDTO toGrpc(IaaSettlementDTO dto) {

        return com.bapis.ad.adp.miniapp.IaaSettlementDTO.newBuilder()
                .setId(dto.getId())
                .setSettleKey(dto.getSettleKey())
                .setLogdate(dto.getLogdate())
                .setTrafficType(dto.getTrafficType().name())
                .setAppType(dto.getAppType().name())
                .setAppId(dto.getAppId())
                .setSettleTime(dto.getSettleTime().getTime())
                .setSettleStatus(dto.getSettleStatus().name())
                .setSettleReason(dto.getSettleReason())
                .setWithdrawBillId(dto.getWithdrawBillId())
                .setIncomeAmt(dto.getIncomeAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setWithdrawAmt(dto.getWithdrawAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setCrmChargeAmt(dto.getCrmChargeAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setExtra(dto.getExtra())
                .setCtime(dto.getCtime().getTime())
                .setMtime(dto.getMtime().getTime())
                .setDeleted(dto.getDeleted())
                .build();

    }


    default com.bapis.ad.adp.miniapp.IaaCrmChargeBillDTO toGrpc(IaaCrmChargeBillDTO dto) {

        return com.bapis.ad.adp.miniapp.IaaCrmChargeBillDTO.newBuilder()
                .setId(dto.getId())
                .setAppType(dto.getAppType().name())
                .setAppId(dto.getAppId())
                .setTrafficType(dto.getTrafficType().name())
                .setLogdate(dto.getLogdate())
                .setChargeAmt(dto.getChargeAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setIncomeAmt(dto.getIncomeAmt().setScale(2, RoundingMode.HALF_UP).toString())
                .setSettlementId(dto.getSettlementId())
                .setWithdrawBillId(dto.getWithdrawBillId())
                .setChargeTime(dto.getChargeTime().getTime())
                .setBillStatus(dto.getBillStatus().name())
                .setReason(dto.getReason())
                .setCtime(dto.getCtime().getTime())
                .setMtime(dto.getMtime().getTime())
                .setDeleted(dto.getDeleted())
                .setExtra(dto.getExtra())
                .build();

    }
}
