package com.bilibili.miniapp.open.portal.interceptor;

import com.bilibili.miniapp.open.common.util.GsonUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.common.entity.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @date 2024/9/24 14:39
 */
public class AbstractInterceptor implements AsyncHandlerInterceptor {

    protected boolean failedFinish(Response<?> res, Integer httpCode, @NotNull HttpServletResponse response) throws Exception {
        response.setStatus(NumberUtil.isPositive(httpCode) ? httpCode : HttpServletResponse.SC_OK);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        writer.append(GsonUtil.toJson(res));
        writer.close();
        return false;
    }
}
