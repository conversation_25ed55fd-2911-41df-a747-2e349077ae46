package com.bilibili.miniapp.open.portal.interceptor;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.RequestAttributeKey;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MiniAppValidation;
import com.bilibili.miniapp.open.portal.vo.common.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/01/02 17:15
 */
@Slf4j
public class ContextAppenderInterceptor extends AbstractInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        Context context = Context.builder()
                .miniAppId(request.getHeader(MiniAppHeader.MINI_APP_APPLET_ID))
                .miniAppKey(request.getHeader(MiniAppHeader.MINI_APP_KEY))
                .miniAppUserAgent(request.getHeader(MiniAppHeader.MINI_APP_USER_AGENT))
                .miniAppPlatform(parseParam(request, MiniAppHeader.MINI_APP_PLATFORM))
                .miniAppVersion(Optional.ofNullable(parseParam(request, MiniAppHeader.MINI_APP_BUILD))
                        .filter(NumberUtils::isDigits).map(Long::parseLong).orElse(0L))
                .accessKey(request.getParameter(PlatformParameter.ACCESS_KEY))
                .sign(request.getParameter(PlatformParameter.SIGN))
                .ts(Optional.ofNullable(request.getParameter(PlatformParameter.TS))
                        .filter(NumberUtils::isDigits).map(Long::parseLong).orElse(0L))
                .buvid(getBuvid(request))
                .isTest(Optional.ofNullable(request.getHeader(MiniAppHeader.MINI_APP_TEST))
                        .map(org.apache.commons.lang3.StringUtils::isNotBlank)
                        .orElse(false))
                .build();

        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            MiniAppValidation miniAppValidation = handlerMethod.getMethodAnnotation(MiniAppValidation.class);
            if (Objects.isNull(miniAppValidation)) {
                miniAppValidation = handlerMethod.getBean().getClass().getAnnotation(MiniAppValidation.class);
            }
            if (Objects.nonNull(miniAppValidation) && !context.isValidMiniAppCall()) {
                return failedFinish(Response.FAIL("非法小程序"), null, response);
            }
        }

        request.setAttribute(RequestAttributeKey.ATTRIBUTE_CONTEXT_KEY, context);
        log.info("[ContextAppenderInterceptor] request context={}", JSON.toJSONString(context));
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) throws Exception {
        request.removeAttribute(RequestAttributeKey.ATTRIBUTE_CONTEXT_KEY);
    }

    private String getBuvid(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (Objects.isNull(cookies)) {
            return "";
        }
        return Arrays.stream(cookies)
                .filter(ck -> ck.getName().contains(MiniAppCookie.MINI_APP_BUVID)
                        || MiniAppCookie.MINI_APP_BUVID.equalsIgnoreCase(ck.getName()))//小程序传的是buvid
                .map(Cookie::getValue)
                .filter(StringUtils::hasLength)
                .findFirst()
                .orElse("");
    }

    //优先从header中取，没有的话query中取
    private String parseParam(HttpServletRequest request, String key) {
        String header = request.getHeader(key);
        if (StringUtils.hasText(header)) {
            return header;
        }
        return request.getParameter(key);
    }

}
