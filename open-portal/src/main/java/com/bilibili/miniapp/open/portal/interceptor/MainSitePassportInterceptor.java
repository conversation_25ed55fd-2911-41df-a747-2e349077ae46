package com.bilibili.miniapp.open.portal.interceptor;

import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.common.entity.RequestAttributeKey;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.bo.up_info.CookieInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.client.MainSitePassportServiceGrpcClient;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;

/**
 * 放置在{@link ContextAppenderInterceptor}之后
 *
 * <AUTHOR>
 * @date 2024/12/09 22:23
 * @see ContextAppenderInterceptor
 */
@Component
public class MainSitePassportInterceptor extends AbstractInterceptor {
    @Autowired
    private MainSitePassportServiceGrpcClient mainSitePassportServiceGrpcClient;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                             @NotNull Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        //优先级：方法 > 类
        //优先获取方法上的注解
        MainSiteLoginValidation mainSiteLoginValidation = handlerMethod.getMethodAnnotation(MainSiteLoginValidation.class);
        if (Objects.isNull(mainSiteLoginValidation)) {
            //如果方法上没有注解，查看对应类上是否存在注解
            mainSiteLoginValidation = handlerMethod.getBean().getClass().getAnnotation(MainSiteLoginValidation.class);
        }
        if (Objects.isNull(mainSiteLoginValidation) || !mainSiteLoginValidation.required()) {
            return true;
        }
        // 先通过access_key验证
        String accessKey = request.getParameter("access_key");
        CookieInfoBo cookieInfo;
        if (accessKey != null) {
            cookieInfo = mainSitePassportServiceGrpcClient.identifyToken(accessKey);
        } else {
            cookieInfo = mainSitePassportServiceGrpcClient.identify(request.getHeader("cookie"));
        }
        if (!(cookieInfo.isLoggedIn() && NumberUtil.isPositive(cookieInfo.getMid()))) {
            return failedFinish(Response.FAIL("Unauthenticated"), HttpServletResponse.SC_UNAUTHORIZED, response);
        }
        Context context = (Context) request.getAttribute(RequestAttributeKey.ATTRIBUTE_CONTEXT_KEY);
        context.setMid(cookieInfo.getMid());
        return true;
    }

    @Override
    public void postHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                           @NotNull Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, @NotNull HttpServletResponse response,
                                @NotNull Object handler, Exception ex) throws Exception {
    }
}
