package com.bilibili.miniapp.open.portal.interceptor;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.entity.SignParameterEntity;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.SignUtil;
import com.bilibili.miniapp.open.portal.annotations.SignValidation;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/01/06 12:09
 */
@Slf4j
@Component
@Aspect
public class SignInterceptor {
    @Autowired
    private IOpenAccessService openAccessService;

    @Pointcut("@annotation(com.bilibili.miniapp.open.portal.annotations.SignValidation)")
    private void pointcut() {
    }

    @Before("pointcut()")
    public void process(JoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        SignValidation signValidation = method.getAnnotation(SignValidation.class);
        if (Objects.isNull(signValidation)) {
            //pointcut决定了一定不为null，但是为了安全起见（后期的可能bug导致的问题），在此做一次判断
            return;
        }
        Object bodyParam = null;
        Map<String, Object> queryParam = Maps.newHashMap();
        Context context = null;
        Object[] args = joinPoint.getArgs();
        Parameter[] methodParameters = method.getParameters();
        for (int i = 0; i < methodParameters.length; i++) {
            Parameter parameter = methodParameters[i];
            Object arg = args[i];
            if (Objects.equals(parameter.getType(), Context.class)) {
                context = (Context) arg;
                //即使context参数有Sign注解也会被忽略
                continue;
            }

            Sign sign = parameter.getAnnotation(Sign.class);
            if (Objects.isNull(sign)) continue;
            if (parameter.isAnnotationPresent(RequestBody.class)) {
                bodyParam = arg;
                continue;
            }

            RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
            if (Objects.nonNull(requestParam)) {
                String key = parameter.getName();//方法参数名：默认签名key
                if (StringUtils.hasText(requestParam.value())) {
                    //如果指定了参数名，则以参数名为签名key
                    key = requestParam.value();
                }
                if (StringUtils.hasText(sign.key())) {
                    //如果自定义了签名key则高优
                    key = sign.key();
                }
                queryParam.put(key, arg);
            }
        }

        AssertUtil.notNull(context, ErrorCodeType.SYSTEM_ERROR.getCode(), "接口声明：签名配置错误，缺少Context");

        if (context.isTest()) {
            return;
        }

        AssertUtil.isTrue(context.isValidOpenApiCall(), ErrorCodeType.BAD_SIGN.getCode(), "缺少签名参数");

        String accessToken;
        long ts = context.getTs();
        //1、理论上不会出现ts > currentTimeMillis的情景，但是如果时钟diff较大（bug引起）或者恶意传了未来的时间，还是会出现的，因此按照绝对值校验
        //2、以目前数据进制情况看，[Math.abs(System.currentTimeMillis() - ts) <= 10000] 包含了对[isPositive(ts)]判断语义，因为ts为负数时，
        //[System.currentTimeMillis() - ts]肯定大于10000，但是这种情况下[System.currentTimeMillis() - ts]本身存在溢出的风险，那么它就有可能小于10000，
        //因此对context#[isPositive(ts)]单独的断言并不是多余的（只是目前看几乎不太可能溢出）
        AssertUtil.isTrue(Math.abs(System.currentTimeMillis() - ts) <= 10000000, ErrorCodeType.BAD_SIGN.getCode(), "签名已过期");
        AssertUtil.hasText(accessToken = openAccessService.getAccessToken(context.getAccessKey()), ErrorCodeType.BAD_SIGN.getCode(), "unauthorized");

        //添加ts签名
        SignParameterEntity signParameterEntity = SignParameterEntity.builder()
                .param1(queryParam)
                .param2(bodyParam)
                .ts(ts)
                .build();
        String signed = SignUtil.sign(signParameterEntity, accessToken);
        AssertUtil.isTrue(Objects.equals(signed, context.getSign()), ErrorCodeType.BAD_SIGN);
    }
}
