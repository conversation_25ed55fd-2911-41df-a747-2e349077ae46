package com.bilibili.miniapp.open.portal.interceptor;

import com.bilibili.miniapp.open.common.util.TraceUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2024/9/4 21:45
 */
public class TraceInterceptor extends AbstractInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        String traceId = request.getHeader(TraceUtil.TRACE_ID);
        if (traceId == null || traceId.isEmpty()) {
            traceId = TraceUtil.genTraceId();
        }
        MDC.put(TraceUtil.TRACE_ID, traceId);
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) throws Exception {
        MDC.remove(TraceUtil.TRACE_ID);
    }
}
