package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.access.OpenAccessResVo;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccessControllerMapper {
    AccessControllerMapper MAPPER = Mappers.getMapper(AccessControllerMapper.class);

    OpenAccessResVo toVo(OpenAccessBo accessBo);
}
