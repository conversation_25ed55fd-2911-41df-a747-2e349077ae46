package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.account.AccountInfoResVo;
import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AccountMapper {
    AccountMapper MAPPER = Mappers.getMapper(AccountMapper.class);

    AccountInfoResVo toVo(AccountInfoBo accountInfoBo);

}
