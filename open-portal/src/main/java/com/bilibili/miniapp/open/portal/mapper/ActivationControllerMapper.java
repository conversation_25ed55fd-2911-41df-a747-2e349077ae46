package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.client.activation.MiniAppActivationReportVo;
import com.bilibili.miniapp.open.service.databus.entity.MiniAppActivationMsg;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 激活上报对象映射器
 */
@Mapper
public interface ActivationControllerMapper {
    
    ActivationControllerMapper MAPPER = Mappers.getMapper(ActivationControllerMapper.class);

    /**
     * 将VO转换为消息对象
     * 
     * @param reportVo 上报VO
     * @return 消息对象
     */
    MiniAppActivationMsg toMsg(MiniAppActivationReportVo reportVo);
}
