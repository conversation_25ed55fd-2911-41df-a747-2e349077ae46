package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.api.dto.AppletInfoReqDto;
import com.bilibili.miniapp.api.dto.AppletInfoRespDto;
import com.bilibili.miniapp.api.dto.applet.season.AppletAuthor;
import com.bilibili.miniapp.api.dto.applet.season.AppletSeasonDto;
import com.bilibili.miniapp.api.dto.applet.season.EpisodeDto;
import com.bilibili.miniapp.api.dto.applet.season.SectionDto;
import com.bilibili.miniapp.open.service.bo.applet.AppletInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortQueryParam;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SectionBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/
@Mapper
public interface AppletMapper {

    AppletMapper MAPPER = Mappers.getMapper(AppletMapper.class);

    AppletShortQueryParam toQueryParam(AppletInfoReqDto reqDto);

    AppletInfoRespDto toRespDto(AppletInfoBo bo);

    AppletSeasonDto toSeasonDto(SeasonBo bo);

    SectionDto toSectionDto(SectionBo bo);

    EpisodeDto toEpisodeDto(EpisodeBo bo);

    AppletAuthor toAuthorDto(UserInfoBo bo);
}
