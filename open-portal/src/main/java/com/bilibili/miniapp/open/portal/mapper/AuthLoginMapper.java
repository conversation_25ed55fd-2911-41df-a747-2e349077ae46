package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.auth.PhoneExchangeRespVo;
import com.bilibili.miniapp.open.portal.vo.auth.PreAuthRespVo;
import com.bilibili.miniapp.open.service.bo.auth.EncryptedEntity;
import com.bilibili.miniapp.open.service.bo.auth.MiniAppAuthBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/17
 **/

@Mapper
public interface AuthLoginMapper {

    AuthLoginMapper MAPPER = Mappers.getMapper(AuthLoginMapper.class);

    PreAuthRespVo toPreAuthRespVo(MiniAppAuthBo bo);


    @Mapping(target = "encryptedPhone", source = "encryptedData")
    PhoneExchangeRespVo toPhoneExchangeRespVo(EncryptedEntity result);
}