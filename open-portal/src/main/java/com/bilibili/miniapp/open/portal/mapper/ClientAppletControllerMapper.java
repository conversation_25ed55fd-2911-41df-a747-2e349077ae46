package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.client.applet.ClientAppletInfoVo;
import com.bilibili.miniapp.open.service.bo.client.applet.ClientAppletInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ClientAppletControllerMapper {
    ClientAppletControllerMapper MAPPER = Mappers.getMapper(ClientAppletControllerMapper.class);


    ClientAppletInfoVo toVo(ClientAppletInfoBo seasonBo);


}
