package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.client.season.ClientSeasonVo;
import com.bilibili.miniapp.open.portal.vo.client.season.ClientUserSeasonVo;
import com.bilibili.miniapp.open.portal.vo.season.PlatformSeasonInfoVo;
import com.bilibili.miniapp.open.service.bo.client.season.ClientUserSeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ClientSeasonControllerMapper {
    ClientSeasonControllerMapper MAPPER = Mappers.getMapper(ClientSeasonControllerMapper.class);


    ClientSeasonVo toVo(SeasonBo seasonBo);


    ClientUserSeasonVo toVo(ClientUserSeasonBo clientUserSeasonBo);
}
