package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.comment.BatchGetReplyBlueLinkInfoReqVo;
import com.bilibili.miniapp.open.portal.vo.comment.BatchGetReplyBlueLinkInfoRespVo;
import com.bilibili.miniapp.open.service.bo.comment.BatchGetReplyBlueLinkInfoReqBo;
import com.bilibili.miniapp.open.service.bo.comment.BatchGetReplyBlueLinkInfoRespBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CommentMapper {

    CommentMapper MAPPER = Mappers.getMapper(CommentMapper.class);

    BatchGetReplyBlueLinkInfoReqBo voToBo(BatchGetReplyBlueLinkInfoReqVo vo);

    BatchGetReplyBlueLinkInfoRespVo boToVo( BatchGetReplyBlueLinkInfoRespBo bo);
}