package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.api.dto.CompanyAdmissionAuditInfoDto;
import com.bilibili.miniapp.api.dto.CompanyAdmissionAuditListDto;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditListBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CompanyAdmissionAuditMapper {

    CompanyAdmissionAuditMapper MAPPER = Mappers.getMapper(CompanyAdmissionAuditMapper.class);

    CompanyAdmissionAuditListDto toDto(CompanyAdmissionAuditListBo bo);

    @Mapping(target = ".", source = "admissionInfo")
    CompanyAdmissionAuditInfoDto toDto(CompanyAdmissionAuditInfoBo bo);

}