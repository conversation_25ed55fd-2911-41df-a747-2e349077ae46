package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.company.CompanyDetailResVo;
import com.bilibili.miniapp.open.portal.vo.company.CompanyInfoVo;
import com.bilibili.miniapp.open.service.bo.company.CompanyInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CompanyControllerMapper {
    CompanyControllerMapper MAPPER = Mappers.getMapper(CompanyControllerMapper.class);

    CompanyInfoBo toBo(CompanyInfoVo companyInfoVo, long mid);

    CompanyDetailResVo toVo(CompanyDetailBo detailBo);
}
