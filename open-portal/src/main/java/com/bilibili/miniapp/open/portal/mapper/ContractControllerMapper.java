package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.contract.ContractSettlementDetailRespVo;
import com.bilibili.miniapp.open.portal.vo.contract.ContractSignUrlRespVo;
import com.bilibili.miniapp.open.portal.vo.contract.SettlementContractVo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSettlementDetailRespBo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSignUrlBo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 合同控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface ContractControllerMapper {

    ContractControllerMapper MAPPER = Mappers.getMapper(ContractControllerMapper.class);

    /**
     * 创建请求VO转BO
     */
    SettlementContractBo voToBo(SettlementContractVo vo);

    /**
     * 详情响应BO转VO
     */
    @Mapping(target = "signatoryAddress", source = "signatoryAddress")
    ContractSettlementDetailRespVo boToVo(ContractSettlementDetailRespBo bo);

    /**
     * 签署链接响应BO转VO
     */
    ContractSignUrlRespVo boToVo(ContractSignUrlBo bo);
}
