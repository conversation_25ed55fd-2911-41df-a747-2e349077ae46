package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.data.EcpmEventLogVo;
import com.bilibili.miniapp.open.service.bo.data.EcpmEventLogBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/4/30
 */
@Mapper
public interface DataMapper {

    DataMapper MAPPER = Mappers.getMapper(DataMapper.class);

    DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Mapping(target = "eventType", source = "eventName")
    @Mapping(target = "id", source = "uniqueKey")
    @Mapping(target = "eventTime", expression = "java(FORMATTER.format(new java.sql.Timestamp(bo.getEventTime()).toLocalDateTime()))")
    EcpmEventLogVo toVo(EcpmEventLogBo bo);

}
