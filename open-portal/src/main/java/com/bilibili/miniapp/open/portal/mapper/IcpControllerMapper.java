package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.api.dto.*;
import com.bilibili.miniapp.open.portal.vo.icp.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.*;
import com.bilibili.miniapp.open.service.bo.icp.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/

@Mapper(imports = {java.sql.Timestamp.class})
public interface IcpControllerMapper {

    IcpControllerMapper MAPPER = Mappers.getMapper(IcpControllerMapper.class);

    IcpConfig toIcpConfig(IcpConfigDto icpConfig);

    IcpConfig.IcpDocumentType toIcpDocumentType(IcpConfigDto.IcpDocumentTypeDto icpDocumentType);

    IcpConfig.IcpAreaCode toIcpAreaCode(IcpConfigDto.IcpAreaCodeDto icpAreaCode);

    IcpConfig.IcpOrgType toIcpOrgType(IcpConfigDto.IcpOrgTypeDto icpOrgType);

    IcpConfig.IcpAppServiceType toIcpAppServiceType(IcpConfigDto.IcpAppServiceTypeDto icpAppServiceType);

    IcpBeiAnResult toIcpBeiAnResult(IcpBeiAnResultDto icpBeiAnResultDto);

    IcpBeiAnResult.BeiAnStatus toIcpBeiAnStatus(IcpBeiAnResultDto.BeiAnStatusDto icpBeiAnStatusDto);

    IcpBeiAnResult.AuditStatus toIcpAuditStatus(IcpBeiAnResultDto.AuditStatusDto icpAuditStatusDto);

    @Mapping(source = "flowStatus", target = "status")
    @Mapping(source = "govAuditDescription", target = "govFailReason")
    @Mapping(source = "platformAudit.failReasons", target = "platformFailReason")
    @Mapping(target = "auditPassTime", expression = "java(state.getGovAuditTime() == null ? null : state.getGovAuditTime().getTime())")
    IcpStatusRespVo toIcpStatusRespVo(IcpReportState state);

    @Mapping(source = "flowStatus", target = "status")
    @Mapping(source = "govAuditDescription", target = "govFailReason")
    @Mapping(source = "platformAudit.failReasons", target = "platformFailReason")
    @Mapping(target = "auditPassTime", expression = "java(state.getGovAuditTime() == null ? null : state.getGovAuditTime().getTime())")
    IcpReportStatusDto toIcpReportStatusDto(IcpReportState state);

    @Mapping(source = "field", target = "fieldName")
    @Mapping(source = "obj", target = "fieldObj")
    IcpPlatformAuditDetail toIcpPlatformAuditDetail(IcpPlatformAudit.IcpAuditResult result);

    @Mapping(source = "field", target = "fieldName")
    @Mapping(source = "obj", target = "fieldObj")
    IcpReportStatusDto.IcpPlatformAuditDetailDto toIcpPlatformAuditDetailDto(IcpPlatformAudit.IcpAuditResult result);

    IcpReportInfo toIcpReportInfo(IcpReportVo vo);

    @Mapping(target = "fzrCardBegin", expression = "java(companyVo.getFzrCardBegin() == null ? null : new Timestamp(companyVo.getFzrCardBegin()))")
    @Mapping(target = "fzrCardEnd", expression = "java(companyVo.getFzrCardEnd() == null ? null : new Timestamp(companyVo.getFzrCardEnd()))")
    IcpCompany toIcpCompany(IcpReportVo.IcpCompanyVo companyVo);

    @Mapping(target = "fzrCardBegin", expression = "java(appVo.getFzrCardBegin() == null ? null : new Timestamp(appVo.getFzrCardBegin()))")
    @Mapping(target = "fzrCardEnd", expression = "java(appVo.getFzrCardEnd() == null ? null : new Timestamp(appVo.getFzrCardEnd()))")
    IcpApp toIcpApp(IcpReportVo.IcpAppVo appVo);

    IcpAttachment toIcpAttachment(IcpReportVo.IcpAttachmentVo attachmentVo);

    IcpConfigVo toIcpConfigVo(IcpConfigBo config);

    IcpConfigTreeVo toIcpConfigTreeVo(IcpConfigTree configTree);

    IcpRemoteConfigDto toIcpConfigDto(IcpConfigBo config);

    IcpConfigTreeDto toIcpConfigTreeDto(IcpConfigTree configTree);

    IcpReportVo toIcpReportReqVo(IcpReportInfo icpReportInfo);

    @Mapping(target = "fzrCardBegin", expression = "java(company.getFzrCardBegin() == null ? null : company.getFzrCardBegin().getTime())")
    @Mapping(target = "fzrCardEnd", expression = "java(company.getFzrCardEnd() == null ? null : company.getFzrCardEnd().getTime())")
    IcpReportVo.IcpCompanyVo toIcpCompanyVo(IcpCompany company);

    @Mapping(target = "fzrCardBegin", expression = "java(app.getFzrCardBegin() == null ? null : app.getFzrCardBegin().getTime())")
    @Mapping(target = "fzrCardEnd", expression = "java(app.getFzrCardEnd() == null ? null : app.getFzrCardEnd().getTime())")
    IcpReportVo.IcpAppVo toIcpAppVo(IcpApp app);

    IcpReportVo.IcpAttachmentVo toIcpAttachmentVo(IcpAttachment attachment);

    IcpReportDto toIcpReportDto(IcpReportInfo icpReportInfo);

    @Mapping(target = "fzrCardBegin", expression = "java(company.getFzrCardBegin() == null ? null : company.getFzrCardBegin().getTime())")
    @Mapping(target = "fzrCardEnd", expression = "java(company.getFzrCardEnd() == null ? null : company.getFzrCardEnd().getTime())")
    IcpReportDto.IcpCompanyDto toIcpCompanyDto(IcpCompany company);

    @Mapping(target = "fzrCardBegin", expression = "java(app.getFzrCardBegin() == null ? null : app.getFzrCardBegin().getTime())")
    @Mapping(target = "fzrCardEnd", expression = "java(app.getFzrCardEnd() == null ? null : app.getFzrCardEnd().getTime())")
    IcpReportDto.IcpAppDto toIcpAppDto(IcpApp app);

    IcpReportDto.IcpAttachmentDto toIcpAttachmentDto(IcpAttachment attachment);

    @Mapping(source = "failReason", target = "failReasons")
    @Mapping(source = "auditResult", target = "auditStatus")
    IcpPlatformAudit toIcpPlatformAudit(IcpPlatformAuditDto auditDto);

    @Mapping(source = "fieldObj", target = "obj")
    @Mapping(source = "fieldName", target = "field")
    @Mapping(source = "reason", target = "reason")
    IcpPlatformAudit.IcpAuditResult toIcpPlatformAuditResult(IcpPlatformAuditDto.IcpPlatformAuditDetailDto auditResultDto);

    @Mapping(target = "submitTime", expression = "java(auditListBo.getSubmitTime() == null ? null : auditListBo.getSubmitTime().getTime())")
    @Mapping(target = "auditTime", expression = "java(auditListBo.getAuditTime() == null ? null : auditListBo.getAuditTime().getTime())")
    IcpPlatformAuditListDto toIcpPlatformAuditListDto(IcpPlatformAuditListBo auditListBo);


    IcpPlatformAuditQueryBo toIcpPlatformAuditQueryBo(IcpPlatformAuditQueryDto queryDto);

    IdentityAuthRespVo toIdentityAuthRespVo(IdentityAuthResp identityAuthResp);

    IcpIdentityResultVo toIdentityAuthRespVo(IdentityResult identityResult);
}
