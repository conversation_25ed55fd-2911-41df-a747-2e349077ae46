package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.miniapp_config.MiniAppCustomLinkVo;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Mapper
public interface MiniAppConfigControllerMapper {
    MiniAppConfigControllerMapper MAPPER = Mappers.getMapper(MiniAppConfigControllerMapper.class);

    MiniAppCustomLinkVo toVo(MiniAppCustomLinkBo miniAppCustomLinkBo);

    MiniAppCustomLinkBo toBo(MiniAppCustomLinkVo miniAppCustomLinkVo);

}