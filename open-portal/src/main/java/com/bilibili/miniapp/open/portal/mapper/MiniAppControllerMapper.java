package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.api.dto.MiniAppAdmissionAuditListDto;
import com.bilibili.miniapp.api.dto.MiniAppAuditDetailDto;
import com.bilibili.miniapp.open.portal.vo.miniapp.MiniAppCategoryTreeVo;
import com.bilibili.miniapp.open.portal.vo.miniapp.MiniAppDetailVo;
import com.bilibili.miniapp.open.portal.vo.miniapp.MiniAppListVo;
import com.bilibili.miniapp.open.portal.vo.miniapp.MiniAppVo;
import com.bilibili.miniapp.open.service.bo.miniapp.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Mapper
public interface MiniAppControllerMapper {
    MiniAppControllerMapper MAPPER = Mappers.getMapper(MiniAppControllerMapper.class);

    MiniAppBo toBo(MiniAppVo vo);

    MiniAppListVo toVo(MiniAppListBo miniAppListBo);

    MiniAppDetailVo toVo(MiniAppDetailBo miniAppDetailBo);


    MiniAppAdmissionAuditListDto toDto(MiniAppAdmissionAuditListBo auditListBo);

    MiniAppAuditDetailDto toDto(MiniAppAdmissionAuditDetailBo auditDetailBo);

    MiniAppCategoryTreeVo toVo(MiniAppCategoryTree miniAppCategoryTree);
}