package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.common.enums.OgvSectionType;
import com.bilibili.miniapp.open.portal.vo.ogv.EpisodeVo;
import com.bilibili.miniapp.open.portal.vo.ogv.SeasonVo;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SectionBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/1 20:18
 */
@Mapper(imports = {StringUtils.class})
public interface OgvSeasonMapper {
    OgvSeasonMapper MAPPER = Mappers.getMapper(OgvSeasonMapper.class);

    @Mapping(target = "episodes", expression = "java(toEpisodeVoFromSection(bo))")
    SeasonVo toSeasonVo(SeasonBo bo);

    @Mapping(target = "seasonId", source = "episode.seasonId")
    //OGV侧没有提供EP维度的封面，但是端上会使用EP维度的封面作为预加载阶段过渡效果，因此使用Season维度的封面下发给小程序
    //为空则兜底图
    @Mapping(target = "cover", expression = "java(getEpCover(episode, season))")
    //OGV侧没有提供EP维度的标题（目前只有长标题），但是端上会使用EP维度的title | long_title 作为完整的标题，因此使用Season维度的标题和EP维度的集索引拼接下发给小程序
    @Mapping(target = "title", expression = "java(String.format(\"%s 第%d集\", season.getTitle(), episode.getOrd()))")
    EpisodeVo toEpisodeVo(EpisodeBo episode, SeasonBo season);

    default String getEpCover(EpisodeBo episode, SeasonBo season) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(episode.getCover())) {
            return episode.getCover();
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(season.getCover())) {
            return season.getCover();
        }
        return "https://i0.hdslb.com/bfs/mall/miniapp/202501/a21109e226e8235888f6b411643cb1df.png";
    }

    default List<EpisodeVo> toEpisodeVoFromSection(SeasonBo season) {
        List<SectionBo> sectionBoList = season.getSections();
        if (CollectionUtils.isEmpty(sectionBoList)) {
            return Collections.emptyList();
        }
        //背景：
        //1、分节没有渠道属性，因此即使查询season时传了小程序的渠道，OGV接口虽然返回了只在小程序渠道分发的剧，但仍然返回了所有的分节，包括可能存在的多正片分节
        //2、根据分节查询EP的接口，OGV过滤了小程序渠道不返回其他渠道的EP，此时对应的EP List为空
        //因此：
        //过滤出EP非空的正片分节
        return sectionBoList.stream()
                .filter(sectionBo -> Objects.equals(sectionBo.getSectionType(), OgvSectionType.FORMAL.getCode())
                        && !CollectionUtils.isEmpty(sectionBo.getEpisodes()))
                .findFirst()
                .map(SectionBo::getEpisodes)
                .stream()
                .flatMap(Collection::stream)
                .map(ep -> toEpisodeVo(ep, season))
                .sorted(Comparator.comparing(EpisodeVo::getOrd))
                .collect(Collectors.toList());
    }
}
