package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.common.mapper.CommonMapper;
import com.bilibili.miniapp.open.portal.vo.order.OrderCreateReqVo;
import com.bilibili.miniapp.open.portal.vo.order.OrderDetailResVo;
import com.bilibili.miniapp.open.portal.vo.order.OrderRefundReqVo;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.bo.order.OrderCreateReq;
import com.bilibili.miniapp.open.service.bo.order.RefundReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/1/16 15:53
 */
@Mapper(uses = CommonMapper.class)
public interface OrderMapper {
    OrderMapper MAPPER = Mappers.getMapper(OrderMapper.class);

    OrderCreateReq toOrderCreateReq(OrderCreateReqVo vo);

    @Mapping(target = "orderId", expression = "java(Long.parseLong(vo.getOrderId()))")
    RefundReq toRefundReq(OrderRefundReqVo vo);

    @Mapping(target = "extraData", source = "extra.devExtraData")
    OrderDetailResVo toOrderDetailResVo(Order order);
}
