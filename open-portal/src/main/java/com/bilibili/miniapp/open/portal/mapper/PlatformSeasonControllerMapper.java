package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.season.PlatformSeasonInfoVo;
import com.bilibili.miniapp.open.service.bo.ogv.PlatformSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PlatformSeasonControllerMapper {
    PlatformSeasonControllerMapper MAPPER = Mappers.getMapper(PlatformSeasonControllerMapper.class);

    @Mapping(target = "alreadyPublished", ignore = true)
    PlatformSeasonInfoVo toVo(SeasonBo seasonBo);

    PlatformSeasonInfoVo toVo(PlatformSeasonInfoBo platformSeasonInfoBo);
}
