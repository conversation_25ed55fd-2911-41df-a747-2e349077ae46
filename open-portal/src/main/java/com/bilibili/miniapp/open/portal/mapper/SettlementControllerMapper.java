package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDateListVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDateVo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDateBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDateListBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 结算控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface SettlementControllerMapper {

    SettlementControllerMapper MAPPER = Mappers.getMapper(SettlementControllerMapper.class);

    /**
     * 结算日期BO转VO
     */
    SettlementDateVo boToVo(SettlementDateBo bo);

    /**
     * 结算日期列表BO转VO
     */
    SettlementDateListVo boToVo(SettlementDateListBo bo);
}
