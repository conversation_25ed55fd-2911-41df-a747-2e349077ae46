package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.api.dto.UserRecentAccessDetailDto;
import com.bilibili.miniapp.api.dto.UserRecentAccessDto;
import com.bilibili.miniapp.open.service.bo.user.UserRecentAccessDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/4
 **/

@Mapper
public interface UserRecentAccessMapper {

    UserRecentAccessMapper MAPPER = Mappers.getMapper(UserRecentAccessMapper.class);

    UserRecentAccessDetailDto toUserRecentAccessDto(UserRecentAccessDetail userRecentAccessDetail);
}
