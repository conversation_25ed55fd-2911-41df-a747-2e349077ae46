package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.yk.YkOrderCallbackReqVo;
import com.bilibili.miniapp.open.service.bo.youku.YkOrderCallbackReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/7
 **/

@Mapper
public interface YkCallbackControllerMapper {

    YkCallbackControllerMapper MAPPER = Mappers.getMapper(YkCallbackControllerMapper.class);

    YkOrderCallbackReq toYkOrderCallbackReq(YkOrderCallbackReqVo reqVo);
}
