package com.bilibili.miniapp.open.portal.validator;

import com.bilibili.miniapp.open.portal.annotations.Conditional;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/18 16:25
 */
public class ConditionalValidator implements ConstraintValidator<Conditional, Object> {

    private Conditional curConditional;

    @Override
    public void initialize(Conditional constraintAnnotation) {
        curConditional = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            //如果没有传，则默认合法
            return true;
        }

        Class<?> clazz = value.getClass();
        if (clazz == Byte.class || clazz == Integer.class || clazz == Short.class || clazz == Long.class) {
            long v = ((Number) value).longValue();
            return v >= curConditional.min() && v < curConditional.max();
        }

        if (CharSequence.class.isAssignableFrom(clazz)) {
            long v = ((CharSequence) value).length();
            return v >= curConditional.min() && v < curConditional.max();
        }
        return true;
    }
}
