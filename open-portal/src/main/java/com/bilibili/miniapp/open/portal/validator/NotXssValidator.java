package com.bilibili.miniapp.open.portal.validator;

import com.bilibili.miniapp.open.portal.annotations.NotXss;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
public class NotXssValidator implements ConstraintValidator<NotXss, String> {
    /**
     * 白名单：安全的HTML标签，不会被clean
     */
    private static final Safelist WHITE_LIST = Safelist.relaxed();
    /**
     * disabled prettyPrint，避免在清理过程中对代码的格式化导致输入和输出产生不必要的差异
     */
    private static final Document.OutputSettings OUTPUT_SETTINGS = new Document.OutputSettings().prettyPrint(false);

    /**
     * 验证输入值是否有效，即是否包含潜在的XSS攻击脚本。
     *
     * @param value   输入值，待检测的内容
     * @param context 上下文对象，提供关于验证环境的信息，如验证失败时的错误消息定制
     * @return true，有效；false，无效
     */
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (!StringUtils.hasText(value)) {
            return true;
        }
        // 使用Jsoup库对输入值进行清理，以移除潜在的XSS攻击脚本，保留安全的HTML元素和属性
        String cleanedValue = Jsoup.clean(value, "", WHITE_LIST, OUTPUT_SETTINGS);
        // 判断输入是否有效
        return Objects.equals(cleanedValue, value);
    }

}
