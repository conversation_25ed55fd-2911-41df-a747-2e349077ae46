package com.bilibili.miniapp.open.portal.vo.ad;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/10 17:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AdPositionVo implements Serializable {
    private static final long serialVersionUID = -8211948475735299121L;
    @Schema(description = "单元id")
    private String adUnitId;
    @Schema(description = "单元类型")
    private Integer adUnitType;
    @Schema(description = "是否上线")
    private Integer isOnline;
}
