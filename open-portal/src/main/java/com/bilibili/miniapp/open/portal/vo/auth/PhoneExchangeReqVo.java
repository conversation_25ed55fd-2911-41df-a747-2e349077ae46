package com.bilibili.miniapp.open.portal.vo.auth;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PhoneExchangeReqVo implements Serializable {

    private static final long serialVersionUID = -458923485960823L;

    @Sign(key = "app_id")
    @NotBlank(message = "app_id不可为空")
    private String appId;

    @Sign(key = "open_id")
    @NotBlank(message = "open_id不可为空")
    private String openId;

    @Sign(key = "preauth_code")
    @NotBlank(message = "preauth_code不可为空")
    private String preauthCode;
}
