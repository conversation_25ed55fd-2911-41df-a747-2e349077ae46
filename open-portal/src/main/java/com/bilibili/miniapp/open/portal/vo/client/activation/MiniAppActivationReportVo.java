package com.bilibili.miniapp.open.portal.vo.client.activation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 小程序激活上报请求VO
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppActivationReportVo {

    /**
     * 用户ID
     */
    private Long mid;

    /**
     * 设备标识
     */
    private String buvid;

    /**
     * 创建时间（时间戳）
     */
    private Long ctime;

    /**
     * 追踪ID
     */
    private String trackId;

    /**
     * 来源
     */
    private String sourceFrom;

    /**
     * 小程序ID
     */
    private String appId;
}
