package com.bilibili.miniapp.open.portal.vo.client.applet;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class ClientAppletInfoVo {
    private String miniAppName;
    private String miniAppLogo;
}
