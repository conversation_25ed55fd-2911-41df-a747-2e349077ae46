package com.bilibili.miniapp.open.portal.vo.client.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ClientSeasonVo {
    private String cover;
    private Integer epCount;
    private Long seasonId;
    private String subTitle;
    private String title;
}
