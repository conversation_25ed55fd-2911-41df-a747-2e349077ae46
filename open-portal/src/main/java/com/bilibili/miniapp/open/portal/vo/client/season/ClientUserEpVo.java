package com.bilibili.miniapp.open.portal.vo.client.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ClientUserEpVo {
    private Long epId;
    private Integer order;
    private Integer paymentStatus;
    private Boolean unlock;
    private Integer width;
    private Integer height;
    private Long duration;
}
