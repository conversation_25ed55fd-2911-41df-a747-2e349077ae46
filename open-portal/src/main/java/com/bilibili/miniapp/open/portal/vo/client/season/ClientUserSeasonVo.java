package com.bilibili.miniapp.open.portal.vo.client.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ClientUserSeasonVo {

    private String cover;
    private List<ClientUserEpVo> epList;
    private Long seasonId;
    private String title;

}
