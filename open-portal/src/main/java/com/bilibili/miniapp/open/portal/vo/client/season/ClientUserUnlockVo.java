package com.bilibili.miniapp.open.portal.vo.client.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ClientUserUnlockVo {
    private List<ClientUnlockEpVo> epList;
    private String openId;
    private Long seasonId;
}
