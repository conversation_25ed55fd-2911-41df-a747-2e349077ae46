package com.bilibili.miniapp.open.portal.vo.comment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BatchGetReplyBlueLinkInfoReqVo {
    /**
     * 回复链接列表
     */
    private List<ReplyUrlVo> replyUrls;
    /**
     * 主题ID
     */
    private SubjectIdVo subjectId;
    /**
     * 用户ID
     */
    private Long mid;

    /**
     * 稿件up的mid
     */
    private Long upMid;

    private String trackId;

    /**
     * 当前页面的唯一标识
     */
    private String spmid;
}