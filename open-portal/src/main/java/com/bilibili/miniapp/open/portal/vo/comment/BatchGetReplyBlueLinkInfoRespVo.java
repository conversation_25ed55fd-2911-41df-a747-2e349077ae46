package com.bilibili.miniapp.open.portal.vo.comment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BatchGetReplyBlueLinkInfoRespVo {
    /**
     * 蓝链信息列表
     */
    private List<BlueLinkInfoVo> blueLinkInfos;
}