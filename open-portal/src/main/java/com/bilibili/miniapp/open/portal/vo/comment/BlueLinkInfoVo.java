package com.bilibili.miniapp.open.portal.vo.comment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BlueLinkInfoVo {
    /**
     * 回复ID
     */
    private Long rpid;
    /**
     * 蓝链材料
     */
    private Map<String, BlueLinkMaterialVo> blueLinkMaterials;
}
