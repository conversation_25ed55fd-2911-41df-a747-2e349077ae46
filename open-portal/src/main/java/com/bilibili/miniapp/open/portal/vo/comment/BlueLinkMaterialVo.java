package com.bilibili.miniapp.open.portal.vo.comment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BlueLinkMaterialVo {
    /**
     * 标题
     */
    private String title;
    /**
     * 前缀图标
     */
    private String prefixIcon;
    /**
     * 应用schema
     */
    private String appSchema;
    /**
     * 曝光报告
     */
    private String exposureReport;
    /**
     * 点击报告
     */
    private String clickReport;
}
