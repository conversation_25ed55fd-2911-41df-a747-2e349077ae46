package com.bilibili.miniapp.open.portal.vo.comment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReplyUrlVo {
    /**
     * 回复ID
     */
    private Long rpid;
    /**
     * 链接列表
     */
    private List<String> urls;
    /**
     * 用户ID
     */
    private Long mid;

}
