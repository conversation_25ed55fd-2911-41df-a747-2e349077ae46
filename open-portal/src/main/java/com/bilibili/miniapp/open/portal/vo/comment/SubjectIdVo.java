package com.bilibili.miniapp.open.portal.vo.comment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SubjectIdVo {

    /**
     * 对象ID
     */
    private Long oid;
    /**
     * 类型
     */
    private Long type;
}
