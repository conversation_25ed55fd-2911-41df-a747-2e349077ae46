package com.bilibili.miniapp.open.portal.vo.common;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.portal.annotations.CallFlag;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/12/9 20:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Context {
    @CallFlag({CallFlag.F.MINI_APP, CallFlag.F.OPEN_PLATFORM})
    private Long mid;

    /**
     * 目前移动端（含小程序）会在cookie中携带
     */
    @CallFlag({CallFlag.F.MINI_APP})
    private String buvid;

    @CallFlag({CallFlag.F.MINI_APP})
    private String miniAppId;

    @CallFlag({CallFlag.F.MINI_APP})
    private String miniAppKey;

    @CallFlag({CallFlag.F.MINI_APP})
    private long miniAppVersion;

    //ios or android
    @CallFlag({CallFlag.F.MINI_APP})
    private String miniAppPlatform;

    @CallFlag({CallFlag.F.MINI_APP})
    private String miniAppUserAgent;

    /**
     * OpenApi访问的accessKey
     *
     * @see Sign
     * @see com.bilibili.miniapp.open.portal.annotations.SignValidation
     */
    @CallFlag({CallFlag.F.OPEN_API})
    private String accessKey;

    /**
     * OpenApi访问的签名
     *
     * @see Sign
     * @see com.bilibili.miniapp.open.portal.annotations.SignValidation
     */
    @CallFlag({CallFlag.F.OPEN_API})
    private String sign;

    /**
     * OpenApi访问的时间戳
     *
     * @see Sign
     * @see com.bilibili.miniapp.open.portal.annotations.SignValidation
     */
    @CallFlag({CallFlag.F.OPEN_API})
    private long ts;

    /**
     * 用于判断是否是测试请求，默认为false
     * 是测试请求时，跳过验签逻辑
     */
    private boolean isTest;

    /**
     * 验证小程序基本信息
     *
     * <p>适用于移动端小程序访问，不适用于开放平台访问</p>
     */
    public boolean isValidMiniAppCall() {
        return StringUtils.hasText(miniAppId) && StringUtils.hasText(miniAppPlatform) && NumberUtil.isPositive(miniAppVersion);
    }

    /**
     * 验证OpenApi公共参数基本信息
     *
     * <p>适用于CP OpenApi访问</p>
     */
    public boolean isValidOpenApiCall() {
        return StringUtils.hasText(accessKey) && StringUtils.hasText(sign) && NumberUtil.isPositive(ts);
    }
}
