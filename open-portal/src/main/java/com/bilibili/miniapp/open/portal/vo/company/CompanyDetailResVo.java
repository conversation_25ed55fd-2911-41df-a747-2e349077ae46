package com.bilibili.miniapp.open.portal.vo.company;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2025/2/28
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompanyDetailResVo {

    private CompanyInfoVo companyInfo;

    private Integer auditStatus;

    private String failReason;

}
