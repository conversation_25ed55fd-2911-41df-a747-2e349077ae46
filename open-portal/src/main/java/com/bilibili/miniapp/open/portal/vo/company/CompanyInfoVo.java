package com.bilibili.miniapp.open.portal.vo.company;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompanyInfoVo {
    private List<String> additionalMaterials;
    private String businessLicense;
    private String companyName;
    private String contactEmail;
    private String creditCode;
    private String officialWebsite;
    private String operatorName;
    private String phoneNumber;
}