package com.bilibili.miniapp.open.portal.vo.contract;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合同结算详情响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ContractSettlementDetailRespVo {
    
    /**
     * 合同ID
     */
    private String contractId;
    
    /**
     * 签约人姓名
     */
    private String signatoryName;
    
    /**
     * 签约人手机号
     */
    private String signatoryPhone;
    
    /**
     * 签约人电子邮箱
     */
    private String signatoryEmail;
    
    /**
     * 联系地址
     */
    private String signatoryAddress;
    
    /**
     * 合同生效开始时间（时间戳，秒）
     */
    private Long contractStartTime;
    
    /**
     * 合同生效结束时间（时间戳，秒）
     */
    private Long contractEndTime;
    
    /**
     * 合同状态，0-待确认签署信息，1-审核中，2-审核未通过，3-待签署，4-未生效，5-生效中，6-已失效
     */
    private Integer contractStatus;
    
    /**
     * 合同审核信息
     */
    private String contractAuditReason;
}
