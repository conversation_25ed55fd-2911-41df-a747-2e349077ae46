package com.bilibili.miniapp.open.portal.vo.data;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/28
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EcpmEventLogVo {
    private String openId;
    private String eventType;
    private String eventTime;
    private String cost;
    private String id;
}
