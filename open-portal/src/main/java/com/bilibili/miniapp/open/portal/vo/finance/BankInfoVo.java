package com.bilibili.miniapp.open.portal.vo.finance;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 银行信息VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BankInfoVo {
    
    /**
     * 银行账户号
     */
    @NotBlank(message = "银行账户号不能为空")
    private String bankAccountNumber;
    
    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空")
    private String bankName;
    
    /**
     * 银行支行名称
     */
    @NotBlank(message = "银行支行名称不能为空")
    private String bankBranchName;
}
