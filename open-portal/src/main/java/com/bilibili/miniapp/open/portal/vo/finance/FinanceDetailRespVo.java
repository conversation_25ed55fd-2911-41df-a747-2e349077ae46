package com.bilibili.miniapp.open.portal.vo.finance;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 财务信息详情响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FinanceDetailRespVo {
    
    /**
     * 银行信息
     */
    private BankInfoVo bankInfo;
    
    /**
     * 发票信息
     */
    private InvoiceInfoVo invoiceInfo;
}
