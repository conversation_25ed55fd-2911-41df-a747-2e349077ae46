package com.bilibili.miniapp.open.portal.vo.finance;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 财务信息创建请求VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FinanceSaveReqVo {
    
    /**
     * 银行信息
     */
    @NotNull(message = "银行信息不能为空")
    @Valid
    private BankInfoVo bankInfo;
    
    /**
     * 发票信息
     */
    @NotNull(message = "发票信息不能为空")
    @Valid
    private InvoiceInfoVo invoiceInfo;
}
