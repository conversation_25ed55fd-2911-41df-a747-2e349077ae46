package com.bilibili.miniapp.open.portal.vo.finance;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 发票信息VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvoiceInfoVo {
    
    /**
     * 税率类型：1-一般纳税人-6%
     */
    @NotNull(message = "税率类型不能为空")
    private Integer taxType;
    
    /**
     * 发票类型：1-增值税专用发票
     */
    @NotNull(message = "发票类型不能为空")
    private Integer invoiceType;
    
    /**
     * 开票项目类别：1-信息技术服务*信息服务费,2-广告服务*广告发布费
     */
    @NotNull(message = "开票项目类别不能为空")
    private Integer invoiceItemCategory;
}
