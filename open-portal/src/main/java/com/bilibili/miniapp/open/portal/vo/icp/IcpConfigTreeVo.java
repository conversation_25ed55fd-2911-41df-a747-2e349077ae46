package com.bilibili.miniapp.open.portal.vo.icp;

import com.bilibili.miniapp.open.service.bo.icp.IcpConfigTree;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpConfigTreeVo {
    private Long value;
    private String label;
    private List<IcpConfigTreeVo> children = new ArrayList<>();
}
