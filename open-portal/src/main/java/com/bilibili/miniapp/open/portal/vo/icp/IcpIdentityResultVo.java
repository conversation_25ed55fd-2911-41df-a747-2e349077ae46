package com.bilibili.miniapp.open.portal.vo.icp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/25
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpIdentityResultVo {
    /**
     * 核验拍摄照
     */
    private String verifyPhoto;
    /**
     * 身份证照片
     */
    private String fzrCard;
    /**
     * 类型
     */
    private Integer fzrLicenseType;

    private String fzrCardNo;

    private String fzrName;

    private Long fzrCardBegin;

    private Long fzrCardEnd;

    private Integer fzrCardLongEffect;

}
