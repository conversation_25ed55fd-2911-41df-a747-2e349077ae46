package com.bilibili.miniapp.open.portal.vo.icp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/19
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpPlatformAuditDetail {

    private Integer fieldObj;

    /**
     * 审核未通过字段
     */
    private String fieldName;

    /**
     * 未通过原因
     */
    private String reason;

    /**
     * 未通过字段名称
     */
    private String name;
}
