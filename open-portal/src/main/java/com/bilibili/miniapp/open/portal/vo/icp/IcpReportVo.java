package com.bilibili.miniapp.open.portal.vo.icp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpReportVo {

    private Long flowId;
    private String appId;
    private IcpCompanyVo company;
    private IcpAppVo app;
    private List<IcpAttachmentVo> attachment;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpCompanyVo {
        private Integer type;
        private Integer licenseType;
        private String licensePhoto;
        private String name;
        private String licenseNo;
        private Integer licenseProvince;
        private Integer licenseCity;
        private Integer licenseCounty;
        private String licenseDetailAddress;
        private Integer contactProvince;
        private Integer contactCity;
        private Integer contactCounty;
        private String contactDetailAddress;
        private String remark;
        private Integer fzrLicenseType;
        private String fzrCardNo;
        private String fzrName;
        private String fzrCardFront;
        private String fzrCardReverse;
        private Long fzrCardBegin;
        private Long fzrCardEnd;
        private Integer fzrCardLongEffect;
        private String fzrPhone;
        private String fzrEmail;
        private String fzrEmergency;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpAppVo {
        private String name;
        private String appId;
        private Integer serviceType;
        private Integer approvalPre;
        private Integer approvalType;
        private String approvalIsbnNo;
        private List<String> approvalAttachment;
        private String remark;
        private Integer fzrLicenseType;
        private String verifyPhoto;
        private String fzrCard;
        private String fzrCardNo;
        private String fzrName;
        private Long fzrCardBegin;
        private Long fzrCardEnd;
        private Integer fzrCardLongEffect;
        private String fzrPhone;
        private String fzrEmail;
        private String fzrEmergency;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IcpAttachmentVo {
        private Integer type;
        private Integer format;
        private String content;
    }
}
