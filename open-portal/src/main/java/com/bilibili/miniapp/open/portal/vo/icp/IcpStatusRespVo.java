package com.bilibili.miniapp.open.portal.vo.icp;

import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/19
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IcpStatusRespVo {

    /**
     * 流程id
     */
    private Long flowId;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * @see IcpFlowStatusEnum
     * 备案流程状态
     */
    private Integer status;

    /**
     * 备案号 备案完成才有
     */
    private String recordNumber;

    /**
     * 平台审核失败原因
     */
    private List<IcpPlatformAuditDetail> platformFailReason;

    /**
     * 管局审核失败原因
     */
    private String govFailReason;

    /**
     * 备案审核通过时间
     */
    private Long auditPassTime;
}
