package com.bilibili.miniapp.open.portal.vo.income;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 收入明细响应VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IncomeDetailRespVo {
    
    /**
     * 收入明细记录列表
     */
    private List<IncomeDetailItemVo> records;
    
    /**
     * 总记录数
     */
    private Long total;
}
