package com.bilibili.miniapp.open.portal.vo.miniapp;

import com.bilibili.miniapp.open.portal.vo.account.AccountInfoResVo;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppAuthListVo {

    /**
     * 如果账户信息发生变化，需要新增类，目前业务上可以复用账户信息对象
     */
    private List<AccountInfoResVo> accounts;
}
