package com.bilibili.miniapp.open.portal.vo.miniapp;

import com.bilibili.miniapp.open.portal.vo.icp.IcpConfigTreeVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/19
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniAppCategoryTreeVo {

    private Long value;
    private String label;
    private List<IcpConfigTreeVo> children = new ArrayList<>();
}
