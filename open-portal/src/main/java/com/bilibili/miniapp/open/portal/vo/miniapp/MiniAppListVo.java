package com.bilibili.miniapp.open.portal.vo.miniapp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppListVo {
    private String admissionId;
    private String appId;
    private String appLogo;
    private String appName;
    private String auditFailureReason;
    /**
     * 审核状态，0-待审核，1-审核通过，2-审核拒绝
     */
    private Integer auditStatus;
    private String categoryName;
    private String companyName;
    /**
     * 1-超级管理员，2-开发者权限，3-体验权限，4-运营权限
     */
    private Integer permission;
    /**
     * 小程序状态，0-未发布，1-已发布
     */
    private Integer status;
}
