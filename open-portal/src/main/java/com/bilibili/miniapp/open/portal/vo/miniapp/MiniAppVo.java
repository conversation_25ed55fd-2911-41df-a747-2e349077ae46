package com.bilibili.miniapp.open.portal.vo.miniapp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppVo {
    private String admissionId;
    private String appDescription;
    private String appId;
    private String appLogo;
    private String appName;
    private List<String> categoryCertifications;
    /**
     * @see com.bilibili.miniapp.open.common.enums.CategoryType
     */
    private Integer categoryId;

    /**
     * @see com.bilibili.miniapp.open.common.enums.AppDevelopType
     */
    private Integer developType;

    private Integer appletVersion;

    private Integer icpSwitch;
}
