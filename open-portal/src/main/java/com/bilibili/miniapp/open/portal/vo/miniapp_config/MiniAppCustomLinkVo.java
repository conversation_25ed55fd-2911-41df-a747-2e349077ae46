package com.bilibili.miniapp.open.portal.vo.miniapp_config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MiniAppCustomLinkVo {
    private String appId;
    private String customPath;
    private String customParams;
}
