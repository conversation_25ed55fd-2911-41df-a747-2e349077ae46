package com.bilibili.miniapp.open.portal.vo.ogv;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 集
 *
 * <AUTHOR>
 * @date 2024/12/10 17:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EpisodeVo implements Serializable {
    private static final long serialVersionUID = -8553640065663613707L;

    @Schema(description = "ssid")
    private Long seasonId;
    @Schema(description = "epid")
    private Long episodeId;
    @Schema(description = "集顺序，比如第1集，第2集")
    private Integer ord;
    //这个比较随意，比如第1集，也可能是第一集，也可能是首集，主要看投稿怎么设置
    @Schema(description = "集标题")
    private String title;
    //类似于集描述，展示在标题的右侧
    @Schema(description = "集长标题")
    private String longTitle;
    @Schema(description = "集封面")
    private String cover;
    @Schema(description = "aid")
    private Long aid;
    @Schema(description = "aid")
    private Long cid;
    @Schema(description = "width")
    private Integer width;
    @Schema(description = "height")
    private Integer height;
}
