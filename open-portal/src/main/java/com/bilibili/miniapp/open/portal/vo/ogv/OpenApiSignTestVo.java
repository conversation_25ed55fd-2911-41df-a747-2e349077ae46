package com.bilibili.miniapp.open.portal.vo.ogv;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/6 11:47
 */
@Data
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OpenApiSignTestVo {
    @Sign(key = "season_id_list")
    private List<Long> seasonIdList;
    @Sign
    private String name;
    @Sign
    private OpenApiSignTestNestVo nest;
}
