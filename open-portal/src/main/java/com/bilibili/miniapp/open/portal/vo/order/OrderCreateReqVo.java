package com.bilibili.miniapp.open.portal.vo.order;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.portal.annotations.Conditional;
import com.bilibili.miniapp.open.portal.annotations.NotXss;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/16 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderCreateReqVo implements Serializable {
    private static final long serialVersionUID = -8525883445059502250L;

    /**
     * 开发者订单id
     */
    @Sign(key = "dev_order_id")
    @NotBlank(message = "dev_order_id不可为空")
    @Length(min = 1, max = 32, message = "dev_order_id长度必须介于[0,32]")
    private String devOrderId;

    /**
     * app_id
     */
    @Sign(key = "app_id")
    @NotBlank(message = "app_id不可为空")
    private String appId;

    /**
     * open_id
     */
    @Sign(key = "open_id")
    @NotBlank(message = "open_id不可为空")
    private String openId;

    /**
     * 商品类型
     */
    @Sign(key = "product_type")
    @PositiveOrZero(message = "product_type不合法，请参考接口文档")
    private Integer productType;

    /**
     * 商品id
     */
    @Sign(key = "product_id")
    @NotBlank(message = "product_id不可为空")
    @Length(min = 1, max = 32, message = "product_id长度必须介于[0,32]")
    private String productId;

    /**
     * 商品名称
     */
    @NotBlank(message = "product_name不可为空")
    @Sign(key = "product_name")
    @Length(min = 1, max = 32, message = "product_name长度必须介于[0,32]")
    private String productName;

    /**
     * 商品描述
     */
    @Sign(key = "product_desc")
    @Conditional(max = 64, message = "product_desc长度必须介于[0,64]")
    private String productDesc;

    /**
     * 订单金额
     */
    @Sign(key = "amount")
    @Positive(message = "amount必须大于0")
    private Long amount;

    /**
     * 开发者支付回调接口
     */
    @Sign(key = "notify_url")
    @NotXss
    @Conditional(max = 128, message = "notify_url长度必须介于[0,128]")
    private String notifyUrl;

    /**
     * 开发者拓展信息
     */
    @Sign(key = "extra_data")
    @Conditional(max = 512, message = "extra_data长度必须介于[0,512]")
    private String extraData;
}
