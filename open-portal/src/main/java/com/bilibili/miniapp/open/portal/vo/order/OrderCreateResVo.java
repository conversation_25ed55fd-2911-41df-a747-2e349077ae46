package com.bilibili.miniapp.open.portal.vo.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/16 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderCreateResVo implements Serializable {
    private static final long serialVersionUID = -8525883445059502250L;

    /**
     * 开平订单id
     */
    private String orderId;

    /**
     * 开发者订单id
     */
    private String devOrderId;

    /**
     * 用于换取支付信息的核心加密数据
     *
     * @see com.bilibili.miniapp.open.service.bo.order.OrderCreateRes
     */
    private String payInfo;
}
