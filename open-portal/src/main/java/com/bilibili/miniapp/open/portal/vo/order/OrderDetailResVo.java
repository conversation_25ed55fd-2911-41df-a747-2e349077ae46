package com.bilibili.miniapp.open.portal.vo.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderDetailResVo {

    private String openId;

    private String devOrderId;

    /**
     * @see com.bilibili.miniapp.open.common.enums.PayStatus
     */
    private Integer payStatus;

    private Long amount;

    private Long payAmount;

    private Long payTime;

    private Integer productType;

    private String productId;

    private String productName;

    private String extraData;

    private Integer payChannel;
}
