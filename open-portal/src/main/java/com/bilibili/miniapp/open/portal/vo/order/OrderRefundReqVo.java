package com.bilibili.miniapp.open.portal.vo.order;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.portal.annotations.Conditional;
import com.bilibili.miniapp.open.portal.annotations.NotXss;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/11 11:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderRefundReqVo implements Serializable {
    private static final long serialVersionUID = -8077388860026761544L;
    /**
     * 开平订单id
     */
    @Sign(key = "order_id")
    @NotBlank(message = "order_id不可为空")
    private String orderId;

    /**
     * 小程序id
     */
    @Sign(key = "app_id")
    @NotBlank(message = "app_id不可为空")
    private String appId;

    /**
     * 开发者订单号
     */
    @Sign(key = "dev_order_id")
    @NotBlank(message = "dev_order_id不可为空")
    @Length(min = 1, max = 32, message = "dev_order_id长度必须介于[0,32]")
    private String devOrderId;

    /**
     * 开发者退款批次id
     */
    @Sign(key = "dev_refund_id")
    @NotBlank(message = "dev_refund_id不可为空")
    @Length(min = 1, max = 32, message = "dev_refund_id长度必须介于[0,32]")
    private String devRefundId;

    /**
     * 退款金额
     */
    @Sign(key = "refund_amount")
    @Positive(message = "refund_amount必须大于0")
    private Long refundAmount;

    /**
     * 退款描述
     */
    @Sign(key = "refund_desc")
    @Conditional(max = 32, message = "refund_desc长度必须介于[0,32]")
    private String refundDesc;

    /**
     * 退款回调地址
     */
    @Sign(key = "notify_url")
    @NotXss
    @Conditional(max = 128, message = "notify_url长度必须介于[0,128]")
    private String notifyUrl;

    /**
     * 开发者拓展信息
     */
    @Sign(key = "extra_data")
    @Conditional(max = 512, message = "extra_data长度必须介于[0,512]")
    private String extraData;
}
