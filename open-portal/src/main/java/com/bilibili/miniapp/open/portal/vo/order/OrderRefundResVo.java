package com.bilibili.miniapp.open.portal.vo.order;

import com.bilibili.miniapp.open.common.enums.RefundStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/11 11:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderRefundResVo implements Serializable {
    private static final long serialVersionUID = -8077388860026761544L;
    /**
     * 退款状态，0：尚未发起退款，1：发起退款成功（退款中），2：发起退款失败，3：退款成功，4：退款失败
     *
     * @see RefundStatus
     */
    private Integer refundStatus;
}
