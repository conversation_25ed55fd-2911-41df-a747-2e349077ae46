package com.bilibili.miniapp.open.portal.vo.payment;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/16 16:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExchangePayInfoReqVo implements Serializable {

    private static final long serialVersionUID = -2313400959024281837L;

    /**
     * 创单接口返回的订单信息
     */
    @NotNull
    private ExchangePayOrderInfoVo orderInfo;
    /**
     * 当前端上上下文数据，比如track_id、sourcefrom等等
     */
    private JSONObject traceInfo;
}
