package com.bilibili.miniapp.open.portal.vo.payment;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.enums.PayType;
import com.bilibili.miniapp.open.service.bo.payment.PayParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/16 16:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExchangePayInfoResVo implements Serializable {

    private static final long serialVersionUID = 3384802501795157803L;

    /**
     * 支付SDK需要的支付参数
     * <p>
     * 注意：不是vo，且必须是驼峰规范，不要轻易调整！！！
     *
     * @see PayParam
     */
    private JSONObject payParam;

    /**
     * 支付类型
     * <p>
     * 0：SDK支付，此时pay_param即支付参数，此时pay_info用不到
     * <p>
     * 1：开平支付，此时用户确认后使用pay_info发起支付确认；
     * <p>
     * 2：需要充值后再支付，此时pay_param即充值参数，充值完成后，使用pay_info发起支付确认；
     *
     * @see PayType
     */
    private int payType;

    /**
     * 支付信息，端上确认支付时，需要透传给服务端的参数
     * <p>
     * 适用于【开平支付】
     */
    private OpenPayInfoVo payInfo;
}
