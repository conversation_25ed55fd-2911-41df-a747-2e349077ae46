package com.bilibili.miniapp.open.portal.vo.payment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/16 16:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExchangePayOrderInfoVo implements Serializable {
    private static final long serialVersionUID = 6527923369272505637L;
    /**
     * 开平订单id，不过暂时用不到，实际使用的是{@link #payInfo}解密之后的数据
     */
    private String orderId;

    /**
     * 预支付的加密信息，实质上是对创单接口响应的加密结果
     * <p>
     * 虽然该字段是订单信息，但是在开发者角度，我们返回这段信息是用于后续支付的，因此使用payInfo更有助于开发者理解
     *
     * @see com.bilibili.miniapp.open.portal.controller.open_api.OpenPlatformOrderController
     * @see com.bilibili.miniapp.open.service.bo.order.OrderCreateRes
     */
    @NotBlank
    private String payInfo;
}
