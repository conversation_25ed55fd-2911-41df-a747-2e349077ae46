package com.bilibili.miniapp.open.portal.vo.payment;

import com.bilibili.miniapp.open.service.bo.payment.OpenPayConfirmInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/20 16:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OpenPayInfoVo implements Serializable {
    private static final long serialVersionUID = -2003063523941191654L;
    /**
     * 提示标题
     */
    private String showTitle;

    /**
     * 提示内容
     */
    private String showContent;

    /**
     * 详细的支付订单信息，加密信息
     *
     * @see OpenPayConfirmInfo
     */
    @NotBlank
    private String confirmInfo;
}
