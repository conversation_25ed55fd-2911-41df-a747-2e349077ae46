package com.bilibili.miniapp.open.portal.vo.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AddSeasonToTabVo {

    private String appId;

    private List<SeasonTabInfoVo> seasonList;
    /**
     * 0-热门推荐，1-新剧速递
     */
    private Integer tabType;
}
