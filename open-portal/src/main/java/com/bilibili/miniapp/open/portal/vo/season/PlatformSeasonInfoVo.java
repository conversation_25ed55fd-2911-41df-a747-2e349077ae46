package com.bilibili.miniapp.open.portal.vo.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PlatformSeasonInfoVo {
    private String cover;
    private Integer epCount;
    /**
     * 0-免费，1-IAA，2-IAP
     */
    private Integer paymentStatus;
    private Long seasonId;
    private String subTitle;
    private String title;
    private Boolean alreadyPublished;
}
