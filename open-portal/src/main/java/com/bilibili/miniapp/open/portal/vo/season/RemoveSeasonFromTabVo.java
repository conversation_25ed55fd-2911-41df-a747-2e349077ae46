package com.bilibili.miniapp.open.portal.vo.season;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RemoveSeasonFromTabVo {

    private String appId;

    private Long seasonId;
    /**
     * 0-热门推荐，1-新剧速递
     */
    private Integer tabType;
}
