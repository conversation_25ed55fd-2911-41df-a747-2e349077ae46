package com.bilibili.miniapp.open.portal.vo.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 结算日期列表VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementDateListVo {
    
    /**
     * 结算日期列表
     */
    @JSONField(name = "settlement_list")
    private List<SettlementDateVo> settlementList;
}
