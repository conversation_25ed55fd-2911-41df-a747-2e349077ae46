package com.bilibili.miniapp.open.portal.vo.settlement;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 结算日期VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementDateVo {
    
    /**
     * 日期
     */
    @JSONField(name = "date")
    private String date;
    
    /**
     * 汇联易预提单id
     */
    @JSONField(name = "accrual_id")
    private String accrualId;
}
