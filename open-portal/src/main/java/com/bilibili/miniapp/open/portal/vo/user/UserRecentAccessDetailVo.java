package com.bilibili.miniapp.open.portal.vo.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/27
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonSerialize
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserRecentAccessDetailVo implements Serializable {

    private static final long serialVersionUID = -1L;

    private String appId;

    private Integer type;

    /**
     * 图标
     */
    private String icon;

    /**
     * 跳转链接，会根据用户上传小程序包判断使用的是新框架还是老框架
     */
    private String linkUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 最近访问时间
     */
    private Long recentVisitTime;

}
