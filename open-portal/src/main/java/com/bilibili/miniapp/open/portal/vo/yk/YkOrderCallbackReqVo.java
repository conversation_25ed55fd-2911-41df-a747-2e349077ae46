package com.bilibili.miniapp.open.portal.vo.yk;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/7
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class YkOrderCallbackReqVo {
    @Sign(key = "order_id")
    @Schema(description = "订单唯⼀id")
    private Long orderId;
    @Schema(description = "订单唯一码，一般不使用")
    @Sign(key = "order_code")
    private String orderCode;
    @Schema(description = "订单类型，签约订单、续费订单、普通订单")
    @Sign(key = "order_type")
    private String orderType;
    @Schema(description = "⽤户唯⼀id")
    @Sign(key = "user_id")
    private String userId;
    @Schema(description = "订单⾦额（元）")
    @Sign(key = "pay_amt")
    private String payAmt;
    @Schema(description = "订单付费时间，如：2024-11-06 13:12:58")
    @Sign(key = "pay_time")
    private String payTime;
    @Schema(description = "商品id")
    @Sign(key = "product_id")
    private Long productId;
    @Schema(description = "商品名称")
    @Sign(key = "product_name")
    private String productName;
    @Schema(description = "会员权益名称")
    @Sign(key = "sku_name")
    private String skuName;
    @Schema(description = "支付方式")
    @Sign(key = "pay_type")
    private String payType;
    @Schema(description = "退款状态，0：未退款，1：已退款")
    @Sign(key = "refund_state")
    private Integer refundState;
    @Schema(description = "退款时间，如：2024-11-06 13:12:58")
    @Sign(key = "refund_time")
    private String refundTime;
    @Schema(description = "退款⾦额（元）")
    @Sign(key = "refund_amt")
    private String refundAmt;
    @Schema(description = "签约订单id，针对续费订单提供")
    @Sign(key = "sign_order_id")
    private Long signOrderId;
    @Schema(description = "签约状态，1：签约，2：解约，0：⾮签约单或签约未⽣效")
    @Sign(key = "sign_state")
    private Integer signState;
    @Schema(description = "解约⽇期，如：20241212")
    @Sign(key = "sign_off_date")
    private String signOffDate;
    @Schema(description = "扩展参数，格式k1=v1&k2=v2\n" +
            "设备属性：openid、platform（系统）、ver（版本）、dev\n" +
            "（设备）\n" +
            "挂链携带额外参数：ug_extra，该参数包含expert_uid（外部\n" +
            "达⼈id）、vid（分集id）、sid（媒体id）、scene（场景id）\n" +
            "等")
    @Sign(key = "args")
    private String args;
    @Schema(description = "订单应⽤：如：抖⾳、快⼿")
    @Sign(key = "app")
    private String app;

    @Sign(key = "time")
    private Long time;
}
