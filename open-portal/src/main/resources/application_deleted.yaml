#覆盖
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 1GB
      max-request-size: 1GB
server:
  port: 8081
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    uri-encoding: UTF-8
springdoc:
  swagger-ui:
    #禁用ui
    enabled: false
  #禁用api
  enable-default-api-docs: false

# 禁用http-server包中的fastjson转化器
http:
  server:
    fastjson:
      enabled: false