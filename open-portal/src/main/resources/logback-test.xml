<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="APP" value="miniapp-open-platform"/>
    <property name="APP_ID" value="miniapp-open-platform"/>
    <property name="LOG_HOME" value="/Users/<USER>/work/log/${APP_ID}"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger.%line [%method] [%X{traceid}] - %msg%n
            </Pattern>
        </layout>
    </appender>

    <appender name="INFO_ONLINE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_ID}.log</file>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger.%line [%method] [%X{traceid}] - %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/${APP_ID}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>3</maxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="INFO_ONLINE"/>
        <neverBlock>true</neverBlock>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
    </appender>

    <appender name="CAT" class="com.dianping.cat.logback.CatLogbackAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>


    <appender name="OpenTelemetry" class="com.bilibili.logback.otel.appender.OpenTelemetryAppender">
        <target>unix:///var/run/lancer/collector_otel.sock</target> <!-- 默认的opentelemetry log proto 监听地址 -->
        <serviceName>sycpb.miniapp.open-platform</serviceName> <!-- 当前应用的appid, 必须存在 -->
        <taskId>000161</taskId> <!-- 原来的 taskId 概念，必须存在，uat: 000069, prod: 000161 -->
        <scheduleDelayMillis>200</scheduleDelayMillis> <!-- 发送日志的最大间隔 -->
        <maxQueueSize>2048</maxQueueSize> <!-- 内存缓存日志的最大条数，超过缓存数量会丢弃日志-->
        <maxExportBatchSize>128</maxExportBatchSize> <!-- 每批发送日志的最大条数 -->
        <meterEnabled>true
        </meterEnabled> <!-- 是否捕获 Metric 数据待发现, 默认只记录数据，不上报，当应用程序使用了 warp-spring-boot-starter-web 模块时会自动发现收集的metric指标并上报，主要用于观察是否存在日志丢失情况 -->
    </appender>


    <root level="INFO">
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="CAT"/>
<!--        <appender-ref ref="OpenTelemetry"/>-->
    </root>

</configuration>