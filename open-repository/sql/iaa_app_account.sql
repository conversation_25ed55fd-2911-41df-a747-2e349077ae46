CREATE TABLE `iaa_app_account`
(
    `id`                        bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '账户自增id',
    `app_type`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '账户类型 mini_game/mini_app',
    `app_id`                    varchar(100) NOT NULL DEFAULT '' COMMENT '账户唯一键',
    `income_amt`                decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计收入,分',
    `income_business_part_amt`  decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计广告收入,分',
    `income_natural_part_amt`   decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计自然收入,分',
    `withdraw_amt`              decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计提现,分，不一定到账，只要预提就发生',
    `withdraw_business_part_amt` decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计提现,分，不一定到账，只要预提就发生',
    `withdraw_natural_part_amt` decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计提现,分，不一定到账，只要预提就发生',
    `crm_charge_amt`            decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '累计CRM扣款,分',
    `settle_times`              int(11)      NOT NULL DEFAULT 0 COMMENT '结算次数',
    `latest_settle_time`        datetime     NOT NULL DEFAULT current_timestamp() COMMENT '最近一次结算时间',
    `ctime`                     datetime     NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`                     datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`                   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除,1:已删除)',
    `extra`                     varchar(2000) NOT NULL DEFAULT '' COMMENT '额外参数',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_app_id` (`app_id`, `app_type`),
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='账户表';