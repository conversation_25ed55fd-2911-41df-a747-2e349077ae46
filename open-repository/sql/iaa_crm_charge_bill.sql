CREATE TABLE `iaa_crm_charge_bill`
(
    `id`                        bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '提现单自增id',
    `app_type`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '账户类型 mini_game/mini_app',
    `app_id`                    varchar(100) NOT NULL DEFAULT '' COMMENT '账户唯一键',
    `traffic_type`              varchar(20)   NOT NULL DEFAULT '' COMMENT '流量类型 natural/business',
    `logdate`                   varchar(50)  NOT NULL DEFAULT '' COMMENT '日志日期',
    `charge_amt`                decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT 'CRM充值金额,分',
    `income_amt`                decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '收入金额，分',
    `settlement_id`             bigint(20)   NOT NULL DEFAULT 0 COMMENT '关联结算单号',
    `withdraw_bill_id`          bigint(20)   NOT NULL DEFAULT 0 COMMENT '提现单ID',
    `charge_time`               datetime     NOT NULL DEFAULT current_timestamp() COMMENT '充值日期',
    `bill_status`               varchar(50)  NOT NULL DEFAULT 'init' COMMENT 'init, success, failed',
    `reason`                    varchar(255) NOT NULL DEFAULT '' COMMENT '充值失败原因',
    `ctime`                     datetime     NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`                     datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`                   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    `extra`                     varchar(2000) NOT NULL DEFAULT '' COMMENT '额外参数',
    PRIMARY KEY (`id`),
    KEY `idx_app_id` (`app_id`),
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='CRM充值账单表';



ALTER TABLE `iaa_crm_charge_bill`
   ADD UNIQUE KEY `idx_app_id_logdate` (`app_id`, `logdate`, `app_type`),
    ADD KEY `idx_settlement_id` (`settlement_id`),
    ADD KEY `idx_withdraw_bill_id` (`withdraw_bill_id`);