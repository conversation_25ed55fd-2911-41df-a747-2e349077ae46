CREATE TABLE `iaa_settlement`
(
    `id`                        bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '结算自增id',
    `settle_key`                varchar(255) NOT NULL DEFAULT '' COMMENT '结算的唯一键保证事务性 UniqueKey:app_id,aggre_logdate,flow_type',
    `logdate`                   varchar(50)  NOT NULL DEFAULT '' COMMENT '聚合日期 20250101',
    `traffic_type`              varchar(20)   NOT NULL DEFAULT '' COMMENT '流量类型 natural/business',
    `app_type`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '账户类型 mini_game/mini_app',
    `app_id`                    varchar(100) NOT NULL DEFAULT '' COMMENT '账户唯一键',
    `settle_time`               datetime     NOT NULL DEFAULT current_timestamp() COMMENT '结算时间',
    `settle_status`             varchar(20)   NOT NULL DEFAULT 'init' COMMENT '结算状态. init:初始化, settled:已结算,settling中,failed失败',
    `settle_reason`             varchar(255) NOT NULL DEFAULT '' COMMENT '结算原因',
    `withdraw_bill_id`          bigint(20)   NOT NULL DEFAULT 0 COMMENT '提现单ID',
    `income_amt`                decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '收入,分',
    `withdraw_amt`              decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '提现金额,分',
    `crm_charge_amt`            decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT 'CRM充值金额,分',
    `extra`                     varchar(2000) NOT NULL DEFAULT '' COMMENT '结算额外信息， json格式，例如结算规则等',
    `ctime`                     datetime     NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`                     datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`                   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除, 预留',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_appid_logdate_traffic` (`app_id`,`logdate`,`app_type`,`traffic_type` ),
    KEY `idx_app_id` (`app_id`),
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='结算表';


ALTER  TABLE `iaa_settlement`
      ADD INDEX `idx_withdraw_bill_id` (`withdraw_bill_id`);



ALTER  TABLE `iaa_settlement`
    ADD INDEX `idx_logdate` (`logdate`);





insert into `iaa_settlement` ( traffic_type, logdate, app_id, app_type, income_amt)
values
    ("business","20241119","biligamefee1f8dacadf8321","mini_game","615600.35"),
    ("natural","20241202","biligame9b4717a319b03e91","mini_game","157.21")
;
