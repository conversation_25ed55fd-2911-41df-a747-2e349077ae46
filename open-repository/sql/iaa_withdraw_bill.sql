CREATE TABLE `iaa_withdraw_bill`
(
    `id`                        bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '提现单自增id',
    `title`                     varchar(255) NOT NULL DEFAULT '' COMMENT '账单抬头',
    `bill_status`               varchar(50)  NOT NULL DEFAULT 'init' COMMENT 'init,withdrawable,withdrawing,success,error,failed',
    `fail_reason`               varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
    `app_type`                  varchar(50)  NOT NULL DEFAULT '' COMMENT '账户类型 mini_game/mini_app',
    `app_id`                    varchar(100) NOT NULL DEFAULT '' COMMENT '账户唯一键',
    `bill_start_time`           datetime     NOT NULL DEFAULT current_timestamp() COMMENT '账单开始时间',
    `bill_end_time`             datetime     NOT NULL DEFAULT current_timestamp() COMMENT '账单结束时间',
    `withdraw_date`             varchar(50)  NOT NULL DEFAULT '' COMMENT '提现账单日 格式202501_1 2025年一月上半月',
    `latest_settle_time`        datetime     NOT NULL DEFAULT current_timestamp() COMMENT '最近一次结算时间',
    `withdraw_apply_time`             datetime     NOT NULL DEFAULT current_timestamp() COMMENT '用户发起提现的时间',
    `withdraw_arrival_time`     datetime     NOT NULL DEFAULT current_timestamp() COMMENT '提现到账时间',
    `income_amt`                decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '当前账单周期内的累计金额',
    `settle_times`              int(11)      NOT NULL DEFAULT 0 COMMENT '结算次数',
    `withdraw_amt`              decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '提现金额',
    `crm_charge_amt`            decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT 'CRM充值金额',
    `income_natural_part_amt`   decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '当前账单周期内的自然收入部分',
    `income_business_part_amt`  decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '当前账单周期内的商业收入部分',
    `withdraw_natural_part_amt` decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '自然收入部分提现金额',
    `withdraw_business_part_amt` decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '商业收入部分提现金额',
    `business_entity_name`      varchar(255) NOT NULL DEFAULT '' COMMENT '业务实体名称',
    `withdraw_apply_amt`      decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '提现请求金额',
    `invoice_img_url`           varchar(255) NOT NULL DEFAULT '' COMMENT '发票图片地址',
    `accrual_id`                varchar(100) NOT NULL DEFAULT '' COMMENT '汇联易预提单id',
    `accrual_extra`             varchar(1500) NOT NULL DEFAULT '' COMMENT '汇联易预提单附加信息',
    `expense_id`                varchar(100) NOT NULL DEFAULT '' COMMENT '汇联易付款单id',
    `expense_extra`             varchar(1500) NOT NULL DEFAULT '' COMMENT '汇联易付款单附加信息',
    `expense_code`              varchar(100) NOT NULL DEFAULT '' COMMENT '汇联易付款单代码',
    `expense_message`           varchar(255) NOT NULL DEFAULT '' COMMENT '汇联易付款单消息',
    `extra`                     varchar(2000) NOT NULL DEFAULT '' COMMENT '预留字段，额外信息',
    `ctime`                     datetime     NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    `mtime`                     datetime     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
    `deleted`                   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_app_id` (`app_id`),
    UNIQUE KEY `idx_app_id_withdraw_date` (`app_id`,`withdraw_date`, `app_type`),
    KEY `ix_mtime` (`mtime`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='提现账单表';




ALTER TABLE `iaa_withdraw_bill`
    ADD KEY `idx_expend_id` (`expense_id`);




ALTER TABLE `iaa_withdraw_bill`
    MODIFY  `invoice_img_url`           varchar(2000) NOT NULL DEFAULT '' COMMENT '发票图片地址',
    MODIFY  `expense_extra`             varchar(5000) NOT NULL DEFAULT '' COMMENT '汇联易付款单附加信息';



ALTER TABLE `iaa_withdraw_bill`
    ADD KEY `idx_status` (`bill_status`);

