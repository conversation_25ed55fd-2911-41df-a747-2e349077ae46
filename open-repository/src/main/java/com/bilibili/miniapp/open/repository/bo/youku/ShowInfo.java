package com.bilibili.miniapp.open.repository.bo.youku;


import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShowInfo {

    private Long _id;
    private int heat;
    private String showThumbUrlHuge;
    private String link;
    private int episodeTotal;
    private BigDecimal reputation;
    private List<YouKuVideo> videos;
    private String showSubtitle;
    private String genre;
    private String showW3H4ThumbUrlHuge;
    private boolean exclusive;
    private String id;
    private String state;
    private int lastStage;
    private String area;
    private String showCategory;
    private String performer;
    private String releaseDate;
    private String director;
    private String updateNotice;
    private String showDesc;
    private boolean completed;
    private String name;
    private boolean paid;
    private int lastEpisode;
    private List<String> language;
    private Integer isDeleted = IsDeleted.VALID.getCode();

    private static final int COMPLETED = 1;
    private static final int UNCOMPLETED = 0;

    private static final int PAID = 1;
    private static final int UNPAID = 0;

    private static final int EXCLUSIVE = 1;
    private static final int UN_EXCLUSIVE = 0;

    public MiniAppYoukuShowPo toPo() {
        return MiniAppYoukuShowPo.builder()
                .showId(id)
                .name(name == null ? "" : name)
                .subtitle(showSubtitle == null ? "" : showSubtitle)
                .category(showCategory == null ? "" : showCategory)
                .genre(genre == null ? "" : genre)
                .thumbUrlHuge(showThumbUrlHuge == null ? "" : showThumbUrlHuge)
                .w3H4ThumbUrlHuge(showW3H4ThumbUrlHuge == null ? "" : showW3H4ThumbUrlHuge)
                .description(showDesc == null ? "" : showDesc)
                .completed(completed ? COMPLETED : UNCOMPLETED)
                .paid(paid ? PAID : UNPAID)
                .episodeTotal(episodeTotal)
                .lastEpisode(lastEpisode)
                .lastStage(lastStage)
                .director(director == null ? "" : director)
                .performer(performer == null ? "" : performer)
                .releaseDate(releaseDate == null ? "" : releaseDate)
                .area(area == null ? "" : area)
                .heat(heat)
                .language(CollectionUtils.isEmpty(language) ? "[]" : JSON.toJSONString(language))
                .exclusive(exclusive ? EXCLUSIVE : UN_EXCLUSIVE)
                .reputation(reputation == null ? "" : reputation.toString())
                .link(link == null ? "" : link)
                .updateNotice(updateNotice == null ? "" : updateNotice)
                .ctime(new Timestamp(System.currentTimeMillis()))
                .mtime(new Timestamp(System.currentTimeMillis()))
                .isDeleted(isDeleted)
                .build();
    }

    public static ShowInfo fromPo(MiniAppYoukuShowPo po) {
        return ShowInfo.builder()
                ._id(po.getId())
                .id(po.getShowId())
                .name(po.getName())
                .showSubtitle(po.getSubtitle())
                .showCategory(po.getCategory())
                .genre(po.getGenre())
                .showThumbUrlHuge(po.getThumbUrlHuge())
                .showW3H4ThumbUrlHuge(po.getW3H4ThumbUrlHuge())
                .showDesc(po.getDescription())
                .completed(po.getCompleted() == COMPLETED)
                .paid(po.getPaid() == PAID)
                .episodeTotal(po.getEpisodeTotal())
                .lastEpisode(po.getLastEpisode())
                .lastStage(po.getLastStage())
                .director(po.getDirector())
                .performer(po.getPerformer())
                .releaseDate(po.getReleaseDate())
                .area(po.getArea())
                .heat(po.getHeat())
                .language(StringUtils.isBlank(po.getLanguage()) ? Collections.emptyList() : Arrays.asList(JSON.parseObject(po.getLanguage(), String[].class)))
                .exclusive(po.getExclusive() == EXCLUSIVE)
                .reputation(new BigDecimal(po.getReputation()))
                .link(po.getLink())
                .updateNotice(po.getUpdateNotice())
                .isDeleted(po.getIsDeleted())
                .build();
    }
}
