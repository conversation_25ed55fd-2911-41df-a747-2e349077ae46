package com.bilibili.miniapp.open.repository.bo.youku;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/7
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YkOrderCallback {
    // 订单唯一标识
    private Long orderId;

    // 主动支付成功/续费订单/退款
    private String orderType;

    // 订单创建时间
    private String orderTime;

    // 支付时间
    private String payTime;

    // 退款时间
    private String refundTime;

    private String payChannel;

    private Long goodsId;

    private String goodsName;

    private String subscription;

    private String payAmt;

    private String refundAmt;

    private Args args;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Args {
        private String openId;

        private String platform;

        private String version;

        private Integer sourcefrom;

        private Long fromAvid;

        private String fromTrackid;

        private String fromYkShowid;

        private String fromYkVideoid;
    }
}
