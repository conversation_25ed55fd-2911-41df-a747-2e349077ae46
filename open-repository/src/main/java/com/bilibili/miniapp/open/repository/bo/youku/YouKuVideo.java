package com.bilibili.miniapp.open.repository.bo.youku;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/19
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class YouKuVideo {
    private Long _id;
    private String verticalThumbnails;
    private BigDecimal showLength;
    private String link;
    private String type;
    private int showVideoStage;
    private int showVideoEpisode;
    private String name;
    private boolean paid;
    private String id;
    private String thumbnails;
    private String showId;
    private String title;
    private Integer isDeleted = IsDeleted.VALID.getCode();

    private static final int PAID = 1;
    private static final int UNPAID = 0;

    public YouKuVideo putShowId(String showId) {
        this.showId = showId;
        return this;
    }

    public MiniAppYoukuVideoPo toPo() {
        return MiniAppYoukuVideoPo.builder()
                .showId(showId)
                .videoId(id == null ? "" : id)
                .name(name == null ? "" : name)
                .title(title == null ? "" : title)
                .link(link == null ? "" : link)
                .thumbnails(thumbnails == null ? "" : thumbnails)
                .verticalThumbnails(verticalThumbnails == null ? "" : verticalThumbnails)
                .showLength(showLength == null ? "" : showLength.toString())
                .type(type == null ? "" : type)
                .stage(showVideoStage)
                .episode(showVideoEpisode)
                .paid(paid ? PAID : UNPAID)
                .ctime(new Timestamp(System.currentTimeMillis()))
                .mtime(new Timestamp(System.currentTimeMillis()))
                .isDeleted(isDeleted)
                .build();
    }

    public static YouKuVideo fromPo(MiniAppYoukuVideoPo po) {
        return YouKuVideo.builder()
                ._id(po.getId())
                .id(po.getVideoId())
                .showId(po.getShowId())
                .name(po.getName())
                .title(po.getTitle())
                .link(po.getLink())
                .thumbnails(po.getThumbnails())
                .verticalThumbnails(po.getVerticalThumbnails())
                .showLength(new BigDecimal(po.getShowLength()))
                .type(po.getType())
                .showVideoStage(po.getStage())
                .showVideoEpisode(po.getEpisode())
                .paid(po.getPaid() == PAID)
                .isDeleted(po.getIsDeleted())
                .build();
    }
}
