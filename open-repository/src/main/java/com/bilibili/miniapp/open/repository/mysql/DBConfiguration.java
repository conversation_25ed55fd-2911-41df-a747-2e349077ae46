package com.bilibili.miniapp.open.repository.mysql;

import com.mchange.v2.c3p0.ComboPooledDataSource;
import lombok.Data;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

import java.beans.PropertyVetoException;


/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Configuration
@MapperScan({"com.bilibili.miniapp.open.repository.mysql.mallapp.dao",
        "com.bilibili.miniapp.open.repository.mysql.miniapp.dao",
        "com.bilibili.miniapp.open.repository.mysql.settlement.mapper"})
public class DBConfiguration {

    @Bean(name = "miniAppDataSource")
    public ComboPooledDataSource miniAppDataSource(MiniAppDbConfigurationProperties configurationProperties) throws PropertyVetoException {
        ComboPooledDataSource dataSource = new ComboPooledDataSource();
        dataSource.setDriverClass("com.mysql.jdbc.Driver");
        dataSource.setJdbcUrl(configurationProperties.getUrl());
        dataSource.setUser(configurationProperties.getUsername());
        dataSource.setPassword(configurationProperties.getPassword());
        dataSource.setMaxPoolSize(10);
        dataSource.setMaxIdleTime(7200);
        dataSource.setPreferredTestQuery("SELECT 1");
        dataSource.setCheckoutTimeout(1800000);
        //The last packet successfully received from the server was xxx milliseconds ago.
        //The last packet sent successfully to the server was 0 milliseconds ago.--连接空闲过久了导致的
        //增加空闲监测
        dataSource.setIdleConnectionTestPeriod(3600);//数据库的wait_timeout是3600s
        return dataSource;
    }

    @Bean(name = "sqlSessionFactory")
    public SqlSessionFactoryBean miniAppSqlSessionFactory(@Qualifier("miniAppDataSource") ComboPooledDataSource dataSource) throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        factoryBean.setMapperLocations(resolver.getResources("classpath:mallapp/mapper/*.xml"));
        return factoryBean;
    }

    @Bean(name = "miniAppTransactionManager")
    public DataSourceTransactionManager masTransactionManager(@Qualifier("miniAppDataSource") ComboPooledDataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }


    @Data
    @Configuration
    @PropertySource(value = {"classpath:database.properties"}, factory = PaladinPropertySourceFactory.class)
    @ConfigurationProperties(prefix = "db.mallapp")
    static class MiniAppDbConfigurationProperties {
        private String url;
        private String username;
        private String password;
    }
}
