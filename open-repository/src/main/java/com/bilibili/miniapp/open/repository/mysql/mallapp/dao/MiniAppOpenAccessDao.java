package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccessPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccessPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAccessDao {
    long countByExample(MiniAppOpenAccessPoExample example);

    int deleteByExample(MiniAppOpenAccessPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAccessPo record);

    int insertBatch(List<MiniAppOpenAccessPo> records);

    int insertUpdateBatch(List<MiniAppOpenAccessPo> records);

    int insert(MiniAppOpenAccessPo record);

    int insertUpdateSelective(MiniAppOpenAccessPo record);

    int insertSelective(MiniAppOpenAccessPo record);

    List<MiniAppOpenAccessPo> selectByExample(MiniAppOpenAccessPoExample example);

    MiniAppOpenAccessPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAccessPo record, @Param("example") MiniAppOpenAccessPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAccessPo record, @Param("example") MiniAppOpenAccessPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAccessPo record);

    int updateByPrimaryKey(MiniAppOpenAccessPo record);
}