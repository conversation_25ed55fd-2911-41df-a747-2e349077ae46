package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAppAdmissionPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAppAdmissionPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAppAdmissionDao {
    long countByExample(MiniAppOpenAppAdmissionPoExample example);

    int deleteByExample(MiniAppOpenAppAdmissionPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAppAdmissionPo record);

    int insertBatch(List<MiniAppOpenAppAdmissionPo> records);

    int insertUpdateBatch(List<MiniAppOpenAppAdmissionPo> records);

    int insert(MiniAppOpenAppAdmissionPo record);

    int insertUpdateSelective(MiniAppOpenAppAdmissionPo record);

    int insertSelective(MiniAppOpenAppAdmissionPo record);

    List<MiniAppOpenAppAdmissionPo> selectByExample(MiniAppOpenAppAdmissionPoExample example);

    MiniAppOpenAppAdmissionPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAppAdmissionPo record, @Param("example") MiniAppOpenAppAdmissionPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAppAdmissionPo record, @Param("example") MiniAppOpenAppAdmissionPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAppAdmissionPo record);

    int updateByPrimaryKey(MiniAppOpenAppAdmissionPo record);
}