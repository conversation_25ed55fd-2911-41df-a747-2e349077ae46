package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAuthorAuthorizationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAuthorAuthorizationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenAuthorAuthorizationDao {
    long countByExample(MiniAppOpenAuthorAuthorizationPoExample example);

    int deleteByExample(MiniAppOpenAuthorAuthorizationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenAuthorAuthorizationPo record);

    int insertBatch(List<MiniAppOpenAuthorAuthorizationPo> records);

    int insertUpdateBatch(List<MiniAppOpenAuthorAuthorizationPo> records);

    int insert(MiniAppOpenAuthorAuthorizationPo record);

    int insertUpdateSelective(MiniAppOpenAuthorAuthorizationPo record);

    int insertSelective(MiniAppOpenAuthorAuthorizationPo record);

    List<MiniAppOpenAuthorAuthorizationPo> selectByExample(MiniAppOpenAuthorAuthorizationPoExample example);

    MiniAppOpenAuthorAuthorizationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenAuthorAuthorizationPo record, @Param("example") MiniAppOpenAuthorAuthorizationPoExample example);

    int updateByExample(@Param("record") MiniAppOpenAuthorAuthorizationPo record, @Param("example") MiniAppOpenAuthorAuthorizationPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenAuthorAuthorizationPo record);

    int updateByPrimaryKey(MiniAppOpenAuthorAuthorizationPo record);
}