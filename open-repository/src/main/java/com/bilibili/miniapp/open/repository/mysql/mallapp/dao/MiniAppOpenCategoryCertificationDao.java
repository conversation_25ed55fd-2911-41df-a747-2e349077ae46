package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCategoryCertificationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCategoryCertificationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenCategoryCertificationDao {
    long countByExample(MiniAppOpenCategoryCertificationPoExample example);

    int deleteByExample(MiniAppOpenCategoryCertificationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenCategoryCertificationPo record);

    int insertBatch(List<MiniAppOpenCategoryCertificationPo> records);

    int insertUpdateBatch(List<MiniAppOpenCategoryCertificationPo> records);

    int insert(MiniAppOpenCategoryCertificationPo record);

    int insertUpdateSelective(MiniAppOpenCategoryCertificationPo record);

    int insertSelective(MiniAppOpenCategoryCertificationPo record);

    List<MiniAppOpenCategoryCertificationPo> selectByExample(MiniAppOpenCategoryCertificationPoExample example);

    MiniAppOpenCategoryCertificationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenCategoryCertificationPo record, @Param("example") MiniAppOpenCategoryCertificationPoExample example);

    int updateByExample(@Param("record") MiniAppOpenCategoryCertificationPo record, @Param("example") MiniAppOpenCategoryCertificationPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenCategoryCertificationPo record);

    int updateByPrimaryKey(MiniAppOpenCategoryCertificationPo record);
}