package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenClientSeasonUnlockPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenClientSeasonUnlockPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenClientSeasonUnlockDao {
    long countByExample(MiniAppOpenClientSeasonUnlockPoExample example);

    int deleteByExample(MiniAppOpenClientSeasonUnlockPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenClientSeasonUnlockPo record);

    int insertBatch(List<MiniAppOpenClientSeasonUnlockPo> records);

    int insertUpdateBatch(List<MiniAppOpenClientSeasonUnlockPo> records);

    int insert(MiniAppOpenClientSeasonUnlockPo record);

    int insertUpdateSelective(MiniAppOpenClientSeasonUnlockPo record);

    int insertSelective(MiniAppOpenClientSeasonUnlockPo record);

    List<MiniAppOpenClientSeasonUnlockPo> selectByExample(MiniAppOpenClientSeasonUnlockPoExample example);

    MiniAppOpenClientSeasonUnlockPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenClientSeasonUnlockPo record, @Param("example") MiniAppOpenClientSeasonUnlockPoExample example);

    int updateByExample(@Param("record") MiniAppOpenClientSeasonUnlockPo record, @Param("example") MiniAppOpenClientSeasonUnlockPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenClientSeasonUnlockPo record);

    int updateByPrimaryKey(MiniAppOpenClientSeasonUnlockPo record);
}