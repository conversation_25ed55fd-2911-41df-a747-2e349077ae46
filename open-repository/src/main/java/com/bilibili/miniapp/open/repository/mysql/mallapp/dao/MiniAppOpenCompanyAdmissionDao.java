package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCompanyAdmissionPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCompanyAdmissionPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MiniAppOpenCompanyAdmissionDao {
    long countByExample(MiniAppOpenCompanyAdmissionPoExample example);

    int deleteByExample(MiniAppOpenCompanyAdmissionPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenCompanyAdmissionPo record);

    int insertBatch(List<MiniAppOpenCompanyAdmissionPo> records);

    int insertUpdateBatch(List<MiniAppOpenCompanyAdmissionPo> records);

    int insert(MiniAppOpenCompanyAdmissionPo record);

    int insertUpdateSelective(MiniAppOpenCompanyAdmissionPo record);

    int insertSelective(MiniAppOpenCompanyAdmissionPo record);

    List<MiniAppOpenCompanyAdmissionPo> selectByExample(MiniAppOpenCompanyAdmissionPoExample example);

    MiniAppOpenCompanyAdmissionPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenCompanyAdmissionPo record, @Param("example") MiniAppOpenCompanyAdmissionPoExample example);

    int updateByExample(@Param("record") MiniAppOpenCompanyAdmissionPo record, @Param("example") MiniAppOpenCompanyAdmissionPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenCompanyAdmissionPo record);

    int updateByPrimaryKey(MiniAppOpenCompanyAdmissionPo record);
}