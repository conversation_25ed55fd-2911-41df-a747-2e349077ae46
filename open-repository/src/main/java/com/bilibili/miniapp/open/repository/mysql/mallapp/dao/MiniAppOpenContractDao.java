package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenContractDao {
    long countByExample(MiniAppOpenContractPoExample example);

    int deleteByExample(MiniAppOpenContractPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenContractPo record);

    int insertBatch(List<MiniAppOpenContractPo> records);

    int insertUpdateBatch(List<MiniAppOpenContractPo> records);

    int insert(MiniAppOpenContractPo record);

    int insertUpdateSelective(MiniAppOpenContractPo record);

    int insertSelective(MiniAppOpenContractPo record);

    List<MiniAppOpenContractPo> selectByExample(MiniAppOpenContractPoExample example);

    MiniAppOpenContractPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenContractPo record, @Param("example") MiniAppOpenContractPoExample example);

    int updateByExample(@Param("record") MiniAppOpenContractPo record, @Param("example") MiniAppOpenContractPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenContractPo record);

    int updateByPrimaryKey(MiniAppOpenContractPo record);
}