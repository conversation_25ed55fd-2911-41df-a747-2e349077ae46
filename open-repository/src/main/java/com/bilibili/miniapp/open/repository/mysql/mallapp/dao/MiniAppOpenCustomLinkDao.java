package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCustomLinkPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCustomLinkPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenCustomLinkDao {
    long countByExample(MiniAppOpenCustomLinkPoExample example);

    int deleteByExample(MiniAppOpenCustomLinkPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenCustomLinkPo record);

    int insertBatch(List<MiniAppOpenCustomLinkPo> records);

    int insertUpdateBatch(List<MiniAppOpenCustomLinkPo> records);

    int insert(MiniAppOpenCustomLinkPo record);

    int insertUpdateSelective(MiniAppOpenCustomLinkPo record);

    int insertSelective(MiniAppOpenCustomLinkPo record);

    List<MiniAppOpenCustomLinkPo> selectByExample(MiniAppOpenCustomLinkPoExample example);

    MiniAppOpenCustomLinkPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenCustomLinkPo record, @Param("example") MiniAppOpenCustomLinkPoExample example);

    int updateByExample(@Param("record") MiniAppOpenCustomLinkPo record, @Param("example") MiniAppOpenCustomLinkPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenCustomLinkPo record);

    int updateByPrimaryKey(MiniAppOpenCustomLinkPo record);
}