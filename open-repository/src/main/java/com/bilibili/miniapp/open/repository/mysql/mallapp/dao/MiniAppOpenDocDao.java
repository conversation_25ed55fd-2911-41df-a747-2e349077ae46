package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenDocPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenDocPoExample;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenDocDao {
    long countByExample(MiniAppOpenDocPoExample example);

    int deleteByExample(MiniAppOpenDocPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenDocPo record);

    int insertBatch(List<MiniAppOpenDocPo> records);

    int insertUpdateBatch(List<MiniAppOpenDocPo> records);

    int insert(MiniAppOpenDocPo record);

    int insertUpdateSelective(MiniAppOpenDocPo record);

    int insertSelective(MiniAppOpenDocPo record);

    List<MiniAppOpenDocPo> selectByExample(MiniAppOpenDocPoExample example);

    MiniAppOpenDocPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenDocPo record, @Param("example") MiniAppOpenDocPoExample example);

    int updateByExample(@Param("record") MiniAppOpenDocPo record, @Param("example") MiniAppOpenDocPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenDocPo record);

    int updateByPrimaryKey(MiniAppOpenDocPo record);
}