package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenFinanceInfoDao {
    long countByExample(MiniAppOpenFinanceInfoPoExample example);

    int deleteByExample(MiniAppOpenFinanceInfoPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenFinanceInfoPo record);

    int insertBatch(List<MiniAppOpenFinanceInfoPo> records);

    int insertUpdateBatch(List<MiniAppOpenFinanceInfoPo> records);

    int insert(MiniAppOpenFinanceInfoPo record);

    int insertUpdateSelective(MiniAppOpenFinanceInfoPo record);

    int insertSelective(MiniAppOpenFinanceInfoPo record);

    List<MiniAppOpenFinanceInfoPo> selectByExample(MiniAppOpenFinanceInfoPoExample example);

    MiniAppOpenFinanceInfoPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenFinanceInfoPo record, @Param("example") MiniAppOpenFinanceInfoPoExample example);

    int updateByExample(@Param("record") MiniAppOpenFinanceInfoPo record, @Param("example") MiniAppOpenFinanceInfoPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenFinanceInfoPo record);

    int updateByPrimaryKey(MiniAppOpenFinanceInfoPo record);
}