package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenLogDao {
    long countByExample(MiniAppOpenLogPoExample example);

    int deleteByExample(MiniAppOpenLogPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenLogPo record);

    int insertBatch(List<MiniAppOpenLogPo> records);

    int insertUpdateBatch(List<MiniAppOpenLogPo> records);

    int insert(MiniAppOpenLogPo record);

    int insertUpdateSelective(MiniAppOpenLogPo record);

    int insertSelective(MiniAppOpenLogPo record);

    List<MiniAppOpenLogPo> selectByExampleWithBLOBs(MiniAppOpenLogPoExample example);

    List<MiniAppOpenLogPo> selectByExample(MiniAppOpenLogPoExample example);

    MiniAppOpenLogPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenLogPo record, @Param("example") MiniAppOpenLogPoExample example);

    int updateByExampleWithBLOBs(@Param("record") MiniAppOpenLogPo record, @Param("example") MiniAppOpenLogPoExample example);

    int updateByExample(@Param("record") MiniAppOpenLogPo record, @Param("example") MiniAppOpenLogPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenLogPo record);

    int updateByPrimaryKeyWithBLOBs(MiniAppOpenLogPo record);

    int updateByPrimaryKey(MiniAppOpenLogPo record);
}