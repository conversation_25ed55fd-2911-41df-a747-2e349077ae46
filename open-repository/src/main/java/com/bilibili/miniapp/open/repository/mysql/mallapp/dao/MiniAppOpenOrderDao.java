package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenOrderDao {
    long countByExample(MiniAppOpenOrderPoExample example);

    int deleteByExample(MiniAppOpenOrderPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenOrderPo record);

    int insertBatch(List<MiniAppOpenOrderPo> records);

    int insertUpdateBatch(List<MiniAppOpenOrderPo> records);

    int insert(MiniAppOpenOrderPo record);

    int insertUpdateSelective(MiniAppOpenOrderPo record);

    int insertSelective(MiniAppOpenOrderPo record);

    List<MiniAppOpenOrderPo> selectByExample(MiniAppOpenOrderPoExample example);

    MiniAppOpenOrderPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenOrderPo record, @Param("example") MiniAppOpenOrderPoExample example);

    int updateByExample(@Param("record") MiniAppOpenOrderPo record, @Param("example") MiniAppOpenOrderPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenOrderPo record);

    int updateByPrimaryKey(MiniAppOpenOrderPo record);
}