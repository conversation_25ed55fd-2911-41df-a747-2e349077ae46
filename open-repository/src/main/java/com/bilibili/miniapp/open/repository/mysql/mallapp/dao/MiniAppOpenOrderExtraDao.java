package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenOrderExtraDao {
    long countByExample(MiniAppOpenOrderExtraPoExample example);

    int deleteByExample(MiniAppOpenOrderExtraPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenOrderExtraPo record);

    int insertBatch(List<MiniAppOpenOrderExtraPo> records);

    int insertUpdateBatch(List<MiniAppOpenOrderExtraPo> records);

    int insert(MiniAppOpenOrderExtraPo record);

    int insertUpdateSelective(MiniAppOpenOrderExtraPo record);

    int insertSelective(MiniAppOpenOrderExtraPo record);

    List<MiniAppOpenOrderExtraPo> selectByExample(MiniAppOpenOrderExtraPoExample example);

    MiniAppOpenOrderExtraPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenOrderExtraPo record, @Param("example") MiniAppOpenOrderExtraPoExample example);

    int updateByExample(@Param("record") MiniAppOpenOrderExtraPo record, @Param("example") MiniAppOpenOrderExtraPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenOrderExtraPo record);

    int updateByPrimaryKey(MiniAppOpenOrderExtraPo record);
}