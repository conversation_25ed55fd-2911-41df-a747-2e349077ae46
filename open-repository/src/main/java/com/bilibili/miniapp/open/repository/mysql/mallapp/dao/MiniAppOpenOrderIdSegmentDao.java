package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderIdSegmentPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderIdSegmentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenOrderIdSegmentDao {
    long countByExample(MiniAppOpenOrderIdSegmentPoExample example);

    int deleteByExample(MiniAppOpenOrderIdSegmentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenOrderIdSegmentPo record);

    int insertBatch(List<MiniAppOpenOrderIdSegmentPo> records);

    int insertUpdateBatch(List<MiniAppOpenOrderIdSegmentPo> records);

    int insert(MiniAppOpenOrderIdSegmentPo record);

    int insertUpdateSelective(MiniAppOpenOrderIdSegmentPo record);

    int insertSelective(MiniAppOpenOrderIdSegmentPo record);

    List<MiniAppOpenOrderIdSegmentPo> selectByExample(MiniAppOpenOrderIdSegmentPoExample example);

    MiniAppOpenOrderIdSegmentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenOrderIdSegmentPo record, @Param("example") MiniAppOpenOrderIdSegmentPoExample example);

    int updateByExample(@Param("record") MiniAppOpenOrderIdSegmentPo record, @Param("example") MiniAppOpenOrderIdSegmentPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenOrderIdSegmentPo record);

    int updateByPrimaryKey(MiniAppOpenOrderIdSegmentPo record);
}