package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenRefundDao {
    long countByExample(MiniAppOpenRefundPoExample example);

    int deleteByExample(MiniAppOpenRefundPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenRefundPo record);

    int insertBatch(List<MiniAppOpenRefundPo> records);

    int insertUpdateBatch(List<MiniAppOpenRefundPo> records);

    int insert(MiniAppOpenRefundPo record);

    int insertUpdateSelective(MiniAppOpenRefundPo record);

    int insertSelective(MiniAppOpenRefundPo record);

    List<MiniAppOpenRefundPo> selectByExample(MiniAppOpenRefundPoExample example);

    MiniAppOpenRefundPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenRefundPo record, @Param("example") MiniAppOpenRefundPoExample example);

    int updateByExample(@Param("record") MiniAppOpenRefundPo record, @Param("example") MiniAppOpenRefundPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenRefundPo record);

    int updateByPrimaryKey(MiniAppOpenRefundPo record);
}