package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenRefundExtraDao {
    long countByExample(MiniAppOpenRefundExtraPoExample example);

    int deleteByExample(MiniAppOpenRefundExtraPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenRefundExtraPo record);

    int insertBatch(List<MiniAppOpenRefundExtraPo> records);

    int insertUpdateBatch(List<MiniAppOpenRefundExtraPo> records);

    int insert(MiniAppOpenRefundExtraPo record);

    int insertUpdateSelective(MiniAppOpenRefundExtraPo record);

    int insertSelective(MiniAppOpenRefundExtraPo record);

    List<MiniAppOpenRefundExtraPo> selectByExample(MiniAppOpenRefundExtraPoExample example);

    MiniAppOpenRefundExtraPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenRefundExtraPo record, @Param("example") MiniAppOpenRefundExtraPoExample example);

    int updateByExample(@Param("record") MiniAppOpenRefundExtraPo record, @Param("example") MiniAppOpenRefundExtraPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenRefundExtraPo record);

    int updateByPrimaryKey(MiniAppOpenRefundExtraPo record);
}