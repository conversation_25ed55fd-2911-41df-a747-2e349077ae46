package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenRetryDao {
    long countByExample(MiniAppOpenRetryPoExample example);

    int deleteByExample(MiniAppOpenRetryPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenRetryPo record);

    int insertBatch(List<MiniAppOpenRetryPo> records);

    int insertUpdateBatch(List<MiniAppOpenRetryPo> records);

    int insert(MiniAppOpenRetryPo record);

    int insertUpdateSelective(MiniAppOpenRetryPo record);

    int insertSelective(MiniAppOpenRetryPo record);

    List<MiniAppOpenRetryPo> selectByExample(MiniAppOpenRetryPoExample example);

    MiniAppOpenRetryPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenRetryPo record, @Param("example") MiniAppOpenRetryPoExample example);

    int updateByExample(@Param("record") MiniAppOpenRetryPo record, @Param("example") MiniAppOpenRetryPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenRetryPo record);

    int updateByPrimaryKey(MiniAppOpenRetryPo record);
}