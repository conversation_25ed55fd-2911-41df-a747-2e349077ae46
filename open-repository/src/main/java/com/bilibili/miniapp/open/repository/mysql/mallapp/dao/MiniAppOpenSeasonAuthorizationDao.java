package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonAuthorizationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonAuthorizationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenSeasonAuthorizationDao {
    long countByExample(MiniAppOpenSeasonAuthorizationPoExample example);

    int deleteByExample(MiniAppOpenSeasonAuthorizationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenSeasonAuthorizationPo record);

    int insertBatch(List<MiniAppOpenSeasonAuthorizationPo> records);

    int insertUpdateBatch(List<MiniAppOpenSeasonAuthorizationPo> records);

    int insert(MiniAppOpenSeasonAuthorizationPo record);

    int insertUpdateSelective(MiniAppOpenSeasonAuthorizationPo record);

    int insertSelective(MiniAppOpenSeasonAuthorizationPo record);

    List<MiniAppOpenSeasonAuthorizationPo> selectByExample(MiniAppOpenSeasonAuthorizationPoExample example);

    MiniAppOpenSeasonAuthorizationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenSeasonAuthorizationPo record, @Param("example") MiniAppOpenSeasonAuthorizationPoExample example);

    int updateByExample(@Param("record") MiniAppOpenSeasonAuthorizationPo record, @Param("example") MiniAppOpenSeasonAuthorizationPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenSeasonAuthorizationPo record);

    int updateByPrimaryKey(MiniAppOpenSeasonAuthorizationPo record);
}