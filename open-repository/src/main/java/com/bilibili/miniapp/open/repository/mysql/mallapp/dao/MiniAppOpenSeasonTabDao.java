package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonTabPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonTabPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenSeasonTabDao {
    long countByExample(MiniAppOpenSeasonTabPoExample example);

    int deleteByExample(MiniAppOpenSeasonTabPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenSeasonTabPo record);

    int insertBatch(List<MiniAppOpenSeasonTabPo> records);

    int insertUpdateBatch(List<MiniAppOpenSeasonTabPo> records);

    int insert(MiniAppOpenSeasonTabPo record);

    int insertUpdateSelective(MiniAppOpenSeasonTabPo record);

    int insertSelective(MiniAppOpenSeasonTabPo record);

    List<MiniAppOpenSeasonTabPo> selectByExample(MiniAppOpenSeasonTabPoExample example);

    MiniAppOpenSeasonTabPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenSeasonTabPo record, @Param("example") MiniAppOpenSeasonTabPoExample example);

    int updateByExample(@Param("record") MiniAppOpenSeasonTabPo record, @Param("example") MiniAppOpenSeasonTabPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenSeasonTabPo record);

    int updateByPrimaryKey(MiniAppOpenSeasonTabPo record);
}