package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

public interface MiniAppOpenSeasonTabExtDao {

    @SelectProvider(type = MiniAppOpenSeasonTabExtSqlProvider.class, method = "queryAlreadyInTabSeasons")
    List<Long> queryAlreadyInTabSeasons(@Param("appId") String appId, @Param("tabType") int tabType, @Param("seasonIds") List<Long> seasonIds);
}