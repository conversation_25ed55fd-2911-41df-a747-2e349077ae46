package com.bilibili.miniapp.open.repository.mysql.mallapp.dao;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
public class MiniAppOpenSeasonTabExtSqlProvider {
    public String queryAlreadyInTabSeasons(@Param("appId") String appId, @Param("tabType") int tabType, @Param("seasonIds") List<Long> seasonIds) {
        return new SQL() {{
            SELECT("season_id");
            FROM("mini_app_open_season_tab");
            WHERE("app_id = #{appId}");
            WHERE("tab_type = #{tabType}");
            WHERE("season_id IN (" + seasonIds.stream().map(Object::toString).collect(Collectors.joining(",")) + ")");
            WHERE("is_deleted = 0");
        }}.toString();
    }
}
