package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenAccessPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * access_key
     */
    private String accessKey;

    /**
     * access_token
     */
    private String accessToken;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 企业id
     */
    private String companyId;

    private static final long serialVersionUID = 1L;
}