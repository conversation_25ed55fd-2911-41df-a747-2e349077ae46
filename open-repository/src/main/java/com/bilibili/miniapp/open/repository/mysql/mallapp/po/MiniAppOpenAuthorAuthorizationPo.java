package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenAuthorAuthorizationPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 内容作者mid
     */
    private Long mid;

    /**
     * 0：无效，1：生效
     */
    private Integer status;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}