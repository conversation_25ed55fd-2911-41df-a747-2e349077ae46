package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenClientSeasonUnlockPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 用户id
     */
    private String openId;

    /**
     * 剧id
     */
    private Long seasonId;

    /**
     * 集id
     */
    private Long epId;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 0-默认，1-删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}