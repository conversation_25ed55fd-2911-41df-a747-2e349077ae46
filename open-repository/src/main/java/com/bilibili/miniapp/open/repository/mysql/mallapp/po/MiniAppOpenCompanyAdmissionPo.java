package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenCompanyAdmissionPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 创建人mid
     */
    private Long mid;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 准入审核状态，0-待审核，1-审核通过，2-审核拒绝
     */
    private Integer auditStatus;

    /**
     * 编辑内容，json格式，记录用户操作的内容
     */
    private String editInfo;

    /**
     * 拒绝准入原因
     */
    private String failReason;

    /**
     * 0-有效，1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}