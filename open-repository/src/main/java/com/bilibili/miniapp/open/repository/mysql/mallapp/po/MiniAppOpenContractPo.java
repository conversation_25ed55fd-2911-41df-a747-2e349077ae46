package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenContractPo implements Serializable {
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 签约人姓名
     */
    private String signatoryName;

    /**
     * 签约人手机号
     */
    private String signatoryPhone;

    /**
     * 签约人email
     */
    private String signatoryEmail;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 合同生效开始时间
     */
    private Timestamp contractStartTime;

    /**
     * 合同生效结束时间
     */
    private Timestamp contractEndTime;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 合同状态，0-待确认签署信息，1-审核中，2-审核未通过，3-待签署，4-未生效，5-生效中，6-已失效
     */
    private Integer contractStatus;

    /**
     * 合同审核信息
     */
    private String contractAuditReason;

    /**
     * 软删除:0-有效,1-删除
     */
    private Integer isDeleted;

    private Timestamp ctime;

    private Timestamp mtime;

    /**
     * 合同变更时间
     */
    private Timestamp contractMtime;

    private static final long serialVersionUID = 1L;
}