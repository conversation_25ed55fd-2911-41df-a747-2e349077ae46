package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenContractPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenContractPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameIsNull() {
            addCriterion("signatory_name is null");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameIsNotNull() {
            addCriterion("signatory_name is not null");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameEqualTo(String value) {
            addCriterion("signatory_name =", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameNotEqualTo(String value) {
            addCriterion("signatory_name <>", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameGreaterThan(String value) {
            addCriterion("signatory_name >", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("signatory_name >=", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameLessThan(String value) {
            addCriterion("signatory_name <", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameLessThanOrEqualTo(String value) {
            addCriterion("signatory_name <=", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameLike(String value) {
            addCriterion("signatory_name like", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameNotLike(String value) {
            addCriterion("signatory_name not like", value, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameIn(List<String> values) {
            addCriterion("signatory_name in", values, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameNotIn(List<String> values) {
            addCriterion("signatory_name not in", values, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameBetween(String value1, String value2) {
            addCriterion("signatory_name between", value1, value2, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryNameNotBetween(String value1, String value2) {
            addCriterion("signatory_name not between", value1, value2, "signatoryName");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneIsNull() {
            addCriterion("signatory_phone is null");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneIsNotNull() {
            addCriterion("signatory_phone is not null");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneEqualTo(String value) {
            addCriterion("signatory_phone =", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneNotEqualTo(String value) {
            addCriterion("signatory_phone <>", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneGreaterThan(String value) {
            addCriterion("signatory_phone >", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("signatory_phone >=", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneLessThan(String value) {
            addCriterion("signatory_phone <", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneLessThanOrEqualTo(String value) {
            addCriterion("signatory_phone <=", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneLike(String value) {
            addCriterion("signatory_phone like", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneNotLike(String value) {
            addCriterion("signatory_phone not like", value, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneIn(List<String> values) {
            addCriterion("signatory_phone in", values, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneNotIn(List<String> values) {
            addCriterion("signatory_phone not in", values, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneBetween(String value1, String value2) {
            addCriterion("signatory_phone between", value1, value2, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryPhoneNotBetween(String value1, String value2) {
            addCriterion("signatory_phone not between", value1, value2, "signatoryPhone");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailIsNull() {
            addCriterion("signatory_email is null");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailIsNotNull() {
            addCriterion("signatory_email is not null");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailEqualTo(String value) {
            addCriterion("signatory_email =", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailNotEqualTo(String value) {
            addCriterion("signatory_email <>", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailGreaterThan(String value) {
            addCriterion("signatory_email >", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailGreaterThanOrEqualTo(String value) {
            addCriterion("signatory_email >=", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailLessThan(String value) {
            addCriterion("signatory_email <", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailLessThanOrEqualTo(String value) {
            addCriterion("signatory_email <=", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailLike(String value) {
            addCriterion("signatory_email like", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailNotLike(String value) {
            addCriterion("signatory_email not like", value, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailIn(List<String> values) {
            addCriterion("signatory_email in", values, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailNotIn(List<String> values) {
            addCriterion("signatory_email not in", values, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailBetween(String value1, String value2) {
            addCriterion("signatory_email between", value1, value2, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andSignatoryEmailNotBetween(String value1, String value2) {
            addCriterion("signatory_email not between", value1, value2, "signatoryEmail");
            return (Criteria) this;
        }

        public Criteria andContactAddressIsNull() {
            addCriterion("contact_address is null");
            return (Criteria) this;
        }

        public Criteria andContactAddressIsNotNull() {
            addCriterion("contact_address is not null");
            return (Criteria) this;
        }

        public Criteria andContactAddressEqualTo(String value) {
            addCriterion("contact_address =", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressNotEqualTo(String value) {
            addCriterion("contact_address <>", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressGreaterThan(String value) {
            addCriterion("contact_address >", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressGreaterThanOrEqualTo(String value) {
            addCriterion("contact_address >=", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressLessThan(String value) {
            addCriterion("contact_address <", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressLessThanOrEqualTo(String value) {
            addCriterion("contact_address <=", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressLike(String value) {
            addCriterion("contact_address like", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressNotLike(String value) {
            addCriterion("contact_address not like", value, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressIn(List<String> values) {
            addCriterion("contact_address in", values, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressNotIn(List<String> values) {
            addCriterion("contact_address not in", values, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressBetween(String value1, String value2) {
            addCriterion("contact_address between", value1, value2, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContactAddressNotBetween(String value1, String value2) {
            addCriterion("contact_address not between", value1, value2, "contactAddress");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeIsNull() {
            addCriterion("contract_start_time is null");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeIsNotNull() {
            addCriterion("contract_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeEqualTo(Timestamp value) {
            addCriterion("contract_start_time =", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeNotEqualTo(Timestamp value) {
            addCriterion("contract_start_time <>", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeGreaterThan(Timestamp value) {
            addCriterion("contract_start_time >", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("contract_start_time >=", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeLessThan(Timestamp value) {
            addCriterion("contract_start_time <", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("contract_start_time <=", value, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeIn(List<Timestamp> values) {
            addCriterion("contract_start_time in", values, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeNotIn(List<Timestamp> values) {
            addCriterion("contract_start_time not in", values, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("contract_start_time between", value1, value2, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("contract_start_time not between", value1, value2, "contractStartTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeIsNull() {
            addCriterion("contract_end_time is null");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeIsNotNull() {
            addCriterion("contract_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeEqualTo(Timestamp value) {
            addCriterion("contract_end_time =", value, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeNotEqualTo(Timestamp value) {
            addCriterion("contract_end_time <>", value, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeGreaterThan(Timestamp value) {
            addCriterion("contract_end_time >", value, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("contract_end_time >=", value, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeLessThan(Timestamp value) {
            addCriterion("contract_end_time <", value, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("contract_end_time <=", value, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeIn(List<Timestamp> values) {
            addCriterion("contract_end_time in", values, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeNotIn(List<Timestamp> values) {
            addCriterion("contract_end_time not in", values, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("contract_end_time between", value1, value2, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("contract_end_time not between", value1, value2, "contractEndTime");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNull() {
            addCriterion("contract_id is null");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNotNull() {
            addCriterion("contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andContractIdEqualTo(String value) {
            addCriterion("contract_id =", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotEqualTo(String value) {
            addCriterion("contract_id <>", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThan(String value) {
            addCriterion("contract_id >", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThanOrEqualTo(String value) {
            addCriterion("contract_id >=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThan(String value) {
            addCriterion("contract_id <", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThanOrEqualTo(String value) {
            addCriterion("contract_id <=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLike(String value) {
            addCriterion("contract_id like", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotLike(String value) {
            addCriterion("contract_id not like", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdIn(List<String> values) {
            addCriterion("contract_id in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotIn(List<String> values) {
            addCriterion("contract_id not in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdBetween(String value1, String value2) {
            addCriterion("contract_id between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotBetween(String value1, String value2) {
            addCriterion("contract_id not between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNull() {
            addCriterion("contract_status is null");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNotNull() {
            addCriterion("contract_status is not null");
            return (Criteria) this;
        }

        public Criteria andContractStatusEqualTo(Integer value) {
            addCriterion("contract_status =", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotEqualTo(Integer value) {
            addCriterion("contract_status <>", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThan(Integer value) {
            addCriterion("contract_status >", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_status >=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThan(Integer value) {
            addCriterion("contract_status <", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThanOrEqualTo(Integer value) {
            addCriterion("contract_status <=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusIn(List<Integer> values) {
            addCriterion("contract_status in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotIn(List<Integer> values) {
            addCriterion("contract_status not in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusBetween(Integer value1, Integer value2) {
            addCriterion("contract_status between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_status not between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonIsNull() {
            addCriterion("contract_audit_reason is null");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonIsNotNull() {
            addCriterion("contract_audit_reason is not null");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonEqualTo(String value) {
            addCriterion("contract_audit_reason =", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonNotEqualTo(String value) {
            addCriterion("contract_audit_reason <>", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonGreaterThan(String value) {
            addCriterion("contract_audit_reason >", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonGreaterThanOrEqualTo(String value) {
            addCriterion("contract_audit_reason >=", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonLessThan(String value) {
            addCriterion("contract_audit_reason <", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonLessThanOrEqualTo(String value) {
            addCriterion("contract_audit_reason <=", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonLike(String value) {
            addCriterion("contract_audit_reason like", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonNotLike(String value) {
            addCriterion("contract_audit_reason not like", value, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonIn(List<String> values) {
            addCriterion("contract_audit_reason in", values, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonNotIn(List<String> values) {
            addCriterion("contract_audit_reason not in", values, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonBetween(String value1, String value2) {
            addCriterion("contract_audit_reason between", value1, value2, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andContractAuditReasonNotBetween(String value1, String value2) {
            addCriterion("contract_audit_reason not between", value1, value2, "contractAuditReason");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeIsNull() {
            addCriterion("contract_mtime is null");
            return (Criteria) this;
        }

        public Criteria andContractMtimeIsNotNull() {
            addCriterion("contract_mtime is not null");
            return (Criteria) this;
        }

        public Criteria andContractMtimeEqualTo(Timestamp value) {
            addCriterion("contract_mtime =", value, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeNotEqualTo(Timestamp value) {
            addCriterion("contract_mtime <>", value, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeGreaterThan(Timestamp value) {
            addCriterion("contract_mtime >", value, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("contract_mtime >=", value, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeLessThan(Timestamp value) {
            addCriterion("contract_mtime <", value, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("contract_mtime <=", value, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeIn(List<Timestamp> values) {
            addCriterion("contract_mtime in", values, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeNotIn(List<Timestamp> values) {
            addCriterion("contract_mtime not in", values, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("contract_mtime between", value1, value2, "contractMtime");
            return (Criteria) this;
        }

        public Criteria andContractMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("contract_mtime not between", value1, value2, "contractMtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}