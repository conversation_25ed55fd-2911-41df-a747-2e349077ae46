package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenCustomLinkPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 自定义path
     */
    private String customPath;

    /**
     * 自定义参数
     */
    private String customParams;

    /**
     * 是否删除，0默认，不删除；1删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}