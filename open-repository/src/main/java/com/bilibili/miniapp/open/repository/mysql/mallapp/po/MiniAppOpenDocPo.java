package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenDocPo implements Serializable {
    /**
     * 节点id，自增主键
     */
    private Long id;

    /**
     * 节点，是对目录或者API的标识，比如miniapp.open.season.query
     */
    private String node;

    /**
     * 节点的名称，比如目录或者API名称
     */
    private String name;

    /**
     * 0：非叶子节点，1：叶子节点
     */
    private Integer type;

    /**
     * 顺序，从0开始，作用于同级才有意义
     */
    private Integer seq;

    /**
     * doc的路径，一般在是在doc时才有值
     */
    private String location;

    /**
     * 版本，采用二阶版本，xx.yy
     */
    private String version;

    /**
     * API的生效标识，0：不生效，1：生效
     */
    private Integer status;

    /**
     * 父级节点id
     */
    private Long parentId;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}