package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenFinanceInfoPo implements Serializable {
    private Long id;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 银行账户
     */
    private String bankAccountNumber;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 开户支行
     */
    private String bankBranchName;

    /**
     * 发票类型：1-增值税专用发票
     */
    private Integer invoiceType;

    /**
     * 税率类型，1：一般纳税人-6%
     */
    private Integer taxType;

    /**
     * 开票项目类别，1-信息技术服务*信息服务费,2-广告服务*广告发布费
     */
    private Integer invoiceItemCategory;

    /**
     * 软删除:0-有效,1-删除
     */
    private Integer isDeleted;

    private Timestamp ctime;

    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}