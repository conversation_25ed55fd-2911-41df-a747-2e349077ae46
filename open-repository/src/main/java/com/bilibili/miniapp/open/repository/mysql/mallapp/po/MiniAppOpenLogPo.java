package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenLogPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 目标对象id
     */
    private Long objId;

    /**
     * 操作对象类型，一般作为业务标识
     */
    private Integer objType;

    /**
     * 0:insert;1:update;2:delete
     */
    private Integer operateType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人IP地址
     */
    private String ip;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除:0是有效,1是删除
     */
    private Integer isDeleted;

    /**
     * 日志内容
     */
    private String content;

    private static final long serialVersionUID = 1L;
}