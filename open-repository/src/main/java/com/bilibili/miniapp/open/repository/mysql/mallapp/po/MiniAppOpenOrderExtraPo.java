package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenOrderExtraPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者透传的拓展信息
     */
    private String devExtraData;

    /**
     * trace信息
     */
    private String traceInfo;

    /**
     * 支付信息
     */
    private String payParamInfo;

    /**
     * 支付回调信息
     */
    private String payNotifyInfo;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除:0是有效,1是删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}