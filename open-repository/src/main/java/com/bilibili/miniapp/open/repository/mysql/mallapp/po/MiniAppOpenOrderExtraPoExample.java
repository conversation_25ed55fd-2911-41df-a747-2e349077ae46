package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenOrderExtraPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenOrderExtraPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Long value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Long value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Long value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Long value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Long> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Long> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Long value1, Long value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataIsNull() {
            addCriterion("dev_extra_data is null");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataIsNotNull() {
            addCriterion("dev_extra_data is not null");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataEqualTo(String value) {
            addCriterion("dev_extra_data =", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotEqualTo(String value) {
            addCriterion("dev_extra_data <>", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataGreaterThan(String value) {
            addCriterion("dev_extra_data >", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataGreaterThanOrEqualTo(String value) {
            addCriterion("dev_extra_data >=", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataLessThan(String value) {
            addCriterion("dev_extra_data <", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataLessThanOrEqualTo(String value) {
            addCriterion("dev_extra_data <=", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataLike(String value) {
            addCriterion("dev_extra_data like", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotLike(String value) {
            addCriterion("dev_extra_data not like", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataIn(List<String> values) {
            addCriterion("dev_extra_data in", values, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotIn(List<String> values) {
            addCriterion("dev_extra_data not in", values, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataBetween(String value1, String value2) {
            addCriterion("dev_extra_data between", value1, value2, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotBetween(String value1, String value2) {
            addCriterion("dev_extra_data not between", value1, value2, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andTraceInfoIsNull() {
            addCriterion("trace_info is null");
            return (Criteria) this;
        }

        public Criteria andTraceInfoIsNotNull() {
            addCriterion("trace_info is not null");
            return (Criteria) this;
        }

        public Criteria andTraceInfoEqualTo(String value) {
            addCriterion("trace_info =", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoNotEqualTo(String value) {
            addCriterion("trace_info <>", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoGreaterThan(String value) {
            addCriterion("trace_info >", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoGreaterThanOrEqualTo(String value) {
            addCriterion("trace_info >=", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoLessThan(String value) {
            addCriterion("trace_info <", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoLessThanOrEqualTo(String value) {
            addCriterion("trace_info <=", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoLike(String value) {
            addCriterion("trace_info like", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoNotLike(String value) {
            addCriterion("trace_info not like", value, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoIn(List<String> values) {
            addCriterion("trace_info in", values, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoNotIn(List<String> values) {
            addCriterion("trace_info not in", values, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoBetween(String value1, String value2) {
            addCriterion("trace_info between", value1, value2, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andTraceInfoNotBetween(String value1, String value2) {
            addCriterion("trace_info not between", value1, value2, "traceInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoIsNull() {
            addCriterion("pay_param_info is null");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoIsNotNull() {
            addCriterion("pay_param_info is not null");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoEqualTo(String value) {
            addCriterion("pay_param_info =", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoNotEqualTo(String value) {
            addCriterion("pay_param_info <>", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoGreaterThan(String value) {
            addCriterion("pay_param_info >", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoGreaterThanOrEqualTo(String value) {
            addCriterion("pay_param_info >=", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoLessThan(String value) {
            addCriterion("pay_param_info <", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoLessThanOrEqualTo(String value) {
            addCriterion("pay_param_info <=", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoLike(String value) {
            addCriterion("pay_param_info like", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoNotLike(String value) {
            addCriterion("pay_param_info not like", value, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoIn(List<String> values) {
            addCriterion("pay_param_info in", values, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoNotIn(List<String> values) {
            addCriterion("pay_param_info not in", values, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoBetween(String value1, String value2) {
            addCriterion("pay_param_info between", value1, value2, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayParamInfoNotBetween(String value1, String value2) {
            addCriterion("pay_param_info not between", value1, value2, "payParamInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoIsNull() {
            addCriterion("pay_notify_info is null");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoIsNotNull() {
            addCriterion("pay_notify_info is not null");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoEqualTo(String value) {
            addCriterion("pay_notify_info =", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoNotEqualTo(String value) {
            addCriterion("pay_notify_info <>", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoGreaterThan(String value) {
            addCriterion("pay_notify_info >", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoGreaterThanOrEqualTo(String value) {
            addCriterion("pay_notify_info >=", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoLessThan(String value) {
            addCriterion("pay_notify_info <", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoLessThanOrEqualTo(String value) {
            addCriterion("pay_notify_info <=", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoLike(String value) {
            addCriterion("pay_notify_info like", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoNotLike(String value) {
            addCriterion("pay_notify_info not like", value, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoIn(List<String> values) {
            addCriterion("pay_notify_info in", values, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoNotIn(List<String> values) {
            addCriterion("pay_notify_info not in", values, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoBetween(String value1, String value2) {
            addCriterion("pay_notify_info between", value1, value2, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andPayNotifyInfoNotBetween(String value1, String value2) {
            addCriterion("pay_notify_info not between", value1, value2, "payNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}