package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenOrderIdSegmentPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * ip
     */
    private String ip;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除:0是有效,1是删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}