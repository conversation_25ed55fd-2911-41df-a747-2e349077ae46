package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenRefundExtraPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 退款表的主键
     */
    private Long refundId;

    /**
     * 开发者透传的拓展信息
     */
    private String devExtraData;

    /**
     * 退款信息
     */
    private String refundParamInfo;

    /**
     * 退款回调信息
     */
    private String refundNotifyInfo;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除:0是有效,1是删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}