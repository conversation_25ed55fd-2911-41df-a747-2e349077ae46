package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenRefundExtraPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenRefundExtraPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRefundIdIsNull() {
            addCriterion("refund_id is null");
            return (Criteria) this;
        }

        public Criteria andRefundIdIsNotNull() {
            addCriterion("refund_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefundIdEqualTo(Long value) {
            addCriterion("refund_id =", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdNotEqualTo(Long value) {
            addCriterion("refund_id <>", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdGreaterThan(Long value) {
            addCriterion("refund_id >", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdGreaterThanOrEqualTo(Long value) {
            addCriterion("refund_id >=", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdLessThan(Long value) {
            addCriterion("refund_id <", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdLessThanOrEqualTo(Long value) {
            addCriterion("refund_id <=", value, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdIn(List<Long> values) {
            addCriterion("refund_id in", values, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdNotIn(List<Long> values) {
            addCriterion("refund_id not in", values, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdBetween(Long value1, Long value2) {
            addCriterion("refund_id between", value1, value2, "refundId");
            return (Criteria) this;
        }

        public Criteria andRefundIdNotBetween(Long value1, Long value2) {
            addCriterion("refund_id not between", value1, value2, "refundId");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataIsNull() {
            addCriterion("dev_extra_data is null");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataIsNotNull() {
            addCriterion("dev_extra_data is not null");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataEqualTo(String value) {
            addCriterion("dev_extra_data =", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotEqualTo(String value) {
            addCriterion("dev_extra_data <>", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataGreaterThan(String value) {
            addCriterion("dev_extra_data >", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataGreaterThanOrEqualTo(String value) {
            addCriterion("dev_extra_data >=", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataLessThan(String value) {
            addCriterion("dev_extra_data <", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataLessThanOrEqualTo(String value) {
            addCriterion("dev_extra_data <=", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataLike(String value) {
            addCriterion("dev_extra_data like", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotLike(String value) {
            addCriterion("dev_extra_data not like", value, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataIn(List<String> values) {
            addCriterion("dev_extra_data in", values, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotIn(List<String> values) {
            addCriterion("dev_extra_data not in", values, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataBetween(String value1, String value2) {
            addCriterion("dev_extra_data between", value1, value2, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andDevExtraDataNotBetween(String value1, String value2) {
            addCriterion("dev_extra_data not between", value1, value2, "devExtraData");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoIsNull() {
            addCriterion("refund_param_info is null");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoIsNotNull() {
            addCriterion("refund_param_info is not null");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoEqualTo(String value) {
            addCriterion("refund_param_info =", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoNotEqualTo(String value) {
            addCriterion("refund_param_info <>", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoGreaterThan(String value) {
            addCriterion("refund_param_info >", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoGreaterThanOrEqualTo(String value) {
            addCriterion("refund_param_info >=", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoLessThan(String value) {
            addCriterion("refund_param_info <", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoLessThanOrEqualTo(String value) {
            addCriterion("refund_param_info <=", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoLike(String value) {
            addCriterion("refund_param_info like", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoNotLike(String value) {
            addCriterion("refund_param_info not like", value, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoIn(List<String> values) {
            addCriterion("refund_param_info in", values, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoNotIn(List<String> values) {
            addCriterion("refund_param_info not in", values, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoBetween(String value1, String value2) {
            addCriterion("refund_param_info between", value1, value2, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundParamInfoNotBetween(String value1, String value2) {
            addCriterion("refund_param_info not between", value1, value2, "refundParamInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoIsNull() {
            addCriterion("refund_notify_info is null");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoIsNotNull() {
            addCriterion("refund_notify_info is not null");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoEqualTo(String value) {
            addCriterion("refund_notify_info =", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoNotEqualTo(String value) {
            addCriterion("refund_notify_info <>", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoGreaterThan(String value) {
            addCriterion("refund_notify_info >", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoGreaterThanOrEqualTo(String value) {
            addCriterion("refund_notify_info >=", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoLessThan(String value) {
            addCriterion("refund_notify_info <", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoLessThanOrEqualTo(String value) {
            addCriterion("refund_notify_info <=", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoLike(String value) {
            addCriterion("refund_notify_info like", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoNotLike(String value) {
            addCriterion("refund_notify_info not like", value, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoIn(List<String> values) {
            addCriterion("refund_notify_info in", values, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoNotIn(List<String> values) {
            addCriterion("refund_notify_info not in", values, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoBetween(String value1, String value2) {
            addCriterion("refund_notify_info between", value1, value2, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andRefundNotifyInfoNotBetween(String value1, String value2) {
            addCriterion("refund_notify_info not between", value1, value2, "refundNotifyInfo");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}