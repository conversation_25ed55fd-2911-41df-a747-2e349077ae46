package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenRefundPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者退款批次id，如果后续支持部分退款，则同一订单的不同退款记录批次id不同
     */
    private String devRefundId;

    /**
     * 支付中台退款批次id
     */
    private String payPlatformRefundId;

    /**
     * 退款金额，单位分
     */
    private Long refundAmount;

    /**
     * 退款状态，0：尚未发起退款，1：发起退款成功（退款中），2：发起退款失败，3：退款成功，4：退款失败
     */
    private Integer refundStatus;

    /**
     * 开发者退款回调状态，0：未回调，1：回调成功，2：回调失败
     */
    private Integer notifyStatus;

    /**
     * 开发者退款回调接口
     */
    private String notifyUrl;

    /**
     * 退款描述
     */
    private String refundDesc;

    /**
     * trace_id，用于支付交易追踪
     */
    private String traceId;

    /**
     * 退款时间
     */
    private Timestamp refundTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除:0是有效,1是删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}