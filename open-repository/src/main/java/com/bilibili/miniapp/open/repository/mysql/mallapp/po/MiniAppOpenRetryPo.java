package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenRetryPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 重试所需要的业务信息，尽可能简短，json格式
     */
    private String bizData;

    /**
     * 重试时的请求id，因为统一业务场景会有多次重试任务，比如订单变更新消息可能会多次，则每次分配不同的id
     */
    private String reqId;

    /**
     * 重试状态，0：失败，1：重试成功，2：失败终态（达到重试上限后依然失败）
     */
    private Integer retryStatus;

    /**
     * 重试次数（含首次调用）
     */
    private Integer retryCount;

    /**
     * 下次重试的时间
     */
    private Timestamp nextTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除：0是有效,1是删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}