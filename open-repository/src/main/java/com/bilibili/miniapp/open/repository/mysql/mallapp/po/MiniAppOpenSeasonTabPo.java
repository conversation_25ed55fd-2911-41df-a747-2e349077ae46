package com.bilibili.miniapp.open.repository.mysql.mallapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenSeasonTabPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 0-热门推荐，1-新剧速递
     */
    private Integer tabType;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 剧id
     */
    private Long seasonId;

    /**
     * 0-有效，1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * up主mid
     */
    private Long upMid;

    private static final long serialVersionUID = 1L;
}