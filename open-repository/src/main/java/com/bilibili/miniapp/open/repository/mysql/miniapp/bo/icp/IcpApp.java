package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.IcpApproveTypeEnum;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpApp {
    private Long wzid;
    private String name;
    private String appId;
    private Integer serviceType;
    private Integer approvalPre;
    private Integer approvalType;
    private String approvalIsbnNo;
    private List<String> approvalAttachment;
    private String remark;
    private Integer fzrLicenseType;
    private String verifyPhoto;
    private String fzrCard;
    private String fzrCardNo;
    private String fzrName;
    private Timestamp fzrCardBegin;
    private Timestamp fzrCardEnd;
    private Integer fzrCardLongEffect;
    private String fzrPhone;
    private String fzrEmail;
    private String fzrEmergency;

    public void validate() {
        AssertUtil.notNull(serviceType, ErrorCodeType.BAD_PARAMETER.getCode(), "服务内容不能为空");
        AssertUtil.notNull(approvalPre, ErrorCodeType.BAD_PARAMETER.getCode(), "备案前置不能为空");
        if (approvalPre == 1) {
            AssertUtil.notNull(approvalType, ErrorCodeType.BAD_PARAMETER.getCode(), "备案类型不能为空");
            AssertUtil.notNull(IcpApproveTypeEnum.getByType(approvalType), ErrorCodeType.BAD_PARAMETER.getCode(), "备案类型不合法");
            AssertUtil.hasText(approvalIsbnNo, ErrorCodeType.BAD_PARAMETER.getCode(), "备案号不能为空");
            AssertUtil.notEmpty(approvalAttachment, ErrorCodeType.BAD_PARAMETER.getCode(), "请上传备案附件");
        }
        AssertUtil.notNull(fzrLicenseType, ErrorCodeType.BAD_PARAMETER.getCode(), "证件类型不能为空");
        AssertUtil.hasText(fzrName, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件名称");
        AssertUtil.hasText(fzrCardNo, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件号码");
        AssertUtil.notNull(fzrCardLongEffect, ErrorCodeType.BAD_PARAMETER.getCode(), "证件有效期不能为空");
        AssertUtil.notNull(fzrCardBegin, ErrorCodeType.BAD_PARAMETER.getCode(), "证件开始时间不能为空");
        if (fzrCardLongEffect == 1) {
            AssertUtil.notNull(fzrCardEnd, ErrorCodeType.BAD_PARAMETER.getCode(), "证件结束时间不能为空");
            AssertUtil.isTrue(fzrCardBegin.before(fzrCardEnd), ErrorCodeType.BAD_PARAMETER.getCode(), "证件开始时间不能大于结束时间");
        }
        AssertUtil.hasText(fzrPhone, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件手机号");
        AssertUtil.hasText(fzrEmail, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件邮箱");
        AssertUtil.hasText(fzrEmergency, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件紧急联系人");
        AssertUtil.hasText(verifyPhoto, ErrorCodeType.BAD_PARAMETER.getCode(), "请通过人证核验获取采集照片");
        AssertUtil.hasText(fzrCard, ErrorCodeType.BAD_PARAMETER.getCode(), "请通过人证核验获取获取证件照片");
    }

    public MiniAppOpenIcpAppPo toPo() {
        return MiniAppOpenIcpAppPo.builder()
                .name(name)
                .wzid(wzid)
                .appId(appId)
                .serviceType(serviceType)
                .approvalPre(approvalPre)
                .approvalType(approvalType)
                .approvalIsbnNo(approvalIsbnNo)
                .approvalAttachment(JSON.toJSONString(approvalAttachment))
                .remark(remark)
                .fzrLicenseType(fzrLicenseType)
                .fzrCard(fzrCard)
                .verifyPhoto(verifyPhoto)
                .fzrCardNo(fzrCardNo)
                .fzrName(fzrName)
                .fzrCardBegin(fzrCardBegin == null ? null : TimeUtil.timestampToDateTimeString(fzrCardBegin))
                .fzrCardEnd(fzrCardEnd == null ? null : TimeUtil.timestampToDateTimeString(fzrCardEnd))
                .fzrCardLongEffect(fzrCardLongEffect)
                .fzrPhone(fzrPhone)
                .fzrEmail(fzrEmail)
                .fzrEmergency(fzrEmergency)
                .build();
    }

    public static IcpApp fromPo(MiniAppOpenIcpAppPo po) {
        if (po == null) {
            return null;
        }
        return IcpApp.builder()
                .name(po.getName())
                .wzid(po.getWzid())
                .appId(po.getAppId())
                .serviceType(po.getServiceType())
                .approvalPre(po.getApprovalPre())
                .approvalType(po.getApprovalType())
                .approvalIsbnNo(po.getApprovalIsbnNo())
                .approvalAttachment(JSON.parseObject(po.getApprovalAttachment(), new TypeReference<>() {
                }))
                .remark(po.getRemark())
                .fzrLicenseType(po.getFzrLicenseType())
                .verifyPhoto(po.getVerifyPhoto())
                .fzrCard(po.getFzrCard())
                .fzrCardNo(po.getFzrCardNo())
                .fzrName(po.getFzrName())
                .fzrCardBegin(po.getFzrCardBegin() == null ? null : TimeUtil.getTimestampByDateTimeStr(po.getFzrCardBegin()))
                .fzrCardEnd(po.getFzrCardEnd() == null ? null : TimeUtil.getTimestampByDateTimeStr(po.getFzrCardEnd()))
                .fzrCardLongEffect(po.getFzrCardLongEffect())
                .fzrPhone(po.getFzrPhone())
                .fzrEmail(po.getFzrEmail())
                .fzrEmergency(po.getFzrEmergency())
                .build();
    }
}
