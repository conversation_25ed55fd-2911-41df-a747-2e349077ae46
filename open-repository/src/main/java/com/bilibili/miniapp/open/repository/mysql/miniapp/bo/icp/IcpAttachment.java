package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAttachmentPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityMaterialPo;
import lombok.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpAttachment {
    private Integer type;
    private Integer format;
    private String content;

    @AllArgsConstructor
    @Getter
    public enum AttachmentFormat {
        //  附件文件格式，1 为.jpg，2 为.png，3 为.mp4
        JPG(1, "jpg"),
        PNG(2, "png"),
        MP4(3, "mp4");

        private final Integer code;
        private final String desc;

        public static AttachmentFormat fromCode(Integer code) {
            for (AttachmentFormat format : AttachmentFormat.values()) {
                if (format.getCode().equals(code)) {
                    return format;
                }
            }
            return null;
        }
    }

    public void validate() {
        AssertUtil.notNull(type, ErrorCodeType.BAD_PARAMETER.getCode(), "附件类型不能为空");
        AssertUtil.notNull(format, ErrorCodeType.BAD_PARAMETER.getCode(), "附件格式不能为空");
        AssertUtil.hasText(content, ErrorCodeType.BAD_PARAMETER.getCode(), "请上传附件");
    }

    public MiniAppOpenIcpAttachmentPo toPo() {
        return MiniAppOpenIcpAttachmentPo.builder()
                .type(type)
                .format(format)
                .content(content)
                .build();
    }

    public MiniAppOpenIcpIdentityMaterialPo toIdentityMaterialPo(Long identityId) {
        return MiniAppOpenIcpIdentityMaterialPo.builder()
                .type(type)
                .format(format)
                .content(content)
                .build();
    }

    public static IcpAttachment fromIdentityMaterialPo(MiniAppOpenIcpIdentityMaterialPo po) {
        if (po == null) {
            return null;
        }
        return IcpAttachment.builder()
                .type(po.getType())
                .format(po.getFormat())
                .content(po.getContent())
                .build();
    }

    public static IcpAttachment fromPo(MiniAppOpenIcpAttachmentPo po) {
        if (po == null) {
            return null;
        }
        return IcpAttachment.builder()
                .type(po.getType())
                .format(po.getFormat())
                .content(po.getContent())
                .build();
    }
}
