package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpCompany {
    private String companyId;
    private Integer type;
    private Integer licenseType;
    private String licensePhoto;
    private String name;
    private String licenseNo;
    private Integer licenseProvince;
    private Integer licenseCity;
    private Integer licenseCounty;
    private String licenseDetailAddress;
    private Integer contactProvince;
    private Integer contactCity;
    private Integer contactCounty;
    private String contactDetailAddress;
    private String remark;
    private Integer fzrLicenseType;
    private String fzrCardNo;
    private String fzrName;
    private String fzrCardFront;
    private String fzrCardReverse;
    private Timestamp fzrCardBegin;
    private Timestamp fzrCardEnd;
    private Integer fzrCardLongEffect;
    private String fzrPhone;
    private String fzrEmail;
    private String fzrEmergency;

    public void validate() {
        AssertUtil.notNull(type, ErrorCodeType.BAD_PARAMETER.getCode(), "主体类型不能为空");
        AssertUtil.notNull(licenseType, ErrorCodeType.BAD_PARAMETER.getCode(), "证件类型不能为空");
        AssertUtil.hasText(licensePhoto, ErrorCodeType.BAD_PARAMETER.getCode(), "请上传证件照片");
        AssertUtil.hasText(name, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件名称");
        AssertUtil.hasText(licenseNo, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件号码");
        AssertUtil.notNull(licenseProvince, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件省份");
        AssertUtil.notNull(licenseCity, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件城市");
        AssertUtil.notNull(licenseCounty, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件区县");
        AssertUtil.hasText(licenseDetailAddress, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写证件详细地址");
        AssertUtil.notNull(contactProvince, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写联系省份");
        AssertUtil.notNull(contactCity, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写联系城市");
        AssertUtil.notNull(contactCounty, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写联系区县");
        AssertUtil.hasText(contactDetailAddress, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写联系详细地址");
        AssertUtil.hasText(fzrCardNo, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人证件号码");
        AssertUtil.hasText(fzrName, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人姓名");
        AssertUtil.hasText(fzrCardFront, ErrorCodeType.BAD_PARAMETER.getCode(), "请上传法人证件正面照");
        AssertUtil.hasText(fzrCardReverse, ErrorCodeType.BAD_PARAMETER.getCode(), "请上传法人证件反面照");
        AssertUtil.notNull(fzrCardBegin, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人证件有效期开始时间");
        if (Objects.equals(fzrCardLongEffect, 0)) {
            AssertUtil.notNull(fzrCardEnd, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人证件有效期结束时间");
            AssertUtil.isTrue(fzrCardBegin.before(fzrCardEnd), ErrorCodeType.BAD_PARAMETER.getCode(), "法人证件有效期开始时间不能大于结束时间");
        }
        AssertUtil.hasText(fzrPhone, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人手机号");
        AssertUtil.hasText(fzrEmail, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人邮箱");
        AssertUtil.hasText(fzrEmergency, ErrorCodeType.BAD_PARAMETER.getCode(), "请填写法人紧急联系人");
        AssertUtil.isTrue(!StringUtils.equals(fzrPhone, fzrEmergency), ErrorCodeType.BAD_PARAMETER.getCode(), "法人手机号和法人紧急联系人不能相同");
    }

    public MiniAppOpenIcpCompanyPo toPo() {
        return MiniAppOpenIcpCompanyPo.builder()
                .companyId(companyId)
                .type(type)
                .licenseType(licenseType)
                .licensePhoto(licensePhoto)
                .name(name)
                .licenseNo(licenseNo)
                .licenseProvince(licenseProvince)
                .licenseCity(licenseCity)
                .licenseCounty(licenseCounty)
                .licenseDetailAddress(licenseDetailAddress)
                .contactProvince(contactProvince)
                .contactCity(contactCity)
                .contactCounty(contactCounty)
                .contactDetailAddress(contactDetailAddress)
                .remark(remark)
                .fzrLicenseType(fzrLicenseType)
                .fzrCardNo(fzrCardNo)
                .fzrName(fzrName)
                .fzrCardFront(fzrCardFront)
                .fzrCardReverse(fzrCardReverse)
                .fzrCardBegin(fzrCardBegin == null ? null : TimeUtil.timestampToDateTimeString(fzrCardBegin))
                .fzrCardEnd(fzrCardEnd == null ? null : TimeUtil.timestampToDateTimeString(fzrCardEnd))
                .fzrCardLongEffect(fzrCardLongEffect)
                .fzrPhone(fzrPhone)
                .fzrEmail(fzrEmail)
                .fzrEmergency(fzrEmergency)
                .build();
    }

    public static IcpCompany fromPo(MiniAppOpenIcpCompanyPo po) {
        if (po == null) {
            return null;
        }
        return IcpCompany.builder()
                .companyId(po.getCompanyId())
                .type(po.getType())
                .licenseType(po.getLicenseType())
                .licensePhoto(po.getLicensePhoto())
                .name(po.getName())
                .licenseNo(po.getLicenseNo())
                .licenseProvince(po.getLicenseProvince())
                .licenseCity(po.getLicenseCity())
                .licenseCounty(po.getLicenseCounty())
                .licenseDetailAddress(po.getLicenseDetailAddress())
                .contactProvince(po.getContactProvince())
                .contactCity(po.getContactCity())
                .contactCounty(po.getContactCounty())
                .contactDetailAddress(po.getContactDetailAddress())
                .remark(po.getRemark())
                .fzrLicenseType(po.getFzrLicenseType())
                .fzrCardNo(po.getFzrCardNo())
                .fzrName(po.getFzrName())
                .fzrCardFront(po.getFzrCardFront())
                .fzrCardReverse(po.getFzrCardReverse())
                .fzrCardBegin(StringUtils.isBlank(po.getFzrCardBegin()) ? null : TimeUtil.getTimestampByDateTimeStr(po.getFzrCardBegin()))
                .fzrCardEnd(StringUtils.isBlank(po.getFzrCardEnd()) ? null : TimeUtil.getTimestampByDateTimeStr(po.getFzrCardEnd()))
                .fzrCardLongEffect(po.getFzrCardLongEffect())
                .fzrPhone(po.getFzrPhone())
                .fzrEmail(po.getFzrEmail())
                .fzrEmergency(po.getFzrEmergency())
                .build();
    }
}
