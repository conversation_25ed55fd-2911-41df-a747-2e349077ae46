package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.util.GsonUtil;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class IcpPlatformAudit {

    private Long id;

    private Long flowId;

    private String appId;

    /**
     * 审核状态 0-待审核 1-审核通过 2-审核不通过
     */
    private Integer auditStatus;

    /**
     * 审核人
     */
    private String operator;

    /**
     * 提审时间
     */
    private Timestamp submitTime;

    /**
     * 审核时间
     */
    private Timestamp auditTime;

    /**
     * 审核失败原因
     */
    private List<IcpAuditResult> failReasons;

    @Data
    public static class IcpAuditResult {
        private Integer obj;
        private String field;
        private String reason;
        private String name;
    }

    public MiniAppOpenIcpAuditPo toPo() {
        MiniAppOpenIcpAuditPo po = new MiniAppOpenIcpAuditPo();
        po.setId(this.id);
        po.setFlowId(this.flowId);
        po.setAppId(this.appId);
        po.setAuditStatus(this.auditStatus);
        po.setOperator(this.operator);
        po.setSubmitTime(this.submitTime);
        po.setAuditTime(this.auditTime == null ? null : TimeUtil.timestampToDateTimeString(this.auditTime));
        po.setAuditResult(GsonUtil.toJson(this.failReasons));
        return po;
    }

    public static IcpPlatformAudit fromPo(MiniAppOpenIcpAuditPo po) {
        if (po == null) {
            return null;
        }
        boolean notAudit = po.getAuditStatus() == 0;
        return IcpPlatformAudit.builder()
                .appId(po.getAppId())
                .id(po.getId())
                .flowId(po.getFlowId())
                .auditStatus(po.getAuditStatus())
                .operator(notAudit ? null : po.getOperator())
                .auditTime(notAudit ? null : TimeUtil.getTimestampByDateTimeStr(po.getAuditTime()))
                .submitTime(po.getSubmitTime())
                .failReasons(notAudit ? List.of() : GsonUtil.toList(po.getAuditResult(), IcpAuditResult.class))
                .build();
    }
}
