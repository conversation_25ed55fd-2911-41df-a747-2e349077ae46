package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPoExample;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpPlatformAuditCondition {

    private List<String> appIdList;

    private List<Integer> auditStatusList;

    private Long flowId;

    private String orderBy;

    private Page page;


    public MiniAppOpenIcpAuditPoExample toExample() {
        MiniAppOpenIcpAuditPoExample example = new MiniAppOpenIcpAuditPoExample();
        MiniAppOpenIcpAuditPoExample.Criteria criteria = example.createCriteria();
        if (appIdList != null && !appIdList.isEmpty()) {
            criteria.andAppIdIn(appIdList);
        }
        if (auditStatusList != null && !auditStatusList.isEmpty()) {
            criteria.andAuditStatusIn(auditStatusList);
        }
        if (flowId != null) {
            criteria.andFlowIdEqualTo(flowId);
        }
        if (StringUtils.isNotBlank(orderBy)) {
            example.setOrderByClause(orderBy);
        }
        if (Objects.nonNull(page)) {
            example.setLimit(page.getLimit());
            example.setOffset(page.getOffset());
        }
        return example;
    }
}
