package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpReportInfo {
    private Long flowId;
    private String appId;
    private IcpCompany company;
    private IcpApp app;
    private List<IcpAttachment> attachment;

    public MiniAppOpenIcpPo toPo() {
        return MiniAppOpenIcpPo.builder()
                .appId(appId)
                .id(flowId)
                .build();
    }

    public void validate() {
        AssertUtil.notNull(company, ErrorCodeType.BAD_PARAMETER.getCode(), "主体信息不能为空");
        AssertUtil.notNull(app, ErrorCodeType.BAD_PARAMETER.getCode(), "小程序信息不能为空");
        AssertUtil.isTrue(StringUtils.equals(appId, app.getAppId()), ErrorCodeType.BAD_PARAMETER.getCode(), "小程序id不一致");
        company.validate();
        app.validate();
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachment.forEach(IcpAttachment::validate);
        }
    }
}
