package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpReportState {

    /**
     * 流程id
     */
    private Long flowId;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序的备案编号
     */
    private String recordNumber;

    /**
     * CP提交备案流程的时间
     */
    private Timestamp submitTime;

    /**
     * 备案流程状态：0-尚未备案，1-平台审核中，2-平台审核未通过，3-工信部短信核验中，4-工信部短信核验超时，5-管局审核中，6-管局审核未通过，7-备案已完成
     */
    private Integer flowStatus;

    /**
     * 管局审核状态：0-未提交管局，1-待审核，2-审核通过，3-审核拒绝
     */
    private Integer govAuditState;

    /**
     * 管局审核时间
     */
    private Timestamp govAuditTime;

    /**
     * 管局审核结果代码
     */
    private Long govAuditCode;

    /**
     * 管局审核代码描述
     */
    private String govAuditMsg;

    /**
     * 管局审核意见
     */
    private String govAuditDescription;

    /**
     * 平台审核信息
     */
    private IcpPlatformAudit platformAudit;

    /**
     * 是否发送工信部审核
     */
    private Integer reportStatus;

    public static IcpReportState fromPo(MiniAppOpenIcpPo po) {
        boolean notAudit = po.getGovAuditState() < 2;
        return IcpReportState.builder()
                .flowId(po.getId())
                .appId(po.getAppId())
                .recordNumber(po.getRecordNumber())
                .submitTime(po.getSubmitTime())
                .flowStatus(po.getFlowStatus())
                .govAuditState(po.getGovAuditState())
                .govAuditTime(notAudit ? null : TimeUtil.getTimestampByDateTimeStr(po.getGovAuditTime()))
                .govAuditCode(notAudit ? null : po.getGovAuditCode())
                .govAuditMsg(notAudit ? null : po.getGovAuditMsg())
                .govAuditDescription(notAudit ? null : po.getGovAuditDescription())
                .reportStatus(po.getReportStatus())
                .build();
    }

    public MiniAppOpenIcpPo toPo() {
        MiniAppOpenIcpPo po = new MiniAppOpenIcpPo();
        po.setId(this.flowId);
        po.setAppId(this.appId);
        po.setRecordNumber(this.recordNumber);
        po.setSubmitTime(this.submitTime);
        po.setFlowStatus(this.flowStatus);
        po.setGovAuditState(this.govAuditState);
        po.setGovAuditTime(this.govAuditTime == null ? null : TimeUtil.timestampToDateTimeString(this.govAuditTime));
        po.setGovAuditCode(this.govAuditCode);
        po.setGovAuditMsg(this.govAuditMsg);
        po.setGovAuditDescription(this.govAuditDescription);
        po.setReportStatus(this.reportStatus);
        return po;
    }

    public void flowReportEdit() {
        // 平台审核失败 -> 平台审核中
        // 管局审核失败 -> 平台审核中
        if (Objects.equals(this.flowStatus, IcpFlowStatusEnum.ICP_STATUS_GOV_AUDIT_FAIL.getStatus())) {
            this.govAuditState = 0;
        }
        if (Objects.nonNull(this.platformAudit)) {
            this.platformAudit.setAuditStatus(0);
            this.platformAudit.setFailReasons(List.of());
        }
        this.flowStatus = IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT.getStatus();
    }

    public void flowPlatformAudit(IcpPlatformAudit platformAudit) {
        if (Objects.isNull(platformAudit)) {
            throw new ServiceException(ErrorCodeType.BAD_DATA);
        }
        Integer auditStatus = platformAudit.getAuditStatus();
        if (Objects.isNull(auditStatus)) {
            throw new ServiceException(ErrorCodeType.BAD_DATA);
        }
        this.platformAudit.setOperator(platformAudit.getOperator());
        this.platformAudit.setAuditTime(new Timestamp(System.currentTimeMillis()));
        if (Objects.equals(auditStatus, 1)) {
            // 审核通过
            this.flowStatus = IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus();
            this.platformAudit.setAuditStatus(1);
            // 上报备案状态为0 等待任务扫描
            this.reportStatus = 0;
        } else if (Objects.equals(auditStatus, 2)) {
            // 审核不通过
            this.flowStatus = IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT_FAIL.getStatus();
            this.platformAudit.setAuditStatus(2);
            this.platformAudit.setFailReasons(Lists.newArrayList(platformAudit.getFailReasons()));
        } else {
            throw new ServiceException(ErrorCodeType.BAD_DATA);
        }
    }

    public boolean needReportRegulation() {
        // (status = 短信核验中 || 短信核验超时) && report_status = 0
        return (Objects.equals(this.flowStatus, IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus())
                || Objects.equals(this.flowStatus, IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY_TIMEOUT.getStatus())
                && Objects.equals(this.reportStatus, 0));
    }
}
