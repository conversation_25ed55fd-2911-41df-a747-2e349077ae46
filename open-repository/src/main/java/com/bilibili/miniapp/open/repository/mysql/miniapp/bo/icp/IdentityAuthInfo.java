package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/27
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IdentityAuthInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 互联网服务ID(业务主键)
     */
    private Long ispWzid;

    /**
     * 单位名称
     */
    private String dwmc;

    /**
     * 省份ID
     */
    private Integer shengid;

    /**
     * 单位性质
     */
    private Integer dwxz;

    /**
     * 证件类型
     */
    private Integer zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 负责人姓名
     */
    private String fzrXm;

    /**
     * 负责人证件类型
     */
    private Integer fzrZjlx;

    /**
     * 负责人证件号
     */
    private String fzrZjhm;

    /**
     * 证件有效期起 yyyy-MM-dd HH:mm:ss格式
     */
    private Timestamp fzrZjyxqStart;

    /**
     * 证件有效期止(长期需转换) yyyy-MM-dd HH:mm:ss格式
     */
    private Timestamp fzrZjyxqEnd;

    /**
     * 网站名称
     */
    private String wzmc;

    /**
     * 服务类型
     */
    private Integer fwlx;

    /**
     * 前置审批(0-无需,1-需要)
     */
    private Integer nrlx;

    /**
     * 网站负责人
     */
    private String wzFzrXm;

    /**
     * 证件类型
     */
    private Integer wzFzrZjlx;

    /**
     * 证件号码
     */
    private String wzFzrZjhm;

    /**
     * 有效期起 yyyy-MM-dd HH:mm:ss格式
     */
    private Timestamp wzFzrZjyxqStart;

    /**
     * 有效期止 yyyy-MM-dd HH:mm:ss格式
     */
    private Timestamp wzFzrZjyxqEnd;

    /**
     * 域名列表(分号分隔)
     */
    private String domain;

    /**
     * 认证核验状态 0-未核验 1-已核验
     */
    private Integer status;

    /**
     * 负责人证件有效期是否长期有效 0-否 1-是
     */
    private Integer fzrZjyxqCqyx;

    /**
     * 网站负责人证件有效期是否长期有效 0-否 1-是
     */
    private Integer wzFzrZjyxqCqyx;

    public MiniAppOpenIcpIdentityAuthPo convertToPo() {
        return MiniAppOpenIcpIdentityAuthPo.builder()
                .id(id)
                .ispWzid(ispWzid)
                .dwmc(dwmc)
                .shengid(shengid)
                .dwxz(dwxz)
                .zjlx(zjlx)
                .zjhm(zjhm)
                .fzrXm(fzrXm)
                .fzrZjlx(fzrZjlx)
                .fzrZjhm(fzrZjhm)
                .fzrZjyxqStart(fzrZjyxqStart == null ? null : TimeUtil.timestampToDateTimeString(fzrZjyxqStart))
                .fzrZjyxqEnd(fzrZjyxqEnd == null ? null : TimeUtil.timestampToDateTimeString(fzrZjyxqEnd))
                .wzmc(wzmc)
                .fwlx(fwlx)
                .nrlx(nrlx)
                .wzFzrXm(wzFzrXm)
                .wzFzrZjlx(wzFzrZjlx)
                .wzFzrZjhm(wzFzrZjhm)
                .wzFzrZjyxqStart(wzFzrZjyxqStart == null ? null : TimeUtil.timestampToDateTimeString(wzFzrZjyxqStart))
                .wzFzrZjyxqEnd(wzFzrZjyxqEnd == null ? null : TimeUtil.timestampToDateTimeString(wzFzrZjyxqEnd))
                .domain(domain)
                .status(status)
                .fzrZjyxqCqyx(fzrZjyxqCqyx)
                .wzFzrZjyxqCqyx(wzFzrZjyxqCqyx)
                .build();
    }

    public static IdentityAuthInfo convertFromPo(MiniAppOpenIcpIdentityAuthPo po) {
        return IdentityAuthInfo.builder()
                .id(po.getId())
                .ispWzid(po.getIspWzid())
                .dwmc(po.getDwmc())
                .shengid(po.getShengid())
                .dwxz(po.getDwxz())
                .zjlx(po.getZjlx())
                .zjhm(po.getZjhm())
                .fzrXm(po.getFzrXm())
                .fzrZjlx(po.getFzrZjlx())
                .fzrZjhm(po.getFzrZjhm())
                .fzrZjyxqStart(StringUtils.isBlank(po.getFzrZjyxqStart()) ? null : TimeUtil.getTimestampByDateTimeStr(po.getFzrZjyxqStart()))
                .fzrZjyxqEnd(StringUtils.isBlank(po.getFzrZjyxqEnd()) ? null : TimeUtil.getTimestampByDateTimeStr(po.getFzrZjyxqEnd()))
                .wzmc(po.getWzmc())
                .fwlx(po.getFwlx())
                .nrlx(po.getNrlx())
                .wzFzrXm(po.getWzFzrXm())
                .wzFzrZjlx(po.getWzFzrZjlx())
                .wzFzrZjhm(po.getWzFzrZjhm())
                .wzFzrZjyxqStart(StringUtils.isBlank(po.getWzFzrZjyxqStart()) ? null : TimeUtil.getTimestampByDateTimeStr(po.getWzFzrZjyxqStart()))
                .wzFzrZjyxqEnd(StringUtils.isBlank(po.getWzFzrZjyxqEnd()) ? null : TimeUtil.getTimestampByDateTimeStr(po.getWzFzrZjyxqEnd()))
                .domain(po.getDomain())
                .status(po.getStatus())
                .fzrZjyxqCqyx(po.getFzrZjyxqCqyx())
                .wzFzrZjyxqCqyx(po.getWzFzrZjyxqCqyx())
                .build();
    }
}
