package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MiniAppIcpConfig {

    private List<MiniAppIcpDocumentType> documentTypeList;
    private List<MiniAppIcpAreaCode> areaCodeList;
    private List<MiniAppIcpOrgType> orgTypeList;
    private List<MiniAppIcpAppServiceType> appServiceTypeList;


    /**
     * 证件类型
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MiniAppIcpDocumentType {
        private Long code;
        private String name;
        private Long orgCode;
        private Integer status;
    }

    /**
     * 区域列表
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MiniAppIcpAreaCode {
        private Long code;
        private String name;
        private Integer type;
        private Integer status;
    }

    /**
     * 单位性质
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MiniAppIcpOrgType {
        private Long code;
        private String name;
        private Integer status;
    }

    /**
     * APP服务类型
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MiniAppIcpAppServiceType {
        private Long code;
        private String name;
        private Long parentCode;
        private Integer status;
    }
}