package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.mini_app;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenCategoryPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/19
 **/

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppCategoryBo {

    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 层级
     */
    private Integer level;

    public static MiniAppCategoryBo fromPo(MiniAppOpenCategoryPo po) {
        return MiniAppCategoryBo.builder()
                .id(po.getId())
                .name(po.getName())
                .parentId(po.getParentId())
                .level(po.getLevel())
                .build();
    }

    public MiniAppOpenCategoryPo toPo() {
        return MiniAppOpenCategoryPo.builder()
                .id(this.id)
                .name(this.name)
                .parentId(this.parentId)
                .level(this.level)
                .build();
    }
}
