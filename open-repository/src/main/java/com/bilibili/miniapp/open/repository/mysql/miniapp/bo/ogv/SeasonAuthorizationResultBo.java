package com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/20
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SeasonAuthorizationResultBo {
    private Long seasonId;
    private String appId;
    private Long seasonMid;
    private Timestamp authTime;
    private boolean defaultTag;
}
