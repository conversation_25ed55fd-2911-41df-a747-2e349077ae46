package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenCategoryPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenCategoryPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenCategoryDao {
    long countByExample(MiniAppOpenCategoryPoExample example);

    int deleteByExample(MiniAppOpenCategoryPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenCategoryPo record);

    int insertBatch(List<MiniAppOpenCategoryPo> records);

    int insertUpdateBatch(List<MiniAppOpenCategoryPo> records);

    int insert(MiniAppOpenCategoryPo record);

    int insertUpdateSelective(MiniAppOpenCategoryPo record);

    int insertSelective(MiniAppOpenCategoryPo record);

    List<MiniAppOpenCategoryPo> selectByExample(MiniAppOpenCategoryPoExample example);

    MiniAppOpenCategoryPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenCategoryPo record, @Param("example") MiniAppOpenCategoryPoExample example);

    int updateByExample(@Param("record") MiniAppOpenCategoryPo record, @Param("example") MiniAppOpenCategoryPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenCategoryPo record);

    int updateByPrimaryKey(MiniAppOpenCategoryPo record);
}