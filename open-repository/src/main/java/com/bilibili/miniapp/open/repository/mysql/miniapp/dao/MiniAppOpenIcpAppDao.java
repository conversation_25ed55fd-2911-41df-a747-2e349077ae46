package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpAppDao {
    long countByExample(MiniAppOpenIcpAppPoExample example);

    int deleteByExample(MiniAppOpenIcpAppPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpAppPo record);

    int insertBatch(List<MiniAppOpenIcpAppPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpAppPo> records);

    int insert(MiniAppOpenIcpAppPo record);

    int insertUpdateSelective(MiniAppOpenIcpAppPo record);

    int insertSelective(MiniAppOpenIcpAppPo record);

    List<MiniAppOpenIcpAppPo> selectByExample(MiniAppOpenIcpAppPoExample example);

    MiniAppOpenIcpAppPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpAppPo record, @Param("example") MiniAppOpenIcpAppPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpAppPo record, @Param("example") MiniAppOpenIcpAppPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpAppPo record);

    int updateByPrimaryKey(MiniAppOpenIcpAppPo record);
}