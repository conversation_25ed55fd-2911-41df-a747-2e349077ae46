package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAttachmentPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAttachmentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpAttachmentDao {
    long countByExample(MiniAppOpenIcpAttachmentPoExample example);

    int deleteByExample(MiniAppOpenIcpAttachmentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpAttachmentPo record);

    int insertBatch(List<MiniAppOpenIcpAttachmentPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpAttachmentPo> records);

    int insert(MiniAppOpenIcpAttachmentPo record);

    int insertUpdateSelective(MiniAppOpenIcpAttachmentPo record);

    int insertSelective(MiniAppOpenIcpAttachmentPo record);

    List<MiniAppOpenIcpAttachmentPo> selectByExample(MiniAppOpenIcpAttachmentPoExample example);

    MiniAppOpenIcpAttachmentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpAttachmentPo record, @Param("example") MiniAppOpenIcpAttachmentPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpAttachmentPo record, @Param("example") MiniAppOpenIcpAttachmentPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpAttachmentPo record);

    int updateByPrimaryKey(MiniAppOpenIcpAttachmentPo record);
}