package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpAuditDao {
    long countByExample(MiniAppOpenIcpAuditPoExample example);

    int deleteByExample(MiniAppOpenIcpAuditPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpAuditPo record);

    int insertBatch(List<MiniAppOpenIcpAuditPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpAuditPo> records);

    int insert(MiniAppOpenIcpAuditPo record);

    int insertUpdateSelective(MiniAppOpenIcpAuditPo record);

    int insertSelective(MiniAppOpenIcpAuditPo record);

    List<MiniAppOpenIcpAuditPo> selectByExample(MiniAppOpenIcpAuditPoExample example);

    MiniAppOpenIcpAuditPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpAuditPo record, @Param("example") MiniAppOpenIcpAuditPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpAuditPo record, @Param("example") MiniAppOpenIcpAuditPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpAuditPo record);

    int updateByPrimaryKey(MiniAppOpenIcpAuditPo record);
}