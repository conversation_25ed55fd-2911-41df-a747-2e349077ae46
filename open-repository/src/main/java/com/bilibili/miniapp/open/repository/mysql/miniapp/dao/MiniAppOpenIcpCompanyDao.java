package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpCompanyDao {
    long countByExample(MiniAppOpenIcpCompanyPoExample example);

    int deleteByExample(MiniAppOpenIcpCompanyPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpCompanyPo record);

    int insertBatch(List<MiniAppOpenIcpCompanyPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpCompanyPo> records);

    int insert(MiniAppOpenIcpCompanyPo record);

    int insertUpdateSelective(MiniAppOpenIcpCompanyPo record);

    int insertSelective(MiniAppOpenIcpCompanyPo record);

    List<MiniAppOpenIcpCompanyPo> selectByExample(MiniAppOpenIcpCompanyPoExample example);

    MiniAppOpenIcpCompanyPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpCompanyPo record, @Param("example") MiniAppOpenIcpCompanyPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpCompanyPo record, @Param("example") MiniAppOpenIcpCompanyPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpCompanyPo record);

    int updateByPrimaryKey(MiniAppOpenIcpCompanyPo record);
}