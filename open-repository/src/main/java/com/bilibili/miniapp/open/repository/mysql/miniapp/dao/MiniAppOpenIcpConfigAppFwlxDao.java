package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigAppFwlxPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigAppFwlxPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpConfigAppFwlxDao {
    long countByExample(MiniAppOpenIcpConfigAppFwlxPoExample example);

    int deleteByExample(MiniAppOpenIcpConfigAppFwlxPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpConfigAppFwlxPo record);

    int insertBatch(List<MiniAppOpenIcpConfigAppFwlxPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpConfigAppFwlxPo> records);

    int insert(MiniAppOpenIcpConfigAppFwlxPo record);

    int insertUpdateSelective(MiniAppOpenIcpConfigAppFwlxPo record);

    int insertSelective(MiniAppOpenIcpConfigAppFwlxPo record);

    List<MiniAppOpenIcpConfigAppFwlxPo> selectByExample(MiniAppOpenIcpConfigAppFwlxPoExample example);

    MiniAppOpenIcpConfigAppFwlxPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpConfigAppFwlxPo record, @Param("example") MiniAppOpenIcpConfigAppFwlxPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpConfigAppFwlxPo record, @Param("example") MiniAppOpenIcpConfigAppFwlxPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpConfigAppFwlxPo record);

    int updateByPrimaryKey(MiniAppOpenIcpConfigAppFwlxPo record);
}