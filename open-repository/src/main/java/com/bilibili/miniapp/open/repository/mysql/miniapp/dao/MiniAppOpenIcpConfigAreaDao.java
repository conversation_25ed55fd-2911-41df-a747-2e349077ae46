package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigAreaPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigAreaPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpConfigAreaDao {
    long countByExample(MiniAppOpenIcpConfigAreaPoExample example);

    int deleteByExample(MiniAppOpenIcpConfigAreaPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpConfigAreaPo record);

    int insertBatch(List<MiniAppOpenIcpConfigAreaPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpConfigAreaPo> records);

    int insert(MiniAppOpenIcpConfigAreaPo record);

    int insertUpdateSelective(MiniAppOpenIcpConfigAreaPo record);

    int insertSelective(MiniAppOpenIcpConfigAreaPo record);

    List<MiniAppOpenIcpConfigAreaPo> selectByExample(MiniAppOpenIcpConfigAreaPoExample example);

    MiniAppOpenIcpConfigAreaPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpConfigAreaPo record, @Param("example") MiniAppOpenIcpConfigAreaPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpConfigAreaPo record, @Param("example") MiniAppOpenIcpConfigAreaPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpConfigAreaPo record);

    int updateByPrimaryKey(MiniAppOpenIcpConfigAreaPo record);
}