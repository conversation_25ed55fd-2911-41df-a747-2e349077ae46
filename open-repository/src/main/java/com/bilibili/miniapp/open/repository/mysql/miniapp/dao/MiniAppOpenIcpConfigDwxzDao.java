package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigDwxzPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigDwxzPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpConfigDwxzDao {
    long countByExample(MiniAppOpenIcpConfigDwxzPoExample example);

    int deleteByExample(MiniAppOpenIcpConfigDwxzPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpConfigDwxzPo record);

    int insertBatch(List<MiniAppOpenIcpConfigDwxzPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpConfigDwxzPo> records);

    int insert(MiniAppOpenIcpConfigDwxzPo record);

    int insertUpdateSelective(MiniAppOpenIcpConfigDwxzPo record);

    int insertSelective(MiniAppOpenIcpConfigDwxzPo record);

    List<MiniAppOpenIcpConfigDwxzPo> selectByExample(MiniAppOpenIcpConfigDwxzPoExample example);

    MiniAppOpenIcpConfigDwxzPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpConfigDwxzPo record, @Param("example") MiniAppOpenIcpConfigDwxzPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpConfigDwxzPo record, @Param("example") MiniAppOpenIcpConfigDwxzPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpConfigDwxzPo record);

    int updateByPrimaryKey(MiniAppOpenIcpConfigDwxzPo record);
}