package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpConfigZjlxDao {
    long countByExample(MiniAppOpenIcpConfigZjlxPoExample example);

    int deleteByExample(MiniAppOpenIcpConfigZjlxPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpConfigZjlxPo record);

    int insertBatch(List<MiniAppOpenIcpConfigZjlxPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpConfigZjlxPo> records);

    int insert(MiniAppOpenIcpConfigZjlxPo record);

    int insertUpdateSelective(MiniAppOpenIcpConfigZjlxPo record);

    int insertSelective(MiniAppOpenIcpConfigZjlxPo record);

    List<MiniAppOpenIcpConfigZjlxPo> selectByExample(MiniAppOpenIcpConfigZjlxPoExample example);

    MiniAppOpenIcpConfigZjlxPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpConfigZjlxPo record, @Param("example") MiniAppOpenIcpConfigZjlxPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpConfigZjlxPo record, @Param("example") MiniAppOpenIcpConfigZjlxPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpConfigZjlxPo record);

    int updateByPrimaryKey(MiniAppOpenIcpConfigZjlxPo record);
}