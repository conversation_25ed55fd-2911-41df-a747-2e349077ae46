package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpDao {
    long countByExample(MiniAppOpenIcpPoExample example);

    int deleteByExample(MiniAppOpenIcpPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpPo record);

    int insertBatch(List<MiniAppOpenIcpPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpPo> records);

    int insert(MiniAppOpenIcpPo record);

    int insertUpdateSelective(MiniAppOpenIcpPo record);

    int insertSelective(MiniAppOpenIcpPo record);

    List<MiniAppOpenIcpPo> selectByExample(MiniAppOpenIcpPoExample example);

    MiniAppOpenIcpPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpPo record, @Param("example") MiniAppOpenIcpPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpPo record, @Param("example") MiniAppOpenIcpPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpPo record);

    int updateByPrimaryKey(MiniAppOpenIcpPo record);
}