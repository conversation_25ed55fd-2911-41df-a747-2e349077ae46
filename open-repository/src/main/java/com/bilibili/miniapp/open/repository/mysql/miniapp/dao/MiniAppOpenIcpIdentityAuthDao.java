package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpIdentityAuthDao {
    long countByExample(MiniAppOpenIcpIdentityAuthPoExample example);

    int deleteByExample(MiniAppOpenIcpIdentityAuthPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpIdentityAuthPo record);

    int insertBatch(List<MiniAppOpenIcpIdentityAuthPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpIdentityAuthPo> records);

    int insert(MiniAppOpenIcpIdentityAuthPo record);

    int insertUpdateSelective(MiniAppOpenIcpIdentityAuthPo record);

    int insertSelective(MiniAppOpenIcpIdentityAuthPo record);

    List<MiniAppOpenIcpIdentityAuthPo> selectByExample(MiniAppOpenIcpIdentityAuthPoExample example);

    MiniAppOpenIcpIdentityAuthPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpIdentityAuthPo record, @Param("example") MiniAppOpenIcpIdentityAuthPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpIdentityAuthPo record, @Param("example") MiniAppOpenIcpIdentityAuthPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpIdentityAuthPo record);

    int updateByPrimaryKey(MiniAppOpenIcpIdentityAuthPo record);
}