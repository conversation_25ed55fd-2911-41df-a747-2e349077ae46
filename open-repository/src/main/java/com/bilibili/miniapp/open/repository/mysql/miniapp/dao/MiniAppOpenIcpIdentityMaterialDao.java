package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityMaterialPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityMaterialPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenIcpIdentityMaterialDao {
    long countByExample(MiniAppOpenIcpIdentityMaterialPoExample example);

    int deleteByExample(MiniAppOpenIcpIdentityMaterialPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenIcpIdentityMaterialPo record);

    int insertBatch(List<MiniAppOpenIcpIdentityMaterialPo> records);

    int insertUpdateBatch(List<MiniAppOpenIcpIdentityMaterialPo> records);

    int insert(MiniAppOpenIcpIdentityMaterialPo record);

    int insertUpdateSelective(MiniAppOpenIcpIdentityMaterialPo record);

    int insertSelective(MiniAppOpenIcpIdentityMaterialPo record);

    List<MiniAppOpenIcpIdentityMaterialPo> selectByExample(MiniAppOpenIcpIdentityMaterialPoExample example);

    MiniAppOpenIcpIdentityMaterialPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenIcpIdentityMaterialPo record, @Param("example") MiniAppOpenIcpIdentityMaterialPoExample example);

    int updateByExample(@Param("record") MiniAppOpenIcpIdentityMaterialPo record, @Param("example") MiniAppOpenIcpIdentityMaterialPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenIcpIdentityMaterialPo record);

    int updateByPrimaryKey(MiniAppOpenIcpIdentityMaterialPo record);
}