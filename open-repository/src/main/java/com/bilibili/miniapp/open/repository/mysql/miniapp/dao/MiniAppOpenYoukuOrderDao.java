package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppOpenYoukuOrderDao {
    long countByExample(MiniAppOpenYoukuOrderPoExample example);

    int deleteByExample(MiniAppOpenYoukuOrderPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppOpenYoukuOrderPo record);

    int insertBatch(List<MiniAppOpenYoukuOrderPo> records);

    int insertUpdateBatch(List<MiniAppOpenYoukuOrderPo> records);

    int insert(MiniAppOpenYoukuOrderPo record);

    int insertUpdateSelective(MiniAppOpenYoukuOrderPo record);

    int insertSelective(MiniAppOpenYoukuOrderPo record);

    List<MiniAppOpenYoukuOrderPo> selectByExample(MiniAppOpenYoukuOrderPoExample example);

    MiniAppOpenYoukuOrderPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppOpenYoukuOrderPo record, @Param("example") MiniAppOpenYoukuOrderPoExample example);

    int updateByExample(@Param("record") MiniAppOpenYoukuOrderPo record, @Param("example") MiniAppOpenYoukuOrderPoExample example);

    int updateByPrimaryKeySelective(MiniAppOpenYoukuOrderPo record);

    int updateByPrimaryKey(MiniAppOpenYoukuOrderPo record);
}