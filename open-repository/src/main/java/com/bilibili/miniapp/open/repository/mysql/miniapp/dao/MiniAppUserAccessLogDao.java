package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppUserAccessLogDao {
    long countByExample(MiniAppUserAccessLogPoExample example);

    int deleteByExample(MiniAppUserAccessLogPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppUserAccessLogPo record);

    int insertBatch(List<MiniAppUserAccessLogPo> records);

    int insertUpdateBatch(List<MiniAppUserAccessLogPo> records);

    int insert(MiniAppUserAccessLogPo record);

    int insertUpdateSelective(MiniAppUserAccessLogPo record);

    int insertSelective(MiniAppUserAccessLogPo record);

    List<MiniAppUserAccessLogPo> selectByExample(MiniAppUserAccessLogPoExample example);

    MiniAppUserAccessLogPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppUserAccessLogPo record, @Param("example") MiniAppUserAccessLogPoExample example);

    int updateByExample(@Param("record") MiniAppUserAccessLogPo record, @Param("example") MiniAppUserAccessLogPoExample example);

    int updateByPrimaryKeySelective(MiniAppUserAccessLogPo record);

    int updateByPrimaryKey(MiniAppUserAccessLogPo record);
}