package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppYoukuShowDao {
    long countByExample(MiniAppYoukuShowPoExample example);

    int deleteByExample(MiniAppYoukuShowPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppYoukuShowPo record);

    int insertBatch(List<MiniAppYoukuShowPo> records);

    int insertUpdateBatch(List<MiniAppYoukuShowPo> records);

    int insert(MiniAppYoukuShowPo record);

    int insertUpdateSelective(MiniAppYoukuShowPo record);

    int insertSelective(MiniAppYoukuShowPo record);

    List<MiniAppYoukuShowPo> selectByExampleWithBLOBs(MiniAppYoukuShowPoExample example);

    List<MiniAppYoukuShowPo> selectByExample(MiniAppYoukuShowPoExample example);

    MiniAppYoukuShowPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppYoukuShowPo record, @Param("example") MiniAppYoukuShowPoExample example);

    int updateByExampleWithBLOBs(@Param("record") MiniAppYoukuShowPo record, @Param("example") MiniAppYoukuShowPoExample example);

    int updateByExample(@Param("record") MiniAppYoukuShowPo record, @Param("example") MiniAppYoukuShowPoExample example);

    int updateByPrimaryKeySelective(MiniAppYoukuShowPo record);

    int updateByPrimaryKeyWithBLOBs(MiniAppYoukuShowPo record);

    int updateByPrimaryKey(MiniAppYoukuShowPo record);
}