package com.bilibili.miniapp.open.repository.mysql.miniapp.dao;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface MiniAppYoukuVideoDao {
    long countByExample(MiniAppYoukuVideoPoExample example);

    int deleteByExample(MiniAppYoukuVideoPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(MiniAppYoukuVideoPo record);

    int insertBatch(List<MiniAppYoukuVideoPo> records);

    int insertUpdateBatch(List<MiniAppYoukuVideoPo> records);

    int insert(MiniAppYoukuVideoPo record);

    int insertUpdateSelective(MiniAppYoukuVideoPo record);

    int insertSelective(MiniAppYoukuVideoPo record);

    List<MiniAppYoukuVideoPo> selectByExample(MiniAppYoukuVideoPoExample example);

    MiniAppYoukuVideoPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MiniAppYoukuVideoPo record, @Param("example") MiniAppYoukuVideoPoExample example);

    int updateByExample(@Param("record") MiniAppYoukuVideoPo record, @Param("example") MiniAppYoukuVideoPoExample example);

    int updateByPrimaryKeySelective(MiniAppYoukuVideoPo record);

    int updateByPrimaryKey(MiniAppYoukuVideoPo record);
}