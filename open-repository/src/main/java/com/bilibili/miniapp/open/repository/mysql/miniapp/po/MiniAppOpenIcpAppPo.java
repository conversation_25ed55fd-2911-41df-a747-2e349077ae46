package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpAppPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 备案流程id
     */
    private Long flowId;

    /**
     * 小程序名称
     */
    private String name;

    /**
     * 小程序app_id
     */
    private String appId;

    /**
     * 服务内容
     */
    private Integer serviceType;

    /**
     * 应用的是否需要前置审批。0不需要，1需要。默认需要
     */
    private Integer approvalPre;

    /**
     * 应用的审批类型。【出版】（固定值）
     */
    private Integer approvalType;

    /**
     * 应用的审批编号
     */
    private String approvalIsbnNo;

    /**
     * 审批附件
     */
    private String approvalAttachment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 应用负责人证件类型。2代表身份证（固定值）
     */
    private Integer fzrLicenseType;

    /**
     * 应用负责人证件编号
     */
    private String fzrCardNo;

    /**
     * 应用负责人姓名
     */
    private String fzrName;

    /**
     * 应用负责人身份证开始时间yyyy-MM-dd HH:mm:ss
     */
    private String fzrCardBegin;

    /**
     * 应用负责人身份证结束时间yyyy-MM-dd HH:mm:ss
     */
    private String fzrCardEnd;

    /**
     * 应用负责人身份证是否长期有效：0-否，1-是
     */
    private Integer fzrCardLongEffect;

    /**
     * 应用负责人手机号码
     */
    private String fzrPhone;

    /**
     * 应用负责人邮箱
     */
    private String fzrEmail;

    /**
     * 应用负责人应急电话
     */
    private String fzrEmergency;

    /**
     * 人证核验状态。0未核验，1核验中，2核验成功，3核验失败
     */
    private Integer idVerificationStatus;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 负责人身份证照片
     */
    private String fzrCard;

    /**
     * 人脸识别照片
     */
    private String verifyPhoto;

    /**
     * 网站id 目前取mini_app.id
     */
    private Long wzid;

    private static final long serialVersionUID = 1L;
}