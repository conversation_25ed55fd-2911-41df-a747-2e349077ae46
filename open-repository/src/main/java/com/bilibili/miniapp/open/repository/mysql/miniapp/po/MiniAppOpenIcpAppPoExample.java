package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenIcpAppPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenIcpAppPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(Long value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(Long value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(Long value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(Long value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(Long value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(Long value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<Long> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<Long> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(Long value1, Long value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(Long value1, Long value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIsNull() {
            addCriterion("service_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIsNotNull() {
            addCriterion("service_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceTypeEqualTo(Integer value) {
            addCriterion("service_type =", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeNotEqualTo(Integer value) {
            addCriterion("service_type <>", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeGreaterThan(Integer value) {
            addCriterion("service_type >", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_type >=", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeLessThan(Integer value) {
            addCriterion("service_type <", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("service_type <=", value, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeIn(List<Integer> values) {
            addCriterion("service_type in", values, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeNotIn(List<Integer> values) {
            addCriterion("service_type not in", values, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeBetween(Integer value1, Integer value2) {
            addCriterion("service_type between", value1, value2, "serviceType");
            return (Criteria) this;
        }

        public Criteria andServiceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_type not between", value1, value2, "serviceType");
            return (Criteria) this;
        }

        public Criteria andApprovalPreIsNull() {
            addCriterion("approval_pre is null");
            return (Criteria) this;
        }

        public Criteria andApprovalPreIsNotNull() {
            addCriterion("approval_pre is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalPreEqualTo(Integer value) {
            addCriterion("approval_pre =", value, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreNotEqualTo(Integer value) {
            addCriterion("approval_pre <>", value, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreGreaterThan(Integer value) {
            addCriterion("approval_pre >", value, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreGreaterThanOrEqualTo(Integer value) {
            addCriterion("approval_pre >=", value, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreLessThan(Integer value) {
            addCriterion("approval_pre <", value, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreLessThanOrEqualTo(Integer value) {
            addCriterion("approval_pre <=", value, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreIn(List<Integer> values) {
            addCriterion("approval_pre in", values, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreNotIn(List<Integer> values) {
            addCriterion("approval_pre not in", values, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreBetween(Integer value1, Integer value2) {
            addCriterion("approval_pre between", value1, value2, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalPreNotBetween(Integer value1, Integer value2) {
            addCriterion("approval_pre not between", value1, value2, "approvalPre");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeIsNull() {
            addCriterion("approval_type is null");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeIsNotNull() {
            addCriterion("approval_type is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeEqualTo(Integer value) {
            addCriterion("approval_type =", value, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeNotEqualTo(Integer value) {
            addCriterion("approval_type <>", value, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeGreaterThan(Integer value) {
            addCriterion("approval_type >", value, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("approval_type >=", value, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeLessThan(Integer value) {
            addCriterion("approval_type <", value, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeLessThanOrEqualTo(Integer value) {
            addCriterion("approval_type <=", value, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeIn(List<Integer> values) {
            addCriterion("approval_type in", values, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeNotIn(List<Integer> values) {
            addCriterion("approval_type not in", values, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeBetween(Integer value1, Integer value2) {
            addCriterion("approval_type between", value1, value2, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("approval_type not between", value1, value2, "approvalType");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoIsNull() {
            addCriterion("approval_isbn_no is null");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoIsNotNull() {
            addCriterion("approval_isbn_no is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoEqualTo(String value) {
            addCriterion("approval_isbn_no =", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoNotEqualTo(String value) {
            addCriterion("approval_isbn_no <>", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoGreaterThan(String value) {
            addCriterion("approval_isbn_no >", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoGreaterThanOrEqualTo(String value) {
            addCriterion("approval_isbn_no >=", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoLessThan(String value) {
            addCriterion("approval_isbn_no <", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoLessThanOrEqualTo(String value) {
            addCriterion("approval_isbn_no <=", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoLike(String value) {
            addCriterion("approval_isbn_no like", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoNotLike(String value) {
            addCriterion("approval_isbn_no not like", value, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoIn(List<String> values) {
            addCriterion("approval_isbn_no in", values, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoNotIn(List<String> values) {
            addCriterion("approval_isbn_no not in", values, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoBetween(String value1, String value2) {
            addCriterion("approval_isbn_no between", value1, value2, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalIsbnNoNotBetween(String value1, String value2) {
            addCriterion("approval_isbn_no not between", value1, value2, "approvalIsbnNo");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentIsNull() {
            addCriterion("approval_attachment is null");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentIsNotNull() {
            addCriterion("approval_attachment is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentEqualTo(String value) {
            addCriterion("approval_attachment =", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentNotEqualTo(String value) {
            addCriterion("approval_attachment <>", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentGreaterThan(String value) {
            addCriterion("approval_attachment >", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentGreaterThanOrEqualTo(String value) {
            addCriterion("approval_attachment >=", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentLessThan(String value) {
            addCriterion("approval_attachment <", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentLessThanOrEqualTo(String value) {
            addCriterion("approval_attachment <=", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentLike(String value) {
            addCriterion("approval_attachment like", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentNotLike(String value) {
            addCriterion("approval_attachment not like", value, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentIn(List<String> values) {
            addCriterion("approval_attachment in", values, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentNotIn(List<String> values) {
            addCriterion("approval_attachment not in", values, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentBetween(String value1, String value2) {
            addCriterion("approval_attachment between", value1, value2, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andApprovalAttachmentNotBetween(String value1, String value2) {
            addCriterion("approval_attachment not between", value1, value2, "approvalAttachment");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeIsNull() {
            addCriterion("fzr_license_type is null");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeIsNotNull() {
            addCriterion("fzr_license_type is not null");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeEqualTo(Integer value) {
            addCriterion("fzr_license_type =", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeNotEqualTo(Integer value) {
            addCriterion("fzr_license_type <>", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeGreaterThan(Integer value) {
            addCriterion("fzr_license_type >", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("fzr_license_type >=", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeLessThan(Integer value) {
            addCriterion("fzr_license_type <", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("fzr_license_type <=", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeIn(List<Integer> values) {
            addCriterion("fzr_license_type in", values, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeNotIn(List<Integer> values) {
            addCriterion("fzr_license_type not in", values, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeBetween(Integer value1, Integer value2) {
            addCriterion("fzr_license_type between", value1, value2, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("fzr_license_type not between", value1, value2, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoIsNull() {
            addCriterion("fzr_card_no is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoIsNotNull() {
            addCriterion("fzr_card_no is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoEqualTo(String value) {
            addCriterion("fzr_card_no =", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotEqualTo(String value) {
            addCriterion("fzr_card_no <>", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoGreaterThan(String value) {
            addCriterion("fzr_card_no >", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_no >=", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoLessThan(String value) {
            addCriterion("fzr_card_no <", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_no <=", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoLike(String value) {
            addCriterion("fzr_card_no like", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotLike(String value) {
            addCriterion("fzr_card_no not like", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoIn(List<String> values) {
            addCriterion("fzr_card_no in", values, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotIn(List<String> values) {
            addCriterion("fzr_card_no not in", values, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoBetween(String value1, String value2) {
            addCriterion("fzr_card_no between", value1, value2, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotBetween(String value1, String value2) {
            addCriterion("fzr_card_no not between", value1, value2, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrNameIsNull() {
            addCriterion("fzr_name is null");
            return (Criteria) this;
        }

        public Criteria andFzrNameIsNotNull() {
            addCriterion("fzr_name is not null");
            return (Criteria) this;
        }

        public Criteria andFzrNameEqualTo(String value) {
            addCriterion("fzr_name =", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotEqualTo(String value) {
            addCriterion("fzr_name <>", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameGreaterThan(String value) {
            addCriterion("fzr_name >", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_name >=", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameLessThan(String value) {
            addCriterion("fzr_name <", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameLessThanOrEqualTo(String value) {
            addCriterion("fzr_name <=", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameLike(String value) {
            addCriterion("fzr_name like", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotLike(String value) {
            addCriterion("fzr_name not like", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameIn(List<String> values) {
            addCriterion("fzr_name in", values, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotIn(List<String> values) {
            addCriterion("fzr_name not in", values, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameBetween(String value1, String value2) {
            addCriterion("fzr_name between", value1, value2, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotBetween(String value1, String value2) {
            addCriterion("fzr_name not between", value1, value2, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginIsNull() {
            addCriterion("fzr_card_begin is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginIsNotNull() {
            addCriterion("fzr_card_begin is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginEqualTo(String value) {
            addCriterion("fzr_card_begin =", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotEqualTo(String value) {
            addCriterion("fzr_card_begin <>", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginGreaterThan(String value) {
            addCriterion("fzr_card_begin >", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_begin >=", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginLessThan(String value) {
            addCriterion("fzr_card_begin <", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_begin <=", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginLike(String value) {
            addCriterion("fzr_card_begin like", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotLike(String value) {
            addCriterion("fzr_card_begin not like", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginIn(List<String> values) {
            addCriterion("fzr_card_begin in", values, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotIn(List<String> values) {
            addCriterion("fzr_card_begin not in", values, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginBetween(String value1, String value2) {
            addCriterion("fzr_card_begin between", value1, value2, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotBetween(String value1, String value2) {
            addCriterion("fzr_card_begin not between", value1, value2, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndIsNull() {
            addCriterion("fzr_card_end is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndIsNotNull() {
            addCriterion("fzr_card_end is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndEqualTo(String value) {
            addCriterion("fzr_card_end =", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotEqualTo(String value) {
            addCriterion("fzr_card_end <>", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndGreaterThan(String value) {
            addCriterion("fzr_card_end >", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_end >=", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndLessThan(String value) {
            addCriterion("fzr_card_end <", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_end <=", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndLike(String value) {
            addCriterion("fzr_card_end like", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotLike(String value) {
            addCriterion("fzr_card_end not like", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndIn(List<String> values) {
            addCriterion("fzr_card_end in", values, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotIn(List<String> values) {
            addCriterion("fzr_card_end not in", values, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndBetween(String value1, String value2) {
            addCriterion("fzr_card_end between", value1, value2, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotBetween(String value1, String value2) {
            addCriterion("fzr_card_end not between", value1, value2, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectIsNull() {
            addCriterion("fzr_card_long_effect is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectIsNotNull() {
            addCriterion("fzr_card_long_effect is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect =", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectNotEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect <>", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectGreaterThan(Integer value) {
            addCriterion("fzr_card_long_effect >", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectGreaterThanOrEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect >=", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectLessThan(Integer value) {
            addCriterion("fzr_card_long_effect <", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectLessThanOrEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect <=", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectIn(List<Integer> values) {
            addCriterion("fzr_card_long_effect in", values, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectNotIn(List<Integer> values) {
            addCriterion("fzr_card_long_effect not in", values, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectBetween(Integer value1, Integer value2) {
            addCriterion("fzr_card_long_effect between", value1, value2, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectNotBetween(Integer value1, Integer value2) {
            addCriterion("fzr_card_long_effect not between", value1, value2, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneIsNull() {
            addCriterion("fzr_phone is null");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneIsNotNull() {
            addCriterion("fzr_phone is not null");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneEqualTo(String value) {
            addCriterion("fzr_phone =", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotEqualTo(String value) {
            addCriterion("fzr_phone <>", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneGreaterThan(String value) {
            addCriterion("fzr_phone >", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_phone >=", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneLessThan(String value) {
            addCriterion("fzr_phone <", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneLessThanOrEqualTo(String value) {
            addCriterion("fzr_phone <=", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneLike(String value) {
            addCriterion("fzr_phone like", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotLike(String value) {
            addCriterion("fzr_phone not like", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneIn(List<String> values) {
            addCriterion("fzr_phone in", values, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotIn(List<String> values) {
            addCriterion("fzr_phone not in", values, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneBetween(String value1, String value2) {
            addCriterion("fzr_phone between", value1, value2, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotBetween(String value1, String value2) {
            addCriterion("fzr_phone not between", value1, value2, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrEmailIsNull() {
            addCriterion("fzr_email is null");
            return (Criteria) this;
        }

        public Criteria andFzrEmailIsNotNull() {
            addCriterion("fzr_email is not null");
            return (Criteria) this;
        }

        public Criteria andFzrEmailEqualTo(String value) {
            addCriterion("fzr_email =", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotEqualTo(String value) {
            addCriterion("fzr_email <>", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailGreaterThan(String value) {
            addCriterion("fzr_email >", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_email >=", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailLessThan(String value) {
            addCriterion("fzr_email <", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailLessThanOrEqualTo(String value) {
            addCriterion("fzr_email <=", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailLike(String value) {
            addCriterion("fzr_email like", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotLike(String value) {
            addCriterion("fzr_email not like", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailIn(List<String> values) {
            addCriterion("fzr_email in", values, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotIn(List<String> values) {
            addCriterion("fzr_email not in", values, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailBetween(String value1, String value2) {
            addCriterion("fzr_email between", value1, value2, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotBetween(String value1, String value2) {
            addCriterion("fzr_email not between", value1, value2, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyIsNull() {
            addCriterion("fzr_emergency is null");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyIsNotNull() {
            addCriterion("fzr_emergency is not null");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyEqualTo(String value) {
            addCriterion("fzr_emergency =", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotEqualTo(String value) {
            addCriterion("fzr_emergency <>", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyGreaterThan(String value) {
            addCriterion("fzr_emergency >", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_emergency >=", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyLessThan(String value) {
            addCriterion("fzr_emergency <", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyLessThanOrEqualTo(String value) {
            addCriterion("fzr_emergency <=", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyLike(String value) {
            addCriterion("fzr_emergency like", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotLike(String value) {
            addCriterion("fzr_emergency not like", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyIn(List<String> values) {
            addCriterion("fzr_emergency in", values, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotIn(List<String> values) {
            addCriterion("fzr_emergency not in", values, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyBetween(String value1, String value2) {
            addCriterion("fzr_emergency between", value1, value2, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotBetween(String value1, String value2) {
            addCriterion("fzr_emergency not between", value1, value2, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusIsNull() {
            addCriterion("id_verification_status is null");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusIsNotNull() {
            addCriterion("id_verification_status is not null");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusEqualTo(Integer value) {
            addCriterion("id_verification_status =", value, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusNotEqualTo(Integer value) {
            addCriterion("id_verification_status <>", value, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusGreaterThan(Integer value) {
            addCriterion("id_verification_status >", value, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("id_verification_status >=", value, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusLessThan(Integer value) {
            addCriterion("id_verification_status <", value, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusLessThanOrEqualTo(Integer value) {
            addCriterion("id_verification_status <=", value, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusIn(List<Integer> values) {
            addCriterion("id_verification_status in", values, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusNotIn(List<Integer> values) {
            addCriterion("id_verification_status not in", values, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusBetween(Integer value1, Integer value2) {
            addCriterion("id_verification_status between", value1, value2, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andIdVerificationStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("id_verification_status not between", value1, value2, "idVerificationStatus");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andFzrCardIsNull() {
            addCriterion("fzr_card is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardIsNotNull() {
            addCriterion("fzr_card is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardEqualTo(String value) {
            addCriterion("fzr_card =", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardNotEqualTo(String value) {
            addCriterion("fzr_card <>", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardGreaterThan(String value) {
            addCriterion("fzr_card >", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card >=", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardLessThan(String value) {
            addCriterion("fzr_card <", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardLessThanOrEqualTo(String value) {
            addCriterion("fzr_card <=", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardLike(String value) {
            addCriterion("fzr_card like", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardNotLike(String value) {
            addCriterion("fzr_card not like", value, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardIn(List<String> values) {
            addCriterion("fzr_card in", values, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardNotIn(List<String> values) {
            addCriterion("fzr_card not in", values, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardBetween(String value1, String value2) {
            addCriterion("fzr_card between", value1, value2, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andFzrCardNotBetween(String value1, String value2) {
            addCriterion("fzr_card not between", value1, value2, "fzrCard");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoIsNull() {
            addCriterion("verify_photo is null");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoIsNotNull() {
            addCriterion("verify_photo is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoEqualTo(String value) {
            addCriterion("verify_photo =", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoNotEqualTo(String value) {
            addCriterion("verify_photo <>", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoGreaterThan(String value) {
            addCriterion("verify_photo >", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoGreaterThanOrEqualTo(String value) {
            addCriterion("verify_photo >=", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoLessThan(String value) {
            addCriterion("verify_photo <", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoLessThanOrEqualTo(String value) {
            addCriterion("verify_photo <=", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoLike(String value) {
            addCriterion("verify_photo like", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoNotLike(String value) {
            addCriterion("verify_photo not like", value, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoIn(List<String> values) {
            addCriterion("verify_photo in", values, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoNotIn(List<String> values) {
            addCriterion("verify_photo not in", values, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoBetween(String value1, String value2) {
            addCriterion("verify_photo between", value1, value2, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andVerifyPhotoNotBetween(String value1, String value2) {
            addCriterion("verify_photo not between", value1, value2, "verifyPhoto");
            return (Criteria) this;
        }

        public Criteria andWzidIsNull() {
            addCriterion("wzid is null");
            return (Criteria) this;
        }

        public Criteria andWzidIsNotNull() {
            addCriterion("wzid is not null");
            return (Criteria) this;
        }

        public Criteria andWzidEqualTo(Long value) {
            addCriterion("wzid =", value, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidNotEqualTo(Long value) {
            addCriterion("wzid <>", value, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidGreaterThan(Long value) {
            addCriterion("wzid >", value, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidGreaterThanOrEqualTo(Long value) {
            addCriterion("wzid >=", value, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidLessThan(Long value) {
            addCriterion("wzid <", value, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidLessThanOrEqualTo(Long value) {
            addCriterion("wzid <=", value, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidIn(List<Long> values) {
            addCriterion("wzid in", values, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidNotIn(List<Long> values) {
            addCriterion("wzid not in", values, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidBetween(Long value1, Long value2) {
            addCriterion("wzid between", value1, value2, "wzid");
            return (Criteria) this;
        }

        public Criteria andWzidNotBetween(Long value1, Long value2) {
            addCriterion("wzid not between", value1, value2, "wzid");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}