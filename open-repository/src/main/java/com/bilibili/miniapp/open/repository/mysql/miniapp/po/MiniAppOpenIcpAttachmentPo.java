package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpAttachmentPo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * icp流程id
     */
    private Long flowId;

    /**
     * 附件用途 9-主体其他材料 10-服务其他材料 11-相关承诺书
     */
    private Integer type;

    /**
     * 附件文件格式：1 为.jpg，2 为.png，3 为.mp4
     */
    private Integer format;

    /**
     * 附件的下载地址
     */
    private String content;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}