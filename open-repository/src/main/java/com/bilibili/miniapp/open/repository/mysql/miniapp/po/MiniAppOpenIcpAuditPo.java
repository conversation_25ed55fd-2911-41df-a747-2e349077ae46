package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpAuditPo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * icp流程id
     */
    private Long flowId;

    /**
     * 小程序app_id
     */
    private String appId;

    /**
     * 需要审核的信息 是一个大json
     */
    private String auditInfo;

    /**
     * 审核结果 是一个大json，包含多个审核字段的状态及原因
     */
    private String auditResult;

    /**
     * 审核状态 0-待审核 1-审核通过 2-审核不通过
     */
    private Integer auditStatus;

    /**
     * 审核人
     */
    private String operator;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 提审时间
     */
    private Timestamp submitTime;

    private static final long serialVersionUID = 1L;
}