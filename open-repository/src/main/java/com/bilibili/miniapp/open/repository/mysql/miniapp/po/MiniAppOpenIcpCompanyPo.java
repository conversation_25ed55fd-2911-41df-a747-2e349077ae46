package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpCompanyPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 备案流程id
     */
    private Long flowId;

    /**
     * 主体类型。1代表企业（固定值）
     */
    private Integer type;

    /**
     * 证件类型。1代表营业执照（固定值）
     */
    private Integer licenseType;

    /**
     * 证件照
     */
    private String licensePhoto;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 证件编号
     */
    private String licenseNo;

    /**
     * 证件注册地址-省
     */
    private Integer licenseProvince;

    /**
     * 证件注册地址-市
     */
    private Integer licenseCity;

    /**
     * 证件注册地址-区县
     */
    private Integer licenseCounty;

    /**
     * 证件详细地址
     */
    private String licenseDetailAddress;

    /**
     * 企业联系地址-省
     */
    private Integer contactProvince;

    /**
     * 企业联系地址-市
     */
    private Integer contactCity;

    /**
     * 企业联系地址-区
     */
    private Integer contactCounty;

    /**
     * 企业联系详细地址
     */
    private String contactDetailAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 企业负责人证件类型。2代表身份证（固定值）
     */
    private Integer fzrLicenseType;

    /**
     * 企业负责人证件编号
     */
    private String fzrCardNo;

    /**
     * 企业负责人姓名
     */
    private String fzrName;

    /**
     * 身份证正面照片
     */
    private String fzrCardFront;

    /**
     * 身份证反面照片
     */
    private String fzrCardReverse;

    /**
     * 企业负责人身份证开始时间yyyy-MM-dd HH:mm:ss
     */
    private String fzrCardBegin;

    /**
     * 企业负责人身份证结束时间yyyy-MM-dd HH:mm:ss
     */
    private String fzrCardEnd;

    /**
     * 企业负责人身份证是否长期有效：0-否，1-是
     */
    private Integer fzrCardLongEffect;

    /**
     * 企业负责人手机号码
     */
    private String fzrPhone;

    /**
     * 企业负责人邮箱
     */
    private String fzrEmail;

    /**
     * 企业负责人应急电话
     */
    private String fzrEmergency;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 企业id mac开头
     */
    private String companyId;

    private static final long serialVersionUID = 1L;
}