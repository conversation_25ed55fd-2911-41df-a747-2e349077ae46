package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenIcpCompanyPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenIcpCompanyPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(Long value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(Long value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(Long value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(Long value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(Long value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(Long value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<Long> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<Long> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(Long value1, Long value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(Long value1, Long value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeIsNull() {
            addCriterion("license_type is null");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeIsNotNull() {
            addCriterion("license_type is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeEqualTo(Integer value) {
            addCriterion("license_type =", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotEqualTo(Integer value) {
            addCriterion("license_type <>", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeGreaterThan(Integer value) {
            addCriterion("license_type >", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("license_type >=", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeLessThan(Integer value) {
            addCriterion("license_type <", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("license_type <=", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeIn(List<Integer> values) {
            addCriterion("license_type in", values, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotIn(List<Integer> values) {
            addCriterion("license_type not in", values, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeBetween(Integer value1, Integer value2) {
            addCriterion("license_type between", value1, value2, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("license_type not between", value1, value2, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoIsNull() {
            addCriterion("license_photo is null");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoIsNotNull() {
            addCriterion("license_photo is not null");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoEqualTo(String value) {
            addCriterion("license_photo =", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoNotEqualTo(String value) {
            addCriterion("license_photo <>", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoGreaterThan(String value) {
            addCriterion("license_photo >", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoGreaterThanOrEqualTo(String value) {
            addCriterion("license_photo >=", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoLessThan(String value) {
            addCriterion("license_photo <", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoLessThanOrEqualTo(String value) {
            addCriterion("license_photo <=", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoLike(String value) {
            addCriterion("license_photo like", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoNotLike(String value) {
            addCriterion("license_photo not like", value, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoIn(List<String> values) {
            addCriterion("license_photo in", values, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoNotIn(List<String> values) {
            addCriterion("license_photo not in", values, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoBetween(String value1, String value2) {
            addCriterion("license_photo between", value1, value2, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andLicensePhotoNotBetween(String value1, String value2) {
            addCriterion("license_photo not between", value1, value2, "licensePhoto");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIsNull() {
            addCriterion("license_no is null");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIsNotNull() {
            addCriterion("license_no is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseNoEqualTo(String value) {
            addCriterion("license_no =", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotEqualTo(String value) {
            addCriterion("license_no <>", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoGreaterThan(String value) {
            addCriterion("license_no >", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoGreaterThanOrEqualTo(String value) {
            addCriterion("license_no >=", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLessThan(String value) {
            addCriterion("license_no <", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLessThanOrEqualTo(String value) {
            addCriterion("license_no <=", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoLike(String value) {
            addCriterion("license_no like", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotLike(String value) {
            addCriterion("license_no not like", value, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoIn(List<String> values) {
            addCriterion("license_no in", values, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotIn(List<String> values) {
            addCriterion("license_no not in", values, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoBetween(String value1, String value2) {
            addCriterion("license_no between", value1, value2, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseNoNotBetween(String value1, String value2) {
            addCriterion("license_no not between", value1, value2, "licenseNo");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceIsNull() {
            addCriterion("license_province is null");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceIsNotNull() {
            addCriterion("license_province is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceEqualTo(Integer value) {
            addCriterion("license_province =", value, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceNotEqualTo(Integer value) {
            addCriterion("license_province <>", value, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceGreaterThan(Integer value) {
            addCriterion("license_province >", value, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceGreaterThanOrEqualTo(Integer value) {
            addCriterion("license_province >=", value, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceLessThan(Integer value) {
            addCriterion("license_province <", value, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceLessThanOrEqualTo(Integer value) {
            addCriterion("license_province <=", value, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceIn(List<Integer> values) {
            addCriterion("license_province in", values, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceNotIn(List<Integer> values) {
            addCriterion("license_province not in", values, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceBetween(Integer value1, Integer value2) {
            addCriterion("license_province between", value1, value2, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseProvinceNotBetween(Integer value1, Integer value2) {
            addCriterion("license_province not between", value1, value2, "licenseProvince");
            return (Criteria) this;
        }

        public Criteria andLicenseCityIsNull() {
            addCriterion("license_city is null");
            return (Criteria) this;
        }

        public Criteria andLicenseCityIsNotNull() {
            addCriterion("license_city is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseCityEqualTo(Integer value) {
            addCriterion("license_city =", value, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityNotEqualTo(Integer value) {
            addCriterion("license_city <>", value, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityGreaterThan(Integer value) {
            addCriterion("license_city >", value, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityGreaterThanOrEqualTo(Integer value) {
            addCriterion("license_city >=", value, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityLessThan(Integer value) {
            addCriterion("license_city <", value, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityLessThanOrEqualTo(Integer value) {
            addCriterion("license_city <=", value, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityIn(List<Integer> values) {
            addCriterion("license_city in", values, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityNotIn(List<Integer> values) {
            addCriterion("license_city not in", values, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityBetween(Integer value1, Integer value2) {
            addCriterion("license_city between", value1, value2, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCityNotBetween(Integer value1, Integer value2) {
            addCriterion("license_city not between", value1, value2, "licenseCity");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyIsNull() {
            addCriterion("license_county is null");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyIsNotNull() {
            addCriterion("license_county is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyEqualTo(Integer value) {
            addCriterion("license_county =", value, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyNotEqualTo(Integer value) {
            addCriterion("license_county <>", value, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyGreaterThan(Integer value) {
            addCriterion("license_county >", value, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyGreaterThanOrEqualTo(Integer value) {
            addCriterion("license_county >=", value, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyLessThan(Integer value) {
            addCriterion("license_county <", value, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyLessThanOrEqualTo(Integer value) {
            addCriterion("license_county <=", value, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyIn(List<Integer> values) {
            addCriterion("license_county in", values, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyNotIn(List<Integer> values) {
            addCriterion("license_county not in", values, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyBetween(Integer value1, Integer value2) {
            addCriterion("license_county between", value1, value2, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseCountyNotBetween(Integer value1, Integer value2) {
            addCriterion("license_county not between", value1, value2, "licenseCounty");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressIsNull() {
            addCriterion("license_detail_address is null");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressIsNotNull() {
            addCriterion("license_detail_address is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressEqualTo(String value) {
            addCriterion("license_detail_address =", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressNotEqualTo(String value) {
            addCriterion("license_detail_address <>", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressGreaterThan(String value) {
            addCriterion("license_detail_address >", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressGreaterThanOrEqualTo(String value) {
            addCriterion("license_detail_address >=", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressLessThan(String value) {
            addCriterion("license_detail_address <", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressLessThanOrEqualTo(String value) {
            addCriterion("license_detail_address <=", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressLike(String value) {
            addCriterion("license_detail_address like", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressNotLike(String value) {
            addCriterion("license_detail_address not like", value, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressIn(List<String> values) {
            addCriterion("license_detail_address in", values, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressNotIn(List<String> values) {
            addCriterion("license_detail_address not in", values, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressBetween(String value1, String value2) {
            addCriterion("license_detail_address between", value1, value2, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andLicenseDetailAddressNotBetween(String value1, String value2) {
            addCriterion("license_detail_address not between", value1, value2, "licenseDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactProvinceIsNull() {
            addCriterion("contact_province is null");
            return (Criteria) this;
        }

        public Criteria andContactProvinceIsNotNull() {
            addCriterion("contact_province is not null");
            return (Criteria) this;
        }

        public Criteria andContactProvinceEqualTo(Integer value) {
            addCriterion("contact_province =", value, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceNotEqualTo(Integer value) {
            addCriterion("contact_province <>", value, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceGreaterThan(Integer value) {
            addCriterion("contact_province >", value, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceGreaterThanOrEqualTo(Integer value) {
            addCriterion("contact_province >=", value, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceLessThan(Integer value) {
            addCriterion("contact_province <", value, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceLessThanOrEqualTo(Integer value) {
            addCriterion("contact_province <=", value, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceIn(List<Integer> values) {
            addCriterion("contact_province in", values, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceNotIn(List<Integer> values) {
            addCriterion("contact_province not in", values, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceBetween(Integer value1, Integer value2) {
            addCriterion("contact_province between", value1, value2, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactProvinceNotBetween(Integer value1, Integer value2) {
            addCriterion("contact_province not between", value1, value2, "contactProvince");
            return (Criteria) this;
        }

        public Criteria andContactCityIsNull() {
            addCriterion("contact_city is null");
            return (Criteria) this;
        }

        public Criteria andContactCityIsNotNull() {
            addCriterion("contact_city is not null");
            return (Criteria) this;
        }

        public Criteria andContactCityEqualTo(Integer value) {
            addCriterion("contact_city =", value, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityNotEqualTo(Integer value) {
            addCriterion("contact_city <>", value, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityGreaterThan(Integer value) {
            addCriterion("contact_city >", value, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityGreaterThanOrEqualTo(Integer value) {
            addCriterion("contact_city >=", value, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityLessThan(Integer value) {
            addCriterion("contact_city <", value, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityLessThanOrEqualTo(Integer value) {
            addCriterion("contact_city <=", value, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityIn(List<Integer> values) {
            addCriterion("contact_city in", values, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityNotIn(List<Integer> values) {
            addCriterion("contact_city not in", values, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityBetween(Integer value1, Integer value2) {
            addCriterion("contact_city between", value1, value2, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCityNotBetween(Integer value1, Integer value2) {
            addCriterion("contact_city not between", value1, value2, "contactCity");
            return (Criteria) this;
        }

        public Criteria andContactCountyIsNull() {
            addCriterion("contact_county is null");
            return (Criteria) this;
        }

        public Criteria andContactCountyIsNotNull() {
            addCriterion("contact_county is not null");
            return (Criteria) this;
        }

        public Criteria andContactCountyEqualTo(Integer value) {
            addCriterion("contact_county =", value, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyNotEqualTo(Integer value) {
            addCriterion("contact_county <>", value, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyGreaterThan(Integer value) {
            addCriterion("contact_county >", value, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyGreaterThanOrEqualTo(Integer value) {
            addCriterion("contact_county >=", value, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyLessThan(Integer value) {
            addCriterion("contact_county <", value, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyLessThanOrEqualTo(Integer value) {
            addCriterion("contact_county <=", value, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyIn(List<Integer> values) {
            addCriterion("contact_county in", values, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyNotIn(List<Integer> values) {
            addCriterion("contact_county not in", values, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyBetween(Integer value1, Integer value2) {
            addCriterion("contact_county between", value1, value2, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactCountyNotBetween(Integer value1, Integer value2) {
            addCriterion("contact_county not between", value1, value2, "contactCounty");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressIsNull() {
            addCriterion("contact_detail_address is null");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressIsNotNull() {
            addCriterion("contact_detail_address is not null");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressEqualTo(String value) {
            addCriterion("contact_detail_address =", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressNotEqualTo(String value) {
            addCriterion("contact_detail_address <>", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressGreaterThan(String value) {
            addCriterion("contact_detail_address >", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressGreaterThanOrEqualTo(String value) {
            addCriterion("contact_detail_address >=", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressLessThan(String value) {
            addCriterion("contact_detail_address <", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressLessThanOrEqualTo(String value) {
            addCriterion("contact_detail_address <=", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressLike(String value) {
            addCriterion("contact_detail_address like", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressNotLike(String value) {
            addCriterion("contact_detail_address not like", value, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressIn(List<String> values) {
            addCriterion("contact_detail_address in", values, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressNotIn(List<String> values) {
            addCriterion("contact_detail_address not in", values, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressBetween(String value1, String value2) {
            addCriterion("contact_detail_address between", value1, value2, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andContactDetailAddressNotBetween(String value1, String value2) {
            addCriterion("contact_detail_address not between", value1, value2, "contactDetailAddress");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeIsNull() {
            addCriterion("fzr_license_type is null");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeIsNotNull() {
            addCriterion("fzr_license_type is not null");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeEqualTo(Integer value) {
            addCriterion("fzr_license_type =", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeNotEqualTo(Integer value) {
            addCriterion("fzr_license_type <>", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeGreaterThan(Integer value) {
            addCriterion("fzr_license_type >", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("fzr_license_type >=", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeLessThan(Integer value) {
            addCriterion("fzr_license_type <", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("fzr_license_type <=", value, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeIn(List<Integer> values) {
            addCriterion("fzr_license_type in", values, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeNotIn(List<Integer> values) {
            addCriterion("fzr_license_type not in", values, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeBetween(Integer value1, Integer value2) {
            addCriterion("fzr_license_type between", value1, value2, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrLicenseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("fzr_license_type not between", value1, value2, "fzrLicenseType");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoIsNull() {
            addCriterion("fzr_card_no is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoIsNotNull() {
            addCriterion("fzr_card_no is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoEqualTo(String value) {
            addCriterion("fzr_card_no =", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotEqualTo(String value) {
            addCriterion("fzr_card_no <>", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoGreaterThan(String value) {
            addCriterion("fzr_card_no >", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_no >=", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoLessThan(String value) {
            addCriterion("fzr_card_no <", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_no <=", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoLike(String value) {
            addCriterion("fzr_card_no like", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotLike(String value) {
            addCriterion("fzr_card_no not like", value, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoIn(List<String> values) {
            addCriterion("fzr_card_no in", values, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotIn(List<String> values) {
            addCriterion("fzr_card_no not in", values, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoBetween(String value1, String value2) {
            addCriterion("fzr_card_no between", value1, value2, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrCardNoNotBetween(String value1, String value2) {
            addCriterion("fzr_card_no not between", value1, value2, "fzrCardNo");
            return (Criteria) this;
        }

        public Criteria andFzrNameIsNull() {
            addCriterion("fzr_name is null");
            return (Criteria) this;
        }

        public Criteria andFzrNameIsNotNull() {
            addCriterion("fzr_name is not null");
            return (Criteria) this;
        }

        public Criteria andFzrNameEqualTo(String value) {
            addCriterion("fzr_name =", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotEqualTo(String value) {
            addCriterion("fzr_name <>", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameGreaterThan(String value) {
            addCriterion("fzr_name >", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_name >=", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameLessThan(String value) {
            addCriterion("fzr_name <", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameLessThanOrEqualTo(String value) {
            addCriterion("fzr_name <=", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameLike(String value) {
            addCriterion("fzr_name like", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotLike(String value) {
            addCriterion("fzr_name not like", value, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameIn(List<String> values) {
            addCriterion("fzr_name in", values, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotIn(List<String> values) {
            addCriterion("fzr_name not in", values, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameBetween(String value1, String value2) {
            addCriterion("fzr_name between", value1, value2, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrNameNotBetween(String value1, String value2) {
            addCriterion("fzr_name not between", value1, value2, "fzrName");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontIsNull() {
            addCriterion("fzr_card_front is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontIsNotNull() {
            addCriterion("fzr_card_front is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontEqualTo(String value) {
            addCriterion("fzr_card_front =", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontNotEqualTo(String value) {
            addCriterion("fzr_card_front <>", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontGreaterThan(String value) {
            addCriterion("fzr_card_front >", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_front >=", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontLessThan(String value) {
            addCriterion("fzr_card_front <", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_front <=", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontLike(String value) {
            addCriterion("fzr_card_front like", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontNotLike(String value) {
            addCriterion("fzr_card_front not like", value, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontIn(List<String> values) {
            addCriterion("fzr_card_front in", values, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontNotIn(List<String> values) {
            addCriterion("fzr_card_front not in", values, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontBetween(String value1, String value2) {
            addCriterion("fzr_card_front between", value1, value2, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardFrontNotBetween(String value1, String value2) {
            addCriterion("fzr_card_front not between", value1, value2, "fzrCardFront");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseIsNull() {
            addCriterion("fzr_card_reverse is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseIsNotNull() {
            addCriterion("fzr_card_reverse is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseEqualTo(String value) {
            addCriterion("fzr_card_reverse =", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseNotEqualTo(String value) {
            addCriterion("fzr_card_reverse <>", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseGreaterThan(String value) {
            addCriterion("fzr_card_reverse >", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_reverse >=", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseLessThan(String value) {
            addCriterion("fzr_card_reverse <", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_reverse <=", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseLike(String value) {
            addCriterion("fzr_card_reverse like", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseNotLike(String value) {
            addCriterion("fzr_card_reverse not like", value, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseIn(List<String> values) {
            addCriterion("fzr_card_reverse in", values, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseNotIn(List<String> values) {
            addCriterion("fzr_card_reverse not in", values, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseBetween(String value1, String value2) {
            addCriterion("fzr_card_reverse between", value1, value2, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardReverseNotBetween(String value1, String value2) {
            addCriterion("fzr_card_reverse not between", value1, value2, "fzrCardReverse");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginIsNull() {
            addCriterion("fzr_card_begin is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginIsNotNull() {
            addCriterion("fzr_card_begin is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginEqualTo(String value) {
            addCriterion("fzr_card_begin =", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotEqualTo(String value) {
            addCriterion("fzr_card_begin <>", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginGreaterThan(String value) {
            addCriterion("fzr_card_begin >", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_begin >=", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginLessThan(String value) {
            addCriterion("fzr_card_begin <", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_begin <=", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginLike(String value) {
            addCriterion("fzr_card_begin like", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotLike(String value) {
            addCriterion("fzr_card_begin not like", value, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginIn(List<String> values) {
            addCriterion("fzr_card_begin in", values, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotIn(List<String> values) {
            addCriterion("fzr_card_begin not in", values, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginBetween(String value1, String value2) {
            addCriterion("fzr_card_begin between", value1, value2, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardBeginNotBetween(String value1, String value2) {
            addCriterion("fzr_card_begin not between", value1, value2, "fzrCardBegin");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndIsNull() {
            addCriterion("fzr_card_end is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndIsNotNull() {
            addCriterion("fzr_card_end is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndEqualTo(String value) {
            addCriterion("fzr_card_end =", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotEqualTo(String value) {
            addCriterion("fzr_card_end <>", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndGreaterThan(String value) {
            addCriterion("fzr_card_end >", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_card_end >=", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndLessThan(String value) {
            addCriterion("fzr_card_end <", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndLessThanOrEqualTo(String value) {
            addCriterion("fzr_card_end <=", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndLike(String value) {
            addCriterion("fzr_card_end like", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotLike(String value) {
            addCriterion("fzr_card_end not like", value, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndIn(List<String> values) {
            addCriterion("fzr_card_end in", values, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotIn(List<String> values) {
            addCriterion("fzr_card_end not in", values, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndBetween(String value1, String value2) {
            addCriterion("fzr_card_end between", value1, value2, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardEndNotBetween(String value1, String value2) {
            addCriterion("fzr_card_end not between", value1, value2, "fzrCardEnd");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectIsNull() {
            addCriterion("fzr_card_long_effect is null");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectIsNotNull() {
            addCriterion("fzr_card_long_effect is not null");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect =", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectNotEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect <>", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectGreaterThan(Integer value) {
            addCriterion("fzr_card_long_effect >", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectGreaterThanOrEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect >=", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectLessThan(Integer value) {
            addCriterion("fzr_card_long_effect <", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectLessThanOrEqualTo(Integer value) {
            addCriterion("fzr_card_long_effect <=", value, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectIn(List<Integer> values) {
            addCriterion("fzr_card_long_effect in", values, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectNotIn(List<Integer> values) {
            addCriterion("fzr_card_long_effect not in", values, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectBetween(Integer value1, Integer value2) {
            addCriterion("fzr_card_long_effect between", value1, value2, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrCardLongEffectNotBetween(Integer value1, Integer value2) {
            addCriterion("fzr_card_long_effect not between", value1, value2, "fzrCardLongEffect");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneIsNull() {
            addCriterion("fzr_phone is null");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneIsNotNull() {
            addCriterion("fzr_phone is not null");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneEqualTo(String value) {
            addCriterion("fzr_phone =", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotEqualTo(String value) {
            addCriterion("fzr_phone <>", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneGreaterThan(String value) {
            addCriterion("fzr_phone >", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_phone >=", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneLessThan(String value) {
            addCriterion("fzr_phone <", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneLessThanOrEqualTo(String value) {
            addCriterion("fzr_phone <=", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneLike(String value) {
            addCriterion("fzr_phone like", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotLike(String value) {
            addCriterion("fzr_phone not like", value, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneIn(List<String> values) {
            addCriterion("fzr_phone in", values, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotIn(List<String> values) {
            addCriterion("fzr_phone not in", values, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneBetween(String value1, String value2) {
            addCriterion("fzr_phone between", value1, value2, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrPhoneNotBetween(String value1, String value2) {
            addCriterion("fzr_phone not between", value1, value2, "fzrPhone");
            return (Criteria) this;
        }

        public Criteria andFzrEmailIsNull() {
            addCriterion("fzr_email is null");
            return (Criteria) this;
        }

        public Criteria andFzrEmailIsNotNull() {
            addCriterion("fzr_email is not null");
            return (Criteria) this;
        }

        public Criteria andFzrEmailEqualTo(String value) {
            addCriterion("fzr_email =", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotEqualTo(String value) {
            addCriterion("fzr_email <>", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailGreaterThan(String value) {
            addCriterion("fzr_email >", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_email >=", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailLessThan(String value) {
            addCriterion("fzr_email <", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailLessThanOrEqualTo(String value) {
            addCriterion("fzr_email <=", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailLike(String value) {
            addCriterion("fzr_email like", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotLike(String value) {
            addCriterion("fzr_email not like", value, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailIn(List<String> values) {
            addCriterion("fzr_email in", values, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotIn(List<String> values) {
            addCriterion("fzr_email not in", values, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailBetween(String value1, String value2) {
            addCriterion("fzr_email between", value1, value2, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmailNotBetween(String value1, String value2) {
            addCriterion("fzr_email not between", value1, value2, "fzrEmail");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyIsNull() {
            addCriterion("fzr_emergency is null");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyIsNotNull() {
            addCriterion("fzr_emergency is not null");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyEqualTo(String value) {
            addCriterion("fzr_emergency =", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotEqualTo(String value) {
            addCriterion("fzr_emergency <>", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyGreaterThan(String value) {
            addCriterion("fzr_emergency >", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_emergency >=", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyLessThan(String value) {
            addCriterion("fzr_emergency <", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyLessThanOrEqualTo(String value) {
            addCriterion("fzr_emergency <=", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyLike(String value) {
            addCriterion("fzr_emergency like", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotLike(String value) {
            addCriterion("fzr_emergency not like", value, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyIn(List<String> values) {
            addCriterion("fzr_emergency in", values, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotIn(List<String> values) {
            addCriterion("fzr_emergency not in", values, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyBetween(String value1, String value2) {
            addCriterion("fzr_emergency between", value1, value2, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andFzrEmergencyNotBetween(String value1, String value2) {
            addCriterion("fzr_emergency not between", value1, value2, "fzrEmergency");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}