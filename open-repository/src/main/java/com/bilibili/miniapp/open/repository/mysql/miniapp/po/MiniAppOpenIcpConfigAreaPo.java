package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpConfigAreaPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 区域码
     */
    private Long code;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 区域类型 1-省 2-市 3-区县
     */
    private Integer type;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer status;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}