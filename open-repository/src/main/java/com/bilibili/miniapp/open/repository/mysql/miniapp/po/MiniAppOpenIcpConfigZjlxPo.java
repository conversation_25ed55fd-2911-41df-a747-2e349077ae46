package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpConfigZjlxPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 证件类型码
     */
    private Long code;

    /**
     * 证件类型名称
     */
    private String name;

    /**
     * 单位性质表的code
     */
    private Long dwflId;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer status;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}