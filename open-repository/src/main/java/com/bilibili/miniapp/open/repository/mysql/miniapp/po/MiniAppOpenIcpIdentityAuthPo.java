package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpIdentityAuthPo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 互联网服务ID(业务主键)
     */
    private Long ispWzid;

    /**
     * 单位名称
     */
    private String dwmc;

    /**
     * 省份ID
     */
    private Integer shengid;

    /**
     * 单位性质
     */
    private Integer dwxz;

    /**
     * 证件类型
     */
    private Integer zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 负责人姓名
     */
    private String fzrXm;

    /**
     * 负责人证件类型
     */
    private Integer fzrZjlx;

    /**
     * 负责人证件号
     */
    private String fzrZjhm;

    /**
     * 证件有效期起 yyyy-MM-dd HH:mm:ss格式
     */
    private String fzrZjyxqStart;

    /**
     * 证件有效期止(长期需转换) yyyy-MM-dd HH:mm:ss格式
     */
    private String fzrZjyxqEnd;

    /**
     * 网站名称
     */
    private String wzmc;

    /**
     * 服务类型
     */
    private Integer fwlx;

    /**
     * 前置审批(0-无需,1-需要)
     */
    private Integer nrlx;

    /**
     * 网站负责人
     */
    private String wzFzrXm;

    /**
     * 证件类型
     */
    private Integer wzFzrZjlx;

    /**
     * 证件号码
     */
    private String wzFzrZjhm;

    /**
     * 有效期起 yyyy-MM-dd HH:mm:ss格式
     */
    private String wzFzrZjyxqStart;

    /**
     * 有效期止 yyyy-MM-dd HH:mm:ss格式
     */
    private String wzFzrZjyxqEnd;

    /**
     * 域名列表(分号分隔)
     */
    private String domain;

    /**
     * 认证核验状态 0-未核验 1-已核验
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 负责人证件有效期是否长期有效 0-否 1-是
     */
    private Integer fzrZjyxqCqyx;

    /**
     * 网站负责人证件有效期是否长期有效 0-否 1-是
     */
    private Integer wzFzrZjyxqCqyx;

    private static final long serialVersionUID = 1L;
}