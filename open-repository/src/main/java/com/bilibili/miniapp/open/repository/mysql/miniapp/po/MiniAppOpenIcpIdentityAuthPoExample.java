package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenIcpIdentityAuthPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenIcpIdentityAuthPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIspWzidIsNull() {
            addCriterion("isp_wzid is null");
            return (Criteria) this;
        }

        public Criteria andIspWzidIsNotNull() {
            addCriterion("isp_wzid is not null");
            return (Criteria) this;
        }

        public Criteria andIspWzidEqualTo(Long value) {
            addCriterion("isp_wzid =", value, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidNotEqualTo(Long value) {
            addCriterion("isp_wzid <>", value, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidGreaterThan(Long value) {
            addCriterion("isp_wzid >", value, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidGreaterThanOrEqualTo(Long value) {
            addCriterion("isp_wzid >=", value, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidLessThan(Long value) {
            addCriterion("isp_wzid <", value, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidLessThanOrEqualTo(Long value) {
            addCriterion("isp_wzid <=", value, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidIn(List<Long> values) {
            addCriterion("isp_wzid in", values, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidNotIn(List<Long> values) {
            addCriterion("isp_wzid not in", values, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidBetween(Long value1, Long value2) {
            addCriterion("isp_wzid between", value1, value2, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andIspWzidNotBetween(Long value1, Long value2) {
            addCriterion("isp_wzid not between", value1, value2, "ispWzid");
            return (Criteria) this;
        }

        public Criteria andDwmcIsNull() {
            addCriterion("dwmc is null");
            return (Criteria) this;
        }

        public Criteria andDwmcIsNotNull() {
            addCriterion("dwmc is not null");
            return (Criteria) this;
        }

        public Criteria andDwmcEqualTo(String value) {
            addCriterion("dwmc =", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcNotEqualTo(String value) {
            addCriterion("dwmc <>", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcGreaterThan(String value) {
            addCriterion("dwmc >", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcGreaterThanOrEqualTo(String value) {
            addCriterion("dwmc >=", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcLessThan(String value) {
            addCriterion("dwmc <", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcLessThanOrEqualTo(String value) {
            addCriterion("dwmc <=", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcLike(String value) {
            addCriterion("dwmc like", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcNotLike(String value) {
            addCriterion("dwmc not like", value, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcIn(List<String> values) {
            addCriterion("dwmc in", values, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcNotIn(List<String> values) {
            addCriterion("dwmc not in", values, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcBetween(String value1, String value2) {
            addCriterion("dwmc between", value1, value2, "dwmc");
            return (Criteria) this;
        }

        public Criteria andDwmcNotBetween(String value1, String value2) {
            addCriterion("dwmc not between", value1, value2, "dwmc");
            return (Criteria) this;
        }

        public Criteria andShengidIsNull() {
            addCriterion("shengid is null");
            return (Criteria) this;
        }

        public Criteria andShengidIsNotNull() {
            addCriterion("shengid is not null");
            return (Criteria) this;
        }

        public Criteria andShengidEqualTo(Integer value) {
            addCriterion("shengid =", value, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidNotEqualTo(Integer value) {
            addCriterion("shengid <>", value, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidGreaterThan(Integer value) {
            addCriterion("shengid >", value, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidGreaterThanOrEqualTo(Integer value) {
            addCriterion("shengid >=", value, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidLessThan(Integer value) {
            addCriterion("shengid <", value, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidLessThanOrEqualTo(Integer value) {
            addCriterion("shengid <=", value, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidIn(List<Integer> values) {
            addCriterion("shengid in", values, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidNotIn(List<Integer> values) {
            addCriterion("shengid not in", values, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidBetween(Integer value1, Integer value2) {
            addCriterion("shengid between", value1, value2, "shengid");
            return (Criteria) this;
        }

        public Criteria andShengidNotBetween(Integer value1, Integer value2) {
            addCriterion("shengid not between", value1, value2, "shengid");
            return (Criteria) this;
        }

        public Criteria andDwxzIsNull() {
            addCriterion("dwxz is null");
            return (Criteria) this;
        }

        public Criteria andDwxzIsNotNull() {
            addCriterion("dwxz is not null");
            return (Criteria) this;
        }

        public Criteria andDwxzEqualTo(Integer value) {
            addCriterion("dwxz =", value, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzNotEqualTo(Integer value) {
            addCriterion("dwxz <>", value, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzGreaterThan(Integer value) {
            addCriterion("dwxz >", value, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzGreaterThanOrEqualTo(Integer value) {
            addCriterion("dwxz >=", value, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzLessThan(Integer value) {
            addCriterion("dwxz <", value, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzLessThanOrEqualTo(Integer value) {
            addCriterion("dwxz <=", value, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzIn(List<Integer> values) {
            addCriterion("dwxz in", values, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzNotIn(List<Integer> values) {
            addCriterion("dwxz not in", values, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzBetween(Integer value1, Integer value2) {
            addCriterion("dwxz between", value1, value2, "dwxz");
            return (Criteria) this;
        }

        public Criteria andDwxzNotBetween(Integer value1, Integer value2) {
            addCriterion("dwxz not between", value1, value2, "dwxz");
            return (Criteria) this;
        }

        public Criteria andZjlxIsNull() {
            addCriterion("zjlx is null");
            return (Criteria) this;
        }

        public Criteria andZjlxIsNotNull() {
            addCriterion("zjlx is not null");
            return (Criteria) this;
        }

        public Criteria andZjlxEqualTo(Integer value) {
            addCriterion("zjlx =", value, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxNotEqualTo(Integer value) {
            addCriterion("zjlx <>", value, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxGreaterThan(Integer value) {
            addCriterion("zjlx >", value, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxGreaterThanOrEqualTo(Integer value) {
            addCriterion("zjlx >=", value, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxLessThan(Integer value) {
            addCriterion("zjlx <", value, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxLessThanOrEqualTo(Integer value) {
            addCriterion("zjlx <=", value, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxIn(List<Integer> values) {
            addCriterion("zjlx in", values, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxNotIn(List<Integer> values) {
            addCriterion("zjlx not in", values, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxBetween(Integer value1, Integer value2) {
            addCriterion("zjlx between", value1, value2, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjlxNotBetween(Integer value1, Integer value2) {
            addCriterion("zjlx not between", value1, value2, "zjlx");
            return (Criteria) this;
        }

        public Criteria andZjhmIsNull() {
            addCriterion("zjhm is null");
            return (Criteria) this;
        }

        public Criteria andZjhmIsNotNull() {
            addCriterion("zjhm is not null");
            return (Criteria) this;
        }

        public Criteria andZjhmEqualTo(String value) {
            addCriterion("zjhm =", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmNotEqualTo(String value) {
            addCriterion("zjhm <>", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmGreaterThan(String value) {
            addCriterion("zjhm >", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmGreaterThanOrEqualTo(String value) {
            addCriterion("zjhm >=", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmLessThan(String value) {
            addCriterion("zjhm <", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmLessThanOrEqualTo(String value) {
            addCriterion("zjhm <=", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmLike(String value) {
            addCriterion("zjhm like", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmNotLike(String value) {
            addCriterion("zjhm not like", value, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmIn(List<String> values) {
            addCriterion("zjhm in", values, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmNotIn(List<String> values) {
            addCriterion("zjhm not in", values, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmBetween(String value1, String value2) {
            addCriterion("zjhm between", value1, value2, "zjhm");
            return (Criteria) this;
        }

        public Criteria andZjhmNotBetween(String value1, String value2) {
            addCriterion("zjhm not between", value1, value2, "zjhm");
            return (Criteria) this;
        }

        public Criteria andFzrXmIsNull() {
            addCriterion("fzr_xm is null");
            return (Criteria) this;
        }

        public Criteria andFzrXmIsNotNull() {
            addCriterion("fzr_xm is not null");
            return (Criteria) this;
        }

        public Criteria andFzrXmEqualTo(String value) {
            addCriterion("fzr_xm =", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmNotEqualTo(String value) {
            addCriterion("fzr_xm <>", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmGreaterThan(String value) {
            addCriterion("fzr_xm >", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_xm >=", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmLessThan(String value) {
            addCriterion("fzr_xm <", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmLessThanOrEqualTo(String value) {
            addCriterion("fzr_xm <=", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmLike(String value) {
            addCriterion("fzr_xm like", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmNotLike(String value) {
            addCriterion("fzr_xm not like", value, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmIn(List<String> values) {
            addCriterion("fzr_xm in", values, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmNotIn(List<String> values) {
            addCriterion("fzr_xm not in", values, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmBetween(String value1, String value2) {
            addCriterion("fzr_xm between", value1, value2, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrXmNotBetween(String value1, String value2) {
            addCriterion("fzr_xm not between", value1, value2, "fzrXm");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxIsNull() {
            addCriterion("fzr_zjlx is null");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxIsNotNull() {
            addCriterion("fzr_zjlx is not null");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxEqualTo(Integer value) {
            addCriterion("fzr_zjlx =", value, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxNotEqualTo(Integer value) {
            addCriterion("fzr_zjlx <>", value, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxGreaterThan(Integer value) {
            addCriterion("fzr_zjlx >", value, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxGreaterThanOrEqualTo(Integer value) {
            addCriterion("fzr_zjlx >=", value, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxLessThan(Integer value) {
            addCriterion("fzr_zjlx <", value, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxLessThanOrEqualTo(Integer value) {
            addCriterion("fzr_zjlx <=", value, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxIn(List<Integer> values) {
            addCriterion("fzr_zjlx in", values, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxNotIn(List<Integer> values) {
            addCriterion("fzr_zjlx not in", values, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxBetween(Integer value1, Integer value2) {
            addCriterion("fzr_zjlx between", value1, value2, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjlxNotBetween(Integer value1, Integer value2) {
            addCriterion("fzr_zjlx not between", value1, value2, "fzrZjlx");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmIsNull() {
            addCriterion("fzr_zjhm is null");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmIsNotNull() {
            addCriterion("fzr_zjhm is not null");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmEqualTo(String value) {
            addCriterion("fzr_zjhm =", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmNotEqualTo(String value) {
            addCriterion("fzr_zjhm <>", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmGreaterThan(String value) {
            addCriterion("fzr_zjhm >", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_zjhm >=", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmLessThan(String value) {
            addCriterion("fzr_zjhm <", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmLessThanOrEqualTo(String value) {
            addCriterion("fzr_zjhm <=", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmLike(String value) {
            addCriterion("fzr_zjhm like", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmNotLike(String value) {
            addCriterion("fzr_zjhm not like", value, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmIn(List<String> values) {
            addCriterion("fzr_zjhm in", values, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmNotIn(List<String> values) {
            addCriterion("fzr_zjhm not in", values, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmBetween(String value1, String value2) {
            addCriterion("fzr_zjhm between", value1, value2, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjhmNotBetween(String value1, String value2) {
            addCriterion("fzr_zjhm not between", value1, value2, "fzrZjhm");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartIsNull() {
            addCriterion("fzr_zjyxq_start is null");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartIsNotNull() {
            addCriterion("fzr_zjyxq_start is not null");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartEqualTo(String value) {
            addCriterion("fzr_zjyxq_start =", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartNotEqualTo(String value) {
            addCriterion("fzr_zjyxq_start <>", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartGreaterThan(String value) {
            addCriterion("fzr_zjyxq_start >", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_zjyxq_start >=", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartLessThan(String value) {
            addCriterion("fzr_zjyxq_start <", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartLessThanOrEqualTo(String value) {
            addCriterion("fzr_zjyxq_start <=", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartLike(String value) {
            addCriterion("fzr_zjyxq_start like", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartNotLike(String value) {
            addCriterion("fzr_zjyxq_start not like", value, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartIn(List<String> values) {
            addCriterion("fzr_zjyxq_start in", values, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartNotIn(List<String> values) {
            addCriterion("fzr_zjyxq_start not in", values, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartBetween(String value1, String value2) {
            addCriterion("fzr_zjyxq_start between", value1, value2, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqStartNotBetween(String value1, String value2) {
            addCriterion("fzr_zjyxq_start not between", value1, value2, "fzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndIsNull() {
            addCriterion("fzr_zjyxq_end is null");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndIsNotNull() {
            addCriterion("fzr_zjyxq_end is not null");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndEqualTo(String value) {
            addCriterion("fzr_zjyxq_end =", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndNotEqualTo(String value) {
            addCriterion("fzr_zjyxq_end <>", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndGreaterThan(String value) {
            addCriterion("fzr_zjyxq_end >", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndGreaterThanOrEqualTo(String value) {
            addCriterion("fzr_zjyxq_end >=", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndLessThan(String value) {
            addCriterion("fzr_zjyxq_end <", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndLessThanOrEqualTo(String value) {
            addCriterion("fzr_zjyxq_end <=", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndLike(String value) {
            addCriterion("fzr_zjyxq_end like", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndNotLike(String value) {
            addCriterion("fzr_zjyxq_end not like", value, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndIn(List<String> values) {
            addCriterion("fzr_zjyxq_end in", values, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndNotIn(List<String> values) {
            addCriterion("fzr_zjyxq_end not in", values, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndBetween(String value1, String value2) {
            addCriterion("fzr_zjyxq_end between", value1, value2, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqEndNotBetween(String value1, String value2) {
            addCriterion("fzr_zjyxq_end not between", value1, value2, "fzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzmcIsNull() {
            addCriterion("wzmc is null");
            return (Criteria) this;
        }

        public Criteria andWzmcIsNotNull() {
            addCriterion("wzmc is not null");
            return (Criteria) this;
        }

        public Criteria andWzmcEqualTo(String value) {
            addCriterion("wzmc =", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcNotEqualTo(String value) {
            addCriterion("wzmc <>", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcGreaterThan(String value) {
            addCriterion("wzmc >", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcGreaterThanOrEqualTo(String value) {
            addCriterion("wzmc >=", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcLessThan(String value) {
            addCriterion("wzmc <", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcLessThanOrEqualTo(String value) {
            addCriterion("wzmc <=", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcLike(String value) {
            addCriterion("wzmc like", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcNotLike(String value) {
            addCriterion("wzmc not like", value, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcIn(List<String> values) {
            addCriterion("wzmc in", values, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcNotIn(List<String> values) {
            addCriterion("wzmc not in", values, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcBetween(String value1, String value2) {
            addCriterion("wzmc between", value1, value2, "wzmc");
            return (Criteria) this;
        }

        public Criteria andWzmcNotBetween(String value1, String value2) {
            addCriterion("wzmc not between", value1, value2, "wzmc");
            return (Criteria) this;
        }

        public Criteria andFwlxIsNull() {
            addCriterion("fwlx is null");
            return (Criteria) this;
        }

        public Criteria andFwlxIsNotNull() {
            addCriterion("fwlx is not null");
            return (Criteria) this;
        }

        public Criteria andFwlxEqualTo(Integer value) {
            addCriterion("fwlx =", value, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxNotEqualTo(Integer value) {
            addCriterion("fwlx <>", value, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxGreaterThan(Integer value) {
            addCriterion("fwlx >", value, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxGreaterThanOrEqualTo(Integer value) {
            addCriterion("fwlx >=", value, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxLessThan(Integer value) {
            addCriterion("fwlx <", value, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxLessThanOrEqualTo(Integer value) {
            addCriterion("fwlx <=", value, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxIn(List<Integer> values) {
            addCriterion("fwlx in", values, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxNotIn(List<Integer> values) {
            addCriterion("fwlx not in", values, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxBetween(Integer value1, Integer value2) {
            addCriterion("fwlx between", value1, value2, "fwlx");
            return (Criteria) this;
        }

        public Criteria andFwlxNotBetween(Integer value1, Integer value2) {
            addCriterion("fwlx not between", value1, value2, "fwlx");
            return (Criteria) this;
        }

        public Criteria andNrlxIsNull() {
            addCriterion("nrlx is null");
            return (Criteria) this;
        }

        public Criteria andNrlxIsNotNull() {
            addCriterion("nrlx is not null");
            return (Criteria) this;
        }

        public Criteria andNrlxEqualTo(Integer value) {
            addCriterion("nrlx =", value, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxNotEqualTo(Integer value) {
            addCriterion("nrlx <>", value, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxGreaterThan(Integer value) {
            addCriterion("nrlx >", value, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxGreaterThanOrEqualTo(Integer value) {
            addCriterion("nrlx >=", value, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxLessThan(Integer value) {
            addCriterion("nrlx <", value, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxLessThanOrEqualTo(Integer value) {
            addCriterion("nrlx <=", value, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxIn(List<Integer> values) {
            addCriterion("nrlx in", values, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxNotIn(List<Integer> values) {
            addCriterion("nrlx not in", values, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxBetween(Integer value1, Integer value2) {
            addCriterion("nrlx between", value1, value2, "nrlx");
            return (Criteria) this;
        }

        public Criteria andNrlxNotBetween(Integer value1, Integer value2) {
            addCriterion("nrlx not between", value1, value2, "nrlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmIsNull() {
            addCriterion("wz_fzr_xm is null");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmIsNotNull() {
            addCriterion("wz_fzr_xm is not null");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmEqualTo(String value) {
            addCriterion("wz_fzr_xm =", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmNotEqualTo(String value) {
            addCriterion("wz_fzr_xm <>", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmGreaterThan(String value) {
            addCriterion("wz_fzr_xm >", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmGreaterThanOrEqualTo(String value) {
            addCriterion("wz_fzr_xm >=", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmLessThan(String value) {
            addCriterion("wz_fzr_xm <", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmLessThanOrEqualTo(String value) {
            addCriterion("wz_fzr_xm <=", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmLike(String value) {
            addCriterion("wz_fzr_xm like", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmNotLike(String value) {
            addCriterion("wz_fzr_xm not like", value, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmIn(List<String> values) {
            addCriterion("wz_fzr_xm in", values, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmNotIn(List<String> values) {
            addCriterion("wz_fzr_xm not in", values, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmBetween(String value1, String value2) {
            addCriterion("wz_fzr_xm between", value1, value2, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrXmNotBetween(String value1, String value2) {
            addCriterion("wz_fzr_xm not between", value1, value2, "wzFzrXm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxIsNull() {
            addCriterion("wz_fzr_zjlx is null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxIsNotNull() {
            addCriterion("wz_fzr_zjlx is not null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxEqualTo(Integer value) {
            addCriterion("wz_fzr_zjlx =", value, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxNotEqualTo(Integer value) {
            addCriterion("wz_fzr_zjlx <>", value, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxGreaterThan(Integer value) {
            addCriterion("wz_fzr_zjlx >", value, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxGreaterThanOrEqualTo(Integer value) {
            addCriterion("wz_fzr_zjlx >=", value, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxLessThan(Integer value) {
            addCriterion("wz_fzr_zjlx <", value, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxLessThanOrEqualTo(Integer value) {
            addCriterion("wz_fzr_zjlx <=", value, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxIn(List<Integer> values) {
            addCriterion("wz_fzr_zjlx in", values, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxNotIn(List<Integer> values) {
            addCriterion("wz_fzr_zjlx not in", values, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxBetween(Integer value1, Integer value2) {
            addCriterion("wz_fzr_zjlx between", value1, value2, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjlxNotBetween(Integer value1, Integer value2) {
            addCriterion("wz_fzr_zjlx not between", value1, value2, "wzFzrZjlx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmIsNull() {
            addCriterion("wz_fzr_zjhm is null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmIsNotNull() {
            addCriterion("wz_fzr_zjhm is not null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmEqualTo(String value) {
            addCriterion("wz_fzr_zjhm =", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmNotEqualTo(String value) {
            addCriterion("wz_fzr_zjhm <>", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmGreaterThan(String value) {
            addCriterion("wz_fzr_zjhm >", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmGreaterThanOrEqualTo(String value) {
            addCriterion("wz_fzr_zjhm >=", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmLessThan(String value) {
            addCriterion("wz_fzr_zjhm <", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmLessThanOrEqualTo(String value) {
            addCriterion("wz_fzr_zjhm <=", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmLike(String value) {
            addCriterion("wz_fzr_zjhm like", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmNotLike(String value) {
            addCriterion("wz_fzr_zjhm not like", value, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmIn(List<String> values) {
            addCriterion("wz_fzr_zjhm in", values, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmNotIn(List<String> values) {
            addCriterion("wz_fzr_zjhm not in", values, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmBetween(String value1, String value2) {
            addCriterion("wz_fzr_zjhm between", value1, value2, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjhmNotBetween(String value1, String value2) {
            addCriterion("wz_fzr_zjhm not between", value1, value2, "wzFzrZjhm");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartIsNull() {
            addCriterion("wz_fzr_zjyxq_start is null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartIsNotNull() {
            addCriterion("wz_fzr_zjyxq_start is not null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_start =", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartNotEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_start <>", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartGreaterThan(String value) {
            addCriterion("wz_fzr_zjyxq_start >", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartGreaterThanOrEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_start >=", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartLessThan(String value) {
            addCriterion("wz_fzr_zjyxq_start <", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartLessThanOrEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_start <=", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartLike(String value) {
            addCriterion("wz_fzr_zjyxq_start like", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartNotLike(String value) {
            addCriterion("wz_fzr_zjyxq_start not like", value, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartIn(List<String> values) {
            addCriterion("wz_fzr_zjyxq_start in", values, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartNotIn(List<String> values) {
            addCriterion("wz_fzr_zjyxq_start not in", values, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartBetween(String value1, String value2) {
            addCriterion("wz_fzr_zjyxq_start between", value1, value2, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqStartNotBetween(String value1, String value2) {
            addCriterion("wz_fzr_zjyxq_start not between", value1, value2, "wzFzrZjyxqStart");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndIsNull() {
            addCriterion("wz_fzr_zjyxq_end is null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndIsNotNull() {
            addCriterion("wz_fzr_zjyxq_end is not null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_end =", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndNotEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_end <>", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndGreaterThan(String value) {
            addCriterion("wz_fzr_zjyxq_end >", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndGreaterThanOrEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_end >=", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndLessThan(String value) {
            addCriterion("wz_fzr_zjyxq_end <", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndLessThanOrEqualTo(String value) {
            addCriterion("wz_fzr_zjyxq_end <=", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndLike(String value) {
            addCriterion("wz_fzr_zjyxq_end like", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndNotLike(String value) {
            addCriterion("wz_fzr_zjyxq_end not like", value, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndIn(List<String> values) {
            addCriterion("wz_fzr_zjyxq_end in", values, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndNotIn(List<String> values) {
            addCriterion("wz_fzr_zjyxq_end not in", values, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndBetween(String value1, String value2) {
            addCriterion("wz_fzr_zjyxq_end between", value1, value2, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqEndNotBetween(String value1, String value2) {
            addCriterion("wz_fzr_zjyxq_end not between", value1, value2, "wzFzrZjyxqEnd");
            return (Criteria) this;
        }

        public Criteria andDomainIsNull() {
            addCriterion("domain is null");
            return (Criteria) this;
        }

        public Criteria andDomainIsNotNull() {
            addCriterion("domain is not null");
            return (Criteria) this;
        }

        public Criteria andDomainEqualTo(String value) {
            addCriterion("domain =", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotEqualTo(String value) {
            addCriterion("domain <>", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainGreaterThan(String value) {
            addCriterion("domain >", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainGreaterThanOrEqualTo(String value) {
            addCriterion("domain >=", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLessThan(String value) {
            addCriterion("domain <", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLessThanOrEqualTo(String value) {
            addCriterion("domain <=", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainLike(String value) {
            addCriterion("domain like", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotLike(String value) {
            addCriterion("domain not like", value, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainIn(List<String> values) {
            addCriterion("domain in", values, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotIn(List<String> values) {
            addCriterion("domain not in", values, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainBetween(String value1, String value2) {
            addCriterion("domain between", value1, value2, "domain");
            return (Criteria) this;
        }

        public Criteria andDomainNotBetween(String value1, String value2) {
            addCriterion("domain not between", value1, value2, "domain");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxIsNull() {
            addCriterion("fzr_zjyxq_cqyx is null");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxIsNotNull() {
            addCriterion("fzr_zjyxq_cqyx is not null");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxEqualTo(Integer value) {
            addCriterion("fzr_zjyxq_cqyx =", value, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxNotEqualTo(Integer value) {
            addCriterion("fzr_zjyxq_cqyx <>", value, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxGreaterThan(Integer value) {
            addCriterion("fzr_zjyxq_cqyx >", value, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxGreaterThanOrEqualTo(Integer value) {
            addCriterion("fzr_zjyxq_cqyx >=", value, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxLessThan(Integer value) {
            addCriterion("fzr_zjyxq_cqyx <", value, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxLessThanOrEqualTo(Integer value) {
            addCriterion("fzr_zjyxq_cqyx <=", value, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxIn(List<Integer> values) {
            addCriterion("fzr_zjyxq_cqyx in", values, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxNotIn(List<Integer> values) {
            addCriterion("fzr_zjyxq_cqyx not in", values, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxBetween(Integer value1, Integer value2) {
            addCriterion("fzr_zjyxq_cqyx between", value1, value2, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andFzrZjyxqCqyxNotBetween(Integer value1, Integer value2) {
            addCriterion("fzr_zjyxq_cqyx not between", value1, value2, "fzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxIsNull() {
            addCriterion("wz_fzr_zjyxq_cqyx is null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxIsNotNull() {
            addCriterion("wz_fzr_zjyxq_cqyx is not null");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxEqualTo(Integer value) {
            addCriterion("wz_fzr_zjyxq_cqyx =", value, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxNotEqualTo(Integer value) {
            addCriterion("wz_fzr_zjyxq_cqyx <>", value, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxGreaterThan(Integer value) {
            addCriterion("wz_fzr_zjyxq_cqyx >", value, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxGreaterThanOrEqualTo(Integer value) {
            addCriterion("wz_fzr_zjyxq_cqyx >=", value, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxLessThan(Integer value) {
            addCriterion("wz_fzr_zjyxq_cqyx <", value, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxLessThanOrEqualTo(Integer value) {
            addCriterion("wz_fzr_zjyxq_cqyx <=", value, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxIn(List<Integer> values) {
            addCriterion("wz_fzr_zjyxq_cqyx in", values, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxNotIn(List<Integer> values) {
            addCriterion("wz_fzr_zjyxq_cqyx not in", values, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxBetween(Integer value1, Integer value2) {
            addCriterion("wz_fzr_zjyxq_cqyx between", value1, value2, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }

        public Criteria andWzFzrZjyxqCqyxNotBetween(Integer value1, Integer value2) {
            addCriterion("wz_fzr_zjyxq_cqyx not between", value1, value2, "wzFzrZjyxqCqyx");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}