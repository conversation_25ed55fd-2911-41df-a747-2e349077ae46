package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpIdentityMaterialPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 人证核验id
     */
    private Long wzid;

    /**
     * 1-服务备案信息真实性核验单 2-主办单位证件 3-主体负责人有效证件 4-服务负责人有效证件 5-服务负责人核验现场拍摄照片电子件 9-主体其他材料 10-服务其他材料 11-相关承诺书
     */
    private Integer type;

    /**
     * 素材地址
     */
    private String content;

    /**
     * 1 为.jpg，2 为.png，3 为.mp4
     */
    private Integer format;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}