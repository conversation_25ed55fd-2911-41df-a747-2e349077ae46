package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenIcpPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序的备案编号
     */
    private String recordNumber;

    /**
     * CP提交备案流程的时间
     */
    private Timestamp submitTime;

    /**
     * 备案流程状态：0-尚未备案，1-平台审核中，2-平台审核未通过，3-工信部短信核验中，4-工信部短信核验超时，5-管局审核中，6-管局审核未通过，7-备案已完成
     */
    private Integer flowStatus;

    /**
     * 管局审核状态：0-未提交管局，1-待审核，2-审核通过，3-审核拒绝
     */
    private Integer govAuditState;

    /**
     * 管局审核时间
     */
    private String govAuditTime;

    /**
     * 管局审核结果代码
     */
    private Long govAuditCode;

    /**
     * 管局审核代码描述
     */
    private String govAuditMsg;

    /**
     * 管局审核意见
     */
    private String govAuditDescription;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 是否已发送工信部审核 0-否 1-是
     */
    private Integer reportStatus;

    private static final long serialVersionUID = 1L;
}