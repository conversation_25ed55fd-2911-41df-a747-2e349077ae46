package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppOpenIcpPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppOpenIcpPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNull() {
            addCriterion("record_number is null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIsNotNull() {
            addCriterion("record_number is not null");
            return (Criteria) this;
        }

        public Criteria andRecordNumberEqualTo(String value) {
            addCriterion("record_number =", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotEqualTo(String value) {
            addCriterion("record_number <>", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThan(String value) {
            addCriterion("record_number >", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberGreaterThanOrEqualTo(String value) {
            addCriterion("record_number >=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThan(String value) {
            addCriterion("record_number <", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLessThanOrEqualTo(String value) {
            addCriterion("record_number <=", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberLike(String value) {
            addCriterion("record_number like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotLike(String value) {
            addCriterion("record_number not like", value, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberIn(List<String> values) {
            addCriterion("record_number in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotIn(List<String> values) {
            addCriterion("record_number not in", values, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberBetween(String value1, String value2) {
            addCriterion("record_number between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andRecordNumberNotBetween(String value1, String value2) {
            addCriterion("record_number not between", value1, value2, "recordNumber");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNull() {
            addCriterion("submit_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNotNull() {
            addCriterion("submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeEqualTo(Timestamp value) {
            addCriterion("submit_time =", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotEqualTo(Timestamp value) {
            addCriterion("submit_time <>", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThan(Timestamp value) {
            addCriterion("submit_time >", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("submit_time >=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThan(Timestamp value) {
            addCriterion("submit_time <", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("submit_time <=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIn(List<Timestamp> values) {
            addCriterion("submit_time in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotIn(List<Timestamp> values) {
            addCriterion("submit_time not in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("submit_time between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("submit_time not between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andFlowStatusIsNull() {
            addCriterion("flow_status is null");
            return (Criteria) this;
        }

        public Criteria andFlowStatusIsNotNull() {
            addCriterion("flow_status is not null");
            return (Criteria) this;
        }

        public Criteria andFlowStatusEqualTo(Integer value) {
            addCriterion("flow_status =", value, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusNotEqualTo(Integer value) {
            addCriterion("flow_status <>", value, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusGreaterThan(Integer value) {
            addCriterion("flow_status >", value, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("flow_status >=", value, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusLessThan(Integer value) {
            addCriterion("flow_status <", value, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusLessThanOrEqualTo(Integer value) {
            addCriterion("flow_status <=", value, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusIn(List<Integer> values) {
            addCriterion("flow_status in", values, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusNotIn(List<Integer> values) {
            addCriterion("flow_status not in", values, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusBetween(Integer value1, Integer value2) {
            addCriterion("flow_status between", value1, value2, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andFlowStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("flow_status not between", value1, value2, "flowStatus");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateIsNull() {
            addCriterion("gov_audit_state is null");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateIsNotNull() {
            addCriterion("gov_audit_state is not null");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateEqualTo(Integer value) {
            addCriterion("gov_audit_state =", value, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateNotEqualTo(Integer value) {
            addCriterion("gov_audit_state <>", value, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateGreaterThan(Integer value) {
            addCriterion("gov_audit_state >", value, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("gov_audit_state >=", value, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateLessThan(Integer value) {
            addCriterion("gov_audit_state <", value, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateLessThanOrEqualTo(Integer value) {
            addCriterion("gov_audit_state <=", value, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateIn(List<Integer> values) {
            addCriterion("gov_audit_state in", values, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateNotIn(List<Integer> values) {
            addCriterion("gov_audit_state not in", values, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateBetween(Integer value1, Integer value2) {
            addCriterion("gov_audit_state between", value1, value2, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditStateNotBetween(Integer value1, Integer value2) {
            addCriterion("gov_audit_state not between", value1, value2, "govAuditState");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeIsNull() {
            addCriterion("gov_audit_time is null");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeIsNotNull() {
            addCriterion("gov_audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeEqualTo(String value) {
            addCriterion("gov_audit_time =", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeNotEqualTo(String value) {
            addCriterion("gov_audit_time <>", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeGreaterThan(String value) {
            addCriterion("gov_audit_time >", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeGreaterThanOrEqualTo(String value) {
            addCriterion("gov_audit_time >=", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeLessThan(String value) {
            addCriterion("gov_audit_time <", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeLessThanOrEqualTo(String value) {
            addCriterion("gov_audit_time <=", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeLike(String value) {
            addCriterion("gov_audit_time like", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeNotLike(String value) {
            addCriterion("gov_audit_time not like", value, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeIn(List<String> values) {
            addCriterion("gov_audit_time in", values, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeNotIn(List<String> values) {
            addCriterion("gov_audit_time not in", values, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeBetween(String value1, String value2) {
            addCriterion("gov_audit_time between", value1, value2, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditTimeNotBetween(String value1, String value2) {
            addCriterion("gov_audit_time not between", value1, value2, "govAuditTime");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeIsNull() {
            addCriterion("gov_audit_code is null");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeIsNotNull() {
            addCriterion("gov_audit_code is not null");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeEqualTo(Long value) {
            addCriterion("gov_audit_code =", value, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeNotEqualTo(Long value) {
            addCriterion("gov_audit_code <>", value, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeGreaterThan(Long value) {
            addCriterion("gov_audit_code >", value, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("gov_audit_code >=", value, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeLessThan(Long value) {
            addCriterion("gov_audit_code <", value, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeLessThanOrEqualTo(Long value) {
            addCriterion("gov_audit_code <=", value, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeIn(List<Long> values) {
            addCriterion("gov_audit_code in", values, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeNotIn(List<Long> values) {
            addCriterion("gov_audit_code not in", values, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeBetween(Long value1, Long value2) {
            addCriterion("gov_audit_code between", value1, value2, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditCodeNotBetween(Long value1, Long value2) {
            addCriterion("gov_audit_code not between", value1, value2, "govAuditCode");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgIsNull() {
            addCriterion("gov_audit_msg is null");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgIsNotNull() {
            addCriterion("gov_audit_msg is not null");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgEqualTo(String value) {
            addCriterion("gov_audit_msg =", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgNotEqualTo(String value) {
            addCriterion("gov_audit_msg <>", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgGreaterThan(String value) {
            addCriterion("gov_audit_msg >", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgGreaterThanOrEqualTo(String value) {
            addCriterion("gov_audit_msg >=", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgLessThan(String value) {
            addCriterion("gov_audit_msg <", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgLessThanOrEqualTo(String value) {
            addCriterion("gov_audit_msg <=", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgLike(String value) {
            addCriterion("gov_audit_msg like", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgNotLike(String value) {
            addCriterion("gov_audit_msg not like", value, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgIn(List<String> values) {
            addCriterion("gov_audit_msg in", values, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgNotIn(List<String> values) {
            addCriterion("gov_audit_msg not in", values, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgBetween(String value1, String value2) {
            addCriterion("gov_audit_msg between", value1, value2, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditMsgNotBetween(String value1, String value2) {
            addCriterion("gov_audit_msg not between", value1, value2, "govAuditMsg");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionIsNull() {
            addCriterion("gov_audit_description is null");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionIsNotNull() {
            addCriterion("gov_audit_description is not null");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionEqualTo(String value) {
            addCriterion("gov_audit_description =", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionNotEqualTo(String value) {
            addCriterion("gov_audit_description <>", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionGreaterThan(String value) {
            addCriterion("gov_audit_description >", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("gov_audit_description >=", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionLessThan(String value) {
            addCriterion("gov_audit_description <", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionLessThanOrEqualTo(String value) {
            addCriterion("gov_audit_description <=", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionLike(String value) {
            addCriterion("gov_audit_description like", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionNotLike(String value) {
            addCriterion("gov_audit_description not like", value, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionIn(List<String> values) {
            addCriterion("gov_audit_description in", values, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionNotIn(List<String> values) {
            addCriterion("gov_audit_description not in", values, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionBetween(String value1, String value2) {
            addCriterion("gov_audit_description between", value1, value2, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andGovAuditDescriptionNotBetween(String value1, String value2) {
            addCriterion("gov_audit_description not between", value1, value2, "govAuditDescription");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNull() {
            addCriterion("report_status is null");
            return (Criteria) this;
        }

        public Criteria andReportStatusIsNotNull() {
            addCriterion("report_status is not null");
            return (Criteria) this;
        }

        public Criteria andReportStatusEqualTo(Integer value) {
            addCriterion("report_status =", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotEqualTo(Integer value) {
            addCriterion("report_status <>", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThan(Integer value) {
            addCriterion("report_status >", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_status >=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThan(Integer value) {
            addCriterion("report_status <", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusLessThanOrEqualTo(Integer value) {
            addCriterion("report_status <=", value, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusIn(List<Integer> values) {
            addCriterion("report_status in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotIn(List<Integer> values) {
            addCriterion("report_status not in", values, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusBetween(Integer value1, Integer value2) {
            addCriterion("report_status between", value1, value2, "reportStatus");
            return (Criteria) this;
        }

        public Criteria andReportStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("report_status not between", value1, value2, "reportStatus");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}