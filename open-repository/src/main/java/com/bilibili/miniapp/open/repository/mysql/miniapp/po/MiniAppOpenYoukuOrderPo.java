package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppOpenYoukuOrderPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 订单唯⼀id
     */
    private Long orderId;

    /**
     * 订单唯一码，一般不使用
     */
    private String orderCode;

    /**
     * 订单类型，签约订单、续费订单、普通订单
     */
    private String orderType;

    /**
     * ⽤户唯⼀id
     */
    private String userId;

    /**
     * 订单⾦额（元）
     */
    private String payAmt;

    /**
     * 订单付费时间，如：2024-11-06 13:12:58
     */
    private String payTime;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 会员权益名称
     */
    private String skuName;

    /**
     * ⽀付⽅式
     */
    private String payType;

    /**
     * 退款状态，0：未退款，1：已退款
     */
    private Integer refundState;

    /**
     * 退款时间，如：2024-11-06 13:12:58
     */
    private String refundTime;

    /**
     * 退款⾦额（元）
     */
    private String refundAmt;

    /**
     * 签约订单id，针对续费订单提供
     */
    private Long signOrderId;

    /**
     * 签约状态，1：签约，2：解约，0：⾮签约单或签约未⽣效
     */
    private Integer signState;

    /**
     * 解约⽇期，如：20241212
     */
    private String signOffDate;

    /**
     * 扩展参数，格式k1=v1&k2=v2 设备属性：openid、platform（系统）、ver（版本）、dev （设备） 挂链携带额外参数：ug_extra，该参数包含expert_uid（外部 达⼈id）、vid（分集id）、sid（媒体id）、scene（场景id） 等
     */
    private String args;

    /**
     * 订单应⽤：如：抖⾳、快⼿
     */
    private String app;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}