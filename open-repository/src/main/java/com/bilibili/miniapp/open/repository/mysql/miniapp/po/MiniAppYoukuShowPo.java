package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppYoukuShowPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 节目ID（示例：debd6922d1604e42863b）
     */
    private String showId;

    /**
     * 节目名称
     */
    private String name;

    /**
     * 节目子标题
     */
    private String subtitle;

    /**
     * 节目主分类
     */
    private String category;

    /**
     * 题材
     */
    private String genre;

    /**
     * 节⽬16:9横版海报(超⼤)
     */
    private String thumbUrlHuge;

    /**
     * 节⽬3:4竖版海报(超⼤)
     */
    private String w3H4ThumbUrlHuge;

    /**
     * 是否已完结 0-否 1-是
     */
    private Integer completed;

    /**
     * 是否是付费节目 0-否 1-是
     */
    private Integer paid;

    /**
     * 总集数
     */
    private Integer episodeTotal;

    /**
     * 最后可播正⽚集号
     */
    private Integer lastEpisode;

    /**
     * 最后可播正⽚数字集号
     */
    private Integer lastStage;

    /**
     * 导演
     */
    private String director;

    /**
     * 演员
     */
    private String performer;

    /**
     * 发⾏时间(YYYY-MM-DD)
     */
    private String releaseDate;

    /**
     * 节目归属地
     */
    private String area;

    /**
     * 热度
     */
    private Integer heat;

    /**
     * 语言
     */
    private String language;

    /**
     * 是否独播 0-否 1-是
     */
    private Integer exclusive;

    /**
     * 评分
     */
    private String reputation;

    /**
     * 链接
     */
    private String link;

    /**
     * 节目更新信息
     */
    private String updateNotice;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 节⽬简介
     */
    private String description;

    private static final long serialVersionUID = 1L;
}