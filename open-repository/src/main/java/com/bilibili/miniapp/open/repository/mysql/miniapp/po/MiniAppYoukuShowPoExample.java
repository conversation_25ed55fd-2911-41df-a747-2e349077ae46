package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class MiniAppYoukuShowPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public MiniAppYoukuShowPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andShowIdIsNull() {
            addCriterion("show_id is null");
            return (Criteria) this;
        }

        public Criteria andShowIdIsNotNull() {
            addCriterion("show_id is not null");
            return (Criteria) this;
        }

        public Criteria andShowIdEqualTo(String value) {
            addCriterion("show_id =", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdNotEqualTo(String value) {
            addCriterion("show_id <>", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdGreaterThan(String value) {
            addCriterion("show_id >", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdGreaterThanOrEqualTo(String value) {
            addCriterion("show_id >=", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdLessThan(String value) {
            addCriterion("show_id <", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdLessThanOrEqualTo(String value) {
            addCriterion("show_id <=", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdLike(String value) {
            addCriterion("show_id like", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdNotLike(String value) {
            addCriterion("show_id not like", value, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdIn(List<String> values) {
            addCriterion("show_id in", values, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdNotIn(List<String> values) {
            addCriterion("show_id not in", values, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdBetween(String value1, String value2) {
            addCriterion("show_id between", value1, value2, "showId");
            return (Criteria) this;
        }

        public Criteria andShowIdNotBetween(String value1, String value2) {
            addCriterion("show_id not between", value1, value2, "showId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andSubtitleIsNull() {
            addCriterion("subtitle is null");
            return (Criteria) this;
        }

        public Criteria andSubtitleIsNotNull() {
            addCriterion("subtitle is not null");
            return (Criteria) this;
        }

        public Criteria andSubtitleEqualTo(String value) {
            addCriterion("subtitle =", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleNotEqualTo(String value) {
            addCriterion("subtitle <>", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleGreaterThan(String value) {
            addCriterion("subtitle >", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleGreaterThanOrEqualTo(String value) {
            addCriterion("subtitle >=", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleLessThan(String value) {
            addCriterion("subtitle <", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleLessThanOrEqualTo(String value) {
            addCriterion("subtitle <=", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleLike(String value) {
            addCriterion("subtitle like", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleNotLike(String value) {
            addCriterion("subtitle not like", value, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleIn(List<String> values) {
            addCriterion("subtitle in", values, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleNotIn(List<String> values) {
            addCriterion("subtitle not in", values, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleBetween(String value1, String value2) {
            addCriterion("subtitle between", value1, value2, "subtitle");
            return (Criteria) this;
        }

        public Criteria andSubtitleNotBetween(String value1, String value2) {
            addCriterion("subtitle not between", value1, value2, "subtitle");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andGenreIsNull() {
            addCriterion("genre is null");
            return (Criteria) this;
        }

        public Criteria andGenreIsNotNull() {
            addCriterion("genre is not null");
            return (Criteria) this;
        }

        public Criteria andGenreEqualTo(String value) {
            addCriterion("genre =", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotEqualTo(String value) {
            addCriterion("genre <>", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreGreaterThan(String value) {
            addCriterion("genre >", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreGreaterThanOrEqualTo(String value) {
            addCriterion("genre >=", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreLessThan(String value) {
            addCriterion("genre <", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreLessThanOrEqualTo(String value) {
            addCriterion("genre <=", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreLike(String value) {
            addCriterion("genre like", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotLike(String value) {
            addCriterion("genre not like", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreIn(List<String> values) {
            addCriterion("genre in", values, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotIn(List<String> values) {
            addCriterion("genre not in", values, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreBetween(String value1, String value2) {
            addCriterion("genre between", value1, value2, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotBetween(String value1, String value2) {
            addCriterion("genre not between", value1, value2, "genre");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeIsNull() {
            addCriterion("thumb_url_huge is null");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeIsNotNull() {
            addCriterion("thumb_url_huge is not null");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeEqualTo(String value) {
            addCriterion("thumb_url_huge =", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeNotEqualTo(String value) {
            addCriterion("thumb_url_huge <>", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeGreaterThan(String value) {
            addCriterion("thumb_url_huge >", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeGreaterThanOrEqualTo(String value) {
            addCriterion("thumb_url_huge >=", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeLessThan(String value) {
            addCriterion("thumb_url_huge <", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeLessThanOrEqualTo(String value) {
            addCriterion("thumb_url_huge <=", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeLike(String value) {
            addCriterion("thumb_url_huge like", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeNotLike(String value) {
            addCriterion("thumb_url_huge not like", value, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeIn(List<String> values) {
            addCriterion("thumb_url_huge in", values, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeNotIn(List<String> values) {
            addCriterion("thumb_url_huge not in", values, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeBetween(String value1, String value2) {
            addCriterion("thumb_url_huge between", value1, value2, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andThumbUrlHugeNotBetween(String value1, String value2) {
            addCriterion("thumb_url_huge not between", value1, value2, "thumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeIsNull() {
            addCriterion("w3_h4_thumb_url_huge is null");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeIsNotNull() {
            addCriterion("w3_h4_thumb_url_huge is not null");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeEqualTo(String value) {
            addCriterion("w3_h4_thumb_url_huge =", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeNotEqualTo(String value) {
            addCriterion("w3_h4_thumb_url_huge <>", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeGreaterThan(String value) {
            addCriterion("w3_h4_thumb_url_huge >", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeGreaterThanOrEqualTo(String value) {
            addCriterion("w3_h4_thumb_url_huge >=", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeLessThan(String value) {
            addCriterion("w3_h4_thumb_url_huge <", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeLessThanOrEqualTo(String value) {
            addCriterion("w3_h4_thumb_url_huge <=", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeLike(String value) {
            addCriterion("w3_h4_thumb_url_huge like", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeNotLike(String value) {
            addCriterion("w3_h4_thumb_url_huge not like", value, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeIn(List<String> values) {
            addCriterion("w3_h4_thumb_url_huge in", values, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeNotIn(List<String> values) {
            addCriterion("w3_h4_thumb_url_huge not in", values, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeBetween(String value1, String value2) {
            addCriterion("w3_h4_thumb_url_huge between", value1, value2, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andW3H4ThumbUrlHugeNotBetween(String value1, String value2) {
            addCriterion("w3_h4_thumb_url_huge not between", value1, value2, "w3H4ThumbUrlHuge");
            return (Criteria) this;
        }

        public Criteria andCompletedIsNull() {
            addCriterion("completed is null");
            return (Criteria) this;
        }

        public Criteria andCompletedIsNotNull() {
            addCriterion("completed is not null");
            return (Criteria) this;
        }

        public Criteria andCompletedEqualTo(Integer value) {
            addCriterion("completed =", value, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedNotEqualTo(Integer value) {
            addCriterion("completed <>", value, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedGreaterThan(Integer value) {
            addCriterion("completed >", value, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("completed >=", value, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedLessThan(Integer value) {
            addCriterion("completed <", value, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedLessThanOrEqualTo(Integer value) {
            addCriterion("completed <=", value, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedIn(List<Integer> values) {
            addCriterion("completed in", values, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedNotIn(List<Integer> values) {
            addCriterion("completed not in", values, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedBetween(Integer value1, Integer value2) {
            addCriterion("completed between", value1, value2, "completed");
            return (Criteria) this;
        }

        public Criteria andCompletedNotBetween(Integer value1, Integer value2) {
            addCriterion("completed not between", value1, value2, "completed");
            return (Criteria) this;
        }

        public Criteria andPaidIsNull() {
            addCriterion("paid is null");
            return (Criteria) this;
        }

        public Criteria andPaidIsNotNull() {
            addCriterion("paid is not null");
            return (Criteria) this;
        }

        public Criteria andPaidEqualTo(Integer value) {
            addCriterion("paid =", value, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidNotEqualTo(Integer value) {
            addCriterion("paid <>", value, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidGreaterThan(Integer value) {
            addCriterion("paid >", value, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidGreaterThanOrEqualTo(Integer value) {
            addCriterion("paid >=", value, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidLessThan(Integer value) {
            addCriterion("paid <", value, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidLessThanOrEqualTo(Integer value) {
            addCriterion("paid <=", value, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidIn(List<Integer> values) {
            addCriterion("paid in", values, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidNotIn(List<Integer> values) {
            addCriterion("paid not in", values, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidBetween(Integer value1, Integer value2) {
            addCriterion("paid between", value1, value2, "paid");
            return (Criteria) this;
        }

        public Criteria andPaidNotBetween(Integer value1, Integer value2) {
            addCriterion("paid not between", value1, value2, "paid");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalIsNull() {
            addCriterion("episode_total is null");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalIsNotNull() {
            addCriterion("episode_total is not null");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalEqualTo(Integer value) {
            addCriterion("episode_total =", value, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalNotEqualTo(Integer value) {
            addCriterion("episode_total <>", value, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalGreaterThan(Integer value) {
            addCriterion("episode_total >", value, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalGreaterThanOrEqualTo(Integer value) {
            addCriterion("episode_total >=", value, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalLessThan(Integer value) {
            addCriterion("episode_total <", value, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalLessThanOrEqualTo(Integer value) {
            addCriterion("episode_total <=", value, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalIn(List<Integer> values) {
            addCriterion("episode_total in", values, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalNotIn(List<Integer> values) {
            addCriterion("episode_total not in", values, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalBetween(Integer value1, Integer value2) {
            addCriterion("episode_total between", value1, value2, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andEpisodeTotalNotBetween(Integer value1, Integer value2) {
            addCriterion("episode_total not between", value1, value2, "episodeTotal");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeIsNull() {
            addCriterion("last_episode is null");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeIsNotNull() {
            addCriterion("last_episode is not null");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeEqualTo(Integer value) {
            addCriterion("last_episode =", value, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeNotEqualTo(Integer value) {
            addCriterion("last_episode <>", value, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeGreaterThan(Integer value) {
            addCriterion("last_episode >", value, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_episode >=", value, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeLessThan(Integer value) {
            addCriterion("last_episode <", value, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeLessThanOrEqualTo(Integer value) {
            addCriterion("last_episode <=", value, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeIn(List<Integer> values) {
            addCriterion("last_episode in", values, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeNotIn(List<Integer> values) {
            addCriterion("last_episode not in", values, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeBetween(Integer value1, Integer value2) {
            addCriterion("last_episode between", value1, value2, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastEpisodeNotBetween(Integer value1, Integer value2) {
            addCriterion("last_episode not between", value1, value2, "lastEpisode");
            return (Criteria) this;
        }

        public Criteria andLastStageIsNull() {
            addCriterion("last_stage is null");
            return (Criteria) this;
        }

        public Criteria andLastStageIsNotNull() {
            addCriterion("last_stage is not null");
            return (Criteria) this;
        }

        public Criteria andLastStageEqualTo(Integer value) {
            addCriterion("last_stage =", value, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageNotEqualTo(Integer value) {
            addCriterion("last_stage <>", value, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageGreaterThan(Integer value) {
            addCriterion("last_stage >", value, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_stage >=", value, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageLessThan(Integer value) {
            addCriterion("last_stage <", value, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageLessThanOrEqualTo(Integer value) {
            addCriterion("last_stage <=", value, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageIn(List<Integer> values) {
            addCriterion("last_stage in", values, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageNotIn(List<Integer> values) {
            addCriterion("last_stage not in", values, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageBetween(Integer value1, Integer value2) {
            addCriterion("last_stage between", value1, value2, "lastStage");
            return (Criteria) this;
        }

        public Criteria andLastStageNotBetween(Integer value1, Integer value2) {
            addCriterion("last_stage not between", value1, value2, "lastStage");
            return (Criteria) this;
        }

        public Criteria andDirectorIsNull() {
            addCriterion("director is null");
            return (Criteria) this;
        }

        public Criteria andDirectorIsNotNull() {
            addCriterion("director is not null");
            return (Criteria) this;
        }

        public Criteria andDirectorEqualTo(String value) {
            addCriterion("director =", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotEqualTo(String value) {
            addCriterion("director <>", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorGreaterThan(String value) {
            addCriterion("director >", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorGreaterThanOrEqualTo(String value) {
            addCriterion("director >=", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorLessThan(String value) {
            addCriterion("director <", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorLessThanOrEqualTo(String value) {
            addCriterion("director <=", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorLike(String value) {
            addCriterion("director like", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotLike(String value) {
            addCriterion("director not like", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorIn(List<String> values) {
            addCriterion("director in", values, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotIn(List<String> values) {
            addCriterion("director not in", values, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorBetween(String value1, String value2) {
            addCriterion("director between", value1, value2, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotBetween(String value1, String value2) {
            addCriterion("director not between", value1, value2, "director");
            return (Criteria) this;
        }

        public Criteria andPerformerIsNull() {
            addCriterion("performer is null");
            return (Criteria) this;
        }

        public Criteria andPerformerIsNotNull() {
            addCriterion("performer is not null");
            return (Criteria) this;
        }

        public Criteria andPerformerEqualTo(String value) {
            addCriterion("performer =", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerNotEqualTo(String value) {
            addCriterion("performer <>", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerGreaterThan(String value) {
            addCriterion("performer >", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerGreaterThanOrEqualTo(String value) {
            addCriterion("performer >=", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerLessThan(String value) {
            addCriterion("performer <", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerLessThanOrEqualTo(String value) {
            addCriterion("performer <=", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerLike(String value) {
            addCriterion("performer like", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerNotLike(String value) {
            addCriterion("performer not like", value, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerIn(List<String> values) {
            addCriterion("performer in", values, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerNotIn(List<String> values) {
            addCriterion("performer not in", values, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerBetween(String value1, String value2) {
            addCriterion("performer between", value1, value2, "performer");
            return (Criteria) this;
        }

        public Criteria andPerformerNotBetween(String value1, String value2) {
            addCriterion("performer not between", value1, value2, "performer");
            return (Criteria) this;
        }

        public Criteria andReleaseDateIsNull() {
            addCriterion("release_date is null");
            return (Criteria) this;
        }

        public Criteria andReleaseDateIsNotNull() {
            addCriterion("release_date is not null");
            return (Criteria) this;
        }

        public Criteria andReleaseDateEqualTo(String value) {
            addCriterion("release_date =", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateNotEqualTo(String value) {
            addCriterion("release_date <>", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateGreaterThan(String value) {
            addCriterion("release_date >", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateGreaterThanOrEqualTo(String value) {
            addCriterion("release_date >=", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateLessThan(String value) {
            addCriterion("release_date <", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateLessThanOrEqualTo(String value) {
            addCriterion("release_date <=", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateLike(String value) {
            addCriterion("release_date like", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateNotLike(String value) {
            addCriterion("release_date not like", value, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateIn(List<String> values) {
            addCriterion("release_date in", values, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateNotIn(List<String> values) {
            addCriterion("release_date not in", values, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateBetween(String value1, String value2) {
            addCriterion("release_date between", value1, value2, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andReleaseDateNotBetween(String value1, String value2) {
            addCriterion("release_date not between", value1, value2, "releaseDate");
            return (Criteria) this;
        }

        public Criteria andAreaIsNull() {
            addCriterion("area is null");
            return (Criteria) this;
        }

        public Criteria andAreaIsNotNull() {
            addCriterion("area is not null");
            return (Criteria) this;
        }

        public Criteria andAreaEqualTo(String value) {
            addCriterion("area =", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotEqualTo(String value) {
            addCriterion("area <>", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThan(String value) {
            addCriterion("area >", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThanOrEqualTo(String value) {
            addCriterion("area >=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThan(String value) {
            addCriterion("area <", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThanOrEqualTo(String value) {
            addCriterion("area <=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLike(String value) {
            addCriterion("area like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotLike(String value) {
            addCriterion("area not like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaIn(List<String> values) {
            addCriterion("area in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotIn(List<String> values) {
            addCriterion("area not in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaBetween(String value1, String value2) {
            addCriterion("area between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotBetween(String value1, String value2) {
            addCriterion("area not between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andHeatIsNull() {
            addCriterion("heat is null");
            return (Criteria) this;
        }

        public Criteria andHeatIsNotNull() {
            addCriterion("heat is not null");
            return (Criteria) this;
        }

        public Criteria andHeatEqualTo(Integer value) {
            addCriterion("heat =", value, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatNotEqualTo(Integer value) {
            addCriterion("heat <>", value, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatGreaterThan(Integer value) {
            addCriterion("heat >", value, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatGreaterThanOrEqualTo(Integer value) {
            addCriterion("heat >=", value, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatLessThan(Integer value) {
            addCriterion("heat <", value, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatLessThanOrEqualTo(Integer value) {
            addCriterion("heat <=", value, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatIn(List<Integer> values) {
            addCriterion("heat in", values, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatNotIn(List<Integer> values) {
            addCriterion("heat not in", values, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatBetween(Integer value1, Integer value2) {
            addCriterion("heat between", value1, value2, "heat");
            return (Criteria) this;
        }

        public Criteria andHeatNotBetween(Integer value1, Integer value2) {
            addCriterion("heat not between", value1, value2, "heat");
            return (Criteria) this;
        }

        public Criteria andLanguageIsNull() {
            addCriterion("language is null");
            return (Criteria) this;
        }

        public Criteria andLanguageIsNotNull() {
            addCriterion("language is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageEqualTo(String value) {
            addCriterion("language =", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageNotEqualTo(String value) {
            addCriterion("language <>", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageGreaterThan(String value) {
            addCriterion("language >", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageGreaterThanOrEqualTo(String value) {
            addCriterion("language >=", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageLessThan(String value) {
            addCriterion("language <", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageLessThanOrEqualTo(String value) {
            addCriterion("language <=", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageLike(String value) {
            addCriterion("language like", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageNotLike(String value) {
            addCriterion("language not like", value, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageIn(List<String> values) {
            addCriterion("language in", values, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageNotIn(List<String> values) {
            addCriterion("language not in", values, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageBetween(String value1, String value2) {
            addCriterion("language between", value1, value2, "language");
            return (Criteria) this;
        }

        public Criteria andLanguageNotBetween(String value1, String value2) {
            addCriterion("language not between", value1, value2, "language");
            return (Criteria) this;
        }

        public Criteria andExclusiveIsNull() {
            addCriterion("exclusive is null");
            return (Criteria) this;
        }

        public Criteria andExclusiveIsNotNull() {
            addCriterion("exclusive is not null");
            return (Criteria) this;
        }

        public Criteria andExclusiveEqualTo(Integer value) {
            addCriterion("exclusive =", value, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveNotEqualTo(Integer value) {
            addCriterion("exclusive <>", value, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveGreaterThan(Integer value) {
            addCriterion("exclusive >", value, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("exclusive >=", value, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveLessThan(Integer value) {
            addCriterion("exclusive <", value, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveLessThanOrEqualTo(Integer value) {
            addCriterion("exclusive <=", value, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveIn(List<Integer> values) {
            addCriterion("exclusive in", values, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveNotIn(List<Integer> values) {
            addCriterion("exclusive not in", values, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveBetween(Integer value1, Integer value2) {
            addCriterion("exclusive between", value1, value2, "exclusive");
            return (Criteria) this;
        }

        public Criteria andExclusiveNotBetween(Integer value1, Integer value2) {
            addCriterion("exclusive not between", value1, value2, "exclusive");
            return (Criteria) this;
        }

        public Criteria andReputationIsNull() {
            addCriterion("reputation is null");
            return (Criteria) this;
        }

        public Criteria andReputationIsNotNull() {
            addCriterion("reputation is not null");
            return (Criteria) this;
        }

        public Criteria andReputationEqualTo(String value) {
            addCriterion("reputation =", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationNotEqualTo(String value) {
            addCriterion("reputation <>", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationGreaterThan(String value) {
            addCriterion("reputation >", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationGreaterThanOrEqualTo(String value) {
            addCriterion("reputation >=", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationLessThan(String value) {
            addCriterion("reputation <", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationLessThanOrEqualTo(String value) {
            addCriterion("reputation <=", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationLike(String value) {
            addCriterion("reputation like", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationNotLike(String value) {
            addCriterion("reputation not like", value, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationIn(List<String> values) {
            addCriterion("reputation in", values, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationNotIn(List<String> values) {
            addCriterion("reputation not in", values, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationBetween(String value1, String value2) {
            addCriterion("reputation between", value1, value2, "reputation");
            return (Criteria) this;
        }

        public Criteria andReputationNotBetween(String value1, String value2) {
            addCriterion("reputation not between", value1, value2, "reputation");
            return (Criteria) this;
        }

        public Criteria andLinkIsNull() {
            addCriterion("link is null");
            return (Criteria) this;
        }

        public Criteria andLinkIsNotNull() {
            addCriterion("link is not null");
            return (Criteria) this;
        }

        public Criteria andLinkEqualTo(String value) {
            addCriterion("link =", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkNotEqualTo(String value) {
            addCriterion("link <>", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkGreaterThan(String value) {
            addCriterion("link >", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkGreaterThanOrEqualTo(String value) {
            addCriterion("link >=", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkLessThan(String value) {
            addCriterion("link <", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkLessThanOrEqualTo(String value) {
            addCriterion("link <=", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkLike(String value) {
            addCriterion("link like", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkNotLike(String value) {
            addCriterion("link not like", value, "link");
            return (Criteria) this;
        }

        public Criteria andLinkIn(List<String> values) {
            addCriterion("link in", values, "link");
            return (Criteria) this;
        }

        public Criteria andLinkNotIn(List<String> values) {
            addCriterion("link not in", values, "link");
            return (Criteria) this;
        }

        public Criteria andLinkBetween(String value1, String value2) {
            addCriterion("link between", value1, value2, "link");
            return (Criteria) this;
        }

        public Criteria andLinkNotBetween(String value1, String value2) {
            addCriterion("link not between", value1, value2, "link");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeIsNull() {
            addCriterion("update_notice is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeIsNotNull() {
            addCriterion("update_notice is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeEqualTo(String value) {
            addCriterion("update_notice =", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeNotEqualTo(String value) {
            addCriterion("update_notice <>", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeGreaterThan(String value) {
            addCriterion("update_notice >", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeGreaterThanOrEqualTo(String value) {
            addCriterion("update_notice >=", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeLessThan(String value) {
            addCriterion("update_notice <", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeLessThanOrEqualTo(String value) {
            addCriterion("update_notice <=", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeLike(String value) {
            addCriterion("update_notice like", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeNotLike(String value) {
            addCriterion("update_notice not like", value, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeIn(List<String> values) {
            addCriterion("update_notice in", values, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeNotIn(List<String> values) {
            addCriterion("update_notice not in", values, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeBetween(String value1, String value2) {
            addCriterion("update_notice between", value1, value2, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andUpdateNoticeNotBetween(String value1, String value2) {
            addCriterion("update_notice not between", value1, value2, "updateNotice");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}