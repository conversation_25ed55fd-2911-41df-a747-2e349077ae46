package com.bilibili.miniapp.open.repository.mysql.miniapp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppYoukuVideoPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 节目id
     */
    private String showId;

    /**
     * 视频id
     */
    private String videoId;

    /**
     * 视频名称
     */
    private String name;

    /**
     * 视频标题
     */
    private String title;

    /**
     * 集数序号
     */
    private Integer stage;

    /**
     * 集号
     */
    private Integer episode;

    /**
     * 视频竖版截图
     */
    private String verticalThumbnails;

    /**
     * 视频横版截图
     */
    private String thumbnails;

    /**
     * 视频类型
     */
    private String type;

    /**
     * 是否付费 0-否 1-是
     */
    private Integer paid;

    /**
     * 视频时⻓(秒)
     */
    private String showLength;

    /**
     * 视频链接
     */
    private String link;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}