package com.bilibili.miniapp.open.repository.mysql.miniapp.repo;

import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.mini_app.MiniAppCategoryBo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/19
 **/
public interface IMiniAppCategoryRepository {

    /**
     * 获取所有小程序分类列表
     * @return
     */
    List<MiniAppCategoryBo> getAllCategoryList();

    /**
     * 初始化小程序分类
     * @param miniAppCategoryBo
     */
    void insertCategory(MiniAppCategoryBo miniAppCategoryBo);

    void batchInsertCategory(List<MiniAppCategoryBo> miniAppCategoryBoList);
}
