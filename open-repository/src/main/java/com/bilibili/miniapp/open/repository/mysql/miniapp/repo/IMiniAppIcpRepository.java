package com.bilibili.miniapp.open.repository.mysql.miniapp.repo;

import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/
public interface IMiniAppIcpRepository {

    void saveIcpReportInfo(IcpReportInfo icpReportInfo);

    void updateIcpReportInfo(IcpReportInfo icpReportInfo);

    IcpReportState getIcpReportState(String appId);

    IcpReportState getIcpReportStateByFlowId(Long flowId);

    IcpReportInfo getIcpReportInfoByFlowId(Long flowId, boolean appendIcpInfo);

    Long saveIcpReportState(IcpReportState icpReportState);

    void saveIcpAttachments(List<IcpAttachment> attachments);

    List<IcpPlatformAudit> queryIcpPlatformAuditByCondition(IcpPlatformAuditCondition condition);

    void saveIdentityMaterial(List<IcpAttachment> attachments, Long identityId);

    List<IcpAttachment> queryIdentityMaterialById(Long identityId);

    List<IcpReportState> queryIcpReportStateByStatus(List<Integer> flowStatusList, Integer reportStatus);

    void saveIcpIdentityAuth(IdentityAuthInfo identityAuthInfo);

    IdentityAuthInfo getIcpIdentityAuthByWzid(Long wzid);

    /**
     * 查询完成备案的备案信息
     * @return
     */
    List<IcpReportState> queryIcpReportFinish();

    List<Long> saveIcpAttachments(List<String> appIds, List<IcpAttachment> attachments, List<Integer> flowStatus);
}
