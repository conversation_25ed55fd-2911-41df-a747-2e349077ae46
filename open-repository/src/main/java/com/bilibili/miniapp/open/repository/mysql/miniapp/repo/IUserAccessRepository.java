package com.bilibili.miniapp.open.repository.mysql.miniapp.repo;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPo;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/
public interface IUserAccessRepository {

    /**
     * 保存用户访问日志
     * @param userAccessLogPo
     */
    void save(MiniAppUserAccessLogPo userAccessLogPo);

    /**
     * 获取指定时间之后的访问记录
     * @param mid 用户id
     * @return 访问记录
     */
    List<MiniAppUserAccessLogPo> queryByMid(Long mid);

    /**
     * 获取指定时间之后的访问记录
     * @param mid 用户id
     * @return 访问记录
     */
    PageResult<MiniAppUserAccessLogPo> queryByMid(Long mid, Integer pageNum, Integer pageSize);

    MiniAppUserAccessLogPo queryByMidAndAppId(Long mid, String appId);

    void updateAccessTime(Long mid, String appId, Timestamp mtime);
}
