package com.bilibili.miniapp.open.repository.mysql.miniapp.repo;

import com.bilibili.miniapp.open.repository.bo.youku.ShowInfo;
import com.bilibili.miniapp.open.repository.bo.youku.YouKuVideo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/19
 **/
public interface IYouKuShowRepository {

    /**
     * 保存优酷节目信息以及视频信息
     * @param showInfo
     */
    void save(ShowInfo showInfo);

    void saveVideo(YouKuVideo video);

    void batchSave(List<ShowInfo> showInfos);

    List<ShowInfo> fastQueryShowInfosByPage(Long fromId, Integer size);

    List<YouKuVideo> fastQueryVideosByPage(Long fromId, Integer size);

    void batchSaveVideos(List<YouKuVideo> videos);

    ShowInfo queryShowInfo(String showId);

    YouKuVideo queryVideo(String videoId);
}
