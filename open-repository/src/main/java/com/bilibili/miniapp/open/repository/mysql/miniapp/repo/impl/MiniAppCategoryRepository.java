package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.mini_app.MiniAppCategoryBo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenCategoryDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenCategoryPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenCategoryPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppCategoryRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/19
 **/

@Repository
public class MiniAppCategoryRepository implements IMiniAppCategoryRepository {

    @Resource
    private MiniAppOpenCategoryDao miniAppOpenCategoryDao;


    @Override
    public List<MiniAppCategoryBo> getAllCategoryList() {
        MiniAppOpenCategoryPoExample example = new MiniAppOpenCategoryPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenCategoryPo> categoryPos = miniAppOpenCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(categoryPos)) {
            return Collections.emptyList();
        }
        return categoryPos.stream().map(MiniAppCategoryBo::fromPo).collect(Collectors.toList());
    }

    @Override
    public void insertCategory(MiniAppCategoryBo miniAppCategoryBo) {
        MiniAppOpenCategoryPo po = miniAppCategoryBo.toPo();
        miniAppOpenCategoryDao.insertSelective(po);
    }

    @Override
    public void batchInsertCategory(List<MiniAppCategoryBo> miniAppCategoryBoList) {
        if (CollectionUtils.isEmpty(miniAppCategoryBoList)) {
            return;
        }
        List<MiniAppOpenCategoryPo> poList = miniAppCategoryBoList.stream().map(MiniAppCategoryBo::toPo).collect(Collectors.toList());
        for (MiniAppOpenCategoryPo miniAppOpenCategoryPo : poList) {
            miniAppOpenCategoryDao.insertUpdateSelective(miniAppOpenCategoryPo);
        }
    }
}
