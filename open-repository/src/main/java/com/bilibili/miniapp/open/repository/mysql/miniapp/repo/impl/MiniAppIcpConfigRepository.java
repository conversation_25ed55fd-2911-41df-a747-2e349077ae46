package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.MiniAppIcpConfig;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpConfigAppFwlxDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpConfigAreaDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpConfigDwxzDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpConfigZjlxDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpConfigRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28
 **/

@Repository
public class MiniAppIcpConfigRepository implements IMiniAppIcpConfigRepository {

    @Resource
    private MiniAppOpenIcpConfigAreaDao icpConfigAreaDao;

    @Resource
    private MiniAppOpenIcpConfigDwxzDao icpConfigDwxzDao;

    @Resource
    private MiniAppOpenIcpConfigZjlxDao icpConfigZjlxDao;

    @Resource
    private MiniAppOpenIcpConfigAppFwlxDao icpConfigAppFwlxDao;


    @Override
    public void saveIcpConfig(MiniAppIcpConfig icpConfig) {
        // 删除所有数据
        deleteAll();
        // 保存区域代码表
        List<MiniAppIcpConfig.MiniAppIcpAreaCode> areaCodeList = icpConfig.getAreaCodeList();
        if (areaCodeList != null && !areaCodeList.isEmpty()) {
            List<MiniAppOpenIcpConfigAreaPo> areaPos = areaCodeList.stream().map(areaCode -> {
                Timestamp now = new Timestamp(System.currentTimeMillis());
                MiniAppOpenIcpConfigAreaPo areaPo = new MiniAppOpenIcpConfigAreaPo();
                areaPo.setCode(areaCode.getCode());
                areaPo.setName(areaCode.getName());
                areaPo.setType(areaCode.getType());
                areaPo.setStatus(areaCode.getStatus());
                areaPo.setIsDeleted(IsDeleted.VALID.getCode());
                areaPo.setCtime(now);
                areaPo.setMtime(now);
                return areaPo;
            }).collect(Collectors.toList());
            icpConfigAreaDao.insertBatch(areaPos);
        }
        // 保存单位性质表
        List<MiniAppIcpConfig.MiniAppIcpOrgType> orgTypeList = icpConfig.getOrgTypeList();
        if (orgTypeList != null && !orgTypeList.isEmpty()) {
            List<MiniAppOpenIcpConfigDwxzPo> dwxzPos = orgTypeList.stream().map(orgType -> {
                Timestamp now = new Timestamp(System.currentTimeMillis());
                MiniAppOpenIcpConfigDwxzPo dwxzPo = new MiniAppOpenIcpConfigDwxzPo();
                dwxzPo.setCode(orgType.getCode());
                dwxzPo.setName(orgType.getName());
                dwxzPo.setStatus(orgType.getStatus());
                dwxzPo.setIsDeleted(IsDeleted.VALID.getCode());
                dwxzPo.setCtime(now);
                dwxzPo.setMtime(now);
                return dwxzPo;
            }).collect(Collectors.toList());
            icpConfigDwxzDao.insertBatch(dwxzPos);
        }
        // 保存证件类型表
        List<MiniAppIcpConfig.MiniAppIcpDocumentType> documentTypeList = icpConfig.getDocumentTypeList();
        if (documentTypeList != null && !documentTypeList.isEmpty()) {
            List<MiniAppOpenIcpConfigZjlxPo> zjlxPos = documentTypeList.stream().map(documentType -> {
                Timestamp now = new Timestamp(System.currentTimeMillis());
                MiniAppOpenIcpConfigZjlxPo zjlxPo = new MiniAppOpenIcpConfigZjlxPo();
                zjlxPo.setCode(documentType.getCode());
                zjlxPo.setName(documentType.getName());
                zjlxPo.setDwflId(documentType.getOrgCode());
                zjlxPo.setStatus(documentType.getStatus());
                zjlxPo.setIsDeleted(IsDeleted.VALID.getCode());
                zjlxPo.setCtime(now);
                zjlxPo.setMtime(now);
                return zjlxPo;
            }).collect(Collectors.toList());
            icpConfigZjlxDao.insertBatch(zjlxPos);
        }
        // 保存小程序服务类型表
        List<MiniAppIcpConfig.MiniAppIcpAppServiceType> serviceTypeList = icpConfig.getAppServiceTypeList();
        if (serviceTypeList != null && !serviceTypeList.isEmpty()) {
            List<MiniAppOpenIcpConfigAppFwlxPo> appFwlxPos = serviceTypeList.stream().map(serviceType -> {
                Timestamp now = new Timestamp(System.currentTimeMillis());
                MiniAppOpenIcpConfigAppFwlxPo appFwlxPo = new MiniAppOpenIcpConfigAppFwlxPo();
                appFwlxPo.setCode(serviceType.getCode());
                appFwlxPo.setName(serviceType.getName());
                appFwlxPo.setParentId(serviceType.getParentCode());
                appFwlxPo.setStatus(serviceType.getStatus());
                appFwlxPo.setIsDeleted(IsDeleted.VALID.getCode());
                appFwlxPo.setCtime(now);
                appFwlxPo.setMtime(now);
                return appFwlxPo;
            }).collect(Collectors.toList());
            icpConfigAppFwlxDao.insertBatch(appFwlxPos);
        }
    }

    @Override
    public MiniAppIcpConfig getIcpConfig() {
        // 获取区域代码表
        List<MiniAppOpenIcpConfigAreaPo> areaList = getAreaList();
        // 获取单位性质表
        List<MiniAppOpenIcpConfigDwxzPo> dwxzList = getDwxzList();
        // 获取证件类型表
        List<MiniAppOpenIcpConfigZjlxPo> zjlxList = getZjlxList();
        // 获取小程序服务类型表
        List<MiniAppOpenIcpConfigAppFwlxPo> fwlxList = getAppFwlxList();
        MiniAppIcpConfig icpConfig = new MiniAppIcpConfig();
        if (areaList != null && !areaList.isEmpty()) {
            icpConfig.setAreaCodeList(areaList.stream().map(area -> MiniAppIcpConfig.MiniAppIcpAreaCode.builder()
                    .code(area.getCode())
                    .name(area.getName())
                    .type(area.getType())
                    .status(area.getStatus())
                    .build()).collect(Collectors.toList()));
        }
        if (dwxzList != null && !dwxzList.isEmpty()) {
            icpConfig.setOrgTypeList(dwxzList.stream().map(dwxz -> MiniAppIcpConfig.MiniAppIcpOrgType.builder()
                    .code(dwxz.getCode())
                    .name(dwxz.getName())
                    .status(dwxz.getStatus())
                    .build()).collect(Collectors.toList()));
        }
        if (zjlxList != null && !zjlxList.isEmpty()) {
            icpConfig.setDocumentTypeList(zjlxList.stream().map(zjlx -> MiniAppIcpConfig.MiniAppIcpDocumentType.builder()
                    .code(zjlx.getCode())
                    .name(zjlx.getName())
                    .orgCode(zjlx.getDwflId())
                    .status(zjlx.getStatus())
                    .build()).collect(Collectors.toList()));
        }
        if (fwlxList != null && !fwlxList.isEmpty()) {
            icpConfig.setAppServiceTypeList(fwlxList.stream().map(fwlx -> MiniAppIcpConfig.MiniAppIcpAppServiceType.builder()
                    .code(fwlx.getCode())
                    .name(fwlx.getName())
                    .parentCode(fwlx.getParentId())
                    .status(fwlx.getStatus())
                    .build()).collect(Collectors.toList()));
        }
        return icpConfig;
    }

    private void deleteAll() {
        // 删除区域代码表
        deleteArea();
        // 删除单位性质表
        deleteDwxz();
        // 删除证件类型表
        deleteZjlx();
        // 删除小程序服务类型表
        deleteAppFwlx();
    }

    private void deleteArea() {
        MiniAppOpenIcpConfigAreaPoExample example = new MiniAppOpenIcpConfigAreaPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        icpConfigAreaDao.updateByExampleSelective(MiniAppOpenIcpConfigAreaPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    private void deleteDwxz() {
        MiniAppOpenIcpConfigDwxzPoExample example = new MiniAppOpenIcpConfigDwxzPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        icpConfigDwxzDao.updateByExampleSelective(MiniAppOpenIcpConfigDwxzPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    private void deleteZjlx() {
        MiniAppOpenIcpConfigZjlxPoExample example = new MiniAppOpenIcpConfigZjlxPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        icpConfigZjlxDao.updateByExampleSelective(MiniAppOpenIcpConfigZjlxPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    private void deleteAppFwlx() {
        MiniAppOpenIcpConfigAppFwlxPoExample example = new MiniAppOpenIcpConfigAppFwlxPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        icpConfigAppFwlxDao.updateByExampleSelective(MiniAppOpenIcpConfigAppFwlxPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    private List<MiniAppOpenIcpConfigAreaPo> getAreaList() {
        MiniAppOpenIcpConfigAreaPoExample example = new MiniAppOpenIcpConfigAreaPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return icpConfigAreaDao.selectByExample(example);
    }

    private List<MiniAppOpenIcpConfigDwxzPo> getDwxzList() {
        MiniAppOpenIcpConfigDwxzPoExample example = new MiniAppOpenIcpConfigDwxzPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return icpConfigDwxzDao.selectByExample(example);
    }

    private List<MiniAppOpenIcpConfigZjlxPo> getZjlxList() {
        MiniAppOpenIcpConfigZjlxPoExample example = new MiniAppOpenIcpConfigZjlxPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return icpConfigZjlxDao.selectByExample(example);
    }

    private List<MiniAppOpenIcpConfigAppFwlxPo> getAppFwlxList() {
        MiniAppOpenIcpConfigAppFwlxPoExample example = new MiniAppOpenIcpConfigAppFwlxPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return icpConfigAppFwlxDao.selectByExample(example);
    }
}
