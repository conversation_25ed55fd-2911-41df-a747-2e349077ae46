package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Repository
public class MiniAppIcpRepository implements IMiniAppIcpRepository {

    @Resource
    private MiniAppOpenIcpAppDao icpAppDao;
    @Resource
    private MiniAppOpenIcpDao icpDao;
    @Resource
    private MiniAppOpenIcpCompanyDao icpCompanyDao;
    @Resource
    private MiniAppOpenIcpAttachmentDao icpAttachmentDao;
    @Resource
    private MiniAppOpenIcpAuditDao icpAuditDao;

    @Resource
    private MiniAppOpenIcpIdentityMaterialDao icpIdentityMaterialDao;

    @Resource
    private MiniAppOpenIcpIdentityAuthDao icpIdentityAuthDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveIcpReportInfo(IcpReportInfo icpReportInfo) {
        Long flowId = icpReportInfo.getFlowId();
        // 保存公司信息
        MiniAppOpenIcpCompanyPo icpCompanyPo = icpReportInfo.getCompany().toPo();
        icpCompanyPo.setFlowId(flowId);
        icpCompanyDao.insertSelective(icpCompanyPo);
        // 保存应用信息
        MiniAppOpenIcpAppPo icpAppPo = icpReportInfo.getApp().toPo();
        icpAppPo.setFlowId(flowId);
        icpAppDao.insertSelective(icpAppPo);
        // 保存附件信息
        List<MiniAppOpenIcpAttachmentPo> icpAttachmentPos = icpReportInfo.getAttachment().stream()
                .map(IcpAttachment::toPo)
                .collect(Collectors.toList());
        for (MiniAppOpenIcpAttachmentPo icpAttachmentPo : icpAttachmentPos) {
            icpAttachmentPo.setFlowId(flowId);
            icpAttachmentDao.insertSelective(icpAttachmentPo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIcpReportInfo(IcpReportInfo icpReportInfo) {
        // 修改公司信息
        Long flowId = icpReportInfo.getFlowId();
        MiniAppOpenIcpCompanyPo oldCompanyPo = getIcpCompanyPoByFlowId(flowId);
        if (Objects.isNull(oldCompanyPo)) {
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "备案公司信息不存在");
        }
        MiniAppOpenIcpCompanyPo icpCompanyPo = icpReportInfo.getCompany().toPo();
        icpCompanyPo.setFlowId(flowId);
        icpCompanyPo.setId(oldCompanyPo.getId());
        icpCompanyDao.updateByPrimaryKeySelective(icpCompanyPo);
        // 修改应用信息
        MiniAppOpenIcpAppPo oldAppPo = getIcpAppPoByFlowId(flowId);
        if (Objects.isNull(oldAppPo)) {
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "备案应用信息不存在");
        }
        MiniAppOpenIcpAppPo icpAppPo = icpReportInfo.getApp().toPo();
        icpAppPo.setFlowId(flowId);
        icpAppPo.setId(oldAppPo.getId());
        icpAppDao.updateByPrimaryKeySelective(icpAppPo);
        // 修改附件信息 删除旧的
        MiniAppOpenIcpAttachmentPoExample example = new MiniAppOpenIcpAttachmentPoExample();
        example.or().andFlowIdEqualTo(flowId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        icpAttachmentDao.updateByExampleSelective(
                MiniAppOpenIcpAttachmentPo.builder()
                        .isDeleted(IsDeleted.DELETED.getCode())
                        .build(),
                example);
        // 插入新的
        List<MiniAppOpenIcpAttachmentPo> icpAttachmentPos = icpReportInfo.getAttachment().stream()
                .map(IcpAttachment::toPo)
                .collect(Collectors.toList());
        for (MiniAppOpenIcpAttachmentPo icpAttachmentPo : icpAttachmentPos) {
            icpAttachmentPo.setFlowId(flowId);
            icpAttachmentDao.insertSelective(icpAttachmentPo);
        }

    }

    @Override
    public IcpReportState getIcpReportState(String appId) {
        // 查询小程序备案状态
        MiniAppOpenIcpPoExample example = new MiniAppOpenIcpPoExample();
        example.or().andAppIdEqualTo(appId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpPo> icpPos = icpDao.selectByExample(example);
        if (CollectionUtils.isEmpty(icpPos)) {
            return null;
        }
        // 查询小程序备案状态
        MiniAppOpenIcpPo openIcpPo = icpPos.get(0);
        IcpReportState reportState = IcpReportState.fromPo(openIcpPo);
        MiniAppOpenIcpAuditPo po = getIcpAuditPoByFlowId(reportState.getFlowId());
        IcpPlatformAudit platformAudit = IcpPlatformAudit.fromPo(po);
        reportState.setPlatformAudit(platformAudit);
        return reportState;
    }

    @Override
    public IcpReportState getIcpReportStateByFlowId(Long flowId) {
        if (Objects.isNull(flowId)) {
            return null;
        }
        MiniAppOpenIcpPo icpPo = getIcpPoByFlowId(flowId);
        if (Objects.isNull(icpPo)) {
            return null;
        }
        // 查询小程序备案状态
        IcpReportState reportState = IcpReportState.fromPo(icpPo);
        MiniAppOpenIcpAuditPo po = getIcpAuditPoByFlowId(reportState.getFlowId());
        IcpPlatformAudit platformAudit = IcpPlatformAudit.fromPo(po);
        reportState.setPlatformAudit(platformAudit);
        return reportState;
    }

    @Override
    public IcpReportInfo getIcpReportInfoByFlowId(Long flowId, boolean appendIcpInfo) {
        MiniAppOpenIcpPo reportInfo = getIcpPoByFlowId(flowId);
        if (Objects.isNull(reportInfo)) {
            return null;
        }
        if (!appendIcpInfo) {
            return IcpReportInfo.builder()
                    .appId(reportInfo.getAppId())
                    .build();
        }
        MiniAppOpenIcpCompanyPo icpCompanyPo = getIcpCompanyPoByFlowId(flowId);
        MiniAppOpenIcpAppPo icpAppPo = getIcpAppPoByFlowId(flowId);
        List<MiniAppOpenIcpAttachmentPo> icpAttachmentPos = getIcpAttachmentPoByFlowId(flowId);
        return IcpReportInfo.builder()
                .flowId(flowId)
                .appId(reportInfo.getAppId())
                .company(IcpCompany.fromPo(icpCompanyPo))
                .app(IcpApp.fromPo(icpAppPo))
                .attachment(icpAttachmentPos.stream()
                        .map(IcpAttachment::fromPo)
                        .collect(Collectors.toList()))
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveIcpReportState(IcpReportState icpReportState) {
        Long flowId = icpReportState.getFlowId();
        if (Objects.isNull(flowId)) {
            // 备案数据不在 新增
            MiniAppOpenIcpPo po = icpReportState.toPo();
            icpDao.insertSelective(po);
            // 新增审核信息
            flowId = po.getId();
            if (Objects.nonNull(icpReportState.getPlatformAudit())) {
                icpReportState.getPlatformAudit().setFlowId(flowId);
                MiniAppOpenIcpAuditPo icpAuditPo = icpReportState.getPlatformAudit().toPo();
                icpAuditDao.insertSelective(icpAuditPo);
            }
            return flowId;
        }
        // 备案数据存在 修改
        icpDao.updateByPrimaryKeySelective(icpReportState.toPo());
        // 更新审核信息
        if (Objects.nonNull(icpReportState.getPlatformAudit())) {
            MiniAppOpenIcpAuditPo icpAuditPo = icpReportState.getPlatformAudit().toPo();
            icpAuditDao.updateByPrimaryKeySelective(icpAuditPo);
        }
        return flowId;
    }

    @Override
    public void saveIcpAttachments(List<IcpAttachment> attachments) {
        List<MiniAppOpenIcpAttachmentPo> icpAttachmentPos = attachments.stream()
                .map(IcpAttachment::toPo)
                .collect(Collectors.toList());
        for (MiniAppOpenIcpAttachmentPo icpAttachmentPo : icpAttachmentPos) {
//            icpAttachmentPo.setFlowId(flowId);
            icpAttachmentDao.insertSelective(icpAttachmentPo);
        }
    }

    @Override
    public List<IcpPlatformAudit> queryIcpPlatformAuditByCondition(IcpPlatformAuditCondition condition) {
        MiniAppOpenIcpAuditPoExample example = condition.toExample();
        List<MiniAppOpenIcpAuditPo> pos = icpAuditDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return List.of();
        }
        return pos.stream().map(IcpPlatformAudit::fromPo).collect(Collectors.toList());
    }

    @Override
    public void saveIdentityMaterial(List<IcpAttachment> attachments, Long identityId) {
        // 先删除旧的，在保存新的
        MiniAppOpenIcpIdentityMaterialPoExample example = new MiniAppOpenIcpIdentityMaterialPoExample();
        example.or().andWzidEqualTo(identityId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        icpIdentityMaterialDao.updateByExampleSelective(
                MiniAppOpenIcpIdentityMaterialPo.builder()
                        .isDeleted(IsDeleted.DELETED.getCode())
                        .build(),
                example);
        // 插入新的
        List<MiniAppOpenIcpIdentityMaterialPo> icpAttachmentPos = attachments.stream()
                .map(attachment -> attachment.toIdentityMaterialPo(identityId))
                .collect(Collectors.toList());
        for (MiniAppOpenIcpIdentityMaterialPo icpAttachmentPo : icpAttachmentPos) {
            icpAttachmentPo.setWzid(identityId);
            icpIdentityMaterialDao.insertSelective(icpAttachmentPo);
        }
    }

    @Override
    public List<IcpAttachment> queryIdentityMaterialById(Long identityId) {
        MiniAppOpenIcpIdentityMaterialPoExample example = new MiniAppOpenIcpIdentityMaterialPoExample();
        example.or().andWzidEqualTo(identityId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpIdentityMaterialPo> identityMaterialPos = icpIdentityMaterialDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(identityMaterialPos)) {
            return identityMaterialPos.stream()
                    .map(IcpAttachment::fromIdentityMaterialPo)
                    .collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public List<IcpReportState> queryIcpReportStateByStatus(List<Integer> flowStatusList, Integer reportStatus) {
        MiniAppOpenIcpPoExample example = new MiniAppOpenIcpPoExample();
        example.or()
                .andFlowStatusIn(flowStatusList)
                .andReportStatusEqualTo(reportStatus)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpPo> icpPos = icpDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(icpPos)) {
            return icpPos.stream()
                    .map(IcpReportState::fromPo)
                    .collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public void saveIcpIdentityAuth(IdentityAuthInfo identityAuthInfo) {
        MiniAppOpenIcpIdentityAuthPo po = getIcpIdentityAuthPoByWzid(identityAuthInfo.getIspWzid());
        if (Objects.nonNull(po)) {
            identityAuthInfo.setId(po.getId());
            icpIdentityAuthDao.updateByPrimaryKeySelective(identityAuthInfo.convertToPo());
            return;
        }
        po = identityAuthInfo.convertToPo();
        icpIdentityAuthDao.insertSelective(po);
    }

    @Override
    public IdentityAuthInfo getIcpIdentityAuthByWzid(Long wzid) {
        MiniAppOpenIcpIdentityAuthPo po = getIcpIdentityAuthPoByWzid(wzid);
        if (Objects.isNull(po)) {
            return null;
        }
        return IdentityAuthInfo.convertFromPo(po);
    }

    @Override
    public List<IcpReportState> queryIcpReportFinish() {
        MiniAppOpenIcpPoExample example = new MiniAppOpenIcpPoExample();
        example.or()
                .andFlowStatusEqualTo(IcpFlowStatusEnum.ICP_STATUS_REGISTER_SUCCESS.getStatus())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpPo> icpPos = icpDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(icpPos)) {
            return icpPos.stream()
                    .map(IcpReportState::fromPo)
                    .collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public List<Long> saveIcpAttachments(List<String> appIds, List<IcpAttachment> attachments, List<Integer> flowStatus) {
        if (CollectionUtils.isEmpty(appIds) || CollectionUtils.isEmpty(attachments)) {
            return List.of();
        }
        MiniAppOpenIcpPoExample openIcpPoExample = new MiniAppOpenIcpPoExample();
        MiniAppOpenIcpPoExample.Criteria criteria = openIcpPoExample.createCriteria();
        criteria.andAppIdIn(appIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (CollectionUtils.isNotEmpty(flowStatus)) {
            criteria.andFlowStatusIn(flowStatus);
        }
        List<MiniAppOpenIcpPo> icpPos = icpDao.selectByExample(openIcpPoExample);
        if (CollectionUtils.isEmpty(icpPos)) {
            return List.of();
        }
        List<Long> flowIds = icpPos.stream().map(MiniAppOpenIcpPo::getId).collect(Collectors.toList());
        for (Long flowId : flowIds) {
            for (IcpAttachment attachment : attachments) {
                MiniAppOpenIcpAttachmentPo icpAttachmentPo = attachment.toPo();
                icpAttachmentPo.setFlowId(flowId);
                icpAttachmentDao.insertSelective(icpAttachmentPo);
            }
        }
        return flowIds;
    }


    private MiniAppOpenIcpPo getIcpPoByFlowId(Long flowId) {
        return icpDao.selectByPrimaryKey(flowId);
    }

    private MiniAppOpenIcpIdentityAuthPo getIcpIdentityAuthPoByWzid(Long lspWzid) {
        MiniAppOpenIcpIdentityAuthPoExample example = new MiniAppOpenIcpIdentityAuthPoExample();
        example.or().andIspWzidEqualTo(lspWzid).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpIdentityAuthPo> identityAuthPos = icpIdentityAuthDao.selectByExample(example);
        if (CollectionUtils.isEmpty(identityAuthPos)) {
            return null;
        } else {
            return identityAuthPos.get(0);
        }
    }

    private MiniAppOpenIcpCompanyPo getIcpCompanyPoByFlowId(Long flowId) {
        MiniAppOpenIcpCompanyPoExample example = new MiniAppOpenIcpCompanyPoExample();
        example.or().andFlowIdEqualTo(flowId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpCompanyPo> pos = icpCompanyDao.selectByExample(example);
        return CollectionUtils.isEmpty(pos) ? null : pos.get(0);
    }

    private MiniAppOpenIcpAppPo getIcpAppPoByFlowId(Long flowId) {
        MiniAppOpenIcpAppPoExample example = new MiniAppOpenIcpAppPoExample();
        example.or().andFlowIdEqualTo(flowId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpAppPo> pos = icpAppDao.selectByExample(example);
        return CollectionUtils.isEmpty(pos) ? null : pos.get(0);
    }

    private List<MiniAppOpenIcpAttachmentPo> getIcpAttachmentPoByFlowId(Long flowId) {
        MiniAppOpenIcpAttachmentPoExample example = new MiniAppOpenIcpAttachmentPoExample();
        example.or().andFlowIdEqualTo(flowId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return icpAttachmentDao.selectByExample(example);
    }

    private MiniAppOpenIcpAuditPo getIcpAuditPoByFlowId(Long flowId) {
        MiniAppOpenIcpAuditPoExample example = new MiniAppOpenIcpAuditPoExample();
        example.or().andFlowIdEqualTo(flowId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenIcpAuditPo> pos = icpAuditDao.selectByExample(example);
        return CollectionUtils.isEmpty(pos) ? null : pos.get(0);
    }
}
