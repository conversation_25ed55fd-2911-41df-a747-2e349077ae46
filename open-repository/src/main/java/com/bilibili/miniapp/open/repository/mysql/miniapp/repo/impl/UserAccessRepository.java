package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppUserAccessLogDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IUserAccessRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 用户访问仓储层(仅负责与数据库交互)
 * @Date 2025/2/26
 **/

@Repository
public class UserAccessRepository implements IUserAccessRepository {

    @Resource
    private MiniAppUserAccessLogDao miniAppUserAccessLogDao;

    @Override
    public void save(MiniAppUserAccessLogPo userAccessLogPo) {
        miniAppUserAccessLogDao.insertSelective(userAccessLogPo);
    }

    @Override
    public List<MiniAppUserAccessLogPo> queryByMid(Long mid) {
        MiniAppUserAccessLogPoExample example = new MiniAppUserAccessLogPoExample();
        example.createCriteria()
                .andMidEqualTo(mid)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("mtime desc");
        List<MiniAppUserAccessLogPo> userAccessLogPos = miniAppUserAccessLogDao.selectByExample(example);
        if (CollectionUtils.isEmpty(userAccessLogPos)) {
            return List.of();
        }
        return userAccessLogPos;
    }

    @Override
    public PageResult<MiniAppUserAccessLogPo> queryByMid(Long mid, Integer pageNum, Integer pageSize) {
        MiniAppUserAccessLogPoExample example = new MiniAppUserAccessLogPoExample();
        example.createCriteria()
                .andMidEqualTo(mid)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("mtime desc");
        long total = miniAppUserAccessLogDao.countByExample(example);
        if (total <= 0) {
            return PageResult.emptyPageResult();
        }
        Page page = Page.valueOf(pageNum, pageSize);
        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        List<MiniAppUserAccessLogPo> userAccessLogPos = miniAppUserAccessLogDao.selectByExample(example);
        if (CollectionUtils.isEmpty(userAccessLogPos)) {
            return PageResult.emptyPageResult();
        }
        return new PageResult<>((int) total, userAccessLogPos);
    }

    @Override
    public MiniAppUserAccessLogPo queryByMidAndAppId(Long mid, String appId) {
        MiniAppUserAccessLogPoExample example = new MiniAppUserAccessLogPoExample();
        example.createCriteria()
                .andMidEqualTo(mid)
                .andAppIdEqualTo(appId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("mtime desc");
        List<MiniAppUserAccessLogPo> userAccessLogPos = miniAppUserAccessLogDao.selectByExample(example);
        return CollectionUtils.isEmpty(userAccessLogPos) ? null : userAccessLogPos.get(0);
    }

    @Override
    public void updateAccessTime(Long mid, String appId, Timestamp mtime) {
        MiniAppUserAccessLogPoExample example = new MiniAppUserAccessLogPoExample();
        example.createCriteria()
                .andMidEqualTo(mid)
                .andAppIdEqualTo(appId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        miniAppUserAccessLogDao.updateByExampleSelective(MiniAppUserAccessLogPo.builder().mtime(mtime).build(), example);
    }
}
