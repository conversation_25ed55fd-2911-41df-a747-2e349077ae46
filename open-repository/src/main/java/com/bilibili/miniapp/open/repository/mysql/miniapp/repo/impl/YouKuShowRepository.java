package com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.bo.youku.ShowInfo;
import com.bilibili.miniapp.open.repository.bo.youku.YouKuVideo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppYoukuShowDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppYoukuVideoDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IYouKuShowRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/19
 **/

@Repository
public class YouKuShowRepository implements IYouKuShowRepository {

    @Resource
    private MiniAppYoukuShowDao showDao;

    @Resource
    private MiniAppYoukuVideoDao videoDao;

    @Override
    public ShowInfo queryShowInfo(String showId) {
        if (StringUtils.isBlank(showId)) {
            return null;
        }
        MiniAppYoukuShowPoExample example = new MiniAppYoukuShowPoExample();
        example.or()
                .andShowIdEqualTo(showId)
                .andIsDeletedEqualTo(0);

        List<MiniAppYoukuShowPo> showPos = showDao.selectByExample(example);
        if (!CollectionUtils.isEmpty(showPos)) {
            return ShowInfo.fromPo(showPos.get(0));
        }
        return null;
    }

    @Override
    public YouKuVideo queryVideo(String videoId) {
        if (StringUtils.isBlank(videoId)) {
            return null;
        }
        MiniAppYoukuVideoPoExample example = new MiniAppYoukuVideoPoExample();
        example.or()
                .andVideoIdEqualTo(videoId)
                .andIsDeletedEqualTo(0);

        List<MiniAppYoukuVideoPo> videoPos = videoDao.selectByExample(example);
        if (!CollectionUtils.isEmpty(videoPos)) {
            return YouKuVideo.fromPo(videoPos.get(0));
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ShowInfo showInfo) {
        MiniAppYoukuShowPo po = showInfo.toPo();
        // 保存剧集信息
        showDao.insertUpdateSelective(po);
        List<YouKuVideo> videos = showInfo.getVideos();
        if (CollectionUtils.isEmpty(videos)) {
            return ;
        }
        videos.forEach(this::saveVideo);
    }

    @Override
    public void saveVideo(YouKuVideo video) {
        videoDao.insertUpdateSelective(video.toPo());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<ShowInfo> showInfos) {
        if (CollectionUtils.isEmpty(showInfos)) {
            return ;
        }
        List<MiniAppYoukuShowPo> youkuShowPos = showInfos.stream().map(ShowInfo::toPo).collect(Collectors.toList());
        showDao.insertUpdateBatch(youkuShowPos);
        List<MiniAppYoukuVideoPo> youkuVideoPos = new ArrayList<>();
        for (ShowInfo showInfo : showInfos) {
            List<YouKuVideo> videos = showInfo.getVideos();
            if (CollectionUtils.isEmpty(videos)) {
                continue;
            }
            youkuVideoPos.addAll(videos.stream().map(video -> video.putShowId(showInfo.getId())).map(YouKuVideo::toPo).collect(Collectors.toList()));
        }
        videoDao.insertUpdateBatch(youkuVideoPos);
    }

    @Override
    public List<ShowInfo> fastQueryShowInfosByPage(Long fromId, Integer size) {
        MiniAppYoukuShowPoExample example = new MiniAppYoukuShowPoExample();;
        example.createCriteria().andIdGreaterThan(fromId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("id asc");
        example.setLimit(size);
        List<MiniAppYoukuShowPo> showPos = showDao.selectByExample(example);
        return showPos.stream().map(ShowInfo::fromPo).collect(Collectors.toList());
    }

    @Override
    public List<YouKuVideo> fastQueryVideosByPage(Long fromId, Integer size) {
        MiniAppYoukuVideoPoExample example = new MiniAppYoukuVideoPoExample();
        example.createCriteria().andIdGreaterThan(fromId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("id asc");
        example.setLimit(size);
        List<MiniAppYoukuVideoPo> videoPos = videoDao.selectByExample(example);
        return videoPos.stream().map(YouKuVideo::fromPo).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveVideos(List<YouKuVideo> videos) {
        if (CollectionUtils.isEmpty(videos)) {
            return ;
        }
        List<MiniAppYoukuVideoPo> videoPos = videos.stream().map(YouKuVideo::toPo).collect(Collectors.toList());
        videoDao.insertUpdateBatch(videoPos);
    }
}
