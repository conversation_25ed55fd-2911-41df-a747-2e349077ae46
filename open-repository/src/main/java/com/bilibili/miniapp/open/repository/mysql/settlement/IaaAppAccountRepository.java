package com.bilibili.miniapp.open.repository.mysql.settlement;

import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaAppAccountCustomMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaAppAccountMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPoExample;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Slf4j
@Repository
public class IaaAppAccountRepository {


    @Resource
    private IaaAppAccountMapper iaaAppAccountMapper;

    @Resource
    private IaaAppAccountCustomMapper iaaAppAccountCustomMapper;


    public @Nonnull IaaAppAccountPo selectOrInsertByAppTypeAndAppId(String appType, String appId)
            throws IllegalArgumentException {

        IaaAppAccountPo account = Optional.ofNullable(
                        this.selectByAppTypeAndAppId(appType, appId))
                .orElseGet(() -> {
                    log.warn("Fail to get the iaa_app_account, try insert one. daily event={}", appId);

                    // 如果此时正好有并发
                    try {
                        iaaAppAccountMapper.insertSelective(
                                new IaaAppAccountPo()
                                        .setAppId(appId)
                                        .setAppType(appType)
                        );
                    } catch (Throwable t) {
                        log.error("Fail to insert iaa_app_account, daily event={}, concurrency insert may occur",
                                appId, t);
                    }

                    return this.selectByAppTypeAndAppId(appType, appId);
                });

        if (account == null) {
            log.error("Unexpected error, can not find the target app account, daily event={}", appId);
            throw new IllegalArgumentException("找不到目标app账户");
        }

        return account;
    }

    public IaaAppAccountPo selectByAppTypeAndAppId(String appType, String appId) {

        IaaAppAccountPoExample example = new IaaAppAccountPoExample();

        example.createCriteria().andAppTypeEqualTo(appType).andAppIdEqualTo(appId);

        List<IaaAppAccountPo> r = iaaAppAccountMapper.selectByExample(example);

        return CollectionUtils.isEmpty(r) ? null : r.get(0);
    }


    public int updateByPrimaryKeySelective(IaaAppAccountPo selective) {

        return iaaAppAccountMapper.updateByPrimaryKeySelective(selective);
    }



    public int increaseByPrimaryKey(IaaAppAccountPo increment) {

        return iaaAppAccountCustomMapper.increaseByPrimaryKey(increment);
    }
}
