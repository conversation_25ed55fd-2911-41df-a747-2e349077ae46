package com.bilibili.miniapp.open.repository.mysql.settlement;

import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaCrmChargeBillMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPoExample;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPoExample.Criteria;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@Slf4j
@Repository
public class IaaCrmChargeBillRepository {

    @Resource
    private IaaCrmChargeBillMapper iaaCrmChargeBillMapper;


    public void insertSelective(IaaCrmChargeBillPo po) {
        iaaCrmChargeBillMapper.insertSelective(po);
    }



    public IaaCrmChargeBillPo insertOrGetCrmChargeBill(

            String appType, String appId, String logdate, String trafficType, BigDecimal incomeAmt,
            Long withdrawBillDate, Long settlementId, String extra

    ){

        IaaCrmChargeBillPo bill = Optional.ofNullable(
                this.selectByAppIdAndDate(appType, appId, logdate)
        ).orElseGet(() -> {
            log.info("Try to insert a new crm charge bill record, appType={}, appId={}, logdate={}",
                    appType, appId, logdate);

            try {
                iaaCrmChargeBillMapper.insertSelective(
                        new IaaCrmChargeBillPo()
                                .setAppType(appType)
                                .setAppId(appId)
                                .setLogdate(logdate)
                                .setTrafficType(trafficType)
                                .setIncomeAmt(incomeAmt)
                                .setWithdrawBillId(withdrawBillDate)
                                .setSettlementId(settlementId)
                                .setExtra(extra)
                );

            } catch (Throwable throwable) {
                log.warn("Fail to insert a new crm charge bill record, may already existed, appType={}, appId={}, "
                        + "logdate={}", appType, appId, logdate, throwable);
            }

            return this.selectByAppIdAndDate(appType, appId, logdate);
        });

        if (bill == null) {
            log.error("Unexpected error, can not find the target crm charge bill record, appType={}, appId={}, logdate={}",
                    appType, appId, logdate);
            throw new IllegalArgumentException("找不到目标crm充值金账单记录");
        }

        return bill;
    }




    public IaaCrmChargeBillPo selectByAppIdAndDate(String appType, String appId, String chargeDate) {

        IaaCrmChargeBillPoExample example = new IaaCrmChargeBillPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAppTypeEqualTo(appType)
                .andLogdateEqualTo(chargeDate);

        List<IaaCrmChargeBillPo> r = iaaCrmChargeBillMapper.selectByExample(example);

        return CollectionUtils.isEmpty(r) ? null : r.get(0);
    }

    public int updateByPrimaryKeySelective(IaaCrmChargeBillPo po) {
        return iaaCrmChargeBillMapper.updateByPrimaryKeySelective(po);
    }


    public List<IaaCrmChargeBillPo> selectAllByStatusInAndIdGtAndLimit(List<String> strings, Long id, int limit) {

        if (CollectionUtils.isEmpty(strings)) {
            return Collections.emptyList();
        }

        IaaCrmChargeBillPoExample example = new IaaCrmChargeBillPoExample();

        example.setLimit(limit);
        example.setOrderByClause("id asc");

        Criteria criteria = example.createCriteria();

        criteria.andBillStatusIn(strings);

        if (id != null) {
            criteria.andIdGreaterThan(id);
        }



        return iaaCrmChargeBillMapper.selectByExample(example);
    }

    public List<IaaCrmChargeBillPo> selectByWithdrawBillIdEq(Long billId) {



        IaaCrmChargeBillPoExample example = new IaaCrmChargeBillPoExample();

        example.createCriteria()
                .andWithdrawBillIdEqualTo(billId);

        return iaaCrmChargeBillMapper.selectByExample(example);
    }

    public List<IaaCrmChargeBillPo> selectByWithdrawBillIdIn(List<Long> billIds) {

        if (CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }

        IaaCrmChargeBillPoExample example = new IaaCrmChargeBillPoExample();

        example.createCriteria()
                .andWithdrawBillIdIn(billIds);

        return iaaCrmChargeBillMapper.selectByExample(example);




    }
}
