package com.bilibili.miniapp.open.repository.mysql.settlement;

import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaSettlementMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPoExample;
import com.google.common.base.Joiner;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/18
 */
@Slf4j
@Repository
public class IaaSettlementRepository {

    @Resource
    private IaaSettlementMapper iaaSettlementMapper;



    public IaaSettlementPo getOrElseInsertSettlement(
            String appType, String appId, String logdate, String trafficType,
            BigDecimal incomeAmt, Long withdrawBllId, String extra
    ) throws IllegalArgumentException{

        IaaSettlementPo settlement = Optional.ofNullable(
                this.selectByAppTypeAppIdLogDateTrafficType(appType, appId, logdate, trafficType)
        ).orElseGet(() -> {

            log.info("Try to insert a new settlement record, appType={}, appId={}, logdate={}, trafficType={}",
                    appType, appId, logdate, trafficType);

            try {
                iaaSettlementMapper.insertSelective(
                        new IaaSettlementPo()
                                .setSettleKey(Joiner.on("_").join(appId, logdate, trafficType, appType))
                                .setAppType(appType)
                                .setAppId(appId)
                                .setLogdate(logdate)
                                .setTrafficType(trafficType)
                                .setIncomeAmt(incomeAmt)
                                .setWithdrawBillId(withdrawBllId)
                                .setExtra(extra)

                );

            } catch (Throwable throwable) {
                log.warn("Fail to insert a new settlement record, may already existed, appType={}, appId={}, "
                        + "logdate={}, trafficType={}", appType, appId, logdate, trafficType, throwable);
            }

            return this.selectByAppTypeAppIdLogDateTrafficType(appType, appId, logdate, trafficType);
        });

        if (settlement == null) {
            log.error("Unexpected error, can not find the target settlement record, appType={}, appId={}, logdate={}, "
                    + "trafficType={}", appType, appId, logdate, trafficType);
            throw new IllegalArgumentException("找不到目标结算记录");
        }

        return settlement;


    }


    public IaaSettlementPo selectByAppTypeAppIdLogDateTrafficType(String appType, String appId, String logdate,
            String trafficType) {

        IaaSettlementPoExample example = new IaaSettlementPoExample();


        example.createCriteria()
                .andAppTypeEqualTo(appType)
                .andAppIdEqualTo(appId)
                .andLogdateEqualTo(logdate)
                .andTrafficTypeEqualTo(trafficType);

        List<IaaSettlementPo> r = iaaSettlementMapper.selectByExample(example);

        return CollectionUtils.isEmpty(r) ? null : r.get(0);

    }


    public int updateByPrimaryKeySelective(IaaSettlementPo record){

        return iaaSettlementMapper.updateByPrimaryKeySelective(record);
    }



    public List<IaaSettlementPo> selectAllByIdGtAndLimitAndStatusIn(
            List<String> statusList, @Nullable Long id, int limit
    ) {

        return this.selectAllByIdGtAndLimitAndStatusInAndLogdateLteEq(statusList, id, limit, null, null);

    }



    /**
     * 建议正序
     * @return
     */
    public List<IaaSettlementPo> selectAllByIdGtAndLimitAndStatusInAndLogdateLteEq(
            List<String> statusList, @Nullable Long id, int limit, String logdateLte, String logdateEq
    ) {



        if (CollectionUtils.isEmpty(statusList)) {
            return List.of();
        }

        IaaSettlementPoExample example = new IaaSettlementPoExample();

        example.setLimit(limit);
        example.setOrderByClause("id asc");

        IaaSettlementPoExample.Criteria criteria = example.createCriteria();

        criteria.andSettleStatusIn(statusList);

        if (id != null) {
            criteria.andIdGreaterThan(id);
        }

        if (logdateLte != null) {
            criteria.andLogdateLessThanOrEqualTo(logdateLte);
        }

        if(logdateEq != null){
            criteria.andLogdateEqualTo(logdateEq);
        }


        return iaaSettlementMapper.selectByExample(example);


    }


    public List<IaaSettlementPo> selectAllByAppIdTrafficTypeAndLogdateRange(
            String appType,
            String appId,
            String trafficType,
            String logdateFrom ,
            String logdateTo
    ){



        IaaSettlementPoExample example = new IaaSettlementPoExample();

        example.createCriteria()
                .andAppTypeEqualTo(appType)
                .andAppIdEqualTo(appId)
                .andTrafficTypeEqualTo(trafficType)
                .andLogdateGreaterThanOrEqualTo(logdateFrom)
                .andLogdateLessThanOrEqualTo(logdateTo);

        return iaaSettlementMapper.selectByExample(example);


    }

    public List<IaaSettlementPo> selectByWithdrawBillIdEq(Long billId) {


        IaaSettlementPoExample example = new IaaSettlementPoExample();

        example.createCriteria().andWithdrawBillIdEqualTo(billId);

        return iaaSettlementMapper.selectByExample(example);

    }


    public List<IaaSettlementPo> selectByWithdrawBillIdIn(
            List<Long> billIds) {


        if(CollectionUtils.isEmpty(billIds)){
            return List.of();
        }

        IaaSettlementPoExample example = new IaaSettlementPoExample();

        example.createCriteria().andWithdrawBillIdIn(billIds);

        return iaaSettlementMapper.selectByExample(example);

    }

    public List<IaaSettlementPo> selectAllByWithdrawBillId(List<Long> billIds) {

        if(CollectionUtils.isEmpty(billIds)){
            return List.of();
        }

        IaaSettlementPoExample example = new IaaSettlementPoExample();

        example.createCriteria().andWithdrawBillIdIn(billIds);

        return iaaSettlementMapper.selectByExample(example);



    }
}
