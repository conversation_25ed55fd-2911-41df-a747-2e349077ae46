package com.bilibili.miniapp.open.repository.mysql.settlement;

import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaWithdrawBillCustomMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaWithdrawBillMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample.Criteria;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@Slf4j
@Repository
public class IaaWithdrawBillRepository {

    @Resource
    private IaaWithdrawBillMapper iaaWithdrawBillMapper;

    @Resource
    private IaaWithdrawBillCustomMapper iaaWithdrawBillCustomMapper;



    public IaaWithdrawBillPo selectByAppTypeAppIdAndWithdrawDate(String appType, String appId, String withdrawDate) {

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAppTypeEqualTo(appType)
                .andWithdrawDateEqualTo(withdrawDate);

        List<IaaWithdrawBillPo> r = iaaWithdrawBillMapper.selectByExample(example);

        return CollectionUtils.isEmpty(r) ? null : r.get(0);

    }

    public List<IaaWithdrawBillPo> selectByAppTypeAppIdsAndWithdrawDate(String appType, List<String> appIds, String withdrawDate) {

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        Criteria criteria = example.createCriteria();
        criteria.andAppTypeEqualTo(appType)
                .andWithdrawDateEqualTo(withdrawDate);
        if (CollectionUtils.isNotEmpty(appIds)) {
            criteria.andAppIdIn(appIds);
        }

        return iaaWithdrawBillMapper.selectByExample(example);
    }


    public IaaWithdrawBillPo insertOrGetWithdrawBillByAppAndWithdrawDate(
            String appType, String appId, String withdrawDate,
            Date billStartTime, Date billEndTime
            ) throws IllegalArgumentException {

        IaaWithdrawBillPo bills = Optional.ofNullable(this.selectByAppTypeAppIdAndWithdrawDate(
                        appType, appId, withdrawDate))
                .orElseGet(() -> {

                    log.warn("Fail to get the iaa_withdraw_bill, try insert one. input={}-{}-{}", appType, appId,
                            withdrawDate);

                    // 如果此时正好有并发
                    try {
                        iaaWithdrawBillMapper.insertSelective(
                                new IaaWithdrawBillPo()
                                        .setAppType(appType)
                                        .setAppId(appId)
                                        .setWithdrawDate(withdrawDate)
                                        .setBillStartTime(billStartTime)
                                        .setBillEndTime(billEndTime)

                        );
                    } catch (Throwable t) {
                        log.error("Fail to insert iaa_withdraw_bill, input={}-{}-{}, concurrency insert may occur",
                                appType, appId, withdrawDate, t);
                    }

                    return this.selectByAppTypeAppIdAndWithdrawDate(
                            appType, appId, withdrawDate);
                });

        if (bills == null) {
            log.error("Fail to insert all withdraw bill records, appType={}, appId={}, withdrawDate={}",
                    appType, appId, withdrawDate);
            throw new IllegalArgumentException("无法找到提现账单记录");
        }
        return bills;

    }





    public List<IaaWithdrawBillPo> selectAllByPrimaryKeys(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        example.createCriteria().andIdIn(ids);

        return iaaWithdrawBillMapper.selectByExample(example);
    }


    public int increaseByPrimaryKey(IaaWithdrawBillPo increment){

        return iaaWithdrawBillCustomMapper.increaseByPrimaryKey(increment);
    }




    public int updateByPrimaryKeySelective(IaaWithdrawBillPo update) {
        return iaaWithdrawBillMapper.updateByPrimaryKeySelective(update);
    }


    /**
     * .setBillStatus(IaaWithdrawBillStatus.withdrawing.name())
     *                             .setWithdrawTime(LocalDateTime.now())
     *                             .setInvoiceImgUrl(applyRequest.getInvoiceImgUrl())
     *                             .setExpenseCode(errorCode)
     *                             .setExpenseExtra(JsonUtil.writeValueAsString(new ExpenseExtra()
     *                                     .setParams(params)
     *                                     .setExpenseCode(errorCode)
     *                                     .setExpenseMessage(errorMessage)
     *                             )
     * @param update
     * @param ids
     * @return
     */
    public int updateBatchByPrimaryKeysSelective(IaaWithdrawBillPo update, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        example.createCriteria().andIdIn(ids);

        return iaaWithdrawBillMapper.updateByExampleSelective(update, example);
    }


    public List<IaaWithdrawBillPo> selectByAppTypAppKeyAndStatusAndWithdrawDateLte(
            String appType, String appId,
            String iaaWithdrawBillStatus,
            String withdrawDate) {

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAppTypeEqualTo(appType)
                .andBillStatusEqualTo(iaaWithdrawBillStatus)
                .andWithdrawDateLessThanOrEqualTo(withdrawDate);

        return iaaWithdrawBillMapper.selectByExample(example);


    }

    public List<IaaWithdrawBillPo> selectByExpenseId(String expenseId) {

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        example.createCriteria().andExpenseIdEqualTo(expenseId);

        return iaaWithdrawBillMapper.selectByExample(example);

    }

    public Optional<IaaWithdrawBillPo> selectByPrimaryKey(Long billId) {

        return Optional.ofNullable(iaaWithdrawBillMapper.selectByPrimaryKey(billId));

    }


    public List<IaaWithdrawBillPo> selectAllByIdIn(List<Long> billIds) {

        if(CollectionUtils.isEmpty(billIds)){
            return Collections.emptyList();
        }

        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();
        example.createCriteria().andIdIn(billIds);

        return iaaWithdrawBillMapper.selectByExample(example);

    }



    public List<IaaWithdrawBillPo> selectAllByIdGtAndLimitAndStatusInAndWithdrawDateLte(Long id, int limit,
            ArrayList<String> strings, String fWithdrawDateLte) {


        IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();

        Criteria criteria = example.createCriteria()
                .andBillStatusIn(strings)
                .andWithdrawDateLessThanOrEqualTo(fWithdrawDateLte);



        if(id != null){
            criteria.andIdGreaterThan(id);
        }


        example.setLimit(limit);
        example.setOrderByClause("id asc");

        return iaaWithdrawBillMapper.selectByExample(example);

    }
}
