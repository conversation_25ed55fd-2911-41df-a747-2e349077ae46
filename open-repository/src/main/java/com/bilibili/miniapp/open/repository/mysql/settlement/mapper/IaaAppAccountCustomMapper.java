package com.bilibili.miniapp.open.repository.mysql.settlement.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IaaAppAccountCustomMapper {


    int increaseByPrimaryKey(IaaAppAccountPo increment);

}