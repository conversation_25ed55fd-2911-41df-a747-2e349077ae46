package com.bilibili.miniapp.open.repository.mysql.settlement.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IaaAppAccountMapper {
    long countByExample(IaaAppAccountPoExample example);

    int deleteByExample(IaaAppAccountPoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IaaAppAccountPo record);

    int insertSelective(IaaAppAccountPo record);

    List<IaaAppAccountPo> selectByExample(IaaAppAccountPoExample example);

    IaaAppAccountPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IaaAppAccountPo record, @Param("example") IaaAppAccountPoExample example);

    int updateByExample(@Param("record") IaaAppAccountPo record, @Param("example") IaaAppAccountPoExample example);

    int updateByPrimaryKeySelective(IaaAppAccountPo record);

    int updateByPrimaryKey(IaaAppAccountPo record);
}