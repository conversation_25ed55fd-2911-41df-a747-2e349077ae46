package com.bilibili.miniapp.open.repository.mysql.settlement.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IaaCrmChargeBillMapper {
    long countByExample(IaaCrmChargeBillPoExample example);

    int deleteByExample(IaaCrmChargeBillPoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IaaCrmChargeBillPo record);

    int insertSelective(IaaCrmChargeBillPo record);

    List<IaaCrmChargeBillPo> selectByExample(IaaCrmChargeBillPoExample example);

    IaaCrmChargeBillPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IaaCrmChargeBillPo record, @Param("example") IaaCrmChargeBillPoExample example);

    int updateByExample(@Param("record") IaaCrmChargeBillPo record, @Param("example") IaaCrmChargeBillPoExample example);

    int updateByPrimaryKeySelective(IaaCrmChargeBillPo record);

    int updateByPrimaryKey(IaaCrmChargeBillPo record);
}