package com.bilibili.miniapp.open.repository.mysql.settlement.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IaaSettlementMapper {
    long countByExample(IaaSettlementPoExample example);

    int deleteByExample(IaaSettlementPoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IaaSettlementPo record);

    int insertSelective(IaaSettlementPo record);

    List<IaaSettlementPo> selectByExample(IaaSettlementPoExample example);

    IaaSettlementPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IaaSettlementPo record, @Param("example") IaaSettlementPoExample example);

    int updateByExample(@Param("record") IaaSettlementPo record, @Param("example") IaaSettlementPoExample example);

    int updateByPrimaryKeySelective(IaaSettlementPo record);

    int updateByPrimaryKey(IaaSettlementPo record);
}