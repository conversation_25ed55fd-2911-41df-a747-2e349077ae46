package com.bilibili.miniapp.open.repository.mysql.settlement.mapper;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IaaWithdrawBillMapper {
    long countByExample(IaaWithdrawBillPoExample example);

    int deleteByExample(IaaWithdrawBillPoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(IaaWithdrawBillPo record);

    int insertSelective(IaaWithdrawBillPo record);

    List<IaaWithdrawBillPo> selectByExample(IaaWithdrawBillPoExample example);

    IaaWithdrawBillPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") IaaWithdrawBillPo record, @Param("example") IaaWithdrawBillPoExample example);

    int updateByExample(@Param("record") IaaWithdrawBillPo record, @Param("example") IaaWithdrawBillPoExample example);

    int updateByPrimaryKeySelective(IaaWithdrawBillPo record);

    int updateByPrimaryKey(IaaWithdrawBillPo record);
}