package com.bilibili.miniapp.open.repository.mysql.settlement.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * iaa_app_account
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class IaaAppAccountPo implements Serializable {
    /**
     * 账户自增id
     */
    private Long id;

    /**
     * 账户类型 mini_game/mini_app
     */
    private String appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 累计收入,分
     */
    private BigDecimal incomeAmt;

    /**
     * 累计广告收入,分
     */
    private BigDecimal incomeBusinessPartAmt;

    /**
     * 累计自然收入,分
     */
    private BigDecimal incomeNaturalPartAmt;

    /**
     * 累计提现,分，不一定到账，只要预提就发生
     */
    private BigDecimal withdrawAmt;

    /**
     * 累计提现,分，不一定到账，只要预提就发生
     */
    private BigDecimal withdrawBusinessPartAmt;

    /**
     * 累计提现,分，不一定到账，只要预提就发生
     */
    private BigDecimal withdrawNaturalPartAmt;

    /**
     * 累计CRM扣款,分
     */
    private BigDecimal crmChargeAmt;

    /**
     * 结算次数
     */
    private Integer settleTimes;

    /**
     * 最近一次结算时间
     */
    private Date latestSettleTime;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 是否删除(0:未删除,1:已删除)
     */
    private Byte deleted;

    /**
     * 额外参数
     */
    private String extra;

    private static final long serialVersionUID = 1L;
}