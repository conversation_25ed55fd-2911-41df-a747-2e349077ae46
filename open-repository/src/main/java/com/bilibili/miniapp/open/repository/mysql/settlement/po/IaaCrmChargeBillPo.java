package com.bilibili.miniapp.open.repository.mysql.settlement.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * iaa_crm_charge_bill
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class IaaCrmChargeBillPo implements Serializable {
    /**
     * 提现单自增id
     */
    private Long id;

    /**
     * 账户类型 mini_game/mini_app
     */
    private String appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 流量类型 natural/business
     */
    private String trafficType;

    /**
     * 日志日期
     */
    private String logdate;

    /**
     * CRM充值金额,分
     */
    private BigDecimal chargeAmt;

    /**
     * 收入金额，分
     */
    private BigDecimal incomeAmt;

    /**
     * 关联结算单号
     */
    private Long settlementId;

    /**
     * 提现单ID
     */
    private Long withdrawBillId;

    /**
     * 充值日期
     */
    private Date chargeTime;

    /**
     * 充值状态，理论上插入就应该只有成功
     */
    private String billStatus;

    /**
     * 充值失败原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 是否删除
     */
    private Byte deleted;

    /**
     * 额外参数
     */
    private String extra;

    private static final long serialVersionUID = 1L;
}