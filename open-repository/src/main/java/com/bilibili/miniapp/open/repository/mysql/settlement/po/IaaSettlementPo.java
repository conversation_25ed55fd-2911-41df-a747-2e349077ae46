package com.bilibili.miniapp.open.repository.mysql.settlement.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * iaa_settlement
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class IaaSettlementPo implements Serializable {
    /**
     * 结算自增id
     */
    private Long id;

    /**
     * 结算的唯一键保证事务性 UniqueKey:app_id,aggre_logdate,flow_type
     */
    private String settleKey;

    /**
     * 聚合日期 20250101
     */
    private String logdate;

    /**
     * 流量类型 natural/business
     */
    private String trafficType;

    /**
     * 账户类型 mini_game/mini_app
     */
    private String appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 结算时间
     */
    private Date settleTime;

    /**
     * 结算状态. init:初始化, settled:已结算,settling中,failed失败
     */
    private String settleStatus;

    /**
     * 结算原因
     */
    private String settleReason;

    /**
     * 提现单ID
     */
    private Long withdrawBillId;

    /**
     * 收入,分
     */
    private BigDecimal incomeAmt;

    /**
     * 提现金额,分
     */
    private BigDecimal withdrawAmt;

    /**
     * CRM充值金额,分
     */
    private BigDecimal crmChargeAmt;

    /**
     * 结算额外信息， json格式，例如结算规则等
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 是否删除, 预留
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

}