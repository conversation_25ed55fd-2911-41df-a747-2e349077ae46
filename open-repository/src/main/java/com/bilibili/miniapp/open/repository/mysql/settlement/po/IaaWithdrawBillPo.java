package com.bilibili.miniapp.open.repository.mysql.settlement.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * iaa_withdraw_bill
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class IaaWithdrawBillPo implements Serializable {
    /**
     * 提现单自增id
     */
    private Long id;

    /**
     * 账单抬头
     */
    private String title;

    /**
     * 账单状态: 初始化, 预提现（待补充发票并确认体现）
     */
    private String billStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 账户类型 mini_game/mini_app
     */
    private String appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 账单开始时间
     */
    private Date billStartTime;

    /**
     * 账单结束时间
     */
    private Date billEndTime;

    /**
     * 提现账单日 格式202501_1 2025年一月上半月
     */
    private String withdrawDate;

    /**
     * 最近一次结算时间
     */
    private Date latestSettleTime;

    /**
     * 用户发起提现的时间
     */
    private Date withdrawApplyTime;

    /**
     * 提现到账时间
     */
    private Date withdrawArrivalTime;

    /**
     * 当前账单周期内的累计金额
     */
    private BigDecimal incomeAmt;

    /**
     * 结算次数
     */
    private Integer settleTimes;

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmt;

    /**
     * CRM充值金额
     */
    private BigDecimal crmChargeAmt;

    /**
     * 当前账单周期内的自然收入部分
     */
    private BigDecimal incomeNaturalPartAmt;

    /**
     * 当前账单周期内的商业收入部分
     */
    private BigDecimal incomeBusinessPartAmt;

    /**
     * 自然收入部分提现金额
     */
    private BigDecimal withdrawNaturalPartAmt;

    /**
     * 商业收入部分提现金额
     */
    private BigDecimal withdrawBusinessPartAmt;

    /**
     * 业务实体名称
     */
    private String businessEntityName;

    /**
     * 提现请求金额
     */
    private BigDecimal withdrawApplyAmt;

    /**
     * 发票图片地址
     */
    private String invoiceImgUrl;

    /**
     * 汇联易预提单id
     */
    private String accrualId;

    /**
     * 汇联易预提单附加信息
     */
    private String accrualExtra;

    /**
     * 汇联易付款单id
     */
    private String expenseId;

    /**
     * 汇联易付款单附加信息
     */
    private String expenseExtra;

    /**
     * 汇联易付款单代码
     */
    private String expenseCode;

    /**
     * 汇联易付款单消息
     */
    private String expenseMessage;

    /**
     * 预留字段，额外信息
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 是否删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;
}