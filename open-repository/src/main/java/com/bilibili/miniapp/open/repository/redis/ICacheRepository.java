package com.bilibili.miniapp.open.repository.redis;

import com.alibaba.fastjson.TypeReference;
import com.bilibili.miniapp.open.common.entity.LockOption;
import org.redisson.api.RLock;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/01/03 14:06
 */
public interface ICacheRepository {
    <T> T getObject(String key, Class<T> clazz);

    <T> T getObject(String key, TypeReference<T> clazz);

    <T> List<T> getList(String key, Class<T> clazz);

    void addAllList(String key, List<?> values);

    void addAllList(String key, List<?> values, long expireTime, TimeUnit timeUnit);

    void clearAndAddAllList(String key, List<?> values, long expireTime, TimeUnit timeUnit);

    <T> Set<T> getSet(String key, Class<T> clazz);

    void addAllSet(String key, Set<?> values);

    void addAllSet(String key, Set<?> values, long expireTime, TimeUnit timeUnit);

    void clearAndAddAllSet(String key, Set<?> values, long expireTime, TimeUnit timeUnit);

    void clearAndAddAllSet(String key, Set<?> values);

    boolean removeAllSet(String key, Set<?> values);

    <T> boolean containsSetValue(String key, T value);

    void setObject(String key, Object value);

    void setObject(String key, Object value, long expireTime, TimeUnit timeUnit);

    void setObject(String key, Object value, LocalDateTime expireAt);

    boolean delete(String key);

    boolean expire(String key, long expireTime, ChronoUnit timeUnit);

    /**
     * 返回并锁定对应资源，上游记得及时释放
     * <pre>
     *      RLock lock = getLock("key");
     *      try {
     *          //to do sth
     *      } finally {
     *          if (Objects.nonNull(lock)) {
     *              lock.unlock();
     *          }
     *      }
     * </pre>
     *
     * @param key must have text
     */
    @Deprecated
    RLock getLock(String key, LockOption option);

    RLock tryLock(String key);

    <T> Map<String, T> multiGetObject(List<String> keys, Class<T> clazz);

    void multiSetObject(Map<String, ?> map, long expireTime, ChronoUnit chronoUnit, double expireJitterPercent);
}
