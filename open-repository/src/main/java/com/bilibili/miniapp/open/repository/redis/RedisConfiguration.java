package com.bilibili.miniapp.open.repository.redis;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Slf4j
@Configuration
public class RedisConfiguration {

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient(RedissonConfigurationProperties redissonConfigurationProperties) {
        Config config = new Config();
        config.setCodec(new StringCodec())
                .useClusterServers()
                .setMasterConnectionPoolSize(redissonConfigurationProperties.getPoolSize())
                .setMasterConnectionMinimumIdleSize(redissonConfigurationProperties.getMinIdle())
                .setConnectTimeout(redissonConfigurationProperties.getConnectTimeout())
                .setTimeout(redissonConfigurationProperties.getTimeout())
                .addNodeAddress(redissonConfigurationProperties.getNodes().split(","));
        for (int i = 0; i < 10; i++) {
            try {
                return Redisson.create(config);
            } catch (Exception e) {
                log.warn("Create RedissonClient fail and retry", e);
            }
        }
        throw new IllegalStateException("Create RedissonClient fail!");
    }

    @Data
    @Configuration
    @PropertySource(value = {"classpath:redis.properties"}, factory = PaladinPropertySourceFactory.class)
    @ConfigurationProperties(prefix = "redisson")
    static class RedissonConfigurationProperties {
        private String nodes;
        private Integer connectTimeout;
        private Integer timeout;
        private Integer poolSize;
        private Integer maxIdle;
        private Integer minIdle;
    }

}
