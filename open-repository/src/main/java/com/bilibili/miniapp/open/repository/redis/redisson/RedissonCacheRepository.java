package com.bilibili.miniapp.open.repository.redis.redisson;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.miniapp.open.common.entity.LockOption;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/01/03 14:06
 */
@Slf4j
@Repository
public class RedissonCacheRepository implements ICacheRepository {
    @Autowired
    private RedissonClient redissonClient;

    @Override
    public <T> T getObject(String key, Class<T> clazz) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        String value = bucket.get();
        return StringUtils.hasText(value) ? JSON.parseObject(value, clazz) : null;
    }

    @Override
    public <T> T getObject(String key, TypeReference<T> clazz) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        String value = bucket.get();
        return StringUtils.hasText(value) ? JSON.parseObject(value, clazz) : null;
    }

    @Override
    public <T> List<T> getList(String key, Class<T> clazz) {
        RList<String> list = redissonClient.getList(key);
        List<String> elements = list.readAll();
        if (CollectionUtils.isEmpty(elements)) {
            return Lists.newLinkedList();
        }
        return elements.stream()
                .map(element -> JSON.parseObject(element, clazz))
                .collect(Collectors.toList());
    }

    @Override
    public void addAllList(String key, List<?> values) {
        RList<String> list = redissonClient.getList(key);
        list.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
    }

    @Override
    public void addAllList(String key, List<?> values, long expireTime, TimeUnit timeUnit) {
        RList<String> list = redissonClient.getList(key);
        list.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
        list.expire(Duration.of(expireTime, timeUnit.toChronoUnit()));
    }

    @Override
    public void clearAndAddAllList(String key, List<?> values, long expireTime, TimeUnit timeUnit) {
        RList<String> list = redissonClient.getList(key);
        list.clear();
        list.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
        list.expire(Duration.of(expireTime, timeUnit.toChronoUnit()));
    }

    @Override
    public <T> Set<T> getSet(String key, Class<T> clazz) {
        RSet<String> set = redissonClient.getSet(key);
        Set<String> elements = set.readAll();
        if (CollectionUtils.isEmpty(elements)) {
            return Sets.newHashSet();
        }
        return set.stream()
                .map(element -> JSON.parseObject(element, clazz))
                .collect(Collectors.toSet());
    }

    @Override
    public void addAllSet(String key, Set<?> values) {
        RSet<String> set = redissonClient.getSet(key);
        set.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
    }

    @Override
    public void addAllSet(String key, Set<?> values, long expireTime, TimeUnit timeUnit) {
        RSet<String> set = redissonClient.getSet(key);
        set.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
        set.expire(Duration.of(expireTime, timeUnit.toChronoUnit()));
    }

    @Override
    public void clearAndAddAllSet(String key, Set<?> values, long expireTime, TimeUnit timeUnit) {
        RSet<String> set = redissonClient.getSet(key);
        set.clear();
        set.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
        set.expire(Duration.of(expireTime, timeUnit.toChronoUnit()));
    }

    @Override
    public void clearAndAddAllSet(String key, Set<?> values) {
        RSet<String> set = redissonClient.getSet(key);
        set.clear();
        set.addAll(values.stream()
                .map(JSON::toJSONString)
                .collect(Collectors.toList()));
    }

    @Override
    public boolean removeAllSet(String key, Set<?> values) {
        RSet<String> set = redissonClient.getSet(key);
        return set.removeAll(values.stream().map(JSON::toJSONString).collect(Collectors.toList()));
    }

    @Override
    public <T> boolean containsSetValue(String key, T value) {
        RSet<String> set = redissonClient.getSet(key);
        return set.contains(JSON.toJSONString(value));
    }

    @Override
    public void setObject(String key, Object value) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(JSON.toJSONString(value));
    }

    @Override
    public void setObject(String key, Object value, long expireTime, TimeUnit timeUnit) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(JSON.toJSONString(value), expireTime, timeUnit);
    }

    @Override
    public void setObject(String key, Object value, LocalDateTime expireAt) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(JSON.toJSONString(value));
        bucket.expire(expireAt.toInstant(ZoneOffset.ofHours(8)));
    }

    @Override
    public boolean delete(String key) {
        return redissonClient.getBucket(key).delete();
    }

    @Override
    public boolean expire(String key, long expireTime, ChronoUnit timeUnit) {
        return redissonClient.getBucket(key).expire(Duration.of(expireTime, timeUnit));
    }

    @Override
    public RLock getLock(String key, LockOption option) {
        AssertUtil.hasText(key, ErrorCodeType.BAD_DATA.getCode(), "key不能为空");
        if (Objects.isNull(option)) {
            option = LockOption.DEFAULT_OPTION;
        }
        RLock lock = null;
        boolean acquired = false;
        Exception ex = null;
        try {
            lock = redissonClient.getLock(key);
            acquired = lock.tryLock(option.getWaitTime(), option.getLeaseTime(), option.getUnit());
        } catch (Exception e) {
            ex = e;
            log.error("[RedissonCacheRepository] get lock for {} error", key, e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        } finally {
            log.info("[RedissonCacheRepository] get lock for {} result={}", key, acquired);
            if (Objects.nonNull(ex) && Objects.nonNull(lock)) {
                //如果异常，则尝试释放锁
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("[RedissonCacheRepository] release lock for {} error", key, e);
                }
            }
        }
        AssertUtil.isTrue(acquired, ErrorCodeType.CONCURRENT_OPERATE.getCode(),
                StringUtils.hasText(option.getErrMsg()) ? option.getErrMsg() : ErrorCodeType.CONCURRENT_OPERATE.getMessage());
        return lock;
    }

    @Override
    public RLock tryLock(String key) {
        RLock lock = redissonClient.getLock(key);
        boolean locked = lock.tryLock();
        AssertUtil.isTrue(locked, ErrorCodeType.CONCURRENT_OPERATE);
        return lock;
    }

    @Override
    public <T> Map<String, T> multiGetObject(List<String> keys, Class<T> clazz) {
        // 参数校验
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量key获取
        Map<String, Object> map = redissonClient.getBuckets().get(keys.toArray(new String[0]));
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }

        Map<String, T> resultMap = new HashMap<>(keys.size());
        map.forEach((key, value) -> {
            if (value != null) {
                resultMap.put(key, JSON.parseObject(value.toString(), clazz));
            }
        });
        return resultMap;
    }

    @Override
    public void multiSetObject(Map<String, ?> map, long expireTime, ChronoUnit chronoUnit, double expireJitterPercent) {
        // 参数校验
        if (MapUtils.isEmpty(map)) {
            return;
        }
        // 1. 时间单位规范化处理
        TimeUnit timeUnit = TimeUnit.of(chronoUnit);

        // 2. 创建批量操作管道
        RBatch batch = redissonClient.createBatch();

        // 3. 指令异步提交
        map.forEach((key, value) -> {
            RBucketAsync<Object> bucket = batch.getBucket(key);

            // 指数退避随机算法
            long randomizedExpire = calculateJitterExpire(expireTime, timeUnit, expireJitterPercent);

            bucket.setAsync(JSON.toJSONString(value), randomizedExpire, timeUnit);
        });

        // 4. 批量执行命令
        try {
            batch.execute();
        } catch (Exception e) {
            log.error("[RedissonCacheRepository] multiSetObject error", e);
        }
    }

    /**
     * 带抖动的过期时间计算公式
     */
    private long calculateJitterExpire(long baseExpire, TimeUnit unit, double expireJitterPercent) {
        // 随机性来源升级为加密安全随机数
        double jitter = ThreadLocalRandom.current().nextDouble(-expireJitterPercent, expireJitterPercent);
        long delta = (long) (baseExpire * jitter);

        // 结果下限保护（最少1个时间单位）
        return Math.max(unit.convert(1, unit), baseExpire + delta);
    }
}
