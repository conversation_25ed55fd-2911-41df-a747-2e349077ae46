<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaAppAccountCustomMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="income_amt" jdbcType="DECIMAL" property="incomeAmt" />
    <result column="income_business_part_amt" jdbcType="DECIMAL" property="incomeBusinessPartAmt" />
    <result column="income_natural_part_amt" jdbcType="DECIMAL" property="incomeNaturalPartAmt" />
    <result column="withdraw_amt" jdbcType="DECIMAL" property="withdrawAmt" />
    <result column="withdraw_business_part_amt" jdbcType="DECIMAL" property="withdrawBusinessPartAmt" />
    <result column="withdraw_natural_part_amt" jdbcType="DECIMAL" property="withdrawNaturalPartAmt" />
    <result column="crm_charge_amt" jdbcType="DECIMAL" property="crmChargeAmt" />
    <result column="settle_times" jdbcType="INTEGER" property="settleTimes" />
    <result column="latest_settle_time" jdbcType="TIMESTAMP" property="latestSettleTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_type, app_id, income_amt, income_business_part_amt, income_natural_part_amt, 
    withdraw_amt, withdraw_business_part_amt, withdraw_natural_part_amt, crm_charge_amt, 
    settle_times, latest_settle_time, ctime, mtime, deleted, extra
  </sql>


  <update id="increaseByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo">
    update iaa_app_account
    <set>
      <if test="incomeAmt != null">
        income_amt = income_amt+ #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeBusinessPartAmt != null">
        income_business_part_amt = income_business_part_amt+ #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeNaturalPartAmt != null">
        income_natural_part_amt = income_natural_part_amt +  #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt = withdraw_amt+ #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawBusinessPartAmt != null">
        withdraw_business_part_amt = withdraw_business_part_amt+ #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt = withdraw_natural_part_amt+ #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt = crm_charge_amt + #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settleTimes != null">
        settle_times = settle_times + #{settleTimes,jdbcType=INTEGER},
      </if>
      <if test="latestSettleTime != null">
        latest_settle_time = #{latestSettleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>