<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaAppAccountMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="income_amt" jdbcType="DECIMAL" property="incomeAmt" />
    <result column="income_business_part_amt" jdbcType="DECIMAL" property="incomeBusinessPartAmt" />
    <result column="income_natural_part_amt" jdbcType="DECIMAL" property="incomeNaturalPartAmt" />
    <result column="withdraw_amt" jdbcType="DECIMAL" property="withdrawAmt" />
    <result column="withdraw_business_part_amt" jdbcType="DECIMAL" property="withdrawBusinessPartAmt" />
    <result column="withdraw_natural_part_amt" jdbcType="DECIMAL" property="withdrawNaturalPartAmt" />
    <result column="crm_charge_amt" jdbcType="DECIMAL" property="crmChargeAmt" />
    <result column="settle_times" jdbcType="INTEGER" property="settleTimes" />
    <result column="latest_settle_time" jdbcType="TIMESTAMP" property="latestSettleTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_type, app_id, income_amt, income_business_part_amt, income_natural_part_amt, 
    withdraw_amt, withdraw_business_part_amt, withdraw_natural_part_amt, crm_charge_amt, 
    settle_times, latest_settle_time, ctime, mtime, deleted, extra
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iaa_app_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iaa_app_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iaa_app_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPoExample">
    delete from iaa_app_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo" useGeneratedKeys="true">
    insert into iaa_app_account (app_type, app_id, income_amt, 
      income_business_part_amt, income_natural_part_amt, 
      withdraw_amt, withdraw_business_part_amt, withdraw_natural_part_amt, 
      crm_charge_amt, settle_times, latest_settle_time, 
      ctime, mtime, deleted, 
      extra)
    values (#{appType,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{incomeAmt,jdbcType=DECIMAL}, 
      #{incomeBusinessPartAmt,jdbcType=DECIMAL}, #{incomeNaturalPartAmt,jdbcType=DECIMAL}, 
      #{withdrawAmt,jdbcType=DECIMAL}, #{withdrawBusinessPartAmt,jdbcType=DECIMAL}, #{withdrawNaturalPartAmt,jdbcType=DECIMAL}, 
      #{crmChargeAmt,jdbcType=DECIMAL}, #{settleTimes,jdbcType=INTEGER}, #{latestSettleTime,jdbcType=TIMESTAMP}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}, 
      #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo" useGeneratedKeys="true">
    insert into iaa_app_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appType != null">
        app_type,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="incomeAmt != null">
        income_amt,
      </if>
      <if test="incomeBusinessPartAmt != null">
        income_business_part_amt,
      </if>
      <if test="incomeNaturalPartAmt != null">
        income_natural_part_amt,
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt,
      </if>
      <if test="withdrawBusinessPartAmt != null">
        withdraw_business_part_amt,
      </if>
      <if test="withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt,
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt,
      </if>
      <if test="settleTimes != null">
        settle_times,
      </if>
      <if test="latestSettleTime != null">
        latest_settle_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appType != null">
        #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="incomeAmt != null">
        #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeBusinessPartAmt != null">
        #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeNaturalPartAmt != null">
        #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawAmt != null">
        #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawBusinessPartAmt != null">
        #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawNaturalPartAmt != null">
        #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settleTimes != null">
        #{settleTimes,jdbcType=INTEGER},
      </if>
      <if test="latestSettleTime != null">
        #{latestSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPoExample" resultType="java.lang.Long">
    select count(*) from iaa_app_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iaa_app_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appType != null">
        app_type = #{record.appType,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.incomeAmt != null">
        income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeBusinessPartAmt != null">
        income_business_part_amt = #{record.incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeNaturalPartAmt != null">
        income_natural_part_amt = #{record.incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.withdrawAmt != null">
        withdraw_amt = #{record.withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.withdrawBusinessPartAmt != null">
        withdraw_business_part_amt = #{record.withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt = #{record.withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.crmChargeAmt != null">
        crm_charge_amt = #{record.crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.settleTimes != null">
        settle_times = #{record.settleTimes,jdbcType=INTEGER},
      </if>
      <if test="record.latestSettleTime != null">
        latest_settle_time = #{record.latestSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iaa_app_account
    set id = #{record.id,jdbcType=BIGINT},
      app_type = #{record.appType,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      income_business_part_amt = #{record.incomeBusinessPartAmt,jdbcType=DECIMAL},
      income_natural_part_amt = #{record.incomeNaturalPartAmt,jdbcType=DECIMAL},
      withdraw_amt = #{record.withdrawAmt,jdbcType=DECIMAL},
      withdraw_business_part_amt = #{record.withdrawBusinessPartAmt,jdbcType=DECIMAL},
      withdraw_natural_part_amt = #{record.withdrawNaturalPartAmt,jdbcType=DECIMAL},
      crm_charge_amt = #{record.crmChargeAmt,jdbcType=DECIMAL},
      settle_times = #{record.settleTimes,jdbcType=INTEGER},
      latest_settle_time = #{record.latestSettleTime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo">
    update iaa_app_account
    <set>
      <if test="appType != null">
        app_type = #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="incomeAmt != null">
        income_amt = #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeBusinessPartAmt != null">
        income_business_part_amt = #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeNaturalPartAmt != null">
        income_natural_part_amt = #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt = #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawBusinessPartAmt != null">
        withdraw_business_part_amt = #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt = #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt = #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settleTimes != null">
        settle_times = #{settleTimes,jdbcType=INTEGER},
      </if>
      <if test="latestSettleTime != null">
        latest_settle_time = #{latestSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo">
    update iaa_app_account
    set app_type = #{appType,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      income_amt = #{incomeAmt,jdbcType=DECIMAL},
      income_business_part_amt = #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      income_natural_part_amt = #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      withdraw_amt = #{withdrawAmt,jdbcType=DECIMAL},
      withdraw_business_part_amt = #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      withdraw_natural_part_amt = #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      crm_charge_amt = #{crmChargeAmt,jdbcType=DECIMAL},
      settle_times = #{settleTimes,jdbcType=INTEGER},
      latest_settle_time = #{latestSettleTime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>