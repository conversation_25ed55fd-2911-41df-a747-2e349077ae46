<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaCrmChargeBillMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="traffic_type" jdbcType="VARCHAR" property="trafficType" />
    <result column="logdate" jdbcType="VARCHAR" property="logdate" />
    <result column="charge_amt" jdbcType="DECIMAL" property="chargeAmt" />
    <result column="income_amt" jdbcType="DECIMAL" property="incomeAmt" />
    <result column="settlement_id" jdbcType="BIGINT" property="settlementId" />
    <result column="withdraw_bill_id" jdbcType="BIGINT" property="withdrawBillId" />
    <result column="charge_time" jdbcType="TIMESTAMP" property="chargeTime" />
    <result column="bill_status" jdbcType="VARCHAR" property="billStatus" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_type, app_id, traffic_type, logdate, charge_amt, income_amt, settlement_id, 
    withdraw_bill_id, charge_time, bill_status, reason, ctime, mtime, deleted, extra
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iaa_crm_charge_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iaa_crm_charge_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iaa_crm_charge_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPoExample">
    delete from iaa_crm_charge_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo" useGeneratedKeys="true">
    insert into iaa_crm_charge_bill (app_type, app_id, traffic_type, 
      logdate, charge_amt, income_amt, 
      settlement_id, withdraw_bill_id, charge_time, 
      bill_status, reason, ctime, 
      mtime, deleted, extra
      )
    values (#{appType,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{trafficType,jdbcType=VARCHAR}, 
      #{logdate,jdbcType=VARCHAR}, #{chargeAmt,jdbcType=DECIMAL}, #{incomeAmt,jdbcType=DECIMAL}, 
      #{settlementId,jdbcType=BIGINT}, #{withdrawBillId,jdbcType=BIGINT}, #{chargeTime,jdbcType=TIMESTAMP}, 
      #{billStatus,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}, #{extra,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo" useGeneratedKeys="true">
    insert into iaa_crm_charge_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appType != null">
        app_type,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
      <if test="logdate != null">
        logdate,
      </if>
      <if test="chargeAmt != null">
        charge_amt,
      </if>
      <if test="incomeAmt != null">
        income_amt,
      </if>
      <if test="settlementId != null">
        settlement_id,
      </if>
      <if test="withdrawBillId != null">
        withdraw_bill_id,
      </if>
      <if test="chargeTime != null">
        charge_time,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appType != null">
        #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=VARCHAR},
      </if>
      <if test="logdate != null">
        #{logdate,jdbcType=VARCHAR},
      </if>
      <if test="chargeAmt != null">
        #{chargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeAmt != null">
        #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settlementId != null">
        #{settlementId,jdbcType=BIGINT},
      </if>
      <if test="withdrawBillId != null">
        #{withdrawBillId,jdbcType=BIGINT},
      </if>
      <if test="chargeTime != null">
        #{chargeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPoExample" resultType="java.lang.Long">
    select count(*) from iaa_crm_charge_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iaa_crm_charge_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appType != null">
        app_type = #{record.appType,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.trafficType != null">
        traffic_type = #{record.trafficType,jdbcType=VARCHAR},
      </if>
      <if test="record.logdate != null">
        logdate = #{record.logdate,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeAmt != null">
        charge_amt = #{record.chargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeAmt != null">
        income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.settlementId != null">
        settlement_id = #{record.settlementId,jdbcType=BIGINT},
      </if>
      <if test="record.withdrawBillId != null">
        withdraw_bill_id = #{record.withdrawBillId,jdbcType=BIGINT},
      </if>
      <if test="record.chargeTime != null">
        charge_time = #{record.chargeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.billStatus != null">
        bill_status = #{record.billStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iaa_crm_charge_bill
    set id = #{record.id,jdbcType=BIGINT},
      app_type = #{record.appType,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      traffic_type = #{record.trafficType,jdbcType=VARCHAR},
      logdate = #{record.logdate,jdbcType=VARCHAR},
      charge_amt = #{record.chargeAmt,jdbcType=DECIMAL},
      income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      settlement_id = #{record.settlementId,jdbcType=BIGINT},
      withdraw_bill_id = #{record.withdrawBillId,jdbcType=BIGINT},
      charge_time = #{record.chargeTime,jdbcType=TIMESTAMP},
      bill_status = #{record.billStatus,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo">
    update iaa_crm_charge_bill
    <set>
      <if test="appType != null">
        app_type = #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        traffic_type = #{trafficType,jdbcType=VARCHAR},
      </if>
      <if test="logdate != null">
        logdate = #{logdate,jdbcType=VARCHAR},
      </if>
      <if test="chargeAmt != null">
        charge_amt = #{chargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeAmt != null">
        income_amt = #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settlementId != null">
        settlement_id = #{settlementId,jdbcType=BIGINT},
      </if>
      <if test="withdrawBillId != null">
        withdraw_bill_id = #{withdrawBillId,jdbcType=BIGINT},
      </if>
      <if test="chargeTime != null">
        charge_time = #{chargeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo">
    update iaa_crm_charge_bill
    set app_type = #{appType,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      traffic_type = #{trafficType,jdbcType=VARCHAR},
      logdate = #{logdate,jdbcType=VARCHAR},
      charge_amt = #{chargeAmt,jdbcType=DECIMAL},
      income_amt = #{incomeAmt,jdbcType=DECIMAL},
      settlement_id = #{settlementId,jdbcType=BIGINT},
      withdraw_bill_id = #{withdrawBillId,jdbcType=BIGINT},
      charge_time = #{chargeTime,jdbcType=TIMESTAMP},
      bill_status = #{billStatus,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>