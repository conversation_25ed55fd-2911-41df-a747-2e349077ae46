<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaSettlementMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="settle_key" jdbcType="VARCHAR" property="settleKey" />
    <result column="logdate" jdbcType="VARCHAR" property="logdate" />
    <result column="traffic_type" jdbcType="VARCHAR" property="trafficType" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="settle_time" jdbcType="TIMESTAMP" property="settleTime" />
    <result column="settle_status" jdbcType="VARCHAR" property="settleStatus" />
    <result column="settle_reason" jdbcType="VARCHAR" property="settleReason" />
    <result column="withdraw_bill_id" jdbcType="BIGINT" property="withdrawBillId" />
    <result column="income_amt" jdbcType="DECIMAL" property="incomeAmt" />
    <result column="withdraw_amt" jdbcType="DECIMAL" property="withdrawAmt" />
    <result column="crm_charge_amt" jdbcType="DECIMAL" property="crmChargeAmt" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, settle_key, logdate, traffic_type, app_type, app_id, settle_time, settle_status, 
    settle_reason, withdraw_bill_id, income_amt, withdraw_amt, crm_charge_amt, extra, 
    ctime, mtime, deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iaa_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iaa_settlement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iaa_settlement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPoExample">
    delete from iaa_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo" useGeneratedKeys="true">
    insert into iaa_settlement (settle_key, logdate, traffic_type, 
      app_type, app_id, settle_time, 
      settle_status, settle_reason, withdraw_bill_id, 
      income_amt, withdraw_amt, crm_charge_amt, 
      extra, ctime, mtime, 
      deleted)
    values (#{settleKey,jdbcType=VARCHAR}, #{logdate,jdbcType=VARCHAR}, #{trafficType,jdbcType=VARCHAR}, 
      #{appType,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{settleTime,jdbcType=TIMESTAMP}, 
      #{settleStatus,jdbcType=VARCHAR}, #{settleReason,jdbcType=VARCHAR}, #{withdrawBillId,jdbcType=BIGINT}, 
      #{incomeAmt,jdbcType=DECIMAL}, #{withdrawAmt,jdbcType=DECIMAL}, #{crmChargeAmt,jdbcType=DECIMAL}, 
      #{extra,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo" useGeneratedKeys="true">
    insert into iaa_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="settleKey != null">
        settle_key,
      </if>
      <if test="logdate != null">
        logdate,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
      <if test="appType != null">
        app_type,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="settleTime != null">
        settle_time,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="settleReason != null">
        settle_reason,
      </if>
      <if test="withdrawBillId != null">
        withdraw_bill_id,
      </if>
      <if test="incomeAmt != null">
        income_amt,
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt,
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="settleKey != null">
        #{settleKey,jdbcType=VARCHAR},
      </if>
      <if test="logdate != null">
        #{logdate,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="settleTime != null">
        #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=VARCHAR},
      </if>
      <if test="settleReason != null">
        #{settleReason,jdbcType=VARCHAR},
      </if>
      <if test="withdrawBillId != null">
        #{withdrawBillId,jdbcType=BIGINT},
      </if>
      <if test="incomeAmt != null">
        #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawAmt != null">
        #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPoExample" resultType="java.lang.Long">
    select count(*) from iaa_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iaa_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.settleKey != null">
        settle_key = #{record.settleKey,jdbcType=VARCHAR},
      </if>
      <if test="record.logdate != null">
        logdate = #{record.logdate,jdbcType=VARCHAR},
      </if>
      <if test="record.trafficType != null">
        traffic_type = #{record.trafficType,jdbcType=VARCHAR},
      </if>
      <if test="record.appType != null">
        app_type = #{record.appType,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.settleTime != null">
        settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settleStatus != null">
        settle_status = #{record.settleStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.settleReason != null">
        settle_reason = #{record.settleReason,jdbcType=VARCHAR},
      </if>
      <if test="record.withdrawBillId != null">
        withdraw_bill_id = #{record.withdrawBillId,jdbcType=BIGINT},
      </if>
      <if test="record.incomeAmt != null">
        income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.withdrawAmt != null">
        withdraw_amt = #{record.withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.crmChargeAmt != null">
        crm_charge_amt = #{record.crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iaa_settlement
    set id = #{record.id,jdbcType=BIGINT},
      settle_key = #{record.settleKey,jdbcType=VARCHAR},
      logdate = #{record.logdate,jdbcType=VARCHAR},
      traffic_type = #{record.trafficType,jdbcType=VARCHAR},
      app_type = #{record.appType,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      settle_time = #{record.settleTime,jdbcType=TIMESTAMP},
      settle_status = #{record.settleStatus,jdbcType=VARCHAR},
      settle_reason = #{record.settleReason,jdbcType=VARCHAR},
      withdraw_bill_id = #{record.withdrawBillId,jdbcType=BIGINT},
      income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      withdraw_amt = #{record.withdrawAmt,jdbcType=DECIMAL},
      crm_charge_amt = #{record.crmChargeAmt,jdbcType=DECIMAL},
      extra = #{record.extra,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo">
    update iaa_settlement
    <set>
      <if test="settleKey != null">
        settle_key = #{settleKey,jdbcType=VARCHAR},
      </if>
      <if test="logdate != null">
        logdate = #{logdate,jdbcType=VARCHAR},
      </if>
      <if test="trafficType != null">
        traffic_type = #{trafficType,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="settleTime != null">
        settle_time = #{settleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=VARCHAR},
      </if>
      <if test="settleReason != null">
        settle_reason = #{settleReason,jdbcType=VARCHAR},
      </if>
      <if test="withdrawBillId != null">
        withdraw_bill_id = #{withdrawBillId,jdbcType=BIGINT},
      </if>
      <if test="incomeAmt != null">
        income_amt = #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt = #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt = #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo">
    update iaa_settlement
    set settle_key = #{settleKey,jdbcType=VARCHAR},
      logdate = #{logdate,jdbcType=VARCHAR},
      traffic_type = #{trafficType,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      settle_time = #{settleTime,jdbcType=TIMESTAMP},
      settle_status = #{settleStatus,jdbcType=VARCHAR},
      settle_reason = #{settleReason,jdbcType=VARCHAR},
      withdraw_bill_id = #{withdrawBillId,jdbcType=BIGINT},
      income_amt = #{incomeAmt,jdbcType=DECIMAL},
      withdraw_amt = #{withdrawAmt,jdbcType=DECIMAL},
      crm_charge_amt = #{crmChargeAmt,jdbcType=DECIMAL},
      extra = #{extra,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>