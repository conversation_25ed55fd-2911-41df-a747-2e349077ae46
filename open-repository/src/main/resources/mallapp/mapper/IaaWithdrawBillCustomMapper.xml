<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaWithdrawBillCustomMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="bill_status" jdbcType="VARCHAR" property="billStatus" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="bill_start_time" jdbcType="TIMESTAMP" property="billStartTime" />
    <result column="bill_end_time" jdbcType="TIMESTAMP" property="billEndTime" />
    <result column="withdraw_date" jdbcType="VARCHAR" property="withdrawDate" />
    <result column="latest_settle_time" jdbcType="TIMESTAMP" property="latestSettleTime" />
    <result column="withdraw_apply_time" jdbcType="TIMESTAMP" property="withdrawApplyTime" />
    <result column="withdraw_arrival_time" jdbcType="TIMESTAMP" property="withdrawArrivalTime" />
    <result column="income_amt" jdbcType="DECIMAL" property="incomeAmt" />
    <result column="settle_times" jdbcType="INTEGER" property="settleTimes" />
    <result column="withdraw_amt" jdbcType="DECIMAL" property="withdrawAmt" />
    <result column="crm_charge_amt" jdbcType="DECIMAL" property="crmChargeAmt" />
    <result column="income_natural_part_amt" jdbcType="DECIMAL" property="incomeNaturalPartAmt" />
    <result column="income_business_part_amt" jdbcType="DECIMAL" property="incomeBusinessPartAmt" />
    <result column="withdraw_natural_part_amt" jdbcType="DECIMAL" property="withdrawNaturalPartAmt" />
    <result column="withdraw_business_part_amt" jdbcType="DECIMAL" property="withdrawBusinessPartAmt" />
    <result column="business_entity_name" jdbcType="VARCHAR" property="businessEntityName" />
    <result column="withdraw_apply_amt" jdbcType="DECIMAL" property="withdrawApplyAmt" />
    <result column="invoice_img_url" jdbcType="VARCHAR" property="invoiceImgUrl" />
    <result column="accrual_id" jdbcType="VARCHAR" property="accrualId" />
    <result column="accrual_extra" jdbcType="VARCHAR" property="accrualExtra" />
    <result column="expense_id" jdbcType="VARCHAR" property="expenseId" />
    <result column="expense_extra" jdbcType="VARCHAR" property="expenseExtra" />
    <result column="expense_code" jdbcType="VARCHAR" property="expenseCode" />
    <result column="expense_message" jdbcType="VARCHAR" property="expenseMessage" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, title, bill_status, fail_reason, app_type, app_id, bill_start_time, bill_end_time, 
    withdraw_date, latest_settle_time, withdraw_apply_time, withdraw_arrival_time, income_amt, 
    settle_times, withdraw_amt, crm_charge_amt, income_natural_part_amt, income_business_part_amt, 
    withdraw_natural_part_amt, withdraw_business_part_amt, business_entity_name, withdraw_apply_amt, 
    invoice_img_url, accrual_id, accrual_extra, expense_id, expense_extra, expense_code, 
    expense_message, extra, ctime, mtime, deleted
  </sql>


  <update id="increaseByPrimaryKey"
    parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo">
    update iaa_withdraw_bill
    <set>
      <if test="incomeAmt != null">
        income_amt = income_amt + #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settleTimes != null">
        settle_times = settle_times+ #{settleTimes,jdbcType=INTEGER},
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt = withdraw_amt+  #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt = crm_charge_amt+ #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeNaturalPartAmt != null">
        income_natural_part_amt = income_natural_part_amt+ #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeBusinessPartAmt != null">
        income_business_part_amt = income_business_part_amt+ #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt = withdraw_natural_part_amt+ #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawBusinessPartAmt != null">
        withdraw_business_part_amt = withdraw_business_part_amt +  #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="latestSettleTime != null">
        latest_settle_time = #{latestSettleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>



</mapper>