<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaWithdrawBillMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="bill_status" jdbcType="VARCHAR" property="billStatus" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="bill_start_time" jdbcType="TIMESTAMP" property="billStartTime" />
    <result column="bill_end_time" jdbcType="TIMESTAMP" property="billEndTime" />
    <result column="withdraw_date" jdbcType="VARCHAR" property="withdrawDate" />
    <result column="latest_settle_time" jdbcType="TIMESTAMP" property="latestSettleTime" />
    <result column="withdraw_apply_time" jdbcType="TIMESTAMP" property="withdrawApplyTime" />
    <result column="withdraw_arrival_time" jdbcType="TIMESTAMP" property="withdrawArrivalTime" />
    <result column="income_amt" jdbcType="DECIMAL" property="incomeAmt" />
    <result column="settle_times" jdbcType="INTEGER" property="settleTimes" />
    <result column="withdraw_amt" jdbcType="DECIMAL" property="withdrawAmt" />
    <result column="crm_charge_amt" jdbcType="DECIMAL" property="crmChargeAmt" />
    <result column="income_natural_part_amt" jdbcType="DECIMAL" property="incomeNaturalPartAmt" />
    <result column="income_business_part_amt" jdbcType="DECIMAL" property="incomeBusinessPartAmt" />
    <result column="withdraw_natural_part_amt" jdbcType="DECIMAL" property="withdrawNaturalPartAmt" />
    <result column="withdraw_business_part_amt" jdbcType="DECIMAL" property="withdrawBusinessPartAmt" />
    <result column="business_entity_name" jdbcType="VARCHAR" property="businessEntityName" />
    <result column="withdraw_apply_amt" jdbcType="DECIMAL" property="withdrawApplyAmt" />
    <result column="invoice_img_url" jdbcType="VARCHAR" property="invoiceImgUrl" />
    <result column="accrual_id" jdbcType="VARCHAR" property="accrualId" />
    <result column="accrual_extra" jdbcType="VARCHAR" property="accrualExtra" />
    <result column="expense_id" jdbcType="VARCHAR" property="expenseId" />
    <result column="expense_extra" jdbcType="VARCHAR" property="expenseExtra" />
    <result column="expense_code" jdbcType="VARCHAR" property="expenseCode" />
    <result column="expense_message" jdbcType="VARCHAR" property="expenseMessage" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, title, bill_status, fail_reason, app_type, app_id, bill_start_time, bill_end_time, 
    withdraw_date, latest_settle_time, withdraw_apply_time, withdraw_arrival_time, income_amt, 
    settle_times, withdraw_amt, crm_charge_amt, income_natural_part_amt, income_business_part_amt, 
    withdraw_natural_part_amt, withdraw_business_part_amt, business_entity_name, withdraw_apply_amt, 
    invoice_img_url, accrual_id, accrual_extra, expense_id, expense_extra, expense_code, 
    expense_message, extra, ctime, mtime, deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from iaa_withdraw_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from iaa_withdraw_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from iaa_withdraw_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample">
    delete from iaa_withdraw_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo" useGeneratedKeys="true">
    insert into iaa_withdraw_bill (title, bill_status, fail_reason, 
      app_type, app_id, bill_start_time, 
      bill_end_time, withdraw_date, latest_settle_time, 
      withdraw_apply_time, withdraw_arrival_time, 
      income_amt, settle_times, withdraw_amt, 
      crm_charge_amt, income_natural_part_amt, income_business_part_amt, 
      withdraw_natural_part_amt, withdraw_business_part_amt, 
      business_entity_name, withdraw_apply_amt, invoice_img_url, 
      accrual_id, accrual_extra, expense_id, 
      expense_extra, expense_code, expense_message, 
      extra, ctime, mtime, 
      deleted)
    values (#{title,jdbcType=VARCHAR}, #{billStatus,jdbcType=VARCHAR}, #{failReason,jdbcType=VARCHAR}, 
      #{appType,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{billStartTime,jdbcType=TIMESTAMP}, 
      #{billEndTime,jdbcType=TIMESTAMP}, #{withdrawDate,jdbcType=VARCHAR}, #{latestSettleTime,jdbcType=TIMESTAMP}, 
      #{withdrawApplyTime,jdbcType=TIMESTAMP}, #{withdrawArrivalTime,jdbcType=TIMESTAMP}, 
      #{incomeAmt,jdbcType=DECIMAL}, #{settleTimes,jdbcType=INTEGER}, #{withdrawAmt,jdbcType=DECIMAL}, 
      #{crmChargeAmt,jdbcType=DECIMAL}, #{incomeNaturalPartAmt,jdbcType=DECIMAL}, #{incomeBusinessPartAmt,jdbcType=DECIMAL}, 
      #{withdrawNaturalPartAmt,jdbcType=DECIMAL}, #{withdrawBusinessPartAmt,jdbcType=DECIMAL}, 
      #{businessEntityName,jdbcType=VARCHAR}, #{withdrawApplyAmt,jdbcType=DECIMAL}, #{invoiceImgUrl,jdbcType=VARCHAR}, 
      #{accrualId,jdbcType=VARCHAR}, #{accrualExtra,jdbcType=VARCHAR}, #{expenseId,jdbcType=VARCHAR}, 
      #{expenseExtra,jdbcType=VARCHAR}, #{expenseCode,jdbcType=VARCHAR}, #{expenseMessage,jdbcType=VARCHAR}, 
      #{extra,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo" useGeneratedKeys="true">
    insert into iaa_withdraw_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="title != null">
        title,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="appType != null">
        app_type,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="billStartTime != null">
        bill_start_time,
      </if>
      <if test="billEndTime != null">
        bill_end_time,
      </if>
      <if test="withdrawDate != null">
        withdraw_date,
      </if>
      <if test="latestSettleTime != null">
        latest_settle_time,
      </if>
      <if test="withdrawApplyTime != null">
        withdraw_apply_time,
      </if>
      <if test="withdrawArrivalTime != null">
        withdraw_arrival_time,
      </if>
      <if test="incomeAmt != null">
        income_amt,
      </if>
      <if test="settleTimes != null">
        settle_times,
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt,
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt,
      </if>
      <if test="incomeNaturalPartAmt != null">
        income_natural_part_amt,
      </if>
      <if test="incomeBusinessPartAmt != null">
        income_business_part_amt,
      </if>
      <if test="withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt,
      </if>
      <if test="withdrawBusinessPartAmt != null">
        withdraw_business_part_amt,
      </if>
      <if test="businessEntityName != null">
        business_entity_name,
      </if>
      <if test="withdrawApplyAmt != null">
        withdraw_apply_amt,
      </if>
      <if test="invoiceImgUrl != null">
        invoice_img_url,
      </if>
      <if test="accrualId != null">
        accrual_id,
      </if>
      <if test="accrualExtra != null">
        accrual_extra,
      </if>
      <if test="expenseId != null">
        expense_id,
      </if>
      <if test="expenseExtra != null">
        expense_extra,
      </if>
      <if test="expenseCode != null">
        expense_code,
      </if>
      <if test="expenseMessage != null">
        expense_message,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="billStartTime != null">
        #{billStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billEndTime != null">
        #{billEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="withdrawDate != null">
        #{withdrawDate,jdbcType=VARCHAR},
      </if>
      <if test="latestSettleTime != null">
        #{latestSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="withdrawApplyTime != null">
        #{withdrawApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="withdrawArrivalTime != null">
        #{withdrawArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="incomeAmt != null">
        #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settleTimes != null">
        #{settleTimes,jdbcType=INTEGER},
      </if>
      <if test="withdrawAmt != null">
        #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeNaturalPartAmt != null">
        #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeBusinessPartAmt != null">
        #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawNaturalPartAmt != null">
        #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawBusinessPartAmt != null">
        #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="businessEntityName != null">
        #{businessEntityName,jdbcType=VARCHAR},
      </if>
      <if test="withdrawApplyAmt != null">
        #{withdrawApplyAmt,jdbcType=DECIMAL},
      </if>
      <if test="invoiceImgUrl != null">
        #{invoiceImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="accrualId != null">
        #{accrualId,jdbcType=VARCHAR},
      </if>
      <if test="accrualExtra != null">
        #{accrualExtra,jdbcType=VARCHAR},
      </if>
      <if test="expenseId != null">
        #{expenseId,jdbcType=VARCHAR},
      </if>
      <if test="expenseExtra != null">
        #{expenseExtra,jdbcType=VARCHAR},
      </if>
      <if test="expenseCode != null">
        #{expenseCode,jdbcType=VARCHAR},
      </if>
      <if test="expenseMessage != null">
        #{expenseMessage,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample" resultType="java.lang.Long">
    select count(*) from iaa_withdraw_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update iaa_withdraw_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.billStatus != null">
        bill_status = #{record.billStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.failReason != null">
        fail_reason = #{record.failReason,jdbcType=VARCHAR},
      </if>
      <if test="record.appType != null">
        app_type = #{record.appType,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.billStartTime != null">
        bill_start_time = #{record.billStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.billEndTime != null">
        bill_end_time = #{record.billEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.withdrawDate != null">
        withdraw_date = #{record.withdrawDate,jdbcType=VARCHAR},
      </if>
      <if test="record.latestSettleTime != null">
        latest_settle_time = #{record.latestSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.withdrawApplyTime != null">
        withdraw_apply_time = #{record.withdrawApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.withdrawArrivalTime != null">
        withdraw_arrival_time = #{record.withdrawArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.incomeAmt != null">
        income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.settleTimes != null">
        settle_times = #{record.settleTimes,jdbcType=INTEGER},
      </if>
      <if test="record.withdrawAmt != null">
        withdraw_amt = #{record.withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.crmChargeAmt != null">
        crm_charge_amt = #{record.crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeNaturalPartAmt != null">
        income_natural_part_amt = #{record.incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.incomeBusinessPartAmt != null">
        income_business_part_amt = #{record.incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt = #{record.withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.withdrawBusinessPartAmt != null">
        withdraw_business_part_amt = #{record.withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.businessEntityName != null">
        business_entity_name = #{record.businessEntityName,jdbcType=VARCHAR},
      </if>
      <if test="record.withdrawApplyAmt != null">
        withdraw_apply_amt = #{record.withdrawApplyAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.invoiceImgUrl != null">
        invoice_img_url = #{record.invoiceImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.accrualId != null">
        accrual_id = #{record.accrualId,jdbcType=VARCHAR},
      </if>
      <if test="record.accrualExtra != null">
        accrual_extra = #{record.accrualExtra,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseId != null">
        expense_id = #{record.expenseId,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseExtra != null">
        expense_extra = #{record.expenseExtra,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseCode != null">
        expense_code = #{record.expenseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseMessage != null">
        expense_message = #{record.expenseMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update iaa_withdraw_bill
    set id = #{record.id,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      bill_status = #{record.billStatus,jdbcType=VARCHAR},
      fail_reason = #{record.failReason,jdbcType=VARCHAR},
      app_type = #{record.appType,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      bill_start_time = #{record.billStartTime,jdbcType=TIMESTAMP},
      bill_end_time = #{record.billEndTime,jdbcType=TIMESTAMP},
      withdraw_date = #{record.withdrawDate,jdbcType=VARCHAR},
      latest_settle_time = #{record.latestSettleTime,jdbcType=TIMESTAMP},
      withdraw_apply_time = #{record.withdrawApplyTime,jdbcType=TIMESTAMP},
      withdraw_arrival_time = #{record.withdrawArrivalTime,jdbcType=TIMESTAMP},
      income_amt = #{record.incomeAmt,jdbcType=DECIMAL},
      settle_times = #{record.settleTimes,jdbcType=INTEGER},
      withdraw_amt = #{record.withdrawAmt,jdbcType=DECIMAL},
      crm_charge_amt = #{record.crmChargeAmt,jdbcType=DECIMAL},
      income_natural_part_amt = #{record.incomeNaturalPartAmt,jdbcType=DECIMAL},
      income_business_part_amt = #{record.incomeBusinessPartAmt,jdbcType=DECIMAL},
      withdraw_natural_part_amt = #{record.withdrawNaturalPartAmt,jdbcType=DECIMAL},
      withdraw_business_part_amt = #{record.withdrawBusinessPartAmt,jdbcType=DECIMAL},
      business_entity_name = #{record.businessEntityName,jdbcType=VARCHAR},
      withdraw_apply_amt = #{record.withdrawApplyAmt,jdbcType=DECIMAL},
      invoice_img_url = #{record.invoiceImgUrl,jdbcType=VARCHAR},
      accrual_id = #{record.accrualId,jdbcType=VARCHAR},
      accrual_extra = #{record.accrualExtra,jdbcType=VARCHAR},
      expense_id = #{record.expenseId,jdbcType=VARCHAR},
      expense_extra = #{record.expenseExtra,jdbcType=VARCHAR},
      expense_code = #{record.expenseCode,jdbcType=VARCHAR},
      expense_message = #{record.expenseMessage,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>


  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo">
    update iaa_withdraw_bill
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="billStartTime != null">
        bill_start_time = #{billStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billEndTime != null">
        bill_end_time = #{billEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="withdrawDate != null">
        withdraw_date = #{withdrawDate,jdbcType=VARCHAR},
      </if>
      <if test="latestSettleTime != null">
        latest_settle_time = #{latestSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="withdrawApplyTime != null">
        withdraw_apply_time = #{withdrawApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="withdrawArrivalTime != null">
        withdraw_arrival_time = #{withdrawArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="incomeAmt != null">
        income_amt = #{incomeAmt,jdbcType=DECIMAL},
      </if>
      <if test="settleTimes != null">
        settle_times = #{settleTimes,jdbcType=INTEGER},
      </if>
      <if test="withdrawAmt != null">
        withdraw_amt = #{withdrawAmt,jdbcType=DECIMAL},
      </if>
      <if test="crmChargeAmt != null">
        crm_charge_amt = #{crmChargeAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeNaturalPartAmt != null">
        income_natural_part_amt = #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="incomeBusinessPartAmt != null">
        income_business_part_amt = #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawNaturalPartAmt != null">
        withdraw_natural_part_amt = #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="withdrawBusinessPartAmt != null">
        withdraw_business_part_amt = #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      </if>
      <if test="businessEntityName != null">
        business_entity_name = #{businessEntityName,jdbcType=VARCHAR},
      </if>
      <if test="withdrawApplyAmt != null">
        withdraw_apply_amt = #{withdrawApplyAmt,jdbcType=DECIMAL},
      </if>
      <if test="invoiceImgUrl != null">
        invoice_img_url = #{invoiceImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="accrualId != null">
        accrual_id = #{accrualId,jdbcType=VARCHAR},
      </if>
      <if test="accrualExtra != null">
        accrual_extra = #{accrualExtra,jdbcType=VARCHAR},
      </if>
      <if test="expenseId != null">
        expense_id = #{expenseId,jdbcType=VARCHAR},
      </if>
      <if test="expenseExtra != null">
        expense_extra = #{expenseExtra,jdbcType=VARCHAR},
      </if>
      <if test="expenseCode != null">
        expense_code = #{expenseCode,jdbcType=VARCHAR},
      </if>
      <if test="expenseMessage != null">
        expense_message = #{expenseMessage,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo">
    update iaa_withdraw_bill
    set title = #{title,jdbcType=VARCHAR},
      bill_status = #{billStatus,jdbcType=VARCHAR},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      bill_start_time = #{billStartTime,jdbcType=TIMESTAMP},
      bill_end_time = #{billEndTime,jdbcType=TIMESTAMP},
      withdraw_date = #{withdrawDate,jdbcType=VARCHAR},
      latest_settle_time = #{latestSettleTime,jdbcType=TIMESTAMP},
      withdraw_apply_time = #{withdrawApplyTime,jdbcType=TIMESTAMP},
      withdraw_arrival_time = #{withdrawArrivalTime,jdbcType=TIMESTAMP},
      income_amt = #{incomeAmt,jdbcType=DECIMAL},
      settle_times = #{settleTimes,jdbcType=INTEGER},
      withdraw_amt = #{withdrawAmt,jdbcType=DECIMAL},
      crm_charge_amt = #{crmChargeAmt,jdbcType=DECIMAL},
      income_natural_part_amt = #{incomeNaturalPartAmt,jdbcType=DECIMAL},
      income_business_part_amt = #{incomeBusinessPartAmt,jdbcType=DECIMAL},
      withdraw_natural_part_amt = #{withdrawNaturalPartAmt,jdbcType=DECIMAL},
      withdraw_business_part_amt = #{withdrawBusinessPartAmt,jdbcType=DECIMAL},
      business_entity_name = #{businessEntityName,jdbcType=VARCHAR},
      withdraw_apply_amt = #{withdrawApplyAmt,jdbcType=DECIMAL},
      invoice_img_url = #{invoiceImgUrl,jdbcType=VARCHAR},
      accrual_id = #{accrualId,jdbcType=VARCHAR},
      accrual_extra = #{accrualExtra,jdbcType=VARCHAR},
      expense_id = #{expenseId,jdbcType=VARCHAR},
      expense_extra = #{expenseExtra,jdbcType=VARCHAR},
      expense_code = #{expenseCode,jdbcType=VARCHAR},
      expense_message = #{expenseMessage,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>