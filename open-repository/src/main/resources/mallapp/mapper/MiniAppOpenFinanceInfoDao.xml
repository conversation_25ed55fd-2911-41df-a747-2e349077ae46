<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenFinanceInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="bank_account_number" jdbcType="VARCHAR" property="bankAccountNumber" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_branch_name" jdbcType="VARCHAR" property="bankBranchName" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="tax_type" jdbcType="TINYINT" property="taxType" />
    <result column="invoice_item_category" jdbcType="TINYINT" property="invoiceItemCategory" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_id, bank_account_number, bank_name, bank_branch_name, invoice_type, tax_type, 
    invoice_item_category, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_finance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_finance_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_finance_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPoExample">
    delete from mini_app_open_finance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_finance_info (company_id, bank_account_number, bank_name, 
      bank_branch_name, invoice_type, tax_type, 
      invoice_item_category, is_deleted, ctime, 
      mtime)
    values (#{companyId,jdbcType=VARCHAR}, #{bankAccountNumber,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{bankBranchName,jdbcType=VARCHAR}, #{invoiceType,jdbcType=TINYINT}, #{taxType,jdbcType=TINYINT}, 
      #{invoiceItemCategory,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_finance_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="bankAccountNumber != null">
        bank_account_number,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankBranchName != null">
        bank_branch_name,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="taxType != null">
        tax_type,
      </if>
      <if test="invoiceItemCategory != null">
        invoice_item_category,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNumber != null">
        #{bankAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankBranchName != null">
        #{bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="taxType != null">
        #{taxType,jdbcType=TINYINT},
      </if>
      <if test="invoiceItemCategory != null">
        #{invoiceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_finance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_finance_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNumber != null">
        bank_account_number = #{record.bankAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankBranchName != null">
        bank_branch_name = #{record.bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceType != null">
        invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="record.taxType != null">
        tax_type = #{record.taxType,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceItemCategory != null">
        invoice_item_category = #{record.invoiceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_finance_info
    set id = #{record.id,jdbcType=BIGINT},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      bank_account_number = #{record.bankAccountNumber,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_branch_name = #{record.bankBranchName,jdbcType=VARCHAR},
      invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      tax_type = #{record.taxType,jdbcType=TINYINT},
      invoice_item_category = #{record.invoiceItemCategory,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    update mini_app_open_finance_info
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNumber != null">
        bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankBranchName != null">
        bank_branch_name = #{bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="taxType != null">
        tax_type = #{taxType,jdbcType=TINYINT},
      </if>
      <if test="invoiceItemCategory != null">
        invoice_item_category = #{invoiceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    update mini_app_open_finance_info
    set company_id = #{companyId,jdbcType=VARCHAR},
      bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_branch_name = #{bankBranchName,jdbcType=VARCHAR},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      tax_type = #{taxType,jdbcType=TINYINT},
      invoice_item_category = #{invoiceItemCategory,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_finance_info (company_id, bank_account_number, bank_name, 
      bank_branch_name, invoice_type, tax_type, 
      invoice_item_category, is_deleted, ctime, 
      mtime)
    values (#{companyId,jdbcType=VARCHAR}, #{bankAccountNumber,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{bankBranchName,jdbcType=VARCHAR}, #{invoiceType,jdbcType=TINYINT}, #{taxType,jdbcType=TINYINT}, 
      #{invoiceItemCategory,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      company_id = values(company_id),
      bank_account_number = values(bank_account_number),
      bank_name = values(bank_name),
      bank_branch_name = values(bank_branch_name),
      invoice_type = values(invoice_type),
      tax_type = values(tax_type),
      invoice_item_category = values(invoice_item_category),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_finance_info
      (company_id,bank_account_number,bank_name,bank_branch_name,invoice_type,tax_type,invoice_item_category,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.companyId,jdbcType=VARCHAR},
        #{item.bankAccountNumber,jdbcType=VARCHAR},
        #{item.bankName,jdbcType=VARCHAR},
        #{item.bankBranchName,jdbcType=VARCHAR},
        #{item.invoiceType,jdbcType=TINYINT},
        #{item.taxType,jdbcType=TINYINT},
        #{item.invoiceItemCategory,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_finance_info
      (company_id,bank_account_number,bank_name,bank_branch_name,invoice_type,tax_type,invoice_item_category,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.companyId,jdbcType=VARCHAR},
        #{item.bankAccountNumber,jdbcType=VARCHAR},
        #{item.bankName,jdbcType=VARCHAR},
        #{item.bankBranchName,jdbcType=VARCHAR},
        #{item.invoiceType,jdbcType=TINYINT},
        #{item.taxType,jdbcType=TINYINT},
        #{item.invoiceItemCategory,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      company_id = values(company_id),
      bank_account_number = values(bank_account_number),
      bank_name = values(bank_name),
      bank_branch_name = values(bank_branch_name),
      invoice_type = values(invoice_type),
      tax_type = values(tax_type),
      invoice_item_category = values(invoice_item_category),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_finance_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="bankAccountNumber != null">
        bank_account_number,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankBranchName != null">
        bank_branch_name,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="taxType != null">
        tax_type,
      </if>
      <if test="invoiceItemCategory != null">
        invoice_item_category,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNumber != null">
        #{bankAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankBranchName != null">
        #{bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="taxType != null">
        #{taxType,jdbcType=TINYINT},
      </if>
      <if test="invoiceItemCategory != null">
        #{invoiceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="companyId != null">
        company_id = values(company_id),
      </if>
      <if test="bankAccountNumber != null">
        bank_account_number = values(bank_account_number),
      </if>
      <if test="bankName != null">
        bank_name = values(bank_name),
      </if>
      <if test="bankBranchName != null">
        bank_branch_name = values(bank_branch_name),
      </if>
      <if test="invoiceType != null">
        invoice_type = values(invoice_type),
      </if>
      <if test="taxType != null">
        tax_type = values(tax_type),
      </if>
      <if test="invoiceItemCategory != null">
        invoice_item_category = values(invoice_item_category),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>