<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpAppDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_id" jdbcType="BIGINT" property="flowId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="service_type" jdbcType="INTEGER" property="serviceType" />
    <result column="approval_pre" jdbcType="INTEGER" property="approvalPre" />
    <result column="approval_type" jdbcType="INTEGER" property="approvalType" />
    <result column="approval_isbn_no" jdbcType="VARCHAR" property="approvalIsbnNo" />
    <result column="approval_attachment" jdbcType="VARCHAR" property="approvalAttachment" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="fzr_license_type" jdbcType="INTEGER" property="fzrLicenseType" />
    <result column="fzr_card_no" jdbcType="VARCHAR" property="fzrCardNo" />
    <result column="fzr_name" jdbcType="VARCHAR" property="fzrName" />
    <result column="fzr_card_begin" jdbcType="VARCHAR" property="fzrCardBegin" />
    <result column="fzr_card_end" jdbcType="VARCHAR" property="fzrCardEnd" />
    <result column="fzr_card_long_effect" jdbcType="TINYINT" property="fzrCardLongEffect" />
    <result column="fzr_phone" jdbcType="VARCHAR" property="fzrPhone" />
    <result column="fzr_email" jdbcType="VARCHAR" property="fzrEmail" />
    <result column="fzr_emergency" jdbcType="VARCHAR" property="fzrEmergency" />
    <result column="id_verification_status" jdbcType="INTEGER" property="idVerificationStatus" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="fzr_card" jdbcType="VARCHAR" property="fzrCard" />
    <result column="verify_photo" jdbcType="VARCHAR" property="verifyPhoto" />
    <result column="wzid" jdbcType="BIGINT" property="wzid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, flow_id, name, app_id, service_type, approval_pre, approval_type, approval_isbn_no, 
    approval_attachment, remark, fzr_license_type, fzr_card_no, fzr_name, fzr_card_begin, 
    fzr_card_end, fzr_card_long_effect, fzr_phone, fzr_email, fzr_emergency, id_verification_status, 
    ctime, mtime, is_deleted, fzr_card, verify_photo, wzid
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_icp_app
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_icp_app
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_icp_app
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPoExample">
    delete from mini_app_open_icp_app
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_app (flow_id, name, app_id, 
      service_type, approval_pre, approval_type, 
      approval_isbn_no, approval_attachment, remark, 
      fzr_license_type, fzr_card_no, fzr_name, 
      fzr_card_begin, fzr_card_end, fzr_card_long_effect, 
      fzr_phone, fzr_email, fzr_emergency, 
      id_verification_status, ctime, mtime, 
      is_deleted, fzr_card, verify_photo, 
      wzid)
    values (#{flowId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{serviceType,jdbcType=INTEGER}, #{approvalPre,jdbcType=INTEGER}, #{approvalType,jdbcType=INTEGER}, 
      #{approvalIsbnNo,jdbcType=VARCHAR}, #{approvalAttachment,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{fzrLicenseType,jdbcType=INTEGER}, #{fzrCardNo,jdbcType=VARCHAR}, #{fzrName,jdbcType=VARCHAR}, 
      #{fzrCardBegin,jdbcType=VARCHAR}, #{fzrCardEnd,jdbcType=VARCHAR}, #{fzrCardLongEffect,jdbcType=TINYINT}, 
      #{fzrPhone,jdbcType=VARCHAR}, #{fzrEmail,jdbcType=VARCHAR}, #{fzrEmergency,jdbcType=VARCHAR}, 
      #{idVerificationStatus,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{fzrCard,jdbcType=VARCHAR}, #{verifyPhoto,jdbcType=VARCHAR}, 
      #{wzid,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_app
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="approvalPre != null">
        approval_pre,
      </if>
      <if test="approvalType != null">
        approval_type,
      </if>
      <if test="approvalIsbnNo != null">
        approval_isbn_no,
      </if>
      <if test="approvalAttachment != null">
        approval_attachment,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type,
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no,
      </if>
      <if test="fzrName != null">
        fzr_name,
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin,
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end,
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect,
      </if>
      <if test="fzrPhone != null">
        fzr_phone,
      </if>
      <if test="fzrEmail != null">
        fzr_email,
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency,
      </if>
      <if test="idVerificationStatus != null">
        id_verification_status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="fzrCard != null">
        fzr_card,
      </if>
      <if test="verifyPhoto != null">
        verify_photo,
      </if>
      <if test="wzid != null">
        wzid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        #{flowId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="approvalPre != null">
        #{approvalPre,jdbcType=INTEGER},
      </if>
      <if test="approvalType != null">
        #{approvalType,jdbcType=INTEGER},
      </if>
      <if test="approvalIsbnNo != null">
        #{approvalIsbnNo,jdbcType=VARCHAR},
      </if>
      <if test="approvalAttachment != null">
        #{approvalAttachment,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fzrLicenseType != null">
        #{fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="fzrCardNo != null">
        #{fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="fzrName != null">
        #{fzrName,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardBegin != null">
        #{fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardEnd != null">
        #{fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardLongEffect != null">
        #{fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="fzrPhone != null">
        #{fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmail != null">
        #{fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmergency != null">
        #{fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="idVerificationStatus != null">
        #{idVerificationStatus,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="fzrCard != null">
        #{fzrCard,jdbcType=VARCHAR},
      </if>
      <if test="verifyPhoto != null">
        #{verifyPhoto,jdbcType=VARCHAR},
      </if>
      <if test="wzid != null">
        #{wzid,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_icp_app
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_icp_app
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=INTEGER},
      </if>
      <if test="record.approvalPre != null">
        approval_pre = #{record.approvalPre,jdbcType=INTEGER},
      </if>
      <if test="record.approvalType != null">
        approval_type = #{record.approvalType,jdbcType=INTEGER},
      </if>
      <if test="record.approvalIsbnNo != null">
        approval_isbn_no = #{record.approvalIsbnNo,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalAttachment != null">
        approval_attachment = #{record.approvalAttachment,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrLicenseType != null">
        fzr_license_type = #{record.fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="record.fzrCardNo != null">
        fzr_card_no = #{record.fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrName != null">
        fzr_name = #{record.fzrName,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardBegin != null">
        fzr_card_begin = #{record.fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardEnd != null">
        fzr_card_end = #{record.fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardLongEffect != null">
        fzr_card_long_effect = #{record.fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="record.fzrPhone != null">
        fzr_phone = #{record.fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrEmail != null">
        fzr_email = #{record.fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrEmergency != null">
        fzr_emergency = #{record.fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="record.idVerificationStatus != null">
        id_verification_status = #{record.idVerificationStatus,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.fzrCard != null">
        fzr_card = #{record.fzrCard,jdbcType=VARCHAR},
      </if>
      <if test="record.verifyPhoto != null">
        verify_photo = #{record.verifyPhoto,jdbcType=VARCHAR},
      </if>
      <if test="record.wzid != null">
        wzid = #{record.wzid,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_icp_app
    set id = #{record.id,jdbcType=BIGINT},
      flow_id = #{record.flowId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      service_type = #{record.serviceType,jdbcType=INTEGER},
      approval_pre = #{record.approvalPre,jdbcType=INTEGER},
      approval_type = #{record.approvalType,jdbcType=INTEGER},
      approval_isbn_no = #{record.approvalIsbnNo,jdbcType=VARCHAR},
      approval_attachment = #{record.approvalAttachment,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      fzr_license_type = #{record.fzrLicenseType,jdbcType=INTEGER},
      fzr_card_no = #{record.fzrCardNo,jdbcType=VARCHAR},
      fzr_name = #{record.fzrName,jdbcType=VARCHAR},
      fzr_card_begin = #{record.fzrCardBegin,jdbcType=VARCHAR},
      fzr_card_end = #{record.fzrCardEnd,jdbcType=VARCHAR},
      fzr_card_long_effect = #{record.fzrCardLongEffect,jdbcType=TINYINT},
      fzr_phone = #{record.fzrPhone,jdbcType=VARCHAR},
      fzr_email = #{record.fzrEmail,jdbcType=VARCHAR},
      fzr_emergency = #{record.fzrEmergency,jdbcType=VARCHAR},
      id_verification_status = #{record.idVerificationStatus,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      fzr_card = #{record.fzrCard,jdbcType=VARCHAR},
      verify_photo = #{record.verifyPhoto,jdbcType=VARCHAR},
      wzid = #{record.wzid,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    update mini_app_open_icp_app
    <set>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="approvalPre != null">
        approval_pre = #{approvalPre,jdbcType=INTEGER},
      </if>
      <if test="approvalType != null">
        approval_type = #{approvalType,jdbcType=INTEGER},
      </if>
      <if test="approvalIsbnNo != null">
        approval_isbn_no = #{approvalIsbnNo,jdbcType=VARCHAR},
      </if>
      <if test="approvalAttachment != null">
        approval_attachment = #{approvalAttachment,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type = #{fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no = #{fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="fzrName != null">
        fzr_name = #{fzrName,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin = #{fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end = #{fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect = #{fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="fzrPhone != null">
        fzr_phone = #{fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmail != null">
        fzr_email = #{fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency = #{fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="idVerificationStatus != null">
        id_verification_status = #{idVerificationStatus,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="fzrCard != null">
        fzr_card = #{fzrCard,jdbcType=VARCHAR},
      </if>
      <if test="verifyPhoto != null">
        verify_photo = #{verifyPhoto,jdbcType=VARCHAR},
      </if>
      <if test="wzid != null">
        wzid = #{wzid,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    update mini_app_open_icp_app
    set flow_id = #{flowId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=INTEGER},
      approval_pre = #{approvalPre,jdbcType=INTEGER},
      approval_type = #{approvalType,jdbcType=INTEGER},
      approval_isbn_no = #{approvalIsbnNo,jdbcType=VARCHAR},
      approval_attachment = #{approvalAttachment,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      fzr_license_type = #{fzrLicenseType,jdbcType=INTEGER},
      fzr_card_no = #{fzrCardNo,jdbcType=VARCHAR},
      fzr_name = #{fzrName,jdbcType=VARCHAR},
      fzr_card_begin = #{fzrCardBegin,jdbcType=VARCHAR},
      fzr_card_end = #{fzrCardEnd,jdbcType=VARCHAR},
      fzr_card_long_effect = #{fzrCardLongEffect,jdbcType=TINYINT},
      fzr_phone = #{fzrPhone,jdbcType=VARCHAR},
      fzr_email = #{fzrEmail,jdbcType=VARCHAR},
      fzr_emergency = #{fzrEmergency,jdbcType=VARCHAR},
      id_verification_status = #{idVerificationStatus,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      fzr_card = #{fzrCard,jdbcType=VARCHAR},
      verify_photo = #{verifyPhoto,jdbcType=VARCHAR},
      wzid = #{wzid,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_app (flow_id, name, app_id, 
      service_type, approval_pre, approval_type, 
      approval_isbn_no, approval_attachment, remark, 
      fzr_license_type, fzr_card_no, fzr_name, 
      fzr_card_begin, fzr_card_end, fzr_card_long_effect, 
      fzr_phone, fzr_email, fzr_emergency, 
      id_verification_status, ctime, mtime, 
      is_deleted, fzr_card, verify_photo, 
      wzid)
    values (#{flowId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{serviceType,jdbcType=INTEGER}, #{approvalPre,jdbcType=INTEGER}, #{approvalType,jdbcType=INTEGER}, 
      #{approvalIsbnNo,jdbcType=VARCHAR}, #{approvalAttachment,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{fzrLicenseType,jdbcType=INTEGER}, #{fzrCardNo,jdbcType=VARCHAR}, #{fzrName,jdbcType=VARCHAR}, 
      #{fzrCardBegin,jdbcType=VARCHAR}, #{fzrCardEnd,jdbcType=VARCHAR}, #{fzrCardLongEffect,jdbcType=TINYINT}, 
      #{fzrPhone,jdbcType=VARCHAR}, #{fzrEmail,jdbcType=VARCHAR}, #{fzrEmergency,jdbcType=VARCHAR}, 
      #{idVerificationStatus,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{fzrCard,jdbcType=VARCHAR}, #{verifyPhoto,jdbcType=VARCHAR}, 
      #{wzid,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      flow_id = values(flow_id),
      name = values(name),
      app_id = values(app_id),
      service_type = values(service_type),
      approval_pre = values(approval_pre),
      approval_type = values(approval_type),
      approval_isbn_no = values(approval_isbn_no),
      approval_attachment = values(approval_attachment),
      remark = values(remark),
      fzr_license_type = values(fzr_license_type),
      fzr_card_no = values(fzr_card_no),
      fzr_name = values(fzr_name),
      fzr_card_begin = values(fzr_card_begin),
      fzr_card_end = values(fzr_card_end),
      fzr_card_long_effect = values(fzr_card_long_effect),
      fzr_phone = values(fzr_phone),
      fzr_email = values(fzr_email),
      fzr_emergency = values(fzr_emergency),
      id_verification_status = values(id_verification_status),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      fzr_card = values(fzr_card),
      verify_photo = values(verify_photo),
      wzid = values(wzid),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_app
      (flow_id,name,app_id,service_type,approval_pre,approval_type,approval_isbn_no,approval_attachment,remark,fzr_license_type,fzr_card_no,fzr_name,fzr_card_begin,fzr_card_end,fzr_card_long_effect,fzr_phone,fzr_email,fzr_emergency,id_verification_status,ctime,mtime,is_deleted,fzr_card,verify_photo,wzid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.flowId,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.appId,jdbcType=VARCHAR},
        #{item.serviceType,jdbcType=INTEGER},
        #{item.approvalPre,jdbcType=INTEGER},
        #{item.approvalType,jdbcType=INTEGER},
        #{item.approvalIsbnNo,jdbcType=VARCHAR},
        #{item.approvalAttachment,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.fzrLicenseType,jdbcType=INTEGER},
        #{item.fzrCardNo,jdbcType=VARCHAR},
        #{item.fzrName,jdbcType=VARCHAR},
        #{item.fzrCardBegin,jdbcType=VARCHAR},
        #{item.fzrCardEnd,jdbcType=VARCHAR},
        #{item.fzrCardLongEffect,jdbcType=TINYINT},
        #{item.fzrPhone,jdbcType=VARCHAR},
        #{item.fzrEmail,jdbcType=VARCHAR},
        #{item.fzrEmergency,jdbcType=VARCHAR},
        #{item.idVerificationStatus,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.fzrCard,jdbcType=VARCHAR},
        #{item.verifyPhoto,jdbcType=VARCHAR},
        #{item.wzid,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_app
      (flow_id,name,app_id,service_type,approval_pre,approval_type,approval_isbn_no,approval_attachment,remark,fzr_license_type,fzr_card_no,fzr_name,fzr_card_begin,fzr_card_end,fzr_card_long_effect,fzr_phone,fzr_email,fzr_emergency,id_verification_status,ctime,mtime,is_deleted,fzr_card,verify_photo,wzid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.flowId,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.appId,jdbcType=VARCHAR},
        #{item.serviceType,jdbcType=INTEGER},
        #{item.approvalPre,jdbcType=INTEGER},
        #{item.approvalType,jdbcType=INTEGER},
        #{item.approvalIsbnNo,jdbcType=VARCHAR},
        #{item.approvalAttachment,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.fzrLicenseType,jdbcType=INTEGER},
        #{item.fzrCardNo,jdbcType=VARCHAR},
        #{item.fzrName,jdbcType=VARCHAR},
        #{item.fzrCardBegin,jdbcType=VARCHAR},
        #{item.fzrCardEnd,jdbcType=VARCHAR},
        #{item.fzrCardLongEffect,jdbcType=TINYINT},
        #{item.fzrPhone,jdbcType=VARCHAR},
        #{item.fzrEmail,jdbcType=VARCHAR},
        #{item.fzrEmergency,jdbcType=VARCHAR},
        #{item.idVerificationStatus,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.fzrCard,jdbcType=VARCHAR},
        #{item.verifyPhoto,jdbcType=VARCHAR},
        #{item.wzid,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      flow_id = values(flow_id),
      name = values(name),
      app_id = values(app_id),
      service_type = values(service_type),
      approval_pre = values(approval_pre),
      approval_type = values(approval_type),
      approval_isbn_no = values(approval_isbn_no),
      approval_attachment = values(approval_attachment),
      remark = values(remark),
      fzr_license_type = values(fzr_license_type),
      fzr_card_no = values(fzr_card_no),
      fzr_name = values(fzr_name),
      fzr_card_begin = values(fzr_card_begin),
      fzr_card_end = values(fzr_card_end),
      fzr_card_long_effect = values(fzr_card_long_effect),
      fzr_phone = values(fzr_phone),
      fzr_email = values(fzr_email),
      fzr_emergency = values(fzr_emergency),
      id_verification_status = values(id_verification_status),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      fzr_card = values(fzr_card),
      verify_photo = values(verify_photo),
      wzid = values(wzid),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAppPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_app
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="approvalPre != null">
        approval_pre,
      </if>
      <if test="approvalType != null">
        approval_type,
      </if>
      <if test="approvalIsbnNo != null">
        approval_isbn_no,
      </if>
      <if test="approvalAttachment != null">
        approval_attachment,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type,
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no,
      </if>
      <if test="fzrName != null">
        fzr_name,
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin,
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end,
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect,
      </if>
      <if test="fzrPhone != null">
        fzr_phone,
      </if>
      <if test="fzrEmail != null">
        fzr_email,
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency,
      </if>
      <if test="idVerificationStatus != null">
        id_verification_status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="fzrCard != null">
        fzr_card,
      </if>
      <if test="verifyPhoto != null">
        verify_photo,
      </if>
      <if test="wzid != null">
        wzid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        #{flowId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=INTEGER},
      </if>
      <if test="approvalPre != null">
        #{approvalPre,jdbcType=INTEGER},
      </if>
      <if test="approvalType != null">
        #{approvalType,jdbcType=INTEGER},
      </if>
      <if test="approvalIsbnNo != null">
        #{approvalIsbnNo,jdbcType=VARCHAR},
      </if>
      <if test="approvalAttachment != null">
        #{approvalAttachment,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fzrLicenseType != null">
        #{fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="fzrCardNo != null">
        #{fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="fzrName != null">
        #{fzrName,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardBegin != null">
        #{fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardEnd != null">
        #{fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardLongEffect != null">
        #{fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="fzrPhone != null">
        #{fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmail != null">
        #{fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmergency != null">
        #{fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="idVerificationStatus != null">
        #{idVerificationStatus,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="fzrCard != null">
        #{fzrCard,jdbcType=VARCHAR},
      </if>
      <if test="verifyPhoto != null">
        #{verifyPhoto,jdbcType=VARCHAR},
      </if>
      <if test="wzid != null">
        #{wzid,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="flowId != null">
        flow_id = values(flow_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="serviceType != null">
        service_type = values(service_type),
      </if>
      <if test="approvalPre != null">
        approval_pre = values(approval_pre),
      </if>
      <if test="approvalType != null">
        approval_type = values(approval_type),
      </if>
      <if test="approvalIsbnNo != null">
        approval_isbn_no = values(approval_isbn_no),
      </if>
      <if test="approvalAttachment != null">
        approval_attachment = values(approval_attachment),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type = values(fzr_license_type),
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no = values(fzr_card_no),
      </if>
      <if test="fzrName != null">
        fzr_name = values(fzr_name),
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin = values(fzr_card_begin),
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end = values(fzr_card_end),
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect = values(fzr_card_long_effect),
      </if>
      <if test="fzrPhone != null">
        fzr_phone = values(fzr_phone),
      </if>
      <if test="fzrEmail != null">
        fzr_email = values(fzr_email),
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency = values(fzr_emergency),
      </if>
      <if test="idVerificationStatus != null">
        id_verification_status = values(id_verification_status),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="fzrCard != null">
        fzr_card = values(fzr_card),
      </if>
      <if test="verifyPhoto != null">
        verify_photo = values(verify_photo),
      </if>
      <if test="wzid != null">
        wzid = values(wzid),
      </if>
    </trim>
  </insert>
</mapper>