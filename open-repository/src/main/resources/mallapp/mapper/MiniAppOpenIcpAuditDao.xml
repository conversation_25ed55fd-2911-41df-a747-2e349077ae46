<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpAuditDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_id" jdbcType="BIGINT" property="flowId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="audit_info" jdbcType="VARCHAR" property="auditInfo" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="audit_time" jdbcType="VARCHAR" property="auditTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, flow_id, app_id, audit_info, audit_result, audit_status, operator, audit_time, 
    ctime, mtime, is_deleted, submit_time
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_icp_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_icp_audit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_icp_audit
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPoExample">
    delete from mini_app_open_icp_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_audit (flow_id, app_id, audit_info, 
      audit_result, audit_status, operator, 
      audit_time, ctime, mtime, 
      is_deleted, submit_time)
    values (#{flowId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{auditInfo,jdbcType=VARCHAR}, 
      #{auditResult,jdbcType=VARCHAR}, #{auditStatus,jdbcType=TINYINT}, #{operator,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{submitTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="auditInfo != null">
        audit_info,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        #{flowId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="auditInfo != null">
        #{auditInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_icp_audit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_icp_audit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.auditInfo != null">
        audit_info = #{record.auditInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResult != null">
        audit_result = #{record.auditResult,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.auditTime != null">
        audit_time = #{record.auditTime,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_icp_audit
    set id = #{record.id,jdbcType=BIGINT},
      flow_id = #{record.flowId,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      audit_info = #{record.auditInfo,jdbcType=VARCHAR},
      audit_result = #{record.auditResult,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      operator = #{record.operator,jdbcType=VARCHAR},
      audit_time = #{record.auditTime,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    update mini_app_open_icp_audit
    <set>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="auditInfo != null">
        audit_info = #{auditInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        audit_result = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    update mini_app_open_icp_audit
    set flow_id = #{flowId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      audit_info = #{auditInfo,jdbcType=VARCHAR},
      audit_result = #{auditResult,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      operator = #{operator,jdbcType=VARCHAR},
      audit_time = #{auditTime,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      submit_time = #{submitTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_audit (flow_id, app_id, audit_info, 
      audit_result, audit_status, operator, 
      audit_time, ctime, mtime, 
      is_deleted, submit_time)
    values (#{flowId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{auditInfo,jdbcType=VARCHAR}, 
      #{auditResult,jdbcType=VARCHAR}, #{auditStatus,jdbcType=TINYINT}, #{operator,jdbcType=VARCHAR}, 
      #{auditTime,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{submitTime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      flow_id = values(flow_id),
      app_id = values(app_id),
      audit_info = values(audit_info),
      audit_result = values(audit_result),
      audit_status = values(audit_status),
      operator = values(operator),
      audit_time = values(audit_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      submit_time = values(submit_time),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_audit
      (flow_id,app_id,audit_info,audit_result,audit_status,operator,audit_time,ctime,mtime,is_deleted,submit_time)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.flowId,jdbcType=BIGINT},
        #{item.appId,jdbcType=VARCHAR},
        #{item.auditInfo,jdbcType=VARCHAR},
        #{item.auditResult,jdbcType=VARCHAR},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.operator,jdbcType=VARCHAR},
        #{item.auditTime,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.submitTime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_audit
      (flow_id,app_id,audit_info,audit_result,audit_status,operator,audit_time,ctime,mtime,is_deleted,submit_time)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.flowId,jdbcType=BIGINT},
        #{item.appId,jdbcType=VARCHAR},
        #{item.auditInfo,jdbcType=VARCHAR},
        #{item.auditResult,jdbcType=VARCHAR},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.operator,jdbcType=VARCHAR},
        #{item.auditTime,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.submitTime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      flow_id = values(flow_id),
      app_id = values(app_id),
      audit_info = values(audit_info),
      audit_result = values(audit_result),
      audit_status = values(audit_status),
      operator = values(operator),
      audit_time = values(audit_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      submit_time = values(submit_time),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpAuditPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_audit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="auditInfo != null">
        audit_info,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        #{flowId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="auditInfo != null">
        #{auditInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="flowId != null">
        flow_id = values(flow_id),
      </if>
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="auditInfo != null">
        audit_info = values(audit_info),
      </if>
      <if test="auditResult != null">
        audit_result = values(audit_result),
      </if>
      <if test="auditStatus != null">
        audit_status = values(audit_status),
      </if>
      <if test="operator != null">
        operator = values(operator),
      </if>
      <if test="auditTime != null">
        audit_time = values(audit_time),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="submitTime != null">
        submit_time = values(submit_time),
      </if>
    </trim>
  </insert>
</mapper>