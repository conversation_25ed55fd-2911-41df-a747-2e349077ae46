<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpCompanyDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_id" jdbcType="BIGINT" property="flowId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="license_type" jdbcType="INTEGER" property="licenseType" />
    <result column="license_photo" jdbcType="VARCHAR" property="licensePhoto" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="license_province" jdbcType="INTEGER" property="licenseProvince" />
    <result column="license_city" jdbcType="INTEGER" property="licenseCity" />
    <result column="license_county" jdbcType="INTEGER" property="licenseCounty" />
    <result column="license_detail_address" jdbcType="VARCHAR" property="licenseDetailAddress" />
    <result column="contact_province" jdbcType="INTEGER" property="contactProvince" />
    <result column="contact_city" jdbcType="INTEGER" property="contactCity" />
    <result column="contact_county" jdbcType="INTEGER" property="contactCounty" />
    <result column="contact_detail_address" jdbcType="VARCHAR" property="contactDetailAddress" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="fzr_license_type" jdbcType="INTEGER" property="fzrLicenseType" />
    <result column="fzr_card_no" jdbcType="VARCHAR" property="fzrCardNo" />
    <result column="fzr_name" jdbcType="VARCHAR" property="fzrName" />
    <result column="fzr_card_front" jdbcType="VARCHAR" property="fzrCardFront" />
    <result column="fzr_card_reverse" jdbcType="VARCHAR" property="fzrCardReverse" />
    <result column="fzr_card_begin" jdbcType="VARCHAR" property="fzrCardBegin" />
    <result column="fzr_card_end" jdbcType="VARCHAR" property="fzrCardEnd" />
    <result column="fzr_card_long_effect" jdbcType="TINYINT" property="fzrCardLongEffect" />
    <result column="fzr_phone" jdbcType="VARCHAR" property="fzrPhone" />
    <result column="fzr_email" jdbcType="VARCHAR" property="fzrEmail" />
    <result column="fzr_emergency" jdbcType="VARCHAR" property="fzrEmergency" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, flow_id, type, license_type, license_photo, name, license_no, license_province, 
    license_city, license_county, license_detail_address, contact_province, contact_city, 
    contact_county, contact_detail_address, remark, fzr_license_type, fzr_card_no, fzr_name, 
    fzr_card_front, fzr_card_reverse, fzr_card_begin, fzr_card_end, fzr_card_long_effect, 
    fzr_phone, fzr_email, fzr_emergency, ctime, mtime, is_deleted, company_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_icp_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_icp_company
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_icp_company
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPoExample">
    delete from mini_app_open_icp_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_company (flow_id, type, license_type, 
      license_photo, name, license_no, 
      license_province, license_city, license_county, 
      license_detail_address, contact_province, 
      contact_city, contact_county, contact_detail_address, 
      remark, fzr_license_type, fzr_card_no, 
      fzr_name, fzr_card_front, fzr_card_reverse, 
      fzr_card_begin, fzr_card_end, fzr_card_long_effect, 
      fzr_phone, fzr_email, fzr_emergency, 
      ctime, mtime, is_deleted, 
      company_id)
    values (#{flowId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{licenseType,jdbcType=INTEGER}, 
      #{licensePhoto,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{licenseNo,jdbcType=VARCHAR}, 
      #{licenseProvince,jdbcType=INTEGER}, #{licenseCity,jdbcType=INTEGER}, #{licenseCounty,jdbcType=INTEGER}, 
      #{licenseDetailAddress,jdbcType=VARCHAR}, #{contactProvince,jdbcType=INTEGER}, 
      #{contactCity,jdbcType=INTEGER}, #{contactCounty,jdbcType=INTEGER}, #{contactDetailAddress,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{fzrLicenseType,jdbcType=INTEGER}, #{fzrCardNo,jdbcType=VARCHAR}, 
      #{fzrName,jdbcType=VARCHAR}, #{fzrCardFront,jdbcType=VARCHAR}, #{fzrCardReverse,jdbcType=VARCHAR}, 
      #{fzrCardBegin,jdbcType=VARCHAR}, #{fzrCardEnd,jdbcType=VARCHAR}, #{fzrCardLongEffect,jdbcType=TINYINT}, 
      #{fzrPhone,jdbcType=VARCHAR}, #{fzrEmail,jdbcType=VARCHAR}, #{fzrEmergency,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{companyId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="licenseType != null">
        license_type,
      </if>
      <if test="licensePhoto != null">
        license_photo,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="licenseProvince != null">
        license_province,
      </if>
      <if test="licenseCity != null">
        license_city,
      </if>
      <if test="licenseCounty != null">
        license_county,
      </if>
      <if test="licenseDetailAddress != null">
        license_detail_address,
      </if>
      <if test="contactProvince != null">
        contact_province,
      </if>
      <if test="contactCity != null">
        contact_city,
      </if>
      <if test="contactCounty != null">
        contact_county,
      </if>
      <if test="contactDetailAddress != null">
        contact_detail_address,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type,
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no,
      </if>
      <if test="fzrName != null">
        fzr_name,
      </if>
      <if test="fzrCardFront != null">
        fzr_card_front,
      </if>
      <if test="fzrCardReverse != null">
        fzr_card_reverse,
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin,
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end,
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect,
      </if>
      <if test="fzrPhone != null">
        fzr_phone,
      </if>
      <if test="fzrEmail != null">
        fzr_email,
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        #{flowId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="licenseType != null">
        #{licenseType,jdbcType=INTEGER},
      </if>
      <if test="licensePhoto != null">
        #{licensePhoto,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="licenseProvince != null">
        #{licenseProvince,jdbcType=INTEGER},
      </if>
      <if test="licenseCity != null">
        #{licenseCity,jdbcType=INTEGER},
      </if>
      <if test="licenseCounty != null">
        #{licenseCounty,jdbcType=INTEGER},
      </if>
      <if test="licenseDetailAddress != null">
        #{licenseDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="contactProvince != null">
        #{contactProvince,jdbcType=INTEGER},
      </if>
      <if test="contactCity != null">
        #{contactCity,jdbcType=INTEGER},
      </if>
      <if test="contactCounty != null">
        #{contactCounty,jdbcType=INTEGER},
      </if>
      <if test="contactDetailAddress != null">
        #{contactDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fzrLicenseType != null">
        #{fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="fzrCardNo != null">
        #{fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="fzrName != null">
        #{fzrName,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardFront != null">
        #{fzrCardFront,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardReverse != null">
        #{fzrCardReverse,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardBegin != null">
        #{fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardEnd != null">
        #{fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardLongEffect != null">
        #{fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="fzrPhone != null">
        #{fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmail != null">
        #{fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmergency != null">
        #{fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_icp_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_icp_company
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.licenseType != null">
        license_type = #{record.licenseType,jdbcType=INTEGER},
      </if>
      <if test="record.licensePhoto != null">
        license_photo = #{record.licensePhoto,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseNo != null">
        license_no = #{record.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseProvince != null">
        license_province = #{record.licenseProvince,jdbcType=INTEGER},
      </if>
      <if test="record.licenseCity != null">
        license_city = #{record.licenseCity,jdbcType=INTEGER},
      </if>
      <if test="record.licenseCounty != null">
        license_county = #{record.licenseCounty,jdbcType=INTEGER},
      </if>
      <if test="record.licenseDetailAddress != null">
        license_detail_address = #{record.licenseDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.contactProvince != null">
        contact_province = #{record.contactProvince,jdbcType=INTEGER},
      </if>
      <if test="record.contactCity != null">
        contact_city = #{record.contactCity,jdbcType=INTEGER},
      </if>
      <if test="record.contactCounty != null">
        contact_county = #{record.contactCounty,jdbcType=INTEGER},
      </if>
      <if test="record.contactDetailAddress != null">
        contact_detail_address = #{record.contactDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrLicenseType != null">
        fzr_license_type = #{record.fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="record.fzrCardNo != null">
        fzr_card_no = #{record.fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrName != null">
        fzr_name = #{record.fzrName,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardFront != null">
        fzr_card_front = #{record.fzrCardFront,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardReverse != null">
        fzr_card_reverse = #{record.fzrCardReverse,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardBegin != null">
        fzr_card_begin = #{record.fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardEnd != null">
        fzr_card_end = #{record.fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrCardLongEffect != null">
        fzr_card_long_effect = #{record.fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="record.fzrPhone != null">
        fzr_phone = #{record.fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrEmail != null">
        fzr_email = #{record.fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrEmergency != null">
        fzr_emergency = #{record.fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_icp_company
    set id = #{record.id,jdbcType=BIGINT},
      flow_id = #{record.flowId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      license_type = #{record.licenseType,jdbcType=INTEGER},
      license_photo = #{record.licensePhoto,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      license_no = #{record.licenseNo,jdbcType=VARCHAR},
      license_province = #{record.licenseProvince,jdbcType=INTEGER},
      license_city = #{record.licenseCity,jdbcType=INTEGER},
      license_county = #{record.licenseCounty,jdbcType=INTEGER},
      license_detail_address = #{record.licenseDetailAddress,jdbcType=VARCHAR},
      contact_province = #{record.contactProvince,jdbcType=INTEGER},
      contact_city = #{record.contactCity,jdbcType=INTEGER},
      contact_county = #{record.contactCounty,jdbcType=INTEGER},
      contact_detail_address = #{record.contactDetailAddress,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      fzr_license_type = #{record.fzrLicenseType,jdbcType=INTEGER},
      fzr_card_no = #{record.fzrCardNo,jdbcType=VARCHAR},
      fzr_name = #{record.fzrName,jdbcType=VARCHAR},
      fzr_card_front = #{record.fzrCardFront,jdbcType=VARCHAR},
      fzr_card_reverse = #{record.fzrCardReverse,jdbcType=VARCHAR},
      fzr_card_begin = #{record.fzrCardBegin,jdbcType=VARCHAR},
      fzr_card_end = #{record.fzrCardEnd,jdbcType=VARCHAR},
      fzr_card_long_effect = #{record.fzrCardLongEffect,jdbcType=TINYINT},
      fzr_phone = #{record.fzrPhone,jdbcType=VARCHAR},
      fzr_email = #{record.fzrEmail,jdbcType=VARCHAR},
      fzr_emergency = #{record.fzrEmergency,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      company_id = #{record.companyId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    update mini_app_open_icp_company
    <set>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="licenseType != null">
        license_type = #{licenseType,jdbcType=INTEGER},
      </if>
      <if test="licensePhoto != null">
        license_photo = #{licensePhoto,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="licenseProvince != null">
        license_province = #{licenseProvince,jdbcType=INTEGER},
      </if>
      <if test="licenseCity != null">
        license_city = #{licenseCity,jdbcType=INTEGER},
      </if>
      <if test="licenseCounty != null">
        license_county = #{licenseCounty,jdbcType=INTEGER},
      </if>
      <if test="licenseDetailAddress != null">
        license_detail_address = #{licenseDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="contactProvince != null">
        contact_province = #{contactProvince,jdbcType=INTEGER},
      </if>
      <if test="contactCity != null">
        contact_city = #{contactCity,jdbcType=INTEGER},
      </if>
      <if test="contactCounty != null">
        contact_county = #{contactCounty,jdbcType=INTEGER},
      </if>
      <if test="contactDetailAddress != null">
        contact_detail_address = #{contactDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type = #{fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no = #{fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="fzrName != null">
        fzr_name = #{fzrName,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardFront != null">
        fzr_card_front = #{fzrCardFront,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardReverse != null">
        fzr_card_reverse = #{fzrCardReverse,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin = #{fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end = #{fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect = #{fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="fzrPhone != null">
        fzr_phone = #{fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmail != null">
        fzr_email = #{fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency = #{fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    update mini_app_open_icp_company
    set flow_id = #{flowId,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      license_type = #{licenseType,jdbcType=INTEGER},
      license_photo = #{licensePhoto,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      license_province = #{licenseProvince,jdbcType=INTEGER},
      license_city = #{licenseCity,jdbcType=INTEGER},
      license_county = #{licenseCounty,jdbcType=INTEGER},
      license_detail_address = #{licenseDetailAddress,jdbcType=VARCHAR},
      contact_province = #{contactProvince,jdbcType=INTEGER},
      contact_city = #{contactCity,jdbcType=INTEGER},
      contact_county = #{contactCounty,jdbcType=INTEGER},
      contact_detail_address = #{contactDetailAddress,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      fzr_license_type = #{fzrLicenseType,jdbcType=INTEGER},
      fzr_card_no = #{fzrCardNo,jdbcType=VARCHAR},
      fzr_name = #{fzrName,jdbcType=VARCHAR},
      fzr_card_front = #{fzrCardFront,jdbcType=VARCHAR},
      fzr_card_reverse = #{fzrCardReverse,jdbcType=VARCHAR},
      fzr_card_begin = #{fzrCardBegin,jdbcType=VARCHAR},
      fzr_card_end = #{fzrCardEnd,jdbcType=VARCHAR},
      fzr_card_long_effect = #{fzrCardLongEffect,jdbcType=TINYINT},
      fzr_phone = #{fzrPhone,jdbcType=VARCHAR},
      fzr_email = #{fzrEmail,jdbcType=VARCHAR},
      fzr_emergency = #{fzrEmergency,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_company (flow_id, type, license_type, 
      license_photo, name, license_no, 
      license_province, license_city, license_county, 
      license_detail_address, contact_province, 
      contact_city, contact_county, contact_detail_address, 
      remark, fzr_license_type, fzr_card_no, 
      fzr_name, fzr_card_front, fzr_card_reverse, 
      fzr_card_begin, fzr_card_end, fzr_card_long_effect, 
      fzr_phone, fzr_email, fzr_emergency, 
      ctime, mtime, is_deleted, 
      company_id)
    values (#{flowId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{licenseType,jdbcType=INTEGER}, 
      #{licensePhoto,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{licenseNo,jdbcType=VARCHAR}, 
      #{licenseProvince,jdbcType=INTEGER}, #{licenseCity,jdbcType=INTEGER}, #{licenseCounty,jdbcType=INTEGER}, 
      #{licenseDetailAddress,jdbcType=VARCHAR}, #{contactProvince,jdbcType=INTEGER}, 
      #{contactCity,jdbcType=INTEGER}, #{contactCounty,jdbcType=INTEGER}, #{contactDetailAddress,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{fzrLicenseType,jdbcType=INTEGER}, #{fzrCardNo,jdbcType=VARCHAR}, 
      #{fzrName,jdbcType=VARCHAR}, #{fzrCardFront,jdbcType=VARCHAR}, #{fzrCardReverse,jdbcType=VARCHAR}, 
      #{fzrCardBegin,jdbcType=VARCHAR}, #{fzrCardEnd,jdbcType=VARCHAR}, #{fzrCardLongEffect,jdbcType=TINYINT}, 
      #{fzrPhone,jdbcType=VARCHAR}, #{fzrEmail,jdbcType=VARCHAR}, #{fzrEmergency,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{companyId,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      flow_id = values(flow_id),
      type = values(type),
      license_type = values(license_type),
      license_photo = values(license_photo),
      name = values(name),
      license_no = values(license_no),
      license_province = values(license_province),
      license_city = values(license_city),
      license_county = values(license_county),
      license_detail_address = values(license_detail_address),
      contact_province = values(contact_province),
      contact_city = values(contact_city),
      contact_county = values(contact_county),
      contact_detail_address = values(contact_detail_address),
      remark = values(remark),
      fzr_license_type = values(fzr_license_type),
      fzr_card_no = values(fzr_card_no),
      fzr_name = values(fzr_name),
      fzr_card_front = values(fzr_card_front),
      fzr_card_reverse = values(fzr_card_reverse),
      fzr_card_begin = values(fzr_card_begin),
      fzr_card_end = values(fzr_card_end),
      fzr_card_long_effect = values(fzr_card_long_effect),
      fzr_phone = values(fzr_phone),
      fzr_email = values(fzr_email),
      fzr_emergency = values(fzr_emergency),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      company_id = values(company_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_company
      (flow_id,type,license_type,license_photo,name,license_no,license_province,license_city,license_county,license_detail_address,contact_province,contact_city,contact_county,contact_detail_address,remark,fzr_license_type,fzr_card_no,fzr_name,fzr_card_front,fzr_card_reverse,fzr_card_begin,fzr_card_end,fzr_card_long_effect,fzr_phone,fzr_email,fzr_emergency,ctime,mtime,is_deleted,company_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.flowId,jdbcType=BIGINT},
        #{item.type,jdbcType=INTEGER},
        #{item.licenseType,jdbcType=INTEGER},
        #{item.licensePhoto,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.licenseNo,jdbcType=VARCHAR},
        #{item.licenseProvince,jdbcType=INTEGER},
        #{item.licenseCity,jdbcType=INTEGER},
        #{item.licenseCounty,jdbcType=INTEGER},
        #{item.licenseDetailAddress,jdbcType=VARCHAR},
        #{item.contactProvince,jdbcType=INTEGER},
        #{item.contactCity,jdbcType=INTEGER},
        #{item.contactCounty,jdbcType=INTEGER},
        #{item.contactDetailAddress,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.fzrLicenseType,jdbcType=INTEGER},
        #{item.fzrCardNo,jdbcType=VARCHAR},
        #{item.fzrName,jdbcType=VARCHAR},
        #{item.fzrCardFront,jdbcType=VARCHAR},
        #{item.fzrCardReverse,jdbcType=VARCHAR},
        #{item.fzrCardBegin,jdbcType=VARCHAR},
        #{item.fzrCardEnd,jdbcType=VARCHAR},
        #{item.fzrCardLongEffect,jdbcType=TINYINT},
        #{item.fzrPhone,jdbcType=VARCHAR},
        #{item.fzrEmail,jdbcType=VARCHAR},
        #{item.fzrEmergency,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.companyId,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_company
      (flow_id,type,license_type,license_photo,name,license_no,license_province,license_city,license_county,license_detail_address,contact_province,contact_city,contact_county,contact_detail_address,remark,fzr_license_type,fzr_card_no,fzr_name,fzr_card_front,fzr_card_reverse,fzr_card_begin,fzr_card_end,fzr_card_long_effect,fzr_phone,fzr_email,fzr_emergency,ctime,mtime,is_deleted,company_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.flowId,jdbcType=BIGINT},
        #{item.type,jdbcType=INTEGER},
        #{item.licenseType,jdbcType=INTEGER},
        #{item.licensePhoto,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.licenseNo,jdbcType=VARCHAR},
        #{item.licenseProvince,jdbcType=INTEGER},
        #{item.licenseCity,jdbcType=INTEGER},
        #{item.licenseCounty,jdbcType=INTEGER},
        #{item.licenseDetailAddress,jdbcType=VARCHAR},
        #{item.contactProvince,jdbcType=INTEGER},
        #{item.contactCity,jdbcType=INTEGER},
        #{item.contactCounty,jdbcType=INTEGER},
        #{item.contactDetailAddress,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.fzrLicenseType,jdbcType=INTEGER},
        #{item.fzrCardNo,jdbcType=VARCHAR},
        #{item.fzrName,jdbcType=VARCHAR},
        #{item.fzrCardFront,jdbcType=VARCHAR},
        #{item.fzrCardReverse,jdbcType=VARCHAR},
        #{item.fzrCardBegin,jdbcType=VARCHAR},
        #{item.fzrCardEnd,jdbcType=VARCHAR},
        #{item.fzrCardLongEffect,jdbcType=TINYINT},
        #{item.fzrPhone,jdbcType=VARCHAR},
        #{item.fzrEmail,jdbcType=VARCHAR},
        #{item.fzrEmergency,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.companyId,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      flow_id = values(flow_id),
      type = values(type),
      license_type = values(license_type),
      license_photo = values(license_photo),
      name = values(name),
      license_no = values(license_no),
      license_province = values(license_province),
      license_city = values(license_city),
      license_county = values(license_county),
      license_detail_address = values(license_detail_address),
      contact_province = values(contact_province),
      contact_city = values(contact_city),
      contact_county = values(contact_county),
      contact_detail_address = values(contact_detail_address),
      remark = values(remark),
      fzr_license_type = values(fzr_license_type),
      fzr_card_no = values(fzr_card_no),
      fzr_name = values(fzr_name),
      fzr_card_front = values(fzr_card_front),
      fzr_card_reverse = values(fzr_card_reverse),
      fzr_card_begin = values(fzr_card_begin),
      fzr_card_end = values(fzr_card_end),
      fzr_card_long_effect = values(fzr_card_long_effect),
      fzr_phone = values(fzr_phone),
      fzr_email = values(fzr_email),
      fzr_emergency = values(fzr_emergency),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      company_id = values(company_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpCompanyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="licenseType != null">
        license_type,
      </if>
      <if test="licensePhoto != null">
        license_photo,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="licenseProvince != null">
        license_province,
      </if>
      <if test="licenseCity != null">
        license_city,
      </if>
      <if test="licenseCounty != null">
        license_county,
      </if>
      <if test="licenseDetailAddress != null">
        license_detail_address,
      </if>
      <if test="contactProvince != null">
        contact_province,
      </if>
      <if test="contactCity != null">
        contact_city,
      </if>
      <if test="contactCounty != null">
        contact_county,
      </if>
      <if test="contactDetailAddress != null">
        contact_detail_address,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type,
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no,
      </if>
      <if test="fzrName != null">
        fzr_name,
      </if>
      <if test="fzrCardFront != null">
        fzr_card_front,
      </if>
      <if test="fzrCardReverse != null">
        fzr_card_reverse,
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin,
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end,
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect,
      </if>
      <if test="fzrPhone != null">
        fzr_phone,
      </if>
      <if test="fzrEmail != null">
        fzr_email,
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowId != null">
        #{flowId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="licenseType != null">
        #{licenseType,jdbcType=INTEGER},
      </if>
      <if test="licensePhoto != null">
        #{licensePhoto,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="licenseProvince != null">
        #{licenseProvince,jdbcType=INTEGER},
      </if>
      <if test="licenseCity != null">
        #{licenseCity,jdbcType=INTEGER},
      </if>
      <if test="licenseCounty != null">
        #{licenseCounty,jdbcType=INTEGER},
      </if>
      <if test="licenseDetailAddress != null">
        #{licenseDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="contactProvince != null">
        #{contactProvince,jdbcType=INTEGER},
      </if>
      <if test="contactCity != null">
        #{contactCity,jdbcType=INTEGER},
      </if>
      <if test="contactCounty != null">
        #{contactCounty,jdbcType=INTEGER},
      </if>
      <if test="contactDetailAddress != null">
        #{contactDetailAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fzrLicenseType != null">
        #{fzrLicenseType,jdbcType=INTEGER},
      </if>
      <if test="fzrCardNo != null">
        #{fzrCardNo,jdbcType=VARCHAR},
      </if>
      <if test="fzrName != null">
        #{fzrName,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardFront != null">
        #{fzrCardFront,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardReverse != null">
        #{fzrCardReverse,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardBegin != null">
        #{fzrCardBegin,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardEnd != null">
        #{fzrCardEnd,jdbcType=VARCHAR},
      </if>
      <if test="fzrCardLongEffect != null">
        #{fzrCardLongEffect,jdbcType=TINYINT},
      </if>
      <if test="fzrPhone != null">
        #{fzrPhone,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmail != null">
        #{fzrEmail,jdbcType=VARCHAR},
      </if>
      <if test="fzrEmergency != null">
        #{fzrEmergency,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="flowId != null">
        flow_id = values(flow_id),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="licenseType != null">
        license_type = values(license_type),
      </if>
      <if test="licensePhoto != null">
        license_photo = values(license_photo),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="licenseNo != null">
        license_no = values(license_no),
      </if>
      <if test="licenseProvince != null">
        license_province = values(license_province),
      </if>
      <if test="licenseCity != null">
        license_city = values(license_city),
      </if>
      <if test="licenseCounty != null">
        license_county = values(license_county),
      </if>
      <if test="licenseDetailAddress != null">
        license_detail_address = values(license_detail_address),
      </if>
      <if test="contactProvince != null">
        contact_province = values(contact_province),
      </if>
      <if test="contactCity != null">
        contact_city = values(contact_city),
      </if>
      <if test="contactCounty != null">
        contact_county = values(contact_county),
      </if>
      <if test="contactDetailAddress != null">
        contact_detail_address = values(contact_detail_address),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="fzrLicenseType != null">
        fzr_license_type = values(fzr_license_type),
      </if>
      <if test="fzrCardNo != null">
        fzr_card_no = values(fzr_card_no),
      </if>
      <if test="fzrName != null">
        fzr_name = values(fzr_name),
      </if>
      <if test="fzrCardFront != null">
        fzr_card_front = values(fzr_card_front),
      </if>
      <if test="fzrCardReverse != null">
        fzr_card_reverse = values(fzr_card_reverse),
      </if>
      <if test="fzrCardBegin != null">
        fzr_card_begin = values(fzr_card_begin),
      </if>
      <if test="fzrCardEnd != null">
        fzr_card_end = values(fzr_card_end),
      </if>
      <if test="fzrCardLongEffect != null">
        fzr_card_long_effect = values(fzr_card_long_effect),
      </if>
      <if test="fzrPhone != null">
        fzr_phone = values(fzr_phone),
      </if>
      <if test="fzrEmail != null">
        fzr_email = values(fzr_email),
      </if>
      <if test="fzrEmergency != null">
        fzr_emergency = values(fzr_emergency),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="companyId != null">
        company_id = values(company_id),
      </if>
    </trim>
  </insert>
</mapper>