<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpConfigZjlxDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="BIGINT" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="dwfl_id" jdbcType="BIGINT" property="dwflId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, name, dwfl_id, status, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_icp_config_zjlx
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_icp_config_zjlx
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_icp_config_zjlx
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPoExample">
    delete from mini_app_open_icp_config_zjlx
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_config_zjlx (code, name, dwfl_id, 
      status, is_deleted, ctime, 
      mtime)
    values (#{code,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{dwflId,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_config_zjlx
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="dwflId != null">
        dwfl_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="dwflId != null">
        #{dwflId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_icp_config_zjlx
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_icp_config_zjlx
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.dwflId != null">
        dwfl_id = #{record.dwflId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_icp_config_zjlx
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      dwfl_id = #{record.dwflId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    update mini_app_open_icp_config_zjlx
    <set>
      <if test="code != null">
        code = #{code,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="dwflId != null">
        dwfl_id = #{dwflId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    update mini_app_open_icp_config_zjlx
    set code = #{code,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      dwfl_id = #{dwflId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_config_zjlx (code, name, dwfl_id, 
      status, is_deleted, ctime, 
      mtime)
    values (#{code,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{dwflId,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      code = values(code),
      name = values(name),
      dwfl_id = values(dwfl_id),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_config_zjlx
      (code,name,dwfl_id,status,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.code,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.dwflId,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_config_zjlx
      (code,name,dwfl_id,status,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.code,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.dwflId,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      code = values(code),
      name = values(name),
      dwfl_id = values(dwfl_id),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpConfigZjlxPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_config_zjlx
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="dwflId != null">
        dwfl_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="dwflId != null">
        #{dwflId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="code != null">
        code = values(code),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="dwflId != null">
        dwfl_id = values(dwfl_id),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>