<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="record_number" jdbcType="VARCHAR" property="recordNumber" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="flow_status" jdbcType="TINYINT" property="flowStatus" />
    <result column="gov_audit_state" jdbcType="TINYINT" property="govAuditState" />
    <result column="gov_audit_time" jdbcType="VARCHAR" property="govAuditTime" />
    <result column="gov_audit_code" jdbcType="BIGINT" property="govAuditCode" />
    <result column="gov_audit_msg" jdbcType="VARCHAR" property="govAuditMsg" />
    <result column="gov_audit_description" jdbcType="VARCHAR" property="govAuditDescription" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="report_status" jdbcType="TINYINT" property="reportStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, record_number, submit_time, flow_status, gov_audit_state, gov_audit_time, 
    gov_audit_code, gov_audit_msg, gov_audit_description, ctime, mtime, is_deleted, report_status
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_icp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_icp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_icp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPoExample">
    delete from mini_app_open_icp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp (app_id, record_number, submit_time, 
      flow_status, gov_audit_state, gov_audit_time, 
      gov_audit_code, gov_audit_msg, gov_audit_description, 
      ctime, mtime, is_deleted, 
      report_status)
    values (#{appId,jdbcType=VARCHAR}, #{recordNumber,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{flowStatus,jdbcType=TINYINT}, #{govAuditState,jdbcType=TINYINT}, #{govAuditTime,jdbcType=VARCHAR}, 
      #{govAuditCode,jdbcType=BIGINT}, #{govAuditMsg,jdbcType=VARCHAR}, #{govAuditDescription,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{reportStatus,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="recordNumber != null">
        record_number,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="flowStatus != null">
        flow_status,
      </if>
      <if test="govAuditState != null">
        gov_audit_state,
      </if>
      <if test="govAuditTime != null">
        gov_audit_time,
      </if>
      <if test="govAuditCode != null">
        gov_audit_code,
      </if>
      <if test="govAuditMsg != null">
        gov_audit_msg,
      </if>
      <if test="govAuditDescription != null">
        gov_audit_description,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="reportStatus != null">
        report_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="recordNumber != null">
        #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowStatus != null">
        #{flowStatus,jdbcType=TINYINT},
      </if>
      <if test="govAuditState != null">
        #{govAuditState,jdbcType=TINYINT},
      </if>
      <if test="govAuditTime != null">
        #{govAuditTime,jdbcType=VARCHAR},
      </if>
      <if test="govAuditCode != null">
        #{govAuditCode,jdbcType=BIGINT},
      </if>
      <if test="govAuditMsg != null">
        #{govAuditMsg,jdbcType=VARCHAR},
      </if>
      <if test="govAuditDescription != null">
        #{govAuditDescription,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_icp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_icp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.recordNumber != null">
        record_number = #{record.recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.flowStatus != null">
        flow_status = #{record.flowStatus,jdbcType=TINYINT},
      </if>
      <if test="record.govAuditState != null">
        gov_audit_state = #{record.govAuditState,jdbcType=TINYINT},
      </if>
      <if test="record.govAuditTime != null">
        gov_audit_time = #{record.govAuditTime,jdbcType=VARCHAR},
      </if>
      <if test="record.govAuditCode != null">
        gov_audit_code = #{record.govAuditCode,jdbcType=BIGINT},
      </if>
      <if test="record.govAuditMsg != null">
        gov_audit_msg = #{record.govAuditMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.govAuditDescription != null">
        gov_audit_description = #{record.govAuditDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.reportStatus != null">
        report_status = #{record.reportStatus,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_icp
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      record_number = #{record.recordNumber,jdbcType=VARCHAR},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      flow_status = #{record.flowStatus,jdbcType=TINYINT},
      gov_audit_state = #{record.govAuditState,jdbcType=TINYINT},
      gov_audit_time = #{record.govAuditTime,jdbcType=VARCHAR},
      gov_audit_code = #{record.govAuditCode,jdbcType=BIGINT},
      gov_audit_msg = #{record.govAuditMsg,jdbcType=VARCHAR},
      gov_audit_description = #{record.govAuditDescription,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      report_status = #{record.reportStatus,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    update mini_app_open_icp
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="recordNumber != null">
        record_number = #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowStatus != null">
        flow_status = #{flowStatus,jdbcType=TINYINT},
      </if>
      <if test="govAuditState != null">
        gov_audit_state = #{govAuditState,jdbcType=TINYINT},
      </if>
      <if test="govAuditTime != null">
        gov_audit_time = #{govAuditTime,jdbcType=VARCHAR},
      </if>
      <if test="govAuditCode != null">
        gov_audit_code = #{govAuditCode,jdbcType=BIGINT},
      </if>
      <if test="govAuditMsg != null">
        gov_audit_msg = #{govAuditMsg,jdbcType=VARCHAR},
      </if>
      <if test="govAuditDescription != null">
        gov_audit_description = #{govAuditDescription,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="reportStatus != null">
        report_status = #{reportStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    update mini_app_open_icp
    set app_id = #{appId,jdbcType=VARCHAR},
      record_number = #{recordNumber,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      flow_status = #{flowStatus,jdbcType=TINYINT},
      gov_audit_state = #{govAuditState,jdbcType=TINYINT},
      gov_audit_time = #{govAuditTime,jdbcType=VARCHAR},
      gov_audit_code = #{govAuditCode,jdbcType=BIGINT},
      gov_audit_msg = #{govAuditMsg,jdbcType=VARCHAR},
      gov_audit_description = #{govAuditDescription,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      report_status = #{reportStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp (app_id, record_number, submit_time, 
      flow_status, gov_audit_state, gov_audit_time, 
      gov_audit_code, gov_audit_msg, gov_audit_description, 
      ctime, mtime, is_deleted, 
      report_status)
    values (#{appId,jdbcType=VARCHAR}, #{recordNumber,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{flowStatus,jdbcType=TINYINT}, #{govAuditState,jdbcType=TINYINT}, #{govAuditTime,jdbcType=VARCHAR}, 
      #{govAuditCode,jdbcType=BIGINT}, #{govAuditMsg,jdbcType=VARCHAR}, #{govAuditDescription,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{reportStatus,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      record_number = values(record_number),
      submit_time = values(submit_time),
      flow_status = values(flow_status),
      gov_audit_state = values(gov_audit_state),
      gov_audit_time = values(gov_audit_time),
      gov_audit_code = values(gov_audit_code),
      gov_audit_msg = values(gov_audit_msg),
      gov_audit_description = values(gov_audit_description),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      report_status = values(report_status),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp
      (app_id,record_number,submit_time,flow_status,gov_audit_state,gov_audit_time,gov_audit_code,gov_audit_msg,gov_audit_description,ctime,mtime,is_deleted,report_status)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.recordNumber,jdbcType=VARCHAR},
        #{item.submitTime,jdbcType=TIMESTAMP},
        #{item.flowStatus,jdbcType=TINYINT},
        #{item.govAuditState,jdbcType=TINYINT},
        #{item.govAuditTime,jdbcType=VARCHAR},
        #{item.govAuditCode,jdbcType=BIGINT},
        #{item.govAuditMsg,jdbcType=VARCHAR},
        #{item.govAuditDescription,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.reportStatus,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp
      (app_id,record_number,submit_time,flow_status,gov_audit_state,gov_audit_time,gov_audit_code,gov_audit_msg,gov_audit_description,ctime,mtime,is_deleted,report_status)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.appId,jdbcType=VARCHAR},
        #{item.recordNumber,jdbcType=VARCHAR},
        #{item.submitTime,jdbcType=TIMESTAMP},
        #{item.flowStatus,jdbcType=TINYINT},
        #{item.govAuditState,jdbcType=TINYINT},
        #{item.govAuditTime,jdbcType=VARCHAR},
        #{item.govAuditCode,jdbcType=BIGINT},
        #{item.govAuditMsg,jdbcType=VARCHAR},
        #{item.govAuditDescription,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.reportStatus,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      app_id = values(app_id),
      record_number = values(record_number),
      submit_time = values(submit_time),
      flow_status = values(flow_status),
      gov_audit_state = values(gov_audit_state),
      gov_audit_time = values(gov_audit_time),
      gov_audit_code = values(gov_audit_code),
      gov_audit_msg = values(gov_audit_msg),
      gov_audit_description = values(gov_audit_description),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      report_status = values(report_status),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="recordNumber != null">
        record_number,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="flowStatus != null">
        flow_status,
      </if>
      <if test="govAuditState != null">
        gov_audit_state,
      </if>
      <if test="govAuditTime != null">
        gov_audit_time,
      </if>
      <if test="govAuditCode != null">
        gov_audit_code,
      </if>
      <if test="govAuditMsg != null">
        gov_audit_msg,
      </if>
      <if test="govAuditDescription != null">
        gov_audit_description,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="reportStatus != null">
        report_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="recordNumber != null">
        #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowStatus != null">
        #{flowStatus,jdbcType=TINYINT},
      </if>
      <if test="govAuditState != null">
        #{govAuditState,jdbcType=TINYINT},
      </if>
      <if test="govAuditTime != null">
        #{govAuditTime,jdbcType=VARCHAR},
      </if>
      <if test="govAuditCode != null">
        #{govAuditCode,jdbcType=BIGINT},
      </if>
      <if test="govAuditMsg != null">
        #{govAuditMsg,jdbcType=VARCHAR},
      </if>
      <if test="govAuditDescription != null">
        #{govAuditDescription,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="recordNumber != null">
        record_number = values(record_number),
      </if>
      <if test="submitTime != null">
        submit_time = values(submit_time),
      </if>
      <if test="flowStatus != null">
        flow_status = values(flow_status),
      </if>
      <if test="govAuditState != null">
        gov_audit_state = values(gov_audit_state),
      </if>
      <if test="govAuditTime != null">
        gov_audit_time = values(gov_audit_time),
      </if>
      <if test="govAuditCode != null">
        gov_audit_code = values(gov_audit_code),
      </if>
      <if test="govAuditMsg != null">
        gov_audit_msg = values(gov_audit_msg),
      </if>
      <if test="govAuditDescription != null">
        gov_audit_description = values(gov_audit_description),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="reportStatus != null">
        report_status = values(report_status),
      </if>
    </trim>
  </insert>
</mapper>