<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenIcpIdentityAuthDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="isp_wzid" jdbcType="BIGINT" property="ispWzid" />
    <result column="dwmc" jdbcType="VARCHAR" property="dwmc" />
    <result column="shengid" jdbcType="INTEGER" property="shengid" />
    <result column="dwxz" jdbcType="TINYINT" property="dwxz" />
    <result column="zjlx" jdbcType="TINYINT" property="zjlx" />
    <result column="zjhm" jdbcType="VARCHAR" property="zjhm" />
    <result column="fzr_xm" jdbcType="VARCHAR" property="fzrXm" />
    <result column="fzr_zjlx" jdbcType="TINYINT" property="fzrZjlx" />
    <result column="fzr_zjhm" jdbcType="VARCHAR" property="fzrZjhm" />
    <result column="fzr_zjyxq_start" jdbcType="VARCHAR" property="fzrZjyxqStart" />
    <result column="fzr_zjyxq_end" jdbcType="VARCHAR" property="fzrZjyxqEnd" />
    <result column="wzmc" jdbcType="VARCHAR" property="wzmc" />
    <result column="fwlx" jdbcType="TINYINT" property="fwlx" />
    <result column="nrlx" jdbcType="TINYINT" property="nrlx" />
    <result column="wz_fzr_xm" jdbcType="VARCHAR" property="wzFzrXm" />
    <result column="wz_fzr_zjlx" jdbcType="TINYINT" property="wzFzrZjlx" />
    <result column="wz_fzr_zjhm" jdbcType="VARCHAR" property="wzFzrZjhm" />
    <result column="wz_fzr_zjyxq_start" jdbcType="VARCHAR" property="wzFzrZjyxqStart" />
    <result column="wz_fzr_zjyxq_end" jdbcType="VARCHAR" property="wzFzrZjyxqEnd" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="fzr_zjyxq_cqyx" jdbcType="TINYINT" property="fzrZjyxqCqyx" />
    <result column="wz_fzr_zjyxq_cqyx" jdbcType="TINYINT" property="wzFzrZjyxqCqyx" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, isp_wzid, dwmc, shengid, dwxz, zjlx, zjhm, fzr_xm, fzr_zjlx, fzr_zjhm, fzr_zjyxq_start, 
    fzr_zjyxq_end, wzmc, fwlx, nrlx, wz_fzr_xm, wz_fzr_zjlx, wz_fzr_zjhm, wz_fzr_zjyxq_start, 
    wz_fzr_zjyxq_end, domain, status, ctime, mtime, is_deleted, fzr_zjyxq_cqyx, wz_fzr_zjyxq_cqyx
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_icp_identity_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_icp_identity_auth
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_icp_identity_auth
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPoExample">
    delete from mini_app_open_icp_identity_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_identity_auth (isp_wzid, dwmc, shengid, 
      dwxz, zjlx, zjhm, fzr_xm, 
      fzr_zjlx, fzr_zjhm, fzr_zjyxq_start, 
      fzr_zjyxq_end, wzmc, fwlx, 
      nrlx, wz_fzr_xm, wz_fzr_zjlx, 
      wz_fzr_zjhm, wz_fzr_zjyxq_start, wz_fzr_zjyxq_end, 
      domain, status, ctime, 
      mtime, is_deleted, fzr_zjyxq_cqyx, 
      wz_fzr_zjyxq_cqyx)
    values (#{ispWzid,jdbcType=BIGINT}, #{dwmc,jdbcType=VARCHAR}, #{shengid,jdbcType=INTEGER}, 
      #{dwxz,jdbcType=TINYINT}, #{zjlx,jdbcType=TINYINT}, #{zjhm,jdbcType=VARCHAR}, #{fzrXm,jdbcType=VARCHAR}, 
      #{fzrZjlx,jdbcType=TINYINT}, #{fzrZjhm,jdbcType=VARCHAR}, #{fzrZjyxqStart,jdbcType=VARCHAR}, 
      #{fzrZjyxqEnd,jdbcType=VARCHAR}, #{wzmc,jdbcType=VARCHAR}, #{fwlx,jdbcType=TINYINT}, 
      #{nrlx,jdbcType=TINYINT}, #{wzFzrXm,jdbcType=VARCHAR}, #{wzFzrZjlx,jdbcType=TINYINT}, 
      #{wzFzrZjhm,jdbcType=VARCHAR}, #{wzFzrZjyxqStart,jdbcType=VARCHAR}, #{wzFzrZjyxqEnd,jdbcType=VARCHAR}, 
      #{domain,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, #{fzrZjyxqCqyx,jdbcType=TINYINT}, 
      #{wzFzrZjyxqCqyx,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_identity_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ispWzid != null">
        isp_wzid,
      </if>
      <if test="dwmc != null">
        dwmc,
      </if>
      <if test="shengid != null">
        shengid,
      </if>
      <if test="dwxz != null">
        dwxz,
      </if>
      <if test="zjlx != null">
        zjlx,
      </if>
      <if test="zjhm != null">
        zjhm,
      </if>
      <if test="fzrXm != null">
        fzr_xm,
      </if>
      <if test="fzrZjlx != null">
        fzr_zjlx,
      </if>
      <if test="fzrZjhm != null">
        fzr_zjhm,
      </if>
      <if test="fzrZjyxqStart != null">
        fzr_zjyxq_start,
      </if>
      <if test="fzrZjyxqEnd != null">
        fzr_zjyxq_end,
      </if>
      <if test="wzmc != null">
        wzmc,
      </if>
      <if test="fwlx != null">
        fwlx,
      </if>
      <if test="nrlx != null">
        nrlx,
      </if>
      <if test="wzFzrXm != null">
        wz_fzr_xm,
      </if>
      <if test="wzFzrZjlx != null">
        wz_fzr_zjlx,
      </if>
      <if test="wzFzrZjhm != null">
        wz_fzr_zjhm,
      </if>
      <if test="wzFzrZjyxqStart != null">
        wz_fzr_zjyxq_start,
      </if>
      <if test="wzFzrZjyxqEnd != null">
        wz_fzr_zjyxq_end,
      </if>
      <if test="domain != null">
        domain,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="fzrZjyxqCqyx != null">
        fzr_zjyxq_cqyx,
      </if>
      <if test="wzFzrZjyxqCqyx != null">
        wz_fzr_zjyxq_cqyx,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ispWzid != null">
        #{ispWzid,jdbcType=BIGINT},
      </if>
      <if test="dwmc != null">
        #{dwmc,jdbcType=VARCHAR},
      </if>
      <if test="shengid != null">
        #{shengid,jdbcType=INTEGER},
      </if>
      <if test="dwxz != null">
        #{dwxz,jdbcType=TINYINT},
      </if>
      <if test="zjlx != null">
        #{zjlx,jdbcType=TINYINT},
      </if>
      <if test="zjhm != null">
        #{zjhm,jdbcType=VARCHAR},
      </if>
      <if test="fzrXm != null">
        #{fzrXm,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjlx != null">
        #{fzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="fzrZjhm != null">
        #{fzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjyxqStart != null">
        #{fzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjyxqEnd != null">
        #{fzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="wzmc != null">
        #{wzmc,jdbcType=VARCHAR},
      </if>
      <if test="fwlx != null">
        #{fwlx,jdbcType=TINYINT},
      </if>
      <if test="nrlx != null">
        #{nrlx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrXm != null">
        #{wzFzrXm,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjlx != null">
        #{wzFzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrZjhm != null">
        #{wzFzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjyxqStart != null">
        #{wzFzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjyxqEnd != null">
        #{wzFzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="fzrZjyxqCqyx != null">
        #{fzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrZjyxqCqyx != null">
        #{wzFzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_icp_identity_auth
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_icp_identity_auth
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ispWzid != null">
        isp_wzid = #{record.ispWzid,jdbcType=BIGINT},
      </if>
      <if test="record.dwmc != null">
        dwmc = #{record.dwmc,jdbcType=VARCHAR},
      </if>
      <if test="record.shengid != null">
        shengid = #{record.shengid,jdbcType=INTEGER},
      </if>
      <if test="record.dwxz != null">
        dwxz = #{record.dwxz,jdbcType=TINYINT},
      </if>
      <if test="record.zjlx != null">
        zjlx = #{record.zjlx,jdbcType=TINYINT},
      </if>
      <if test="record.zjhm != null">
        zjhm = #{record.zjhm,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrXm != null">
        fzr_xm = #{record.fzrXm,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrZjlx != null">
        fzr_zjlx = #{record.fzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="record.fzrZjhm != null">
        fzr_zjhm = #{record.fzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrZjyxqStart != null">
        fzr_zjyxq_start = #{record.fzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="record.fzrZjyxqEnd != null">
        fzr_zjyxq_end = #{record.fzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.wzmc != null">
        wzmc = #{record.wzmc,jdbcType=VARCHAR},
      </if>
      <if test="record.fwlx != null">
        fwlx = #{record.fwlx,jdbcType=TINYINT},
      </if>
      <if test="record.nrlx != null">
        nrlx = #{record.nrlx,jdbcType=TINYINT},
      </if>
      <if test="record.wzFzrXm != null">
        wz_fzr_xm = #{record.wzFzrXm,jdbcType=VARCHAR},
      </if>
      <if test="record.wzFzrZjlx != null">
        wz_fzr_zjlx = #{record.wzFzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="record.wzFzrZjhm != null">
        wz_fzr_zjhm = #{record.wzFzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="record.wzFzrZjyxqStart != null">
        wz_fzr_zjyxq_start = #{record.wzFzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="record.wzFzrZjyxqEnd != null">
        wz_fzr_zjyxq_end = #{record.wzFzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.domain != null">
        domain = #{record.domain,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.fzrZjyxqCqyx != null">
        fzr_zjyxq_cqyx = #{record.fzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
      <if test="record.wzFzrZjyxqCqyx != null">
        wz_fzr_zjyxq_cqyx = #{record.wzFzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_icp_identity_auth
    set id = #{record.id,jdbcType=BIGINT},
      isp_wzid = #{record.ispWzid,jdbcType=BIGINT},
      dwmc = #{record.dwmc,jdbcType=VARCHAR},
      shengid = #{record.shengid,jdbcType=INTEGER},
      dwxz = #{record.dwxz,jdbcType=TINYINT},
      zjlx = #{record.zjlx,jdbcType=TINYINT},
      zjhm = #{record.zjhm,jdbcType=VARCHAR},
      fzr_xm = #{record.fzrXm,jdbcType=VARCHAR},
      fzr_zjlx = #{record.fzrZjlx,jdbcType=TINYINT},
      fzr_zjhm = #{record.fzrZjhm,jdbcType=VARCHAR},
      fzr_zjyxq_start = #{record.fzrZjyxqStart,jdbcType=VARCHAR},
      fzr_zjyxq_end = #{record.fzrZjyxqEnd,jdbcType=VARCHAR},
      wzmc = #{record.wzmc,jdbcType=VARCHAR},
      fwlx = #{record.fwlx,jdbcType=TINYINT},
      nrlx = #{record.nrlx,jdbcType=TINYINT},
      wz_fzr_xm = #{record.wzFzrXm,jdbcType=VARCHAR},
      wz_fzr_zjlx = #{record.wzFzrZjlx,jdbcType=TINYINT},
      wz_fzr_zjhm = #{record.wzFzrZjhm,jdbcType=VARCHAR},
      wz_fzr_zjyxq_start = #{record.wzFzrZjyxqStart,jdbcType=VARCHAR},
      wz_fzr_zjyxq_end = #{record.wzFzrZjyxqEnd,jdbcType=VARCHAR},
      domain = #{record.domain,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      fzr_zjyxq_cqyx = #{record.fzrZjyxqCqyx,jdbcType=TINYINT},
      wz_fzr_zjyxq_cqyx = #{record.wzFzrZjyxqCqyx,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    update mini_app_open_icp_identity_auth
    <set>
      <if test="ispWzid != null">
        isp_wzid = #{ispWzid,jdbcType=BIGINT},
      </if>
      <if test="dwmc != null">
        dwmc = #{dwmc,jdbcType=VARCHAR},
      </if>
      <if test="shengid != null">
        shengid = #{shengid,jdbcType=INTEGER},
      </if>
      <if test="dwxz != null">
        dwxz = #{dwxz,jdbcType=TINYINT},
      </if>
      <if test="zjlx != null">
        zjlx = #{zjlx,jdbcType=TINYINT},
      </if>
      <if test="zjhm != null">
        zjhm = #{zjhm,jdbcType=VARCHAR},
      </if>
      <if test="fzrXm != null">
        fzr_xm = #{fzrXm,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjlx != null">
        fzr_zjlx = #{fzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="fzrZjhm != null">
        fzr_zjhm = #{fzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjyxqStart != null">
        fzr_zjyxq_start = #{fzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjyxqEnd != null">
        fzr_zjyxq_end = #{fzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="wzmc != null">
        wzmc = #{wzmc,jdbcType=VARCHAR},
      </if>
      <if test="fwlx != null">
        fwlx = #{fwlx,jdbcType=TINYINT},
      </if>
      <if test="nrlx != null">
        nrlx = #{nrlx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrXm != null">
        wz_fzr_xm = #{wzFzrXm,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjlx != null">
        wz_fzr_zjlx = #{wzFzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrZjhm != null">
        wz_fzr_zjhm = #{wzFzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjyxqStart != null">
        wz_fzr_zjyxq_start = #{wzFzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjyxqEnd != null">
        wz_fzr_zjyxq_end = #{wzFzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        domain = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="fzrZjyxqCqyx != null">
        fzr_zjyxq_cqyx = #{fzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrZjyxqCqyx != null">
        wz_fzr_zjyxq_cqyx = #{wzFzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    update mini_app_open_icp_identity_auth
    set isp_wzid = #{ispWzid,jdbcType=BIGINT},
      dwmc = #{dwmc,jdbcType=VARCHAR},
      shengid = #{shengid,jdbcType=INTEGER},
      dwxz = #{dwxz,jdbcType=TINYINT},
      zjlx = #{zjlx,jdbcType=TINYINT},
      zjhm = #{zjhm,jdbcType=VARCHAR},
      fzr_xm = #{fzrXm,jdbcType=VARCHAR},
      fzr_zjlx = #{fzrZjlx,jdbcType=TINYINT},
      fzr_zjhm = #{fzrZjhm,jdbcType=VARCHAR},
      fzr_zjyxq_start = #{fzrZjyxqStart,jdbcType=VARCHAR},
      fzr_zjyxq_end = #{fzrZjyxqEnd,jdbcType=VARCHAR},
      wzmc = #{wzmc,jdbcType=VARCHAR},
      fwlx = #{fwlx,jdbcType=TINYINT},
      nrlx = #{nrlx,jdbcType=TINYINT},
      wz_fzr_xm = #{wzFzrXm,jdbcType=VARCHAR},
      wz_fzr_zjlx = #{wzFzrZjlx,jdbcType=TINYINT},
      wz_fzr_zjhm = #{wzFzrZjhm,jdbcType=VARCHAR},
      wz_fzr_zjyxq_start = #{wzFzrZjyxqStart,jdbcType=VARCHAR},
      wz_fzr_zjyxq_end = #{wzFzrZjyxqEnd,jdbcType=VARCHAR},
      domain = #{domain,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      fzr_zjyxq_cqyx = #{fzrZjyxqCqyx,jdbcType=TINYINT},
      wz_fzr_zjyxq_cqyx = #{wzFzrZjyxqCqyx,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_identity_auth (isp_wzid, dwmc, shengid, 
      dwxz, zjlx, zjhm, fzr_xm, 
      fzr_zjlx, fzr_zjhm, fzr_zjyxq_start, 
      fzr_zjyxq_end, wzmc, fwlx, 
      nrlx, wz_fzr_xm, wz_fzr_zjlx, 
      wz_fzr_zjhm, wz_fzr_zjyxq_start, wz_fzr_zjyxq_end, 
      domain, status, ctime, 
      mtime, is_deleted, fzr_zjyxq_cqyx, 
      wz_fzr_zjyxq_cqyx)
    values (#{ispWzid,jdbcType=BIGINT}, #{dwmc,jdbcType=VARCHAR}, #{shengid,jdbcType=INTEGER}, 
      #{dwxz,jdbcType=TINYINT}, #{zjlx,jdbcType=TINYINT}, #{zjhm,jdbcType=VARCHAR}, #{fzrXm,jdbcType=VARCHAR}, 
      #{fzrZjlx,jdbcType=TINYINT}, #{fzrZjhm,jdbcType=VARCHAR}, #{fzrZjyxqStart,jdbcType=VARCHAR}, 
      #{fzrZjyxqEnd,jdbcType=VARCHAR}, #{wzmc,jdbcType=VARCHAR}, #{fwlx,jdbcType=TINYINT}, 
      #{nrlx,jdbcType=TINYINT}, #{wzFzrXm,jdbcType=VARCHAR}, #{wzFzrZjlx,jdbcType=TINYINT}, 
      #{wzFzrZjhm,jdbcType=VARCHAR}, #{wzFzrZjyxqStart,jdbcType=VARCHAR}, #{wzFzrZjyxqEnd,jdbcType=VARCHAR}, 
      #{domain,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, #{fzrZjyxqCqyx,jdbcType=TINYINT}, 
      #{wzFzrZjyxqCqyx,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      isp_wzid = values(isp_wzid),
      dwmc = values(dwmc),
      shengid = values(shengid),
      dwxz = values(dwxz),
      zjlx = values(zjlx),
      zjhm = values(zjhm),
      fzr_xm = values(fzr_xm),
      fzr_zjlx = values(fzr_zjlx),
      fzr_zjhm = values(fzr_zjhm),
      fzr_zjyxq_start = values(fzr_zjyxq_start),
      fzr_zjyxq_end = values(fzr_zjyxq_end),
      wzmc = values(wzmc),
      fwlx = values(fwlx),
      nrlx = values(nrlx),
      wz_fzr_xm = values(wz_fzr_xm),
      wz_fzr_zjlx = values(wz_fzr_zjlx),
      wz_fzr_zjhm = values(wz_fzr_zjhm),
      wz_fzr_zjyxq_start = values(wz_fzr_zjyxq_start),
      wz_fzr_zjyxq_end = values(wz_fzr_zjyxq_end),
      domain = values(domain),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      fzr_zjyxq_cqyx = values(fzr_zjyxq_cqyx),
      wz_fzr_zjyxq_cqyx = values(wz_fzr_zjyxq_cqyx),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_identity_auth
      (isp_wzid,dwmc,shengid,dwxz,zjlx,zjhm,fzr_xm,fzr_zjlx,fzr_zjhm,fzr_zjyxq_start,fzr_zjyxq_end,wzmc,fwlx,nrlx,wz_fzr_xm,wz_fzr_zjlx,wz_fzr_zjhm,wz_fzr_zjyxq_start,wz_fzr_zjyxq_end,domain,status,ctime,mtime,is_deleted,fzr_zjyxq_cqyx,wz_fzr_zjyxq_cqyx)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ispWzid,jdbcType=BIGINT},
        #{item.dwmc,jdbcType=VARCHAR},
        #{item.shengid,jdbcType=INTEGER},
        #{item.dwxz,jdbcType=TINYINT},
        #{item.zjlx,jdbcType=TINYINT},
        #{item.zjhm,jdbcType=VARCHAR},
        #{item.fzrXm,jdbcType=VARCHAR},
        #{item.fzrZjlx,jdbcType=TINYINT},
        #{item.fzrZjhm,jdbcType=VARCHAR},
        #{item.fzrZjyxqStart,jdbcType=VARCHAR},
        #{item.fzrZjyxqEnd,jdbcType=VARCHAR},
        #{item.wzmc,jdbcType=VARCHAR},
        #{item.fwlx,jdbcType=TINYINT},
        #{item.nrlx,jdbcType=TINYINT},
        #{item.wzFzrXm,jdbcType=VARCHAR},
        #{item.wzFzrZjlx,jdbcType=TINYINT},
        #{item.wzFzrZjhm,jdbcType=VARCHAR},
        #{item.wzFzrZjyxqStart,jdbcType=VARCHAR},
        #{item.wzFzrZjyxqEnd,jdbcType=VARCHAR},
        #{item.domain,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.fzrZjyxqCqyx,jdbcType=TINYINT},
        #{item.wzFzrZjyxqCqyx,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_icp_identity_auth
      (isp_wzid,dwmc,shengid,dwxz,zjlx,zjhm,fzr_xm,fzr_zjlx,fzr_zjhm,fzr_zjyxq_start,fzr_zjyxq_end,wzmc,fwlx,nrlx,wz_fzr_xm,wz_fzr_zjlx,wz_fzr_zjhm,wz_fzr_zjyxq_start,wz_fzr_zjyxq_end,domain,status,ctime,mtime,is_deleted,fzr_zjyxq_cqyx,wz_fzr_zjyxq_cqyx)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ispWzid,jdbcType=BIGINT},
        #{item.dwmc,jdbcType=VARCHAR},
        #{item.shengid,jdbcType=INTEGER},
        #{item.dwxz,jdbcType=TINYINT},
        #{item.zjlx,jdbcType=TINYINT},
        #{item.zjhm,jdbcType=VARCHAR},
        #{item.fzrXm,jdbcType=VARCHAR},
        #{item.fzrZjlx,jdbcType=TINYINT},
        #{item.fzrZjhm,jdbcType=VARCHAR},
        #{item.fzrZjyxqStart,jdbcType=VARCHAR},
        #{item.fzrZjyxqEnd,jdbcType=VARCHAR},
        #{item.wzmc,jdbcType=VARCHAR},
        #{item.fwlx,jdbcType=TINYINT},
        #{item.nrlx,jdbcType=TINYINT},
        #{item.wzFzrXm,jdbcType=VARCHAR},
        #{item.wzFzrZjlx,jdbcType=TINYINT},
        #{item.wzFzrZjhm,jdbcType=VARCHAR},
        #{item.wzFzrZjyxqStart,jdbcType=VARCHAR},
        #{item.wzFzrZjyxqEnd,jdbcType=VARCHAR},
        #{item.domain,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.fzrZjyxqCqyx,jdbcType=TINYINT},
        #{item.wzFzrZjyxqCqyx,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      isp_wzid = values(isp_wzid),
      dwmc = values(dwmc),
      shengid = values(shengid),
      dwxz = values(dwxz),
      zjlx = values(zjlx),
      zjhm = values(zjhm),
      fzr_xm = values(fzr_xm),
      fzr_zjlx = values(fzr_zjlx),
      fzr_zjhm = values(fzr_zjhm),
      fzr_zjyxq_start = values(fzr_zjyxq_start),
      fzr_zjyxq_end = values(fzr_zjyxq_end),
      wzmc = values(wzmc),
      fwlx = values(fwlx),
      nrlx = values(nrlx),
      wz_fzr_xm = values(wz_fzr_xm),
      wz_fzr_zjlx = values(wz_fzr_zjlx),
      wz_fzr_zjhm = values(wz_fzr_zjhm),
      wz_fzr_zjyxq_start = values(wz_fzr_zjyxq_start),
      wz_fzr_zjyxq_end = values(wz_fzr_zjyxq_end),
      domain = values(domain),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      fzr_zjyxq_cqyx = values(fzr_zjyxq_cqyx),
      wz_fzr_zjyxq_cqyx = values(wz_fzr_zjyxq_cqyx),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenIcpIdentityAuthPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_icp_identity_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ispWzid != null">
        isp_wzid,
      </if>
      <if test="dwmc != null">
        dwmc,
      </if>
      <if test="shengid != null">
        shengid,
      </if>
      <if test="dwxz != null">
        dwxz,
      </if>
      <if test="zjlx != null">
        zjlx,
      </if>
      <if test="zjhm != null">
        zjhm,
      </if>
      <if test="fzrXm != null">
        fzr_xm,
      </if>
      <if test="fzrZjlx != null">
        fzr_zjlx,
      </if>
      <if test="fzrZjhm != null">
        fzr_zjhm,
      </if>
      <if test="fzrZjyxqStart != null">
        fzr_zjyxq_start,
      </if>
      <if test="fzrZjyxqEnd != null">
        fzr_zjyxq_end,
      </if>
      <if test="wzmc != null">
        wzmc,
      </if>
      <if test="fwlx != null">
        fwlx,
      </if>
      <if test="nrlx != null">
        nrlx,
      </if>
      <if test="wzFzrXm != null">
        wz_fzr_xm,
      </if>
      <if test="wzFzrZjlx != null">
        wz_fzr_zjlx,
      </if>
      <if test="wzFzrZjhm != null">
        wz_fzr_zjhm,
      </if>
      <if test="wzFzrZjyxqStart != null">
        wz_fzr_zjyxq_start,
      </if>
      <if test="wzFzrZjyxqEnd != null">
        wz_fzr_zjyxq_end,
      </if>
      <if test="domain != null">
        domain,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="fzrZjyxqCqyx != null">
        fzr_zjyxq_cqyx,
      </if>
      <if test="wzFzrZjyxqCqyx != null">
        wz_fzr_zjyxq_cqyx,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ispWzid != null">
        #{ispWzid,jdbcType=BIGINT},
      </if>
      <if test="dwmc != null">
        #{dwmc,jdbcType=VARCHAR},
      </if>
      <if test="shengid != null">
        #{shengid,jdbcType=INTEGER},
      </if>
      <if test="dwxz != null">
        #{dwxz,jdbcType=TINYINT},
      </if>
      <if test="zjlx != null">
        #{zjlx,jdbcType=TINYINT},
      </if>
      <if test="zjhm != null">
        #{zjhm,jdbcType=VARCHAR},
      </if>
      <if test="fzrXm != null">
        #{fzrXm,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjlx != null">
        #{fzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="fzrZjhm != null">
        #{fzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjyxqStart != null">
        #{fzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="fzrZjyxqEnd != null">
        #{fzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="wzmc != null">
        #{wzmc,jdbcType=VARCHAR},
      </if>
      <if test="fwlx != null">
        #{fwlx,jdbcType=TINYINT},
      </if>
      <if test="nrlx != null">
        #{nrlx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrXm != null">
        #{wzFzrXm,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjlx != null">
        #{wzFzrZjlx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrZjhm != null">
        #{wzFzrZjhm,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjyxqStart != null">
        #{wzFzrZjyxqStart,jdbcType=VARCHAR},
      </if>
      <if test="wzFzrZjyxqEnd != null">
        #{wzFzrZjyxqEnd,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="fzrZjyxqCqyx != null">
        #{fzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
      <if test="wzFzrZjyxqCqyx != null">
        #{wzFzrZjyxqCqyx,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ispWzid != null">
        isp_wzid = values(isp_wzid),
      </if>
      <if test="dwmc != null">
        dwmc = values(dwmc),
      </if>
      <if test="shengid != null">
        shengid = values(shengid),
      </if>
      <if test="dwxz != null">
        dwxz = values(dwxz),
      </if>
      <if test="zjlx != null">
        zjlx = values(zjlx),
      </if>
      <if test="zjhm != null">
        zjhm = values(zjhm),
      </if>
      <if test="fzrXm != null">
        fzr_xm = values(fzr_xm),
      </if>
      <if test="fzrZjlx != null">
        fzr_zjlx = values(fzr_zjlx),
      </if>
      <if test="fzrZjhm != null">
        fzr_zjhm = values(fzr_zjhm),
      </if>
      <if test="fzrZjyxqStart != null">
        fzr_zjyxq_start = values(fzr_zjyxq_start),
      </if>
      <if test="fzrZjyxqEnd != null">
        fzr_zjyxq_end = values(fzr_zjyxq_end),
      </if>
      <if test="wzmc != null">
        wzmc = values(wzmc),
      </if>
      <if test="fwlx != null">
        fwlx = values(fwlx),
      </if>
      <if test="nrlx != null">
        nrlx = values(nrlx),
      </if>
      <if test="wzFzrXm != null">
        wz_fzr_xm = values(wz_fzr_xm),
      </if>
      <if test="wzFzrZjlx != null">
        wz_fzr_zjlx = values(wz_fzr_zjlx),
      </if>
      <if test="wzFzrZjhm != null">
        wz_fzr_zjhm = values(wz_fzr_zjhm),
      </if>
      <if test="wzFzrZjyxqStart != null">
        wz_fzr_zjyxq_start = values(wz_fzr_zjyxq_start),
      </if>
      <if test="wzFzrZjyxqEnd != null">
        wz_fzr_zjyxq_end = values(wz_fzr_zjyxq_end),
      </if>
      <if test="domain != null">
        domain = values(domain),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="fzrZjyxqCqyx != null">
        fzr_zjyxq_cqyx = values(fzr_zjyxq_cqyx),
      </if>
      <if test="wzFzrZjyxqCqyx != null">
        wz_fzr_zjyxq_cqyx = values(wz_fzr_zjyxq_cqyx),
      </if>
    </trim>
  </insert>
</mapper>