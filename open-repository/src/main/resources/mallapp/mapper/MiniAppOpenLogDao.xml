<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenLogDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="obj_id" jdbcType="BIGINT" property="objId" />
    <result column="obj_type" jdbcType="INTEGER" property="objType" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, obj_id, obj_type, operate_type, operator, ip, ctime, mtime, is_deleted
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mini_app_open_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mini_app_open_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPoExample">
    delete from mini_app_open_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_log (obj_id, obj_type, operate_type, 
      operator, ip, ctime, 
      mtime, is_deleted, content
      )
    values (#{objId,jdbcType=BIGINT}, #{objType,jdbcType=INTEGER}, #{operateType,jdbcType=INTEGER}, 
      #{operator,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        obj_id,
      </if>
      <if test="objType != null">
        obj_type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="objType != null">
        #{objType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.objId != null">
        obj_id = #{record.objId,jdbcType=BIGINT},
      </if>
      <if test="record.objType != null">
        obj_type = #{record.objType,jdbcType=INTEGER},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update mini_app_open_log
    set id = #{record.id,jdbcType=BIGINT},
      obj_id = #{record.objId,jdbcType=BIGINT},
      obj_type = #{record.objType,jdbcType=INTEGER},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      operator = #{record.operator,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_log
    set id = #{record.id,jdbcType=BIGINT},
      obj_id = #{record.objId,jdbcType=BIGINT},
      obj_type = #{record.objType,jdbcType=INTEGER},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      operator = #{record.operator,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    update mini_app_open_log
    <set>
      <if test="objId != null">
        obj_id = #{objId,jdbcType=BIGINT},
      </if>
      <if test="objType != null">
        obj_type = #{objType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    update mini_app_open_log
    set obj_id = #{objId,jdbcType=BIGINT},
      obj_type = #{objType,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      operator = #{operator,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    update mini_app_open_log
    set obj_id = #{objId,jdbcType=BIGINT},
      obj_type = #{objType,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      operator = #{operator,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_log (obj_id, obj_type, operate_type, 
      operator, ip, ctime, 
      mtime, is_deleted, content
      )
    values (#{objId,jdbcType=BIGINT}, #{objType,jdbcType=INTEGER}, #{operateType,jdbcType=INTEGER}, 
      #{operator,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      obj_id = values(obj_id),
      obj_type = values(obj_type),
      operate_type = values(operate_type),
      operator = values(operator),
      ip = values(ip),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      content = values(content),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_log
      (obj_id,obj_type,operate_type,operator,ip,ctime,mtime,is_deleted,content)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.objId,jdbcType=BIGINT},
        #{item.objType,jdbcType=INTEGER},
        #{item.operateType,jdbcType=INTEGER},
        #{item.operator,jdbcType=VARCHAR},
        #{item.ip,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.content,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_log
      (obj_id,obj_type,operate_type,operator,ip,ctime,mtime,is_deleted,content)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.objId,jdbcType=BIGINT},
        #{item.objType,jdbcType=INTEGER},
        #{item.operateType,jdbcType=INTEGER},
        #{item.operator,jdbcType=VARCHAR},
        #{item.ip,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.content,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      obj_id = values(obj_id),
      obj_type = values(obj_type),
      operate_type = values(operate_type),
      operator = values(operator),
      ip = values(ip),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      content = values(content),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        obj_id,
      </if>
      <if test="objType != null">
        obj_type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="objType != null">
        #{objType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="objId != null">
        obj_id = values(obj_id),
      </if>
      <if test="objType != null">
        obj_type = values(obj_type),
      </if>
      <if test="operateType != null">
        operate_type = values(operate_type),
      </if>
      <if test="operator != null">
        operator = values(operator),
      </if>
      <if test="ip != null">
        ip = values(ip),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="content != null">
        content = values(content),
      </if>
    </trim>
  </insert>
</mapper>