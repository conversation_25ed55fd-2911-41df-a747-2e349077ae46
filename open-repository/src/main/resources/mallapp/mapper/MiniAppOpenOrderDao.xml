<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenOrderDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="dev_order_id" jdbcType="VARCHAR" property="devOrderId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="access_key" jdbcType="VARCHAR" property="accessKey" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_desc" jdbcType="VARCHAR" property="productDesc" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="pay_amount" jdbcType="BIGINT" property="payAmount" />
    <result column="settle_ratio" jdbcType="INTEGER" property="settleRatio" />
    <result column="dist_ratio" jdbcType="INTEGER" property="distRatio" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="notify_status" jdbcType="INTEGER" property="notifyStatus" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="tx_id" jdbcType="VARCHAR" property="txId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="source_channel" jdbcType="INTEGER" property="sourceChannel" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="pay_channel" jdbcType="INTEGER" property="payChannel" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, dev_order_id, app_id, open_id, mid, access_key, product_type, product_id, 
    product_name, product_desc, amount, pay_amount, settle_ratio, dist_ratio, order_status, 
    pay_status, settle_status, notify_status, notify_url, tx_id, platform, source_channel, 
    trace_id, pay_channel, pay_time, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPoExample">
    delete from mini_app_open_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order (order_id, dev_order_id, app_id, 
      open_id, mid, access_key, 
      product_type, product_id, product_name, 
      product_desc, amount, pay_amount, 
      settle_ratio, dist_ratio, order_status, 
      pay_status, settle_status, notify_status, 
      notify_url, tx_id, platform, 
      source_channel, trace_id, pay_channel, 
      pay_time, ctime, mtime, 
      is_deleted)
    values (#{orderId,jdbcType=BIGINT}, #{devOrderId,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{openId,jdbcType=VARCHAR}, #{mid,jdbcType=BIGINT}, #{accessKey,jdbcType=VARCHAR}, 
      #{productType,jdbcType=INTEGER}, #{productId,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{productDesc,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{payAmount,jdbcType=BIGINT}, 
      #{settleRatio,jdbcType=INTEGER}, #{distRatio,jdbcType=INTEGER}, #{orderStatus,jdbcType=INTEGER}, 
      #{payStatus,jdbcType=INTEGER}, #{settleStatus,jdbcType=INTEGER}, #{notifyStatus,jdbcType=INTEGER}, 
      #{notifyUrl,jdbcType=VARCHAR}, #{txId,jdbcType=VARCHAR}, #{platform,jdbcType=INTEGER}, 
      #{sourceChannel,jdbcType=INTEGER}, #{traceId,jdbcType=VARCHAR}, #{payChannel,jdbcType=INTEGER}, 
      #{payTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="devOrderId != null">
        dev_order_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="accessKey != null">
        access_key,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productDesc != null">
        product_desc,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="settleRatio != null">
        settle_ratio,
      </if>
      <if test="distRatio != null">
        dist_ratio,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="notifyStatus != null">
        notify_status,
      </if>
      <if test="notifyUrl != null">
        notify_url,
      </if>
      <if test="txId != null">
        tx_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="sourceChannel != null">
        source_channel,
      </if>
      <if test="traceId != null">
        trace_id,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devOrderId != null">
        #{devOrderId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="accessKey != null">
        #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="settleRatio != null">
        #{settleRatio,jdbcType=INTEGER},
      </if>
      <if test="distRatio != null">
        #{distRatio,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyStatus != null">
        #{notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="txId != null">
        #{txId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="sourceChannel != null">
        #{sourceChannel,jdbcType=INTEGER},
      </if>
      <if test="traceId != null">
        #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.devOrderId != null">
        dev_order_id = #{record.devOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.openId != null">
        open_id = #{record.openId,jdbcType=VARCHAR},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.accessKey != null">
        access_key = #{record.accessKey,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.productDesc != null">
        product_desc = #{record.productDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=BIGINT},
      </if>
      <if test="record.settleRatio != null">
        settle_ratio = #{record.settleRatio,jdbcType=INTEGER},
      </if>
      <if test="record.distRatio != null">
        dist_ratio = #{record.distRatio,jdbcType=INTEGER},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=INTEGER},
      </if>
      <if test="record.settleStatus != null">
        settle_status = #{record.settleStatus,jdbcType=INTEGER},
      </if>
      <if test="record.notifyStatus != null">
        notify_status = #{record.notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.notifyUrl != null">
        notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.txId != null">
        tx_id = #{record.txId,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.sourceChannel != null">
        source_channel = #{record.sourceChannel,jdbcType=INTEGER},
      </if>
      <if test="record.traceId != null">
        trace_id = #{record.traceId,jdbcType=VARCHAR},
      </if>
      <if test="record.payChannel != null">
        pay_channel = #{record.payChannel,jdbcType=INTEGER},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_order
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      dev_order_id = #{record.devOrderId,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      open_id = #{record.openId,jdbcType=VARCHAR},
      mid = #{record.mid,jdbcType=BIGINT},
      access_key = #{record.accessKey,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_desc = #{record.productDesc,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      pay_amount = #{record.payAmount,jdbcType=BIGINT},
      settle_ratio = #{record.settleRatio,jdbcType=INTEGER},
      dist_ratio = #{record.distRatio,jdbcType=INTEGER},
      order_status = #{record.orderStatus,jdbcType=INTEGER},
      pay_status = #{record.payStatus,jdbcType=INTEGER},
      settle_status = #{record.settleStatus,jdbcType=INTEGER},
      notify_status = #{record.notifyStatus,jdbcType=INTEGER},
      notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      tx_id = #{record.txId,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=INTEGER},
      source_channel = #{record.sourceChannel,jdbcType=INTEGER},
      trace_id = #{record.traceId,jdbcType=VARCHAR},
      pay_channel = #{record.payChannel,jdbcType=INTEGER},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    update mini_app_open_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devOrderId != null">
        dev_order_id = #{devOrderId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="accessKey != null">
        access_key = #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        product_desc = #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="settleRatio != null">
        settle_ratio = #{settleRatio,jdbcType=INTEGER},
      </if>
      <if test="distRatio != null">
        dist_ratio = #{distRatio,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyStatus != null">
        notify_status = #{notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        notify_url = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="txId != null">
        tx_id = #{txId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="sourceChannel != null">
        source_channel = #{sourceChannel,jdbcType=INTEGER},
      </if>
      <if test="traceId != null">
        trace_id = #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    update mini_app_open_order
    set order_id = #{orderId,jdbcType=BIGINT},
      dev_order_id = #{devOrderId,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      open_id = #{openId,jdbcType=VARCHAR},
      mid = #{mid,jdbcType=BIGINT},
      access_key = #{accessKey,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_desc = #{productDesc,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      pay_amount = #{payAmount,jdbcType=BIGINT},
      settle_ratio = #{settleRatio,jdbcType=INTEGER},
      dist_ratio = #{distRatio,jdbcType=INTEGER},
      order_status = #{orderStatus,jdbcType=INTEGER},
      pay_status = #{payStatus,jdbcType=INTEGER},
      settle_status = #{settleStatus,jdbcType=INTEGER},
      notify_status = #{notifyStatus,jdbcType=INTEGER},
      notify_url = #{notifyUrl,jdbcType=VARCHAR},
      tx_id = #{txId,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=INTEGER},
      source_channel = #{sourceChannel,jdbcType=INTEGER},
      trace_id = #{traceId,jdbcType=VARCHAR},
      pay_channel = #{payChannel,jdbcType=INTEGER},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order (order_id, dev_order_id, app_id, 
      open_id, mid, access_key, 
      product_type, product_id, product_name, 
      product_desc, amount, pay_amount, 
      settle_ratio, dist_ratio, order_status, 
      pay_status, settle_status, notify_status, 
      notify_url, tx_id, platform, 
      source_channel, trace_id, pay_channel, 
      pay_time, ctime, mtime, 
      is_deleted)
    values (#{orderId,jdbcType=BIGINT}, #{devOrderId,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{openId,jdbcType=VARCHAR}, #{mid,jdbcType=BIGINT}, #{accessKey,jdbcType=VARCHAR}, 
      #{productType,jdbcType=INTEGER}, #{productId,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{productDesc,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{payAmount,jdbcType=BIGINT}, 
      #{settleRatio,jdbcType=INTEGER}, #{distRatio,jdbcType=INTEGER}, #{orderStatus,jdbcType=INTEGER}, 
      #{payStatus,jdbcType=INTEGER}, #{settleStatus,jdbcType=INTEGER}, #{notifyStatus,jdbcType=INTEGER}, 
      #{notifyUrl,jdbcType=VARCHAR}, #{txId,jdbcType=VARCHAR}, #{platform,jdbcType=INTEGER}, 
      #{sourceChannel,jdbcType=INTEGER}, #{traceId,jdbcType=VARCHAR}, #{payChannel,jdbcType=INTEGER}, 
      #{payTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      dev_order_id = values(dev_order_id),
      app_id = values(app_id),
      open_id = values(open_id),
      mid = values(mid),
      access_key = values(access_key),
      product_type = values(product_type),
      product_id = values(product_id),
      product_name = values(product_name),
      product_desc = values(product_desc),
      amount = values(amount),
      pay_amount = values(pay_amount),
      settle_ratio = values(settle_ratio),
      dist_ratio = values(dist_ratio),
      order_status = values(order_status),
      pay_status = values(pay_status),
      settle_status = values(settle_status),
      notify_status = values(notify_status),
      notify_url = values(notify_url),
      tx_id = values(tx_id),
      platform = values(platform),
      source_channel = values(source_channel),
      trace_id = values(trace_id),
      pay_channel = values(pay_channel),
      pay_time = values(pay_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_order
      (order_id,dev_order_id,app_id,open_id,mid,access_key,product_type,product_id,product_name,product_desc,amount,pay_amount,settle_ratio,dist_ratio,order_status,pay_status,settle_status,notify_status,notify_url,tx_id,platform,source_channel,trace_id,pay_channel,pay_time,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.devOrderId,jdbcType=VARCHAR},
        #{item.appId,jdbcType=VARCHAR},
        #{item.openId,jdbcType=VARCHAR},
        #{item.mid,jdbcType=BIGINT},
        #{item.accessKey,jdbcType=VARCHAR},
        #{item.productType,jdbcType=INTEGER},
        #{item.productId,jdbcType=VARCHAR},
        #{item.productName,jdbcType=VARCHAR},
        #{item.productDesc,jdbcType=VARCHAR},
        #{item.amount,jdbcType=BIGINT},
        #{item.payAmount,jdbcType=BIGINT},
        #{item.settleRatio,jdbcType=INTEGER},
        #{item.distRatio,jdbcType=INTEGER},
        #{item.orderStatus,jdbcType=INTEGER},
        #{item.payStatus,jdbcType=INTEGER},
        #{item.settleStatus,jdbcType=INTEGER},
        #{item.notifyStatus,jdbcType=INTEGER},
        #{item.notifyUrl,jdbcType=VARCHAR},
        #{item.txId,jdbcType=VARCHAR},
        #{item.platform,jdbcType=INTEGER},
        #{item.sourceChannel,jdbcType=INTEGER},
        #{item.traceId,jdbcType=VARCHAR},
        #{item.payChannel,jdbcType=INTEGER},
        #{item.payTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_order
      (order_id,dev_order_id,app_id,open_id,mid,access_key,product_type,product_id,product_name,product_desc,amount,pay_amount,settle_ratio,dist_ratio,order_status,pay_status,settle_status,notify_status,notify_url,tx_id,platform,source_channel,trace_id,pay_channel,pay_time,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.devOrderId,jdbcType=VARCHAR},
        #{item.appId,jdbcType=VARCHAR},
        #{item.openId,jdbcType=VARCHAR},
        #{item.mid,jdbcType=BIGINT},
        #{item.accessKey,jdbcType=VARCHAR},
        #{item.productType,jdbcType=INTEGER},
        #{item.productId,jdbcType=VARCHAR},
        #{item.productName,jdbcType=VARCHAR},
        #{item.productDesc,jdbcType=VARCHAR},
        #{item.amount,jdbcType=BIGINT},
        #{item.payAmount,jdbcType=BIGINT},
        #{item.settleRatio,jdbcType=INTEGER},
        #{item.distRatio,jdbcType=INTEGER},
        #{item.orderStatus,jdbcType=INTEGER},
        #{item.payStatus,jdbcType=INTEGER},
        #{item.settleStatus,jdbcType=INTEGER},
        #{item.notifyStatus,jdbcType=INTEGER},
        #{item.notifyUrl,jdbcType=VARCHAR},
        #{item.txId,jdbcType=VARCHAR},
        #{item.platform,jdbcType=INTEGER},
        #{item.sourceChannel,jdbcType=INTEGER},
        #{item.traceId,jdbcType=VARCHAR},
        #{item.payChannel,jdbcType=INTEGER},
        #{item.payTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      dev_order_id = values(dev_order_id),
      app_id = values(app_id),
      open_id = values(open_id),
      mid = values(mid),
      access_key = values(access_key),
      product_type = values(product_type),
      product_id = values(product_id),
      product_name = values(product_name),
      product_desc = values(product_desc),
      amount = values(amount),
      pay_amount = values(pay_amount),
      settle_ratio = values(settle_ratio),
      dist_ratio = values(dist_ratio),
      order_status = values(order_status),
      pay_status = values(pay_status),
      settle_status = values(settle_status),
      notify_status = values(notify_status),
      notify_url = values(notify_url),
      tx_id = values(tx_id),
      platform = values(platform),
      source_channel = values(source_channel),
      trace_id = values(trace_id),
      pay_channel = values(pay_channel),
      pay_time = values(pay_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="devOrderId != null">
        dev_order_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="accessKey != null">
        access_key,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productDesc != null">
        product_desc,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="settleRatio != null">
        settle_ratio,
      </if>
      <if test="distRatio != null">
        dist_ratio,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="notifyStatus != null">
        notify_status,
      </if>
      <if test="notifyUrl != null">
        notify_url,
      </if>
      <if test="txId != null">
        tx_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="sourceChannel != null">
        source_channel,
      </if>
      <if test="traceId != null">
        trace_id,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devOrderId != null">
        #{devOrderId,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="accessKey != null">
        #{accessKey,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="settleRatio != null">
        #{settleRatio,jdbcType=INTEGER},
      </if>
      <if test="distRatio != null">
        #{distRatio,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyStatus != null">
        #{notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="txId != null">
        #{txId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="sourceChannel != null">
        #{sourceChannel,jdbcType=INTEGER},
      </if>
      <if test="traceId != null">
        #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=INTEGER},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="devOrderId != null">
        dev_order_id = values(dev_order_id),
      </if>
      <if test="appId != null">
        app_id = values(app_id),
      </if>
      <if test="openId != null">
        open_id = values(open_id),
      </if>
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="accessKey != null">
        access_key = values(access_key),
      </if>
      <if test="productType != null">
        product_type = values(product_type),
      </if>
      <if test="productId != null">
        product_id = values(product_id),
      </if>
      <if test="productName != null">
        product_name = values(product_name),
      </if>
      <if test="productDesc != null">
        product_desc = values(product_desc),
      </if>
      <if test="amount != null">
        amount = values(amount),
      </if>
      <if test="payAmount != null">
        pay_amount = values(pay_amount),
      </if>
      <if test="settleRatio != null">
        settle_ratio = values(settle_ratio),
      </if>
      <if test="distRatio != null">
        dist_ratio = values(dist_ratio),
      </if>
      <if test="orderStatus != null">
        order_status = values(order_status),
      </if>
      <if test="payStatus != null">
        pay_status = values(pay_status),
      </if>
      <if test="settleStatus != null">
        settle_status = values(settle_status),
      </if>
      <if test="notifyStatus != null">
        notify_status = values(notify_status),
      </if>
      <if test="notifyUrl != null">
        notify_url = values(notify_url),
      </if>
      <if test="txId != null">
        tx_id = values(tx_id),
      </if>
      <if test="platform != null">
        platform = values(platform),
      </if>
      <if test="sourceChannel != null">
        source_channel = values(source_channel),
      </if>
      <if test="traceId != null">
        trace_id = values(trace_id),
      </if>
      <if test="payChannel != null">
        pay_channel = values(pay_channel),
      </if>
      <if test="payTime != null">
        pay_time = values(pay_time),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>