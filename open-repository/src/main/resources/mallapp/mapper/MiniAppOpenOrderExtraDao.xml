<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenOrderExtraDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="dev_extra_data" jdbcType="VARCHAR" property="devExtraData" />
    <result column="trace_info" jdbcType="VARCHAR" property="traceInfo" />
    <result column="pay_param_info" jdbcType="VARCHAR" property="payParamInfo" />
    <result column="pay_notify_info" jdbcType="VARCHAR" property="payNotifyInfo" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, dev_extra_data, trace_info, pay_param_info, pay_notify_info, ctime, 
    mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_order_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_order_extra
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_order_extra
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPoExample">
    delete from mini_app_open_order_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order_extra (order_id, dev_extra_data, trace_info, 
      pay_param_info, pay_notify_info, ctime, 
      mtime, is_deleted)
    values (#{orderId,jdbcType=BIGINT}, #{devExtraData,jdbcType=VARCHAR}, #{traceInfo,jdbcType=VARCHAR}, 
      #{payParamInfo,jdbcType=VARCHAR}, #{payNotifyInfo,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="devExtraData != null">
        dev_extra_data,
      </if>
      <if test="traceInfo != null">
        trace_info,
      </if>
      <if test="payParamInfo != null">
        pay_param_info,
      </if>
      <if test="payNotifyInfo != null">
        pay_notify_info,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devExtraData != null">
        #{devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="traceInfo != null">
        #{traceInfo,jdbcType=VARCHAR},
      </if>
      <if test="payParamInfo != null">
        #{payParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="payNotifyInfo != null">
        #{payNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_order_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_order_extra
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.devExtraData != null">
        dev_extra_data = #{record.devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="record.traceInfo != null">
        trace_info = #{record.traceInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.payParamInfo != null">
        pay_param_info = #{record.payParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.payNotifyInfo != null">
        pay_notify_info = #{record.payNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_order_extra
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      dev_extra_data = #{record.devExtraData,jdbcType=VARCHAR},
      trace_info = #{record.traceInfo,jdbcType=VARCHAR},
      pay_param_info = #{record.payParamInfo,jdbcType=VARCHAR},
      pay_notify_info = #{record.payNotifyInfo,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    update mini_app_open_order_extra
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devExtraData != null">
        dev_extra_data = #{devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="traceInfo != null">
        trace_info = #{traceInfo,jdbcType=VARCHAR},
      </if>
      <if test="payParamInfo != null">
        pay_param_info = #{payParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="payNotifyInfo != null">
        pay_notify_info = #{payNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    update mini_app_open_order_extra
    set order_id = #{orderId,jdbcType=BIGINT},
      dev_extra_data = #{devExtraData,jdbcType=VARCHAR},
      trace_info = #{traceInfo,jdbcType=VARCHAR},
      pay_param_info = #{payParamInfo,jdbcType=VARCHAR},
      pay_notify_info = #{payNotifyInfo,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order_extra (order_id, dev_extra_data, trace_info, 
      pay_param_info, pay_notify_info, ctime, 
      mtime, is_deleted)
    values (#{orderId,jdbcType=BIGINT}, #{devExtraData,jdbcType=VARCHAR}, #{traceInfo,jdbcType=VARCHAR}, 
      #{payParamInfo,jdbcType=VARCHAR}, #{payNotifyInfo,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      dev_extra_data = values(dev_extra_data),
      trace_info = values(trace_info),
      pay_param_info = values(pay_param_info),
      pay_notify_info = values(pay_notify_info),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_order_extra
      (order_id,dev_extra_data,trace_info,pay_param_info,pay_notify_info,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.devExtraData,jdbcType=VARCHAR},
        #{item.traceInfo,jdbcType=VARCHAR},
        #{item.payParamInfo,jdbcType=VARCHAR},
        #{item.payNotifyInfo,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_order_extra
      (order_id,dev_extra_data,trace_info,pay_param_info,pay_notify_info,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.devExtraData,jdbcType=VARCHAR},
        #{item.traceInfo,jdbcType=VARCHAR},
        #{item.payParamInfo,jdbcType=VARCHAR},
        #{item.payNotifyInfo,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      dev_extra_data = values(dev_extra_data),
      trace_info = values(trace_info),
      pay_param_info = values(pay_param_info),
      pay_notify_info = values(pay_notify_info),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_order_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="devExtraData != null">
        dev_extra_data,
      </if>
      <if test="traceInfo != null">
        trace_info,
      </if>
      <if test="payParamInfo != null">
        pay_param_info,
      </if>
      <if test="payNotifyInfo != null">
        pay_notify_info,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devExtraData != null">
        #{devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="traceInfo != null">
        #{traceInfo,jdbcType=VARCHAR},
      </if>
      <if test="payParamInfo != null">
        #{payParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="payNotifyInfo != null">
        #{payNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="devExtraData != null">
        dev_extra_data = values(dev_extra_data),
      </if>
      <if test="traceInfo != null">
        trace_info = values(trace_info),
      </if>
      <if test="payParamInfo != null">
        pay_param_info = values(pay_param_info),
      </if>
      <if test="payNotifyInfo != null">
        pay_notify_info = values(pay_notify_info),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>