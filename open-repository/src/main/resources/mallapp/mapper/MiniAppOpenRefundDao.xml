<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRefundDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="dev_refund_id" jdbcType="VARCHAR" property="devRefundId" />
    <result column="pay_platform_refund_id" jdbcType="VARCHAR" property="payPlatformRefundId" />
    <result column="refund_amount" jdbcType="BIGINT" property="refundAmount" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="notify_status" jdbcType="INTEGER" property="notifyStatus" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="refund_desc" jdbcType="VARCHAR" property="refundDesc" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, dev_refund_id, pay_platform_refund_id, refund_amount, refund_status, 
    notify_status, notify_url, refund_desc, trace_id, refund_time, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_refund
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_refund
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPoExample">
    delete from mini_app_open_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund (order_id, dev_refund_id, pay_platform_refund_id, 
      refund_amount, refund_status, notify_status, 
      notify_url, refund_desc, trace_id, 
      refund_time, ctime, mtime, 
      is_deleted)
    values (#{orderId,jdbcType=BIGINT}, #{devRefundId,jdbcType=VARCHAR}, #{payPlatformRefundId,jdbcType=VARCHAR}, 
      #{refundAmount,jdbcType=BIGINT}, #{refundStatus,jdbcType=INTEGER}, #{notifyStatus,jdbcType=INTEGER}, 
      #{notifyUrl,jdbcType=VARCHAR}, #{refundDesc,jdbcType=VARCHAR}, #{traceId,jdbcType=VARCHAR}, 
      #{refundTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="devRefundId != null">
        dev_refund_id,
      </if>
      <if test="payPlatformRefundId != null">
        pay_platform_refund_id,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="notifyStatus != null">
        notify_status,
      </if>
      <if test="notifyUrl != null">
        notify_url,
      </if>
      <if test="refundDesc != null">
        refund_desc,
      </if>
      <if test="traceId != null">
        trace_id,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devRefundId != null">
        #{devRefundId,jdbcType=VARCHAR},
      </if>
      <if test="payPlatformRefundId != null">
        #{payPlatformRefundId,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyStatus != null">
        #{notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="refundDesc != null">
        #{refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_refund
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.devRefundId != null">
        dev_refund_id = #{record.devRefundId,jdbcType=VARCHAR},
      </if>
      <if test="record.payPlatformRefundId != null">
        pay_platform_refund_id = #{record.payPlatformRefundId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.refundStatus != null">
        refund_status = #{record.refundStatus,jdbcType=INTEGER},
      </if>
      <if test="record.notifyStatus != null">
        notify_status = #{record.notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.notifyUrl != null">
        notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.refundDesc != null">
        refund_desc = #{record.refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.traceId != null">
        trace_id = #{record.traceId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_refund
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      dev_refund_id = #{record.devRefundId,jdbcType=VARCHAR},
      pay_platform_refund_id = #{record.payPlatformRefundId,jdbcType=VARCHAR},
      refund_amount = #{record.refundAmount,jdbcType=BIGINT},
      refund_status = #{record.refundStatus,jdbcType=INTEGER},
      notify_status = #{record.notifyStatus,jdbcType=INTEGER},
      notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      refund_desc = #{record.refundDesc,jdbcType=VARCHAR},
      trace_id = #{record.traceId,jdbcType=VARCHAR},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    update mini_app_open_refund
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devRefundId != null">
        dev_refund_id = #{devRefundId,jdbcType=VARCHAR},
      </if>
      <if test="payPlatformRefundId != null">
        pay_platform_refund_id = #{payPlatformRefundId,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyStatus != null">
        notify_status = #{notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        notify_url = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="refundDesc != null">
        refund_desc = #{refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        trace_id = #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    update mini_app_open_refund
    set order_id = #{orderId,jdbcType=BIGINT},
      dev_refund_id = #{devRefundId,jdbcType=VARCHAR},
      pay_platform_refund_id = #{payPlatformRefundId,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=BIGINT},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      notify_status = #{notifyStatus,jdbcType=INTEGER},
      notify_url = #{notifyUrl,jdbcType=VARCHAR},
      refund_desc = #{refundDesc,jdbcType=VARCHAR},
      trace_id = #{traceId,jdbcType=VARCHAR},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund (order_id, dev_refund_id, pay_platform_refund_id, 
      refund_amount, refund_status, notify_status, 
      notify_url, refund_desc, trace_id, 
      refund_time, ctime, mtime, 
      is_deleted)
    values (#{orderId,jdbcType=BIGINT}, #{devRefundId,jdbcType=VARCHAR}, #{payPlatformRefundId,jdbcType=VARCHAR}, 
      #{refundAmount,jdbcType=BIGINT}, #{refundStatus,jdbcType=INTEGER}, #{notifyStatus,jdbcType=INTEGER}, 
      #{notifyUrl,jdbcType=VARCHAR}, #{refundDesc,jdbcType=VARCHAR}, #{traceId,jdbcType=VARCHAR}, 
      #{refundTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      dev_refund_id = values(dev_refund_id),
      pay_platform_refund_id = values(pay_platform_refund_id),
      refund_amount = values(refund_amount),
      refund_status = values(refund_status),
      notify_status = values(notify_status),
      notify_url = values(notify_url),
      refund_desc = values(refund_desc),
      trace_id = values(trace_id),
      refund_time = values(refund_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_refund
      (order_id,dev_refund_id,pay_platform_refund_id,refund_amount,refund_status,notify_status,notify_url,refund_desc,trace_id,refund_time,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.devRefundId,jdbcType=VARCHAR},
        #{item.payPlatformRefundId,jdbcType=VARCHAR},
        #{item.refundAmount,jdbcType=BIGINT},
        #{item.refundStatus,jdbcType=INTEGER},
        #{item.notifyStatus,jdbcType=INTEGER},
        #{item.notifyUrl,jdbcType=VARCHAR},
        #{item.refundDesc,jdbcType=VARCHAR},
        #{item.traceId,jdbcType=VARCHAR},
        #{item.refundTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_refund
      (order_id,dev_refund_id,pay_platform_refund_id,refund_amount,refund_status,notify_status,notify_url,refund_desc,trace_id,refund_time,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.devRefundId,jdbcType=VARCHAR},
        #{item.payPlatformRefundId,jdbcType=VARCHAR},
        #{item.refundAmount,jdbcType=BIGINT},
        #{item.refundStatus,jdbcType=INTEGER},
        #{item.notifyStatus,jdbcType=INTEGER},
        #{item.notifyUrl,jdbcType=VARCHAR},
        #{item.refundDesc,jdbcType=VARCHAR},
        #{item.traceId,jdbcType=VARCHAR},
        #{item.refundTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      dev_refund_id = values(dev_refund_id),
      pay_platform_refund_id = values(pay_platform_refund_id),
      refund_amount = values(refund_amount),
      refund_status = values(refund_status),
      notify_status = values(notify_status),
      notify_url = values(notify_url),
      refund_desc = values(refund_desc),
      trace_id = values(trace_id),
      refund_time = values(refund_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="devRefundId != null">
        dev_refund_id,
      </if>
      <if test="payPlatformRefundId != null">
        pay_platform_refund_id,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="notifyStatus != null">
        notify_status,
      </if>
      <if test="notifyUrl != null">
        notify_url,
      </if>
      <if test="refundDesc != null">
        refund_desc,
      </if>
      <if test="traceId != null">
        trace_id,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="devRefundId != null">
        #{devRefundId,jdbcType=VARCHAR},
      </if>
      <if test="payPlatformRefundId != null">
        #{payPlatformRefundId,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyStatus != null">
        #{notifyStatus,jdbcType=INTEGER},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="refundDesc != null">
        #{refundDesc,jdbcType=VARCHAR},
      </if>
      <if test="traceId != null">
        #{traceId,jdbcType=VARCHAR},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="devRefundId != null">
        dev_refund_id = values(dev_refund_id),
      </if>
      <if test="payPlatformRefundId != null">
        pay_platform_refund_id = values(pay_platform_refund_id),
      </if>
      <if test="refundAmount != null">
        refund_amount = values(refund_amount),
      </if>
      <if test="refundStatus != null">
        refund_status = values(refund_status),
      </if>
      <if test="notifyStatus != null">
        notify_status = values(notify_status),
      </if>
      <if test="notifyUrl != null">
        notify_url = values(notify_url),
      </if>
      <if test="refundDesc != null">
        refund_desc = values(refund_desc),
      </if>
      <if test="traceId != null">
        trace_id = values(trace_id),
      </if>
      <if test="refundTime != null">
        refund_time = values(refund_time),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>