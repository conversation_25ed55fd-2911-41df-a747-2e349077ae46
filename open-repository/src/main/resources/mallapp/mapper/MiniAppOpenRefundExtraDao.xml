<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRefundExtraDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="refund_id" jdbcType="BIGINT" property="refundId" />
    <result column="dev_extra_data" jdbcType="VARCHAR" property="devExtraData" />
    <result column="refund_param_info" jdbcType="VARCHAR" property="refundParamInfo" />
    <result column="refund_notify_info" jdbcType="VARCHAR" property="refundNotifyInfo" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, refund_id, dev_extra_data, refund_param_info, refund_notify_info, ctime, mtime, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_refund_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_refund_extra
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_refund_extra
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPoExample">
    delete from mini_app_open_refund_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund_extra (refund_id, dev_extra_data, refund_param_info, 
      refund_notify_info, ctime, mtime, 
      is_deleted)
    values (#{refundId,jdbcType=BIGINT}, #{devExtraData,jdbcType=VARCHAR}, #{refundParamInfo,jdbcType=VARCHAR}, 
      #{refundNotifyInfo,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="devExtraData != null">
        dev_extra_data,
      </if>
      <if test="refundParamInfo != null">
        refund_param_info,
      </if>
      <if test="refundNotifyInfo != null">
        refund_notify_info,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refundId != null">
        #{refundId,jdbcType=BIGINT},
      </if>
      <if test="devExtraData != null">
        #{devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="refundParamInfo != null">
        #{refundParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="refundNotifyInfo != null">
        #{refundNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_refund_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_refund_extra
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.refundId != null">
        refund_id = #{record.refundId,jdbcType=BIGINT},
      </if>
      <if test="record.devExtraData != null">
        dev_extra_data = #{record.devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="record.refundParamInfo != null">
        refund_param_info = #{record.refundParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.refundNotifyInfo != null">
        refund_notify_info = #{record.refundNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_refund_extra
    set id = #{record.id,jdbcType=BIGINT},
      refund_id = #{record.refundId,jdbcType=BIGINT},
      dev_extra_data = #{record.devExtraData,jdbcType=VARCHAR},
      refund_param_info = #{record.refundParamInfo,jdbcType=VARCHAR},
      refund_notify_info = #{record.refundNotifyInfo,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    update mini_app_open_refund_extra
    <set>
      <if test="refundId != null">
        refund_id = #{refundId,jdbcType=BIGINT},
      </if>
      <if test="devExtraData != null">
        dev_extra_data = #{devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="refundParamInfo != null">
        refund_param_info = #{refundParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="refundNotifyInfo != null">
        refund_notify_info = #{refundNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    update mini_app_open_refund_extra
    set refund_id = #{refundId,jdbcType=BIGINT},
      dev_extra_data = #{devExtraData,jdbcType=VARCHAR},
      refund_param_info = #{refundParamInfo,jdbcType=VARCHAR},
      refund_notify_info = #{refundNotifyInfo,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund_extra (refund_id, dev_extra_data, refund_param_info, 
      refund_notify_info, ctime, mtime, 
      is_deleted)
    values (#{refundId,jdbcType=BIGINT}, #{devExtraData,jdbcType=VARCHAR}, #{refundParamInfo,jdbcType=VARCHAR}, 
      #{refundNotifyInfo,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      refund_id = values(refund_id),
      dev_extra_data = values(dev_extra_data),
      refund_param_info = values(refund_param_info),
      refund_notify_info = values(refund_notify_info),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_refund_extra
      (refund_id,dev_extra_data,refund_param_info,refund_notify_info,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.refundId,jdbcType=BIGINT},
        #{item.devExtraData,jdbcType=VARCHAR},
        #{item.refundParamInfo,jdbcType=VARCHAR},
        #{item.refundNotifyInfo,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_refund_extra
      (refund_id,dev_extra_data,refund_param_info,refund_notify_info,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.refundId,jdbcType=BIGINT},
        #{item.devExtraData,jdbcType=VARCHAR},
        #{item.refundParamInfo,jdbcType=VARCHAR},
        #{item.refundNotifyInfo,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      refund_id = values(refund_id),
      dev_extra_data = values(dev_extra_data),
      refund_param_info = values(refund_param_info),
      refund_notify_info = values(refund_notify_info),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_refund_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refundId != null">
        refund_id,
      </if>
      <if test="devExtraData != null">
        dev_extra_data,
      </if>
      <if test="refundParamInfo != null">
        refund_param_info,
      </if>
      <if test="refundNotifyInfo != null">
        refund_notify_info,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refundId != null">
        #{refundId,jdbcType=BIGINT},
      </if>
      <if test="devExtraData != null">
        #{devExtraData,jdbcType=VARCHAR},
      </if>
      <if test="refundParamInfo != null">
        #{refundParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="refundNotifyInfo != null">
        #{refundNotifyInfo,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="refundId != null">
        refund_id = values(refund_id),
      </if>
      <if test="devExtraData != null">
        dev_extra_data = values(dev_extra_data),
      </if>
      <if test="refundParamInfo != null">
        refund_param_info = values(refund_param_info),
      </if>
      <if test="refundNotifyInfo != null">
        refund_notify_info = values(refund_notify_info),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>