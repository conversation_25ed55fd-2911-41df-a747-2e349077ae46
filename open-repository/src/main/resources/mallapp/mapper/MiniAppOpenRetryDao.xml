<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRetryDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="biz_data" jdbcType="VARCHAR" property="bizData" />
    <result column="req_id" jdbcType="VARCHAR" property="reqId" />
    <result column="retry_status" jdbcType="INTEGER" property="retryStatus" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="next_time" jdbcType="TIMESTAMP" property="nextTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_id, biz_type, biz_data, req_id, retry_status, retry_count, next_time, ctime, 
    mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_retry
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_retry
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_retry
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPoExample">
    delete from mini_app_open_retry
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_retry (biz_id, biz_type, biz_data, 
      req_id, retry_status, retry_count, 
      next_time, ctime, mtime, 
      is_deleted)
    values (#{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=INTEGER}, #{bizData,jdbcType=VARCHAR}, 
      #{reqId,jdbcType=VARCHAR}, #{retryStatus,jdbcType=INTEGER}, #{retryCount,jdbcType=INTEGER}, 
      #{nextTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_retry
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizData != null">
        biz_data,
      </if>
      <if test="reqId != null">
        req_id,
      </if>
      <if test="retryStatus != null">
        retry_status,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="nextTime != null">
        next_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizData != null">
        #{bizData,jdbcType=VARCHAR},
      </if>
      <if test="reqId != null">
        #{reqId,jdbcType=VARCHAR},
      </if>
      <if test="retryStatus != null">
        #{retryStatus,jdbcType=INTEGER},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="nextTime != null">
        #{nextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_retry
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_retry
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.bizData != null">
        biz_data = #{record.bizData,jdbcType=VARCHAR},
      </if>
      <if test="record.reqId != null">
        req_id = #{record.reqId,jdbcType=VARCHAR},
      </if>
      <if test="record.retryStatus != null">
        retry_status = #{record.retryStatus,jdbcType=INTEGER},
      </if>
      <if test="record.retryCount != null">
        retry_count = #{record.retryCount,jdbcType=INTEGER},
      </if>
      <if test="record.nextTime != null">
        next_time = #{record.nextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_retry
    set id = #{record.id,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      biz_data = #{record.bizData,jdbcType=VARCHAR},
      req_id = #{record.reqId,jdbcType=VARCHAR},
      retry_status = #{record.retryStatus,jdbcType=INTEGER},
      retry_count = #{record.retryCount,jdbcType=INTEGER},
      next_time = #{record.nextTime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    update mini_app_open_retry
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizData != null">
        biz_data = #{bizData,jdbcType=VARCHAR},
      </if>
      <if test="reqId != null">
        req_id = #{reqId,jdbcType=VARCHAR},
      </if>
      <if test="retryStatus != null">
        retry_status = #{retryStatus,jdbcType=INTEGER},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="nextTime != null">
        next_time = #{nextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    update mini_app_open_retry
    set biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=INTEGER},
      biz_data = #{bizData,jdbcType=VARCHAR},
      req_id = #{reqId,jdbcType=VARCHAR},
      retry_status = #{retryStatus,jdbcType=INTEGER},
      retry_count = #{retryCount,jdbcType=INTEGER},
      next_time = #{nextTime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_retry (biz_id, biz_type, biz_data, 
      req_id, retry_status, retry_count, 
      next_time, ctime, mtime, 
      is_deleted)
    values (#{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=INTEGER}, #{bizData,jdbcType=VARCHAR}, 
      #{reqId,jdbcType=VARCHAR}, #{retryStatus,jdbcType=INTEGER}, #{retryCount,jdbcType=INTEGER}, 
      #{nextTime,jdbcType=TIMESTAMP}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      biz_id = values(biz_id),
      biz_type = values(biz_type),
      biz_data = values(biz_data),
      req_id = values(req_id),
      retry_status = values(retry_status),
      retry_count = values(retry_count),
      next_time = values(next_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_retry
      (biz_id,biz_type,biz_data,req_id,retry_status,retry_count,next_time,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bizId,jdbcType=BIGINT},
        #{item.bizType,jdbcType=INTEGER},
        #{item.bizData,jdbcType=VARCHAR},
        #{item.reqId,jdbcType=VARCHAR},
        #{item.retryStatus,jdbcType=INTEGER},
        #{item.retryCount,jdbcType=INTEGER},
        #{item.nextTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_retry
      (biz_id,biz_type,biz_data,req_id,retry_status,retry_count,next_time,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bizId,jdbcType=BIGINT},
        #{item.bizType,jdbcType=INTEGER},
        #{item.bizData,jdbcType=VARCHAR},
        #{item.reqId,jdbcType=VARCHAR},
        #{item.retryStatus,jdbcType=INTEGER},
        #{item.retryCount,jdbcType=INTEGER},
        #{item.nextTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      biz_id = values(biz_id),
      biz_type = values(biz_type),
      biz_data = values(biz_data),
      req_id = values(req_id),
      retry_status = values(retry_status),
      retry_count = values(retry_count),
      next_time = values(next_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_retry
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizData != null">
        biz_data,
      </if>
      <if test="reqId != null">
        req_id,
      </if>
      <if test="retryStatus != null">
        retry_status,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="nextTime != null">
        next_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizData != null">
        #{bizData,jdbcType=VARCHAR},
      </if>
      <if test="reqId != null">
        #{reqId,jdbcType=VARCHAR},
      </if>
      <if test="retryStatus != null">
        #{retryStatus,jdbcType=INTEGER},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="nextTime != null">
        #{nextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="bizId != null">
        biz_id = values(biz_id),
      </if>
      <if test="bizType != null">
        biz_type = values(biz_type),
      </if>
      <if test="bizData != null">
        biz_data = values(biz_data),
      </if>
      <if test="reqId != null">
        req_id = values(req_id),
      </if>
      <if test="retryStatus != null">
        retry_status = values(retry_status),
      </if>
      <if test="retryCount != null">
        retry_count = values(retry_count),
      </if>
      <if test="nextTime != null">
        next_time = values(next_time),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>