<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonAuthorizationExtDao">
    <!--自定义的查询方法，请勿覆盖-->
    <select id="selectSeasonAuthedBySeasonId" resultType="com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo">
        select
            season_authorization.season_id as seasonId,
            author_authorization.app_id as appId,
            author_authorization.mid as seasonMid,
            author_authorization.mtime as authTime
        from
            mini_app_open_author_authorization as author_authorization
            inner join mini_app_open_season_authorization as season_authorization on author_authorization.mid = season_authorization.season_mid
        where
            season_authorization.season_id in
                <foreach collection="seasonIdList" item="seasonIdItem" index="index1" open="(" separator="," close=")">
                    #{seasonIdItem}
                </foreach>
            and author_authorization.status = 1
            and author_authorization.is_deleted = 0
            and season_authorization.is_deleted = 0
        group by
            seasonId,
            appId,
            seasonMid,
            authTime;
    </select>

    <select id="selectDistinctAppIds" resultType="string">
        select distinct(app_id)
        from
            mini_app_open_author_authorization
        where
            status = 1
            and is_deleted = 0
    </select>


</mapper>