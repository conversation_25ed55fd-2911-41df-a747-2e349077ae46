<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppOpenYoukuOrderDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="pay_amt" jdbcType="VARCHAR" property="payAmt" />
    <result column="pay_time" jdbcType="VARCHAR" property="payTime" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="refund_state" jdbcType="TINYINT" property="refundState" />
    <result column="refund_time" jdbcType="VARCHAR" property="refundTime" />
    <result column="refund_amt" jdbcType="VARCHAR" property="refundAmt" />
    <result column="sign_order_id" jdbcType="BIGINT" property="signOrderId" />
    <result column="sign_state" jdbcType="TINYINT" property="signState" />
    <result column="sign_off_date" jdbcType="VARCHAR" property="signOffDate" />
    <result column="args" jdbcType="VARCHAR" property="args" />
    <result column="app" jdbcType="VARCHAR" property="app" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_code, order_type, user_id, pay_amt, pay_time, product_id, product_name, 
    sku_name, pay_type, refund_state, refund_time, refund_amt, sign_order_id, sign_state, 
    sign_off_date, args, app, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_open_youku_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_open_youku_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_open_youku_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPoExample">
    delete from mini_app_open_youku_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_youku_order (order_id, order_code, order_type, 
      user_id, pay_amt, pay_time, 
      product_id, product_name, sku_name, 
      pay_type, refund_state, refund_time, 
      refund_amt, sign_order_id, sign_state, 
      sign_off_date, args, app, 
      is_deleted, ctime, mtime
      )
    values (#{orderId,jdbcType=BIGINT}, #{orderCode,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{payAmt,jdbcType=VARCHAR}, #{payTime,jdbcType=VARCHAR}, 
      #{productId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{payType,jdbcType=VARCHAR}, #{refundState,jdbcType=TINYINT}, #{refundTime,jdbcType=VARCHAR}, 
      #{refundAmt,jdbcType=VARCHAR}, #{signOrderId,jdbcType=BIGINT}, #{signState,jdbcType=TINYINT}, 
      #{signOffDate,jdbcType=VARCHAR}, #{args,jdbcType=VARCHAR}, #{app,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_youku_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="payAmt != null">
        pay_amt,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="refundState != null">
        refund_state,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="refundAmt != null">
        refund_amt,
      </if>
      <if test="signOrderId != null">
        sign_order_id,
      </if>
      <if test="signState != null">
        sign_state,
      </if>
      <if test="signOffDate != null">
        sign_off_date,
      </if>
      <if test="args != null">
        args,
      </if>
      <if test="app != null">
        app,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="payAmt != null">
        #{payAmt,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="refundState != null">
        #{refundState,jdbcType=TINYINT},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=VARCHAR},
      </if>
      <if test="refundAmt != null">
        #{refundAmt,jdbcType=VARCHAR},
      </if>
      <if test="signOrderId != null">
        #{signOrderId,jdbcType=BIGINT},
      </if>
      <if test="signState != null">
        #{signState,jdbcType=TINYINT},
      </if>
      <if test="signOffDate != null">
        #{signOffDate,jdbcType=VARCHAR},
      </if>
      <if test="args != null">
        #{args,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        #{app,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_open_youku_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_open_youku_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderCode != null">
        order_code = #{record.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.payAmt != null">
        pay_amt = #{record.payAmt,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=VARCHAR},
      </if>
      <if test="record.refundState != null">
        refund_state = #{record.refundState,jdbcType=TINYINT},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmt != null">
        refund_amt = #{record.refundAmt,jdbcType=VARCHAR},
      </if>
      <if test="record.signOrderId != null">
        sign_order_id = #{record.signOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.signState != null">
        sign_state = #{record.signState,jdbcType=TINYINT},
      </if>
      <if test="record.signOffDate != null">
        sign_off_date = #{record.signOffDate,jdbcType=VARCHAR},
      </if>
      <if test="record.args != null">
        args = #{record.args,jdbcType=VARCHAR},
      </if>
      <if test="record.app != null">
        app = #{record.app,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_open_youku_order
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_code = #{record.orderCode,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      pay_amt = #{record.payAmt,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_name = #{record.productName,jdbcType=VARCHAR},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      pay_type = #{record.payType,jdbcType=VARCHAR},
      refund_state = #{record.refundState,jdbcType=TINYINT},
      refund_time = #{record.refundTime,jdbcType=VARCHAR},
      refund_amt = #{record.refundAmt,jdbcType=VARCHAR},
      sign_order_id = #{record.signOrderId,jdbcType=BIGINT},
      sign_state = #{record.signState,jdbcType=TINYINT},
      sign_off_date = #{record.signOffDate,jdbcType=VARCHAR},
      args = #{record.args,jdbcType=VARCHAR},
      app = #{record.app,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    update mini_app_open_youku_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="payAmt != null">
        pay_amt = #{payAmt,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="refundState != null">
        refund_state = #{refundState,jdbcType=TINYINT},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=VARCHAR},
      </if>
      <if test="refundAmt != null">
        refund_amt = #{refundAmt,jdbcType=VARCHAR},
      </if>
      <if test="signOrderId != null">
        sign_order_id = #{signOrderId,jdbcType=BIGINT},
      </if>
      <if test="signState != null">
        sign_state = #{signState,jdbcType=TINYINT},
      </if>
      <if test="signOffDate != null">
        sign_off_date = #{signOffDate,jdbcType=VARCHAR},
      </if>
      <if test="args != null">
        args = #{args,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        app = #{app,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    update mini_app_open_youku_order
    set order_id = #{orderId,jdbcType=BIGINT},
      order_code = #{orderCode,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      pay_amt = #{payAmt,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=VARCHAR},
      refund_state = #{refundState,jdbcType=TINYINT},
      refund_time = #{refundTime,jdbcType=VARCHAR},
      refund_amt = #{refundAmt,jdbcType=VARCHAR},
      sign_order_id = #{signOrderId,jdbcType=BIGINT},
      sign_state = #{signState,jdbcType=TINYINT},
      sign_off_date = #{signOffDate,jdbcType=VARCHAR},
      args = #{args,jdbcType=VARCHAR},
      app = #{app,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_youku_order (order_id, order_code, order_type, 
      user_id, pay_amt, pay_time, 
      product_id, product_name, sku_name, 
      pay_type, refund_state, refund_time, 
      refund_amt, sign_order_id, sign_state, 
      sign_off_date, args, app, 
      is_deleted, ctime, mtime
      )
    values (#{orderId,jdbcType=BIGINT}, #{orderCode,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{payAmt,jdbcType=VARCHAR}, #{payTime,jdbcType=VARCHAR}, 
      #{productId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{payType,jdbcType=VARCHAR}, #{refundState,jdbcType=TINYINT}, #{refundTime,jdbcType=VARCHAR}, 
      #{refundAmt,jdbcType=VARCHAR}, #{signOrderId,jdbcType=BIGINT}, #{signState,jdbcType=TINYINT}, 
      #{signOffDate,jdbcType=VARCHAR}, #{args,jdbcType=VARCHAR}, #{app,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      order_code = values(order_code),
      order_type = values(order_type),
      user_id = values(user_id),
      pay_amt = values(pay_amt),
      pay_time = values(pay_time),
      product_id = values(product_id),
      product_name = values(product_name),
      sku_name = values(sku_name),
      pay_type = values(pay_type),
      refund_state = values(refund_state),
      refund_time = values(refund_time),
      refund_amt = values(refund_amt),
      sign_order_id = values(sign_order_id),
      sign_state = values(sign_state),
      sign_off_date = values(sign_off_date),
      args = values(args),
      app = values(app),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_youku_order
      (order_id,order_code,order_type,user_id,pay_amt,pay_time,product_id,product_name,sku_name,pay_type,refund_state,refund_time,refund_amt,sign_order_id,sign_state,sign_off_date,args,app,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.orderCode,jdbcType=VARCHAR},
        #{item.orderType,jdbcType=VARCHAR},
        #{item.userId,jdbcType=VARCHAR},
        #{item.payAmt,jdbcType=VARCHAR},
        #{item.payTime,jdbcType=VARCHAR},
        #{item.productId,jdbcType=BIGINT},
        #{item.productName,jdbcType=VARCHAR},
        #{item.skuName,jdbcType=VARCHAR},
        #{item.payType,jdbcType=VARCHAR},
        #{item.refundState,jdbcType=TINYINT},
        #{item.refundTime,jdbcType=VARCHAR},
        #{item.refundAmt,jdbcType=VARCHAR},
        #{item.signOrderId,jdbcType=BIGINT},
        #{item.signState,jdbcType=TINYINT},
        #{item.signOffDate,jdbcType=VARCHAR},
        #{item.args,jdbcType=VARCHAR},
        #{item.app,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_open_youku_order
      (order_id,order_code,order_type,user_id,pay_amt,pay_time,product_id,product_name,sku_name,pay_type,refund_state,refund_time,refund_amt,sign_order_id,sign_state,sign_off_date,args,app,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=BIGINT},
        #{item.orderCode,jdbcType=VARCHAR},
        #{item.orderType,jdbcType=VARCHAR},
        #{item.userId,jdbcType=VARCHAR},
        #{item.payAmt,jdbcType=VARCHAR},
        #{item.payTime,jdbcType=VARCHAR},
        #{item.productId,jdbcType=BIGINT},
        #{item.productName,jdbcType=VARCHAR},
        #{item.skuName,jdbcType=VARCHAR},
        #{item.payType,jdbcType=VARCHAR},
        #{item.refundState,jdbcType=TINYINT},
        #{item.refundTime,jdbcType=VARCHAR},
        #{item.refundAmt,jdbcType=VARCHAR},
        #{item.signOrderId,jdbcType=BIGINT},
        #{item.signState,jdbcType=TINYINT},
        #{item.signOffDate,jdbcType=VARCHAR},
        #{item.args,jdbcType=VARCHAR},
        #{item.app,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      order_code = values(order_code),
      order_type = values(order_type),
      user_id = values(user_id),
      pay_amt = values(pay_amt),
      pay_time = values(pay_time),
      product_id = values(product_id),
      product_name = values(product_name),
      sku_name = values(sku_name),
      pay_type = values(pay_type),
      refund_state = values(refund_state),
      refund_time = values(refund_time),
      refund_amt = values(refund_amt),
      sign_order_id = values(sign_order_id),
      sign_state = values(sign_state),
      sign_off_date = values(sign_off_date),
      args = values(args),
      app = values(app),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_open_youku_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="payAmt != null">
        pay_amt,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="refundState != null">
        refund_state,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="refundAmt != null">
        refund_amt,
      </if>
      <if test="signOrderId != null">
        sign_order_id,
      </if>
      <if test="signState != null">
        sign_state,
      </if>
      <if test="signOffDate != null">
        sign_off_date,
      </if>
      <if test="args != null">
        args,
      </if>
      <if test="app != null">
        app,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="payAmt != null">
        #{payAmt,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="refundState != null">
        #{refundState,jdbcType=TINYINT},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=VARCHAR},
      </if>
      <if test="refundAmt != null">
        #{refundAmt,jdbcType=VARCHAR},
      </if>
      <if test="signOrderId != null">
        #{signOrderId,jdbcType=BIGINT},
      </if>
      <if test="signState != null">
        #{signState,jdbcType=TINYINT},
      </if>
      <if test="signOffDate != null">
        #{signOffDate,jdbcType=VARCHAR},
      </if>
      <if test="args != null">
        #{args,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        #{app,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="orderCode != null">
        order_code = values(order_code),
      </if>
      <if test="orderType != null">
        order_type = values(order_type),
      </if>
      <if test="userId != null">
        user_id = values(user_id),
      </if>
      <if test="payAmt != null">
        pay_amt = values(pay_amt),
      </if>
      <if test="payTime != null">
        pay_time = values(pay_time),
      </if>
      <if test="productId != null">
        product_id = values(product_id),
      </if>
      <if test="productName != null">
        product_name = values(product_name),
      </if>
      <if test="skuName != null">
        sku_name = values(sku_name),
      </if>
      <if test="payType != null">
        pay_type = values(pay_type),
      </if>
      <if test="refundState != null">
        refund_state = values(refund_state),
      </if>
      <if test="refundTime != null">
        refund_time = values(refund_time),
      </if>
      <if test="refundAmt != null">
        refund_amt = values(refund_amt),
      </if>
      <if test="signOrderId != null">
        sign_order_id = values(sign_order_id),
      </if>
      <if test="signState != null">
        sign_state = values(sign_state),
      </if>
      <if test="signOffDate != null">
        sign_off_date = values(sign_off_date),
      </if>
      <if test="args != null">
        args = values(args),
      </if>
      <if test="app != null">
        app = values(app),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>