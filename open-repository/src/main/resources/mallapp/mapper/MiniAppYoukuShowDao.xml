<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppYoukuShowDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="show_id" jdbcType="VARCHAR" property="showId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="subtitle" jdbcType="VARCHAR" property="subtitle" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="genre" jdbcType="VARCHAR" property="genre" />
    <result column="thumb_url_huge" jdbcType="VARCHAR" property="thumbUrlHuge" />
    <result column="w3_h4_thumb_url_huge" jdbcType="VARCHAR" property="w3H4ThumbUrlHuge" />
    <result column="completed" jdbcType="TINYINT" property="completed" />
    <result column="paid" jdbcType="TINYINT" property="paid" />
    <result column="episode_total" jdbcType="INTEGER" property="episodeTotal" />
    <result column="last_episode" jdbcType="INTEGER" property="lastEpisode" />
    <result column="last_stage" jdbcType="INTEGER" property="lastStage" />
    <result column="director" jdbcType="VARCHAR" property="director" />
    <result column="performer" jdbcType="VARCHAR" property="performer" />
    <result column="release_date" jdbcType="VARCHAR" property="releaseDate" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="heat" jdbcType="INTEGER" property="heat" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="exclusive" jdbcType="TINYINT" property="exclusive" />
    <result column="reputation" jdbcType="VARCHAR" property="reputation" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="update_notice" jdbcType="VARCHAR" property="updateNotice" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, show_id, name, subtitle, category, genre, thumb_url_huge, w3_h4_thumb_url_huge, 
    completed, paid, episode_total, last_episode, last_stage, director, performer, release_date, 
    area, heat, language, exclusive, reputation, link, update_notice, ctime, mtime, is_deleted
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mini_app_youku_show
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_youku_show
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mini_app_youku_show
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_youku_show
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPoExample">
    delete from mini_app_youku_show
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_show (show_id, name, subtitle, 
      category, genre, thumb_url_huge, 
      w3_h4_thumb_url_huge, completed, paid, 
      episode_total, last_episode, last_stage, 
      director, performer, release_date, 
      area, heat, language, 
      exclusive, reputation, link, 
      update_notice, ctime, mtime, 
      is_deleted, description)
    values (#{showId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{subtitle,jdbcType=VARCHAR}, 
      #{category,jdbcType=VARCHAR}, #{genre,jdbcType=VARCHAR}, #{thumbUrlHuge,jdbcType=VARCHAR}, 
      #{w3H4ThumbUrlHuge,jdbcType=VARCHAR}, #{completed,jdbcType=TINYINT}, #{paid,jdbcType=TINYINT}, 
      #{episodeTotal,jdbcType=INTEGER}, #{lastEpisode,jdbcType=INTEGER}, #{lastStage,jdbcType=INTEGER}, 
      #{director,jdbcType=VARCHAR}, #{performer,jdbcType=VARCHAR}, #{releaseDate,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{heat,jdbcType=INTEGER}, #{language,jdbcType=VARCHAR}, 
      #{exclusive,jdbcType=TINYINT}, #{reputation,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR}, 
      #{updateNotice,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_show
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        show_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="subtitle != null">
        subtitle,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="genre != null">
        genre,
      </if>
      <if test="thumbUrlHuge != null">
        thumb_url_huge,
      </if>
      <if test="w3H4ThumbUrlHuge != null">
        w3_h4_thumb_url_huge,
      </if>
      <if test="completed != null">
        completed,
      </if>
      <if test="paid != null">
        paid,
      </if>
      <if test="episodeTotal != null">
        episode_total,
      </if>
      <if test="lastEpisode != null">
        last_episode,
      </if>
      <if test="lastStage != null">
        last_stage,
      </if>
      <if test="director != null">
        director,
      </if>
      <if test="performer != null">
        performer,
      </if>
      <if test="releaseDate != null">
        release_date,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="heat != null">
        heat,
      </if>
      <if test="language != null">
        language,
      </if>
      <if test="exclusive != null">
        exclusive,
      </if>
      <if test="reputation != null">
        reputation,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="updateNotice != null">
        update_notice,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        #{showId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="subtitle != null">
        #{subtitle,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="genre != null">
        #{genre,jdbcType=VARCHAR},
      </if>
      <if test="thumbUrlHuge != null">
        #{thumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="w3H4ThumbUrlHuge != null">
        #{w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="completed != null">
        #{completed,jdbcType=TINYINT},
      </if>
      <if test="paid != null">
        #{paid,jdbcType=TINYINT},
      </if>
      <if test="episodeTotal != null">
        #{episodeTotal,jdbcType=INTEGER},
      </if>
      <if test="lastEpisode != null">
        #{lastEpisode,jdbcType=INTEGER},
      </if>
      <if test="lastStage != null">
        #{lastStage,jdbcType=INTEGER},
      </if>
      <if test="director != null">
        #{director,jdbcType=VARCHAR},
      </if>
      <if test="performer != null">
        #{performer,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        #{releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="heat != null">
        #{heat,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="exclusive != null">
        #{exclusive,jdbcType=TINYINT},
      </if>
      <if test="reputation != null">
        #{reputation,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="updateNotice != null">
        #{updateNotice,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_youku_show
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_youku_show
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.showId != null">
        show_id = #{record.showId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.subtitle != null">
        subtitle = #{record.subtitle,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.genre != null">
        genre = #{record.genre,jdbcType=VARCHAR},
      </if>
      <if test="record.thumbUrlHuge != null">
        thumb_url_huge = #{record.thumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="record.w3H4ThumbUrlHuge != null">
        w3_h4_thumb_url_huge = #{record.w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="record.completed != null">
        completed = #{record.completed,jdbcType=TINYINT},
      </if>
      <if test="record.paid != null">
        paid = #{record.paid,jdbcType=TINYINT},
      </if>
      <if test="record.episodeTotal != null">
        episode_total = #{record.episodeTotal,jdbcType=INTEGER},
      </if>
      <if test="record.lastEpisode != null">
        last_episode = #{record.lastEpisode,jdbcType=INTEGER},
      </if>
      <if test="record.lastStage != null">
        last_stage = #{record.lastStage,jdbcType=INTEGER},
      </if>
      <if test="record.director != null">
        director = #{record.director,jdbcType=VARCHAR},
      </if>
      <if test="record.performer != null">
        performer = #{record.performer,jdbcType=VARCHAR},
      </if>
      <if test="record.releaseDate != null">
        release_date = #{record.releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.heat != null">
        heat = #{record.heat,jdbcType=INTEGER},
      </if>
      <if test="record.language != null">
        language = #{record.language,jdbcType=VARCHAR},
      </if>
      <if test="record.exclusive != null">
        exclusive = #{record.exclusive,jdbcType=TINYINT},
      </if>
      <if test="record.reputation != null">
        reputation = #{record.reputation,jdbcType=VARCHAR},
      </if>
      <if test="record.link != null">
        link = #{record.link,jdbcType=VARCHAR},
      </if>
      <if test="record.updateNotice != null">
        update_notice = #{record.updateNotice,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update mini_app_youku_show
    set id = #{record.id,jdbcType=BIGINT},
      show_id = #{record.showId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      subtitle = #{record.subtitle,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      genre = #{record.genre,jdbcType=VARCHAR},
      thumb_url_huge = #{record.thumbUrlHuge,jdbcType=VARCHAR},
      w3_h4_thumb_url_huge = #{record.w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      completed = #{record.completed,jdbcType=TINYINT},
      paid = #{record.paid,jdbcType=TINYINT},
      episode_total = #{record.episodeTotal,jdbcType=INTEGER},
      last_episode = #{record.lastEpisode,jdbcType=INTEGER},
      last_stage = #{record.lastStage,jdbcType=INTEGER},
      director = #{record.director,jdbcType=VARCHAR},
      performer = #{record.performer,jdbcType=VARCHAR},
      release_date = #{record.releaseDate,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      heat = #{record.heat,jdbcType=INTEGER},
      language = #{record.language,jdbcType=VARCHAR},
      exclusive = #{record.exclusive,jdbcType=TINYINT},
      reputation = #{record.reputation,jdbcType=VARCHAR},
      link = #{record.link,jdbcType=VARCHAR},
      update_notice = #{record.updateNotice,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      description = #{record.description,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_youku_show
    set id = #{record.id,jdbcType=BIGINT},
      show_id = #{record.showId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      subtitle = #{record.subtitle,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      genre = #{record.genre,jdbcType=VARCHAR},
      thumb_url_huge = #{record.thumbUrlHuge,jdbcType=VARCHAR},
      w3_h4_thumb_url_huge = #{record.w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      completed = #{record.completed,jdbcType=TINYINT},
      paid = #{record.paid,jdbcType=TINYINT},
      episode_total = #{record.episodeTotal,jdbcType=INTEGER},
      last_episode = #{record.lastEpisode,jdbcType=INTEGER},
      last_stage = #{record.lastStage,jdbcType=INTEGER},
      director = #{record.director,jdbcType=VARCHAR},
      performer = #{record.performer,jdbcType=VARCHAR},
      release_date = #{record.releaseDate,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      heat = #{record.heat,jdbcType=INTEGER},
      language = #{record.language,jdbcType=VARCHAR},
      exclusive = #{record.exclusive,jdbcType=TINYINT},
      reputation = #{record.reputation,jdbcType=VARCHAR},
      link = #{record.link,jdbcType=VARCHAR},
      update_notice = #{record.updateNotice,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    update mini_app_youku_show
    <set>
      <if test="showId != null">
        show_id = #{showId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="subtitle != null">
        subtitle = #{subtitle,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="genre != null">
        genre = #{genre,jdbcType=VARCHAR},
      </if>
      <if test="thumbUrlHuge != null">
        thumb_url_huge = #{thumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="w3H4ThumbUrlHuge != null">
        w3_h4_thumb_url_huge = #{w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="completed != null">
        completed = #{completed,jdbcType=TINYINT},
      </if>
      <if test="paid != null">
        paid = #{paid,jdbcType=TINYINT},
      </if>
      <if test="episodeTotal != null">
        episode_total = #{episodeTotal,jdbcType=INTEGER},
      </if>
      <if test="lastEpisode != null">
        last_episode = #{lastEpisode,jdbcType=INTEGER},
      </if>
      <if test="lastStage != null">
        last_stage = #{lastStage,jdbcType=INTEGER},
      </if>
      <if test="director != null">
        director = #{director,jdbcType=VARCHAR},
      </if>
      <if test="performer != null">
        performer = #{performer,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        release_date = #{releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="heat != null">
        heat = #{heat,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        language = #{language,jdbcType=VARCHAR},
      </if>
      <if test="exclusive != null">
        exclusive = #{exclusive,jdbcType=TINYINT},
      </if>
      <if test="reputation != null">
        reputation = #{reputation,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        link = #{link,jdbcType=VARCHAR},
      </if>
      <if test="updateNotice != null">
        update_notice = #{updateNotice,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    update mini_app_youku_show
    set show_id = #{showId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      subtitle = #{subtitle,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      genre = #{genre,jdbcType=VARCHAR},
      thumb_url_huge = #{thumbUrlHuge,jdbcType=VARCHAR},
      w3_h4_thumb_url_huge = #{w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      completed = #{completed,jdbcType=TINYINT},
      paid = #{paid,jdbcType=TINYINT},
      episode_total = #{episodeTotal,jdbcType=INTEGER},
      last_episode = #{lastEpisode,jdbcType=INTEGER},
      last_stage = #{lastStage,jdbcType=INTEGER},
      director = #{director,jdbcType=VARCHAR},
      performer = #{performer,jdbcType=VARCHAR},
      release_date = #{releaseDate,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      heat = #{heat,jdbcType=INTEGER},
      language = #{language,jdbcType=VARCHAR},
      exclusive = #{exclusive,jdbcType=TINYINT},
      reputation = #{reputation,jdbcType=VARCHAR},
      link = #{link,jdbcType=VARCHAR},
      update_notice = #{updateNotice,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    update mini_app_youku_show
    set show_id = #{showId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      subtitle = #{subtitle,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      genre = #{genre,jdbcType=VARCHAR},
      thumb_url_huge = #{thumbUrlHuge,jdbcType=VARCHAR},
      w3_h4_thumb_url_huge = #{w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      completed = #{completed,jdbcType=TINYINT},
      paid = #{paid,jdbcType=TINYINT},
      episode_total = #{episodeTotal,jdbcType=INTEGER},
      last_episode = #{lastEpisode,jdbcType=INTEGER},
      last_stage = #{lastStage,jdbcType=INTEGER},
      director = #{director,jdbcType=VARCHAR},
      performer = #{performer,jdbcType=VARCHAR},
      release_date = #{releaseDate,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      heat = #{heat,jdbcType=INTEGER},
      language = #{language,jdbcType=VARCHAR},
      exclusive = #{exclusive,jdbcType=TINYINT},
      reputation = #{reputation,jdbcType=VARCHAR},
      link = #{link,jdbcType=VARCHAR},
      update_notice = #{updateNotice,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_show (show_id, name, subtitle, 
      category, genre, thumb_url_huge, 
      w3_h4_thumb_url_huge, completed, paid, 
      episode_total, last_episode, last_stage, 
      director, performer, release_date, 
      area, heat, language, 
      exclusive, reputation, link, 
      update_notice, ctime, mtime, 
      is_deleted, description)
    values (#{showId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{subtitle,jdbcType=VARCHAR}, 
      #{category,jdbcType=VARCHAR}, #{genre,jdbcType=VARCHAR}, #{thumbUrlHuge,jdbcType=VARCHAR}, 
      #{w3H4ThumbUrlHuge,jdbcType=VARCHAR}, #{completed,jdbcType=TINYINT}, #{paid,jdbcType=TINYINT}, 
      #{episodeTotal,jdbcType=INTEGER}, #{lastEpisode,jdbcType=INTEGER}, #{lastStage,jdbcType=INTEGER}, 
      #{director,jdbcType=VARCHAR}, #{performer,jdbcType=VARCHAR}, #{releaseDate,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{heat,jdbcType=INTEGER}, #{language,jdbcType=VARCHAR}, 
      #{exclusive,jdbcType=TINYINT}, #{reputation,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR}, 
      #{updateNotice,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{description,jdbcType=LONGVARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      show_id = values(show_id),
      name = values(name),
      subtitle = values(subtitle),
      category = values(category),
      genre = values(genre),
      thumb_url_huge = values(thumb_url_huge),
      w3_h4_thumb_url_huge = values(w3_h4_thumb_url_huge),
      completed = values(completed),
      paid = values(paid),
      episode_total = values(episode_total),
      last_episode = values(last_episode),
      last_stage = values(last_stage),
      director = values(director),
      performer = values(performer),
      release_date = values(release_date),
      area = values(area),
      heat = values(heat),
      language = values(language),
      exclusive = values(exclusive),
      reputation = values(reputation),
      link = values(link),
      update_notice = values(update_notice),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      description = values(description),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_youku_show
      (show_id,name,subtitle,category,genre,thumb_url_huge,w3_h4_thumb_url_huge,completed,paid,episode_total,last_episode,last_stage,director,performer,release_date,area,heat,language,exclusive,reputation,link,update_notice,ctime,mtime,is_deleted,description)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.showId,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.subtitle,jdbcType=VARCHAR},
        #{item.category,jdbcType=VARCHAR},
        #{item.genre,jdbcType=VARCHAR},
        #{item.thumbUrlHuge,jdbcType=VARCHAR},
        #{item.w3H4ThumbUrlHuge,jdbcType=VARCHAR},
        #{item.completed,jdbcType=TINYINT},
        #{item.paid,jdbcType=TINYINT},
        #{item.episodeTotal,jdbcType=INTEGER},
        #{item.lastEpisode,jdbcType=INTEGER},
        #{item.lastStage,jdbcType=INTEGER},
        #{item.director,jdbcType=VARCHAR},
        #{item.performer,jdbcType=VARCHAR},
        #{item.releaseDate,jdbcType=VARCHAR},
        #{item.area,jdbcType=VARCHAR},
        #{item.heat,jdbcType=INTEGER},
        #{item.language,jdbcType=VARCHAR},
        #{item.exclusive,jdbcType=TINYINT},
        #{item.reputation,jdbcType=VARCHAR},
        #{item.link,jdbcType=VARCHAR},
        #{item.updateNotice,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.description,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_youku_show
      (show_id,name,subtitle,category,genre,thumb_url_huge,w3_h4_thumb_url_huge,completed,paid,episode_total,last_episode,last_stage,director,performer,release_date,area,heat,language,exclusive,reputation,link,update_notice,ctime,mtime,is_deleted,description)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.showId,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.subtitle,jdbcType=VARCHAR},
        #{item.category,jdbcType=VARCHAR},
        #{item.genre,jdbcType=VARCHAR},
        #{item.thumbUrlHuge,jdbcType=VARCHAR},
        #{item.w3H4ThumbUrlHuge,jdbcType=VARCHAR},
        #{item.completed,jdbcType=TINYINT},
        #{item.paid,jdbcType=TINYINT},
        #{item.episodeTotal,jdbcType=INTEGER},
        #{item.lastEpisode,jdbcType=INTEGER},
        #{item.lastStage,jdbcType=INTEGER},
        #{item.director,jdbcType=VARCHAR},
        #{item.performer,jdbcType=VARCHAR},
        #{item.releaseDate,jdbcType=VARCHAR},
        #{item.area,jdbcType=VARCHAR},
        #{item.heat,jdbcType=INTEGER},
        #{item.language,jdbcType=VARCHAR},
        #{item.exclusive,jdbcType=TINYINT},
        #{item.reputation,jdbcType=VARCHAR},
        #{item.link,jdbcType=VARCHAR},
        #{item.updateNotice,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.description,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      show_id = values(show_id),
      name = values(name),
      subtitle = values(subtitle),
      category = values(category),
      genre = values(genre),
      thumb_url_huge = values(thumb_url_huge),
      w3_h4_thumb_url_huge = values(w3_h4_thumb_url_huge),
      completed = values(completed),
      paid = values(paid),
      episode_total = values(episode_total),
      last_episode = values(last_episode),
      last_stage = values(last_stage),
      director = values(director),
      performer = values(performer),
      release_date = values(release_date),
      area = values(area),
      heat = values(heat),
      language = values(language),
      exclusive = values(exclusive),
      reputation = values(reputation),
      link = values(link),
      update_notice = values(update_notice),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      description = values(description),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuShowPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_show
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        show_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="subtitle != null">
        subtitle,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="genre != null">
        genre,
      </if>
      <if test="thumbUrlHuge != null">
        thumb_url_huge,
      </if>
      <if test="w3H4ThumbUrlHuge != null">
        w3_h4_thumb_url_huge,
      </if>
      <if test="completed != null">
        completed,
      </if>
      <if test="paid != null">
        paid,
      </if>
      <if test="episodeTotal != null">
        episode_total,
      </if>
      <if test="lastEpisode != null">
        last_episode,
      </if>
      <if test="lastStage != null">
        last_stage,
      </if>
      <if test="director != null">
        director,
      </if>
      <if test="performer != null">
        performer,
      </if>
      <if test="releaseDate != null">
        release_date,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="heat != null">
        heat,
      </if>
      <if test="language != null">
        language,
      </if>
      <if test="exclusive != null">
        exclusive,
      </if>
      <if test="reputation != null">
        reputation,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="updateNotice != null">
        update_notice,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        #{showId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="subtitle != null">
        #{subtitle,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="genre != null">
        #{genre,jdbcType=VARCHAR},
      </if>
      <if test="thumbUrlHuge != null">
        #{thumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="w3H4ThumbUrlHuge != null">
        #{w3H4ThumbUrlHuge,jdbcType=VARCHAR},
      </if>
      <if test="completed != null">
        #{completed,jdbcType=TINYINT},
      </if>
      <if test="paid != null">
        #{paid,jdbcType=TINYINT},
      </if>
      <if test="episodeTotal != null">
        #{episodeTotal,jdbcType=INTEGER},
      </if>
      <if test="lastEpisode != null">
        #{lastEpisode,jdbcType=INTEGER},
      </if>
      <if test="lastStage != null">
        #{lastStage,jdbcType=INTEGER},
      </if>
      <if test="director != null">
        #{director,jdbcType=VARCHAR},
      </if>
      <if test="performer != null">
        #{performer,jdbcType=VARCHAR},
      </if>
      <if test="releaseDate != null">
        #{releaseDate,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="heat != null">
        #{heat,jdbcType=INTEGER},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="exclusive != null">
        #{exclusive,jdbcType=TINYINT},
      </if>
      <if test="reputation != null">
        #{reputation,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="updateNotice != null">
        #{updateNotice,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="showId != null">
        show_id = values(show_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="subtitle != null">
        subtitle = values(subtitle),
      </if>
      <if test="category != null">
        category = values(category),
      </if>
      <if test="genre != null">
        genre = values(genre),
      </if>
      <if test="thumbUrlHuge != null">
        thumb_url_huge = values(thumb_url_huge),
      </if>
      <if test="w3H4ThumbUrlHuge != null">
        w3_h4_thumb_url_huge = values(w3_h4_thumb_url_huge),
      </if>
      <if test="completed != null">
        completed = values(completed),
      </if>
      <if test="paid != null">
        paid = values(paid),
      </if>
      <if test="episodeTotal != null">
        episode_total = values(episode_total),
      </if>
      <if test="lastEpisode != null">
        last_episode = values(last_episode),
      </if>
      <if test="lastStage != null">
        last_stage = values(last_stage),
      </if>
      <if test="director != null">
        director = values(director),
      </if>
      <if test="performer != null">
        performer = values(performer),
      </if>
      <if test="releaseDate != null">
        release_date = values(release_date),
      </if>
      <if test="area != null">
        area = values(area),
      </if>
      <if test="heat != null">
        heat = values(heat),
      </if>
      <if test="language != null">
        language = values(language),
      </if>
      <if test="exclusive != null">
        exclusive = values(exclusive),
      </if>
      <if test="reputation != null">
        reputation = values(reputation),
      </if>
      <if test="link != null">
        link = values(link),
      </if>
      <if test="updateNotice != null">
        update_notice = values(update_notice),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
    </trim>
  </insert>
</mapper>