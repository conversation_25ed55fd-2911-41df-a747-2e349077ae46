<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppYoukuVideoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="show_id" jdbcType="VARCHAR" property="showId" />
    <result column="video_id" jdbcType="VARCHAR" property="videoId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="stage" jdbcType="INTEGER" property="stage" />
    <result column="episode" jdbcType="INTEGER" property="episode" />
    <result column="vertical_thumbnails" jdbcType="VARCHAR" property="verticalThumbnails" />
    <result column="thumbnails" jdbcType="VARCHAR" property="thumbnails" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="paid" jdbcType="TINYINT" property="paid" />
    <result column="show_length" jdbcType="VARCHAR" property="showLength" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, show_id, video_id, name, title, stage, episode, vertical_thumbnails, thumbnails, 
    type, paid, show_length, link, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mini_app_youku_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mini_app_youku_video
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mini_app_youku_video
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPoExample">
    delete from mini_app_youku_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_video (show_id, video_id, name, 
      title, stage, episode, 
      vertical_thumbnails, thumbnails, type, 
      paid, show_length, link, 
      ctime, mtime, is_deleted
      )
    values (#{showId,jdbcType=VARCHAR}, #{videoId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{stage,jdbcType=INTEGER}, #{episode,jdbcType=INTEGER}, 
      #{verticalThumbnails,jdbcType=VARCHAR}, #{thumbnails,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{paid,jdbcType=TINYINT}, #{showLength,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        show_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="stage != null">
        stage,
      </if>
      <if test="episode != null">
        episode,
      </if>
      <if test="verticalThumbnails != null">
        vertical_thumbnails,
      </if>
      <if test="thumbnails != null">
        thumbnails,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="paid != null">
        paid,
      </if>
      <if test="showLength != null">
        show_length,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        #{showId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=INTEGER},
      </if>
      <if test="episode != null">
        #{episode,jdbcType=INTEGER},
      </if>
      <if test="verticalThumbnails != null">
        #{verticalThumbnails,jdbcType=VARCHAR},
      </if>
      <if test="thumbnails != null">
        #{thumbnails,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="paid != null">
        #{paid,jdbcType=TINYINT},
      </if>
      <if test="showLength != null">
        #{showLength,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPoExample" resultType="java.lang.Long">
    select count(*) from mini_app_youku_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mini_app_youku_video
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.showId != null">
        show_id = #{record.showId,jdbcType=VARCHAR},
      </if>
      <if test="record.videoId != null">
        video_id = #{record.videoId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.stage != null">
        stage = #{record.stage,jdbcType=INTEGER},
      </if>
      <if test="record.episode != null">
        episode = #{record.episode,jdbcType=INTEGER},
      </if>
      <if test="record.verticalThumbnails != null">
        vertical_thumbnails = #{record.verticalThumbnails,jdbcType=VARCHAR},
      </if>
      <if test="record.thumbnails != null">
        thumbnails = #{record.thumbnails,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.paid != null">
        paid = #{record.paid,jdbcType=TINYINT},
      </if>
      <if test="record.showLength != null">
        show_length = #{record.showLength,jdbcType=VARCHAR},
      </if>
      <if test="record.link != null">
        link = #{record.link,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mini_app_youku_video
    set id = #{record.id,jdbcType=BIGINT},
      show_id = #{record.showId,jdbcType=VARCHAR},
      video_id = #{record.videoId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      stage = #{record.stage,jdbcType=INTEGER},
      episode = #{record.episode,jdbcType=INTEGER},
      vertical_thumbnails = #{record.verticalThumbnails,jdbcType=VARCHAR},
      thumbnails = #{record.thumbnails,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      paid = #{record.paid,jdbcType=TINYINT},
      show_length = #{record.showLength,jdbcType=VARCHAR},
      link = #{record.link,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    update mini_app_youku_video
    <set>
      <if test="showId != null">
        show_id = #{showId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        stage = #{stage,jdbcType=INTEGER},
      </if>
      <if test="episode != null">
        episode = #{episode,jdbcType=INTEGER},
      </if>
      <if test="verticalThumbnails != null">
        vertical_thumbnails = #{verticalThumbnails,jdbcType=VARCHAR},
      </if>
      <if test="thumbnails != null">
        thumbnails = #{thumbnails,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="paid != null">
        paid = #{paid,jdbcType=TINYINT},
      </if>
      <if test="showLength != null">
        show_length = #{showLength,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        link = #{link,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    update mini_app_youku_video
    set show_id = #{showId,jdbcType=VARCHAR},
      video_id = #{videoId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      stage = #{stage,jdbcType=INTEGER},
      episode = #{episode,jdbcType=INTEGER},
      vertical_thumbnails = #{verticalThumbnails,jdbcType=VARCHAR},
      thumbnails = #{thumbnails,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      paid = #{paid,jdbcType=TINYINT},
      show_length = #{showLength,jdbcType=VARCHAR},
      link = #{link,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_video (show_id, video_id, name, 
      title, stage, episode, 
      vertical_thumbnails, thumbnails, type, 
      paid, show_length, link, 
      ctime, mtime, is_deleted
      )
    values (#{showId,jdbcType=VARCHAR}, #{videoId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{stage,jdbcType=INTEGER}, #{episode,jdbcType=INTEGER}, 
      #{verticalThumbnails,jdbcType=VARCHAR}, #{thumbnails,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{paid,jdbcType=TINYINT}, #{showLength,jdbcType=VARCHAR}, #{link,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      show_id = values(show_id),
      video_id = values(video_id),
      name = values(name),
      title = values(title),
      stage = values(stage),
      episode = values(episode),
      vertical_thumbnails = values(vertical_thumbnails),
      thumbnails = values(thumbnails),
      type = values(type),
      paid = values(paid),
      show_length = values(show_length),
      link = values(link),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      mini_app_youku_video
      (show_id,video_id,name,title,stage,episode,vertical_thumbnails,thumbnails,type,paid,show_length,link,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.showId,jdbcType=VARCHAR},
        #{item.videoId,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR},
        #{item.stage,jdbcType=INTEGER},
        #{item.episode,jdbcType=INTEGER},
        #{item.verticalThumbnails,jdbcType=VARCHAR},
        #{item.thumbnails,jdbcType=VARCHAR},
        #{item.type,jdbcType=VARCHAR},
        #{item.paid,jdbcType=TINYINT},
        #{item.showLength,jdbcType=VARCHAR},
        #{item.link,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      mini_app_youku_video
      (show_id,video_id,name,title,stage,episode,vertical_thumbnails,thumbnails,type,paid,show_length,link,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.showId,jdbcType=VARCHAR},
        #{item.videoId,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR},
        #{item.stage,jdbcType=INTEGER},
        #{item.episode,jdbcType=INTEGER},
        #{item.verticalThumbnails,jdbcType=VARCHAR},
        #{item.thumbnails,jdbcType=VARCHAR},
        #{item.type,jdbcType=VARCHAR},
        #{item.paid,jdbcType=TINYINT},
        #{item.showLength,jdbcType=VARCHAR},
        #{item.link,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      show_id = values(show_id),
      video_id = values(video_id),
      name = values(name),
      title = values(title),
      stage = values(stage),
      episode = values(episode),
      vertical_thumbnails = values(vertical_thumbnails),
      thumbnails = values(thumbnails),
      type = values(type),
      paid = values(paid),
      show_length = values(show_length),
      link = values(link),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppYoukuVideoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mini_app_youku_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        show_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="stage != null">
        stage,
      </if>
      <if test="episode != null">
        episode,
      </if>
      <if test="verticalThumbnails != null">
        vertical_thumbnails,
      </if>
      <if test="thumbnails != null">
        thumbnails,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="paid != null">
        paid,
      </if>
      <if test="showLength != null">
        show_length,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="showId != null">
        #{showId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=INTEGER},
      </if>
      <if test="episode != null">
        #{episode,jdbcType=INTEGER},
      </if>
      <if test="verticalThumbnails != null">
        #{verticalThumbnails,jdbcType=VARCHAR},
      </if>
      <if test="thumbnails != null">
        #{thumbnails,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="paid != null">
        #{paid,jdbcType=TINYINT},
      </if>
      <if test="showLength != null">
        #{showLength,jdbcType=VARCHAR},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="showId != null">
        show_id = values(show_id),
      </if>
      <if test="videoId != null">
        video_id = values(video_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="title != null">
        title = values(title),
      </if>
      <if test="stage != null">
        stage = values(stage),
      </if>
      <if test="episode != null">
        episode = values(episode),
      </if>
      <if test="verticalThumbnails != null">
        vertical_thumbnails = values(vertical_thumbnails),
      </if>
      <if test="thumbnails != null">
        thumbnails = values(thumbnails),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="paid != null">
        paid = values(paid),
      </if>
      <if test="showLength != null">
        show_length = values(show_length),
      </if>
      <if test="link != null">
        link = values(link),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>