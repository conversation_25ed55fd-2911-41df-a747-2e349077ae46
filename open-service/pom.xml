<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bilibili.miniapp</groupId>
        <artifactId>open-platform</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>open-platform-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>open-service</name>
    <description>open-service</description>
    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <junit.version>4.10</junit.version>
        <vavr.version>0.10.2</vavr.version>
        <grpc-master.version>********.master.17440842550000.3a640662668c</grpc-master.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
            <version>${vavr.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.miniapp</groupId>
            <artifactId>open-platform-common</artifactId>
            <version>${miniapp.open.api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.miniapp</groupId>
            <artifactId>open-platform-repository</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- web服务必须依赖，包含基础的可观测性功能-->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-web</artifactId>
        </dependency>
        <!-- 必须依赖，接入公司日志平台-->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-logging</artifactId>
        </dependency>

        <!-- gRPC相关 -->
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-grpc</artifactId>
        </dependency>

        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>starter</artifactId>
        </dependency>
        <dependency>
            <groupId>pleiades.component.http</groupId>
            <artifactId>http-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- retrofit + okhttp的封装 -->
        <dependency>
            <groupId>pleiades.component.http</groupId>
            <artifactId>http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.9.22.1</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-paladin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.business.cmpt</groupId>
            <artifactId>idatabus-client-spring-boot-starter</artifactId>
            <version>${idatabus.starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.business.cmpt</groupId>
            <artifactId>cat-client-spring-boot-starter</artifactId>
            <version>${cat.starter.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.mall</groupId>
            <artifactId>miniapp-api</artifactId>
            <version>${miniapp-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bilibili.mall</groupId>
                    <artifactId>mall-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bilibili.mall</groupId>
                    <artifactId>kraken-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3-transfer-manager</artifactId>
        </dependency>

        <!--        grpc start             -->

        <dependency>
<!--            https://git.bilibili.co/bapis/bapis/-/blob/master/pgc/servant/season/season/Season2Service.proto#L19-->
            <groupId>co.bilibili.buf</groupId>
            <artifactId>pgc_servant.season.season_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.miniapp_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>


        <dependency>
            <!--        https://git.bilibili.co/bapis/bapis/-/tree/master/passport/service/identify  -->
            <groupId>co.bilibili.buf</groupId>
            <artifactId>passport_service.identify_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>

        <dependency>
            <!--        https://git.bilibili.co/bapis/bapis/-/tree/master/passport/service/user  -->
            <groupId>co.bilibili.buf</groupId>
            <artifactId>passport_service.user_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>
        <!--        https://git.bilibili.co/bapis/bapis/-/tree/short-play-open-platform/pgc/service/season/season-->
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>pgc_service.season.season_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>
        <dependency>
            <!--            https://git.bilibili.co/bapis/bapis/-/tree/master/account/service  -->
            <groupId>co.bilibili.buf</groupId>
            <artifactId>account_service_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>
        <dependency>
            <!--            https://git.bilibili.co/bapis/bapis/-/tree/master/archive/service   -->
            <groupId>co.bilibili.buf</groupId>
            <artifactId>archive_service_grpc_java</artifactId>
            <version>${grpc-master.version}</version>
        </dependency>
        <!--        grpc end               -->

        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-core</artifactId>
        </dependency>

        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <!-- 默认包含以下子依赖 -->
            <!-- junit-jupiter | mockito-core | assertj-core | jsonassert | ... -->
            <exclusions>
                <!-- 如需使用更新版本可排除旧版本 -->
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- === 增强测试工具 === -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.bilibili.regulation</groupId>
            <artifactId>regulation-api</artifactId>
        </dependency>
    </dependencies>
</project>
