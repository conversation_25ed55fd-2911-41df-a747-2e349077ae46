package com.bilibili.miniapp.open.service.aspect;

import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Slf4j
@Aspect
@Component
public class LockAspect {

    @Autowired
    private RedissonCacheRepository redis;

    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(com.bilibili.miniapp.open.service.aspect.LockRequest)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        LockRequest lockRequest = method.getAnnotation(LockRequest.class);

        String lockKey = buildLockKey(args, method, lockRequest);

        RLock lock = redis.tryLock(lockKey);

        try {
            return joinPoint.proceed();
        } finally {
            lock.unlock();
        }
    }

    private String buildLockKey(Object[] args, Method method, LockRequest lockRequest) {
        String key = lockRequest.key();
        String prefix = lockRequest.prefix();
        String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);

        if (parameterNames == null || parameterNames.length == 0) {
            return prefix + key;
        } else {
            Expression expression = parser.parseExpression(key);
            EvaluationContext context = new StandardEvaluationContext();

            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }

            return prefix + expression.getValue(context, String.class);
        }
    }
}
