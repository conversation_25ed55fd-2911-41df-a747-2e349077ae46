package com.bilibili.miniapp.open.service.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LockRequest {

    /**
     * 锁键表达式，支持 SpEL 表达式
     * 例如：#mid+':finance:create'
     */
    String key();

    /**
     * 锁默认前缀
     * @see com.bilibili.miniapp.open.repository.redis.RedisKeyPattern
     */
    String prefix() default "mp_open_lock_";
}
