package com.bilibili.miniapp.open.service.biz;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.bilibili.regulation.api.dto.BiliApiRespDTO;
import com.bilibili.regulation.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.breaker.exception.BiliBreakerRejectedException;

import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025/1/2 21:07
 */
@Slf4j
public class AbstractOpenService {

    protected <T> T call(String methodName, Function<RequestBody, BiliCall<Response<T>>> caller, Object obj) {
        return call(methodName, () -> caller.apply(toRequestBody(obj)));
    }

    protected <T> T call(String methodName, Supplier<BiliCall<Response<T>>> caller) {
        try {
            Response<T> res = caller.get().execute().body();
            if (Objects.isNull(res)) {
                throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "暂无数据");
            }
            if (!Objects.equals(ErrorCodeType.SUCCESS.getCode(), res.getCode())) {
                log.info("[AbstractOpenService] {} error, code={}, msg={}", methodName, res.getCode(), res.getMessage());
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), res.getMessage());
            }
            return res.getData();
        } catch (BiliBreakerRejectedException e) {
            log.error("[AbstractOpenService] {} BiliBreakerRejectedException error", methodName, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_BUSY.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("[AbstractOpenService] {} error", methodName, e);
            throw e;
        }
    }

    protected <T> T callBiliResponse(String methodName, BiFunction<String, RequestBody, BiliCall<BiliApiRespDTO<T>>> caller, Object obj, String token) {
        return callBiliResponse(methodName, () -> caller.apply(token, toRequestBodyUnderLine(obj)));
    }

    protected <T> T callBiliResponse(String methodName, Supplier<BiliCall<BiliApiRespDTO<T>>> caller) {
        try {
            BiliApiRespDTO<T> res = caller.get().execute().body();
            if (Objects.isNull(res)) {
                throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "暂无数据");
            }
            if (!Objects.equals(Constants.CODE_SUCCESS, res.getCode())) {
                log.info("[AbstractOpenService] {} error, code={}, msg={}", methodName, res.getCode(), res.getMessage());
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), res.getMessage());
            }
            return res.getData();
        } catch (BiliBreakerRejectedException e) {
            log.error("[AbstractOpenService] {} BiliBreakerRejectedException error", methodName, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_BUSY.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("[AbstractOpenService] {} error", methodName, e);
            throw e;
        }
    }


    protected <T> T callWithoutWrap(String methodName, Supplier<BiliCall<T>> caller) {
        try {
            T res = caller.get().execute().body();
            if (Objects.isNull(res)) {
                throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "暂无数据");
            }
            return res;
        } catch (BiliBreakerRejectedException e) {
            log.error("[AbstractOpenService] {} BiliBreakerRejectedException error", methodName, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_BUSY.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("[AbstractOpenService] {} error", methodName, e);
            throw e;
        }
    }

    protected RequestBody toRequestBody(Object obj) {
        String jsonString = JSON.toJSONString(obj);
        log.info("BiliCall toRequestBody request={}", jsonString);
        return RequestBody.Companion.create(jsonString, MediaType.parse("application/json"));
    }

    protected RequestBody toRequestBody(String obj) {
        return RequestBody.Companion.create(obj, MediaType.parse("application/json"));
    }

    protected RequestBody toRequestBodyUnderLine(Object obj) {
        String jsonString = JsonUtil.writeValueAsString(obj);
        log.info("BiliCall toRequestBody request={}", jsonString);
        return RequestBody.Companion.create(jsonString, MediaType.parse("application/json"));
    }
}
