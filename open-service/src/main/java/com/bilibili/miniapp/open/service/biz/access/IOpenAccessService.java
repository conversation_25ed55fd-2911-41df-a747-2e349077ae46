package com.bilibili.miniapp.open.service.biz.access;

import com.bilibili.miniapp.open.common.annotations.ReadFlag;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;

/**
 * <AUTHOR>
 * @date 2025/1/3 16:30
 */
public interface IOpenAccessService {
    /**
     * 生成一对Access
     * <p>
     * {@param bizName} 业务名 must have text
     */
    OpenAccessBo generateAccess(String companyId, String bizName);

    /**
     * 查询access_key分配的access_token
     * <p>依赖缓存</p>
     * {@param accessKey} must have text
     */
    @ReadFlag({ReadFlag.F.REDIS})
    String getAccessToken(String accessKey);

    /**
     * 查询access_key分配的接入信息
     * {@param accessKey} must have text
     */
    @ReadFlag({ReadFlag.F.DB})
    OpenAccessBo getAccess(String accessKey);

    OpenAccessBo getAccess(Long mid);


    OpenAccessBo getAccess(String companyId, String name);
}
