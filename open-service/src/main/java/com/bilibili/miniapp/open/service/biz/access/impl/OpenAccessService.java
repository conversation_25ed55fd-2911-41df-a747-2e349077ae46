package com.bilibili.miniapp.open.service.biz.access.impl;

import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAccessDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccessPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccessPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/1/6 17:50
 */
@Slf4j
@Service
public class OpenAccessService implements IOpenAccessService {
    @Autowired
    private MiniAppOpenAccessDao openAccessDao;
    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;
    @Autowired
    private ICompanyService companyService;

    @Override
    public OpenAccessBo generateAccess(String companyId, String bizName) {
        Assert.hasText(companyId, "企业id不能为空");
        Assert.hasText(bizName, "企业名称不能为空");

        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_ACCESS_CREATION.getPattern(), companyId);
        RLock lock = cacheRepository.tryLock(lockKey);
        try {
            OpenAccessBo access = getAccess(companyId, bizName);
            if (access != null) {
                return access;
            }
            Pair<String, String> accessPair = generateAccessPair();
            MiniAppOpenAccessPo miniAppOpenAccessPo = MiniAppOpenAccessPo.builder()
                    .bizName(bizName)
                    .accessKey(accessPair.getKey())
                    .accessToken(accessPair.getValue())
                    .companyId(companyId)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .build();
            openAccessDao.insertSelective(miniAppOpenAccessPo);
            cacheRepository.setObject(buildApiAccessKeyTokenKey(accessPair.getKey()), accessPair.getValue());
            return OpenAccessBo.builder()
                    .accessKey(accessPair.getKey())
                    .accessToken(accessPair.getValue())
                    .build();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public String getAccessToken(String accessKey) {
        return cacheRepository.getObject(buildApiAccessKeyTokenKey(accessKey), String.class);
    }

    @Override
    public OpenAccessBo getAccess(Long mid) {
        CompanyDetailBo createdCompanyDetail = companyService.getCreatedCompanyDetail(mid);
        if (createdCompanyDetail == null) {
            return null;
        }
        CompanyInfoBo companyInfo = createdCompanyDetail.getCompanyInfo();
        return getAccess(companyInfo.getCompanyId(), companyInfo.getCompanyName());
    }

    @Override
    public OpenAccessBo getAccess(String accessKey) {
        if (!StringUtils.hasText(accessKey)) {
            return null;
        }
        MiniAppOpenAccessPoExample example = new MiniAppOpenAccessPoExample();
        example.createCriteria()
                .andAccessKeyEqualTo(accessKey)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<MiniAppOpenAccessPo> appOpenAccessPos = openAccessDao.selectByExample(example);
        if (CollectionUtils.isEmpty(appOpenAccessPos)) {
            return null;
        }
        MiniAppOpenAccessPo miniAppOpenAccessPo = appOpenAccessPos.get(0);
        return OpenAccessBo.builder()
                .accessKey(miniAppOpenAccessPo.getAccessKey())
                .accessToken(miniAppOpenAccessPo.getAccessToken())
                .bizName(miniAppOpenAccessPo.getBizName())
                .build();
    }

    @Override
    public OpenAccessBo getAccess(String companyId, String name) {

        if (org.apache.commons.lang3.StringUtils.isBlank(companyId)) {
            return null;
        }
        MiniAppOpenAccessPoExample example = new MiniAppOpenAccessPoExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andBizNameEqualTo(name)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenAccessPo> appOpenAccessPos = openAccessDao.selectByExample(example);
        if (CollectionUtils.isEmpty(appOpenAccessPos)) {
            return null;
        }
        MiniAppOpenAccessPo miniAppOpenAccessPo = appOpenAccessPos.get(0);
        return OpenAccessBo.builder()
                .accessKey(miniAppOpenAccessPo.getAccessKey())
                .accessToken(miniAppOpenAccessPo.getAccessToken())
                .bizName(miniAppOpenAccessPo.getBizName())
                .build();
    }


    private String buildApiAccessKeyTokenKey(String accessKey) {
        return String.format(RedisKeyPattern.API_ACCESS_KEY_TOKEN.getPattern(), accessKey);
    }

    public static Pair<String, String> generateAccessPair() {
        //32位
        String randomAccessKey = UUID.randomUUID().toString().replaceAll("-", "");
        //32位
        String randomAccessToken = UUID.randomUUID().toString().replaceAll("-", "");
        //LengthEncodedWithBase64 = ⌈原字节数/3⌉*4
        //因此，accessTokenLength=⌈32/3⌉*4=44
        //实际签名使用的是HmacSHA256，其密钥密钥最好等于32B（即256b），因此截取前32B
        String accessToken = Base64.getEncoder().encodeToString(randomAccessToken.getBytes());
        //将可能导致URL安全隐患的特殊字符替换为B
        accessToken = accessToken.replaceAll("[+/=]", "B");
        return Pair.of(randomAccessKey, accessToken.substring(0, 32));
    }

    public static void main(String[] args) {
        System.out.println(OpenAccessService.generateAccessPair());
    }
}
