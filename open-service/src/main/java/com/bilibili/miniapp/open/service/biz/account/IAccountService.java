package com.bilibili.miniapp.open.service.biz.account;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
public interface IAccountService {

    AccountInfoBo getAccountInfo(long mid);

    List<AccountInfoBo> getAccountInfos(List<Long> midList);

    boolean isCompanyOwner(Long mid);

    boolean hasPermission(Long mid, String appId, MiniAppPermission permission);

    boolean hasAnyPermission(Long mid, String appId);

    Integer getUserRole(Long mid, String appId);

    Integer getPermission(String appId, Map<String, MiniAppDTO> mainAppMap, Map<String, MiniAppMemberDTO> roleMap);
}
