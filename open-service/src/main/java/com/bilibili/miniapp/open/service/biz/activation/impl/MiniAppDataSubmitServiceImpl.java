package com.bilibili.miniapp.open.service.biz.activation.impl;

import com.bilibili.miniapp.open.service.biz.activation.IMiniAppDataSubmitService;
import com.bilibili.miniapp.open.service.databus.entity.MiniAppActivationMsg;
import com.bilibili.miniapp.open.service.databus.producer.MiniAppActivationProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 小程序激活上报服务实现
 */
@Slf4j
@Service
public class MiniAppDataSubmitServiceImpl implements IMiniAppDataSubmitService {

    @Autowired
    private MiniAppActivationProducer miniAppActivationProducer;

    @Override
    public void reportActivation(MiniAppActivationMsg activationMsg) {
        miniAppActivationProducer.publishActivationMsg(activationMsg);
    }
}
