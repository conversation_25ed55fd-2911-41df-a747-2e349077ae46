package com.bilibili.miniapp.open.service.biz.applet;

import com.bilibili.miniapp.open.service.bo.applet.AppletInfoBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortContext;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortQueryParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/
public interface IAppletService {

    /**
     * 获取小程序短剧信息列表
     * @return
     */
    List<AppletShortBo> getAppletShortInfoList(AppletShortContext context);

    AppletInfoBo getAppletInfo(AppletShortQueryParam queryParam) throws Exception;

    void refreshAppletInfoCache() throws Exception;
}
