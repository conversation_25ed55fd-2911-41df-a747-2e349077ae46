package com.bilibili.miniapp.open.service.biz.applet;

import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/

public interface IAppletUrlService {

    /**
     * 小程序主页
     * @param appId 小程序id
     * @param appletVersion 小程序框架版本
     * @param params query参数
     * @return
     */
    String getMainPageUrl(String appId, Integer appletVersion, Map<String, Object> params);

    String getSeasonPageUrl(String appId, Integer appletVersion, String path, Map<String, Object> params);
}
