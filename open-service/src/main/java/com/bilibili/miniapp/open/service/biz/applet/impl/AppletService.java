package com.bilibili.miniapp.open.service.biz.applet.impl;

import com.alibaba.fastjson2.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.biz.applet.IAppletService;
import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.IOgvSeasonService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationExtService;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortBo;
import com.bilibili.miniapp.open.service.bo.applet.AppletShortContext;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.impl.SeasonAuthorizationService;
import com.bilibili.miniapp.open.service.bo.applet.*;
import com.bilibili.miniapp.open.service.bo.ogv.EpisodeBo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonQueryBo;
import com.bilibili.miniapp.open.service.config.AppPathConfigCenter;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.bilibili.miniapp.open.service.util.ForkJoinUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@Service
@Slf4j
public class AppletService implements IAppletService {

    @Resource
    private ISeasonAuthorizationExtService seasonAuthorizationExtService;

    @Resource
    private SeasonAuthorizationService seasonAuthorizationService;

    @Resource
    private MiniAppRemoteService miniAppRemoteService;

    @Resource
    private AppPathConfigCenter pathConfigCenter;

    @Resource
    private IOgvSeasonService ogvSeasonService;

    @Resource
    private IAppletUrlService appletUrlService;

    @Autowired
    private IMiniAppCustomLinkService miniAppCustomLinkService;

    @Resource
    private IAuthorAuthorizationService authorAuthorizationService;

    private final ThreadPoolExecutor parallelExecutor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_SHORT_PLAY);

    private final ThreadPoolExecutor refreshExecutor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_DEFAULT);

//    @Override
//    public List<AppletShortBo> getAppletShortInfoList(AppletShortContext context) {
//        StopWatch watch = new StopWatch("AppletShortInfoPipeline");
//        try {
//            List<Long> aids = context.getAids();
//            AssertUtil.isTrue(aids.size() <= 50, ErrorCodeType.BAD_PARAMETER);
//
//            // 并行查询
//            // 增加并行度监控
//            CompletableFuture<List<EpisodeBo>> episodeFuture = CompletableFuture.supplyAsync(() -> {
//                watch.start("QueryEpisodes");
//                List<EpisodeBo> episodeBos = ogvSeasonService.queryEpisodeByAids(aids);
//                watch.stop();
//                return episodeBos;
//            }, parallelExecutor);
//            CompletableFuture<Map<Long, List<SeasonAuthorizationResultBo>>> authFuture = episodeFuture.thenApplyAsync(episodeBos -> {
//                watch.start("QuerySeasonAuth");
//                if (CollectionUtils.isEmpty(episodeBos)) {
//                    return Collections.emptyMap();
//                }
//                List<Long> seasonIds = episodeBos.stream().map(EpisodeBo::getSeasonId).collect(Collectors.toList());
//                // 取出所有的season_id查询是否授权
//                Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIdsWithCache = seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
//                watch.stop();
//                return authorizedAppIdsWithCache;
//            });
//            CompletableFuture<Map<String, MiniAppBaseInfoDto>> appInfoFuture = authFuture.thenApplyAsync(longListMap -> {
//                watch.start("QueryAppInfos");
//                // 获取所有去重的appId
//                List<String> allAppIds = longListMap.values().stream().flatMap(List::stream).map(SeasonAuthorizationResultBo::getAppId).distinct().collect(Collectors.toList());
//                // 获取所有的小程序信息
//                Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(allAppIds);
//                watch.stop();
//                return appBaseInfoDtoMap;
//            });
//            return CompletableFuture.allOf(episodeFuture, authFuture, appInfoFuture)
//                    .thenApplyAsync(v -> {
//                        watch.start("AssembleResult");
//                        List<AppletShortBo> appletShortBos = assembleResult(
//                                episodeFuture.join(),
//                                authFuture.join(),
//                                appInfoFuture.join(),
//                                context.getSourceFrom()
//                        );
//                        watch.stop();
//                        return appletShortBos;
//                    }, parallelExecutor)
//                    .exceptionally(ex -> {
//                        log.error("[AppletService] getAppletShortInfoList failed", ex);
//                        return Collections.emptyList();
//                    }).join();
//        } finally {
//            log.info("[AppletService] getAppletShortInfoList Performance metrics:\n{}", watch.prettyPrint());
//        }
//    }

    @Override
    public List<AppletShortBo> getAppletShortInfoList(AppletShortContext context) {
        StopWatch watch = new StopWatch("AppletShortInfoPipeline");
        try {
            List<Long> aids = context.getAids();
            AssertUtil.isTrue(aids.size() <= 50, ErrorCodeType.BAD_PARAMETER);
            // 增加并行度监控
            // 通过avid列表查询episode列表
            watch.start("QueryEpisodes");
            List<EpisodeBo> episodeBos = ogvSeasonService.queryEpisodeByAids(aids);
            watch.stop();
            // 获取剧授权小程序
            watch.start("QuerySeasonAuth");
            Map<Long, List<SeasonAuthorizationResultBo>> seasonAuth = extractSeasonAuth(episodeBos);
            watch.stop();
            // 获取小程序信息
            watch.start("QueryAppInfos");
            Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = extractAppInfos(seasonAuth);
            watch.stop();
            // 汇总结果
            watch.start("AssembleResult");
            List<AppletShortBo> appletShortBos = assembleResult(episodeBos, seasonAuth, appBaseInfoDtoMap, context.getSourceFrom());
            watch.stop();
            return appletShortBos;
        } finally {
            log.info("[AppletService] getAppletShortInfoList Performance metrics:\n{}", watch.prettyPrint());
        }
    }

    @Override
    public AppletInfoBo getAppletInfo(AppletShortQueryParam queryParam) throws Exception {
        String appId = queryParam.getAppId();
        Long seasonId = queryParam.getSeasonId();
        Map<Long, SeasonBo> seasonBoMap = ogvSeasonService.querySeason4Short(SeasonQueryBo.builder()
                .seasonIdList(List.of(seasonId))
                .appendEpisode(queryParam.isAppendEpisode())
                .appendVideoDimension(queryParam.isAppendVideoDimension())
                .appendAuthorDetail(queryParam.isAppendAuthorDetail())
                .build());
        SeasonBo season = seasonBoMap.get(seasonId);
        if (season == null) {
            log.error("[AppletService] getAppletInfo failed, season not found");
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "剧集不存在");
        }
        UserInfoBo author = season.getAuthor();
        if (author == null || !authorAuthorizationService.isAuthorized(appId, author.getMid())) {
            log.error("[AppletService] getAppletInfo failed, season not authorized");
            throw new ServiceException(ErrorCodeType.UNAUTHORIZED.getCode(), "剧集未授权");
        }
        Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap = miniAppRemoteService.queryAppInfosWithinCache(List.of(appId));
        if (MapUtils.isEmpty(appBaseInfoDtoMap)) {
            log.error("[AppletService] getAppletInfo failed, appId not found");
            throw new ServiceException(ErrorCodeType.NO_DATA);
        }
        MiniAppBaseInfoDto appBaseInfoDto = appBaseInfoDtoMap.get(appId);

        AppletCustomizedContext context = getCustomizedContext(pathConfigCenter.getP(), appId);
        if (Objects.isNull(context)) {
            log.error("[AppletService] getAppletInfo failed, path context is null");
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(), "当前小程序不支持种草");
        }

        return AppletInfoBo.builder()
                .season(season)
                .appletVersion(appBaseInfoDto.getAppletVersion())
                .icon(appBaseInfoDto.getLogo())
                .title(appBaseInfoDto.getName())
                .appId(appBaseInfoDto.getAppId())
                .customizedPath(context.getPath())
                .customizedParams(context.getParams())
                .build();
    }

    @Override
    public void refreshAppletInfoCache() {
        List<String> allAuthorizedAppIds = seasonAuthorizationExtService.getAllAuthorizedAppIds();
        if (CollectionUtils.isEmpty(allAuthorizedAppIds)) {
            log.info("[AppletService] No authorized appIds found");
            return;
        }
        Lists.partition(allAuthorizedAppIds, 16).forEach(appIds -> {
            try {
                refreshExecutor.execute(() -> {
                    try {
                        miniAppRemoteService.refreshMiniAppCache(appIds);
                    } catch (Exception e) {
                        log.error("[AppletService] refreshAppletInfoCache failed. [appIds={}]", appIds, e );
                    }
                });
            } catch (Throwable ex) {
                log.error("[AppletService] refreshAppletInfoCache failed. [appIds={}]", appIds, ex);
            }
        });
    }

    private List<AppletShortBo> assembleResult(List<EpisodeBo> episodeBoList,
                                               Map<Long, List<SeasonAuthorizationResultBo>> authorizedAppIds,
                                               Map<String, MiniAppBaseInfoDto> appBaseInfoDtoMap,
                                               Integer sourceFrom) {
        StopWatch stopWatch = new StopWatch("AssembleResult");
        // 构建返回结果
        List<AppletShortBo> result = Lists.newArrayList();
        // 获取所有的appId
        Set<String> appIds = appBaseInfoDtoMap.keySet();
        Map<String, AppletCustomizedContext> customizedContextMap = getCustomizedContextMap(new ArrayList<>(appIds));
        for (EpisodeBo value : episodeBoList) {
            stopWatch.start("BuildAppletShortBo-" + value.getAid());
            AppletShortBo.AppletShortBoBuilder builder = AppletShortBo.builder();
            builder.seasonId(value.getSeasonId());
            builder.episodeId(value.getEpisodeId());
            builder.aid(value.getAid());
            List<SeasonAuthorizationResultBo> seasonAuthInfos = authorizedAppIds.get(value.getSeasonId());
            if (CollectionUtils.isEmpty(seasonAuthInfos)) {
                // 没有授权信息 直接跳过
                continue;
            } else {
                builder.status(AppletShortBo.Status.builder().isMiniApp(true).build());
                List<AppletShortBo.Candidate> candidates = seasonAuthInfos.stream().map(info -> {
                    String appId = info.getAppId();
                    MiniAppBaseInfoDto baseInfoDto = appBaseInfoDtoMap.get(appId);
                    String jumpUrl = buildJumpUrl(customizedContextMap.get(appId), appId, baseInfoDto.getAppletVersion(), value.getSeasonId(), value.getEpisodeId(), sourceFrom);
//                    String jumpUrl = buildJumpUrl(baseInfoDto, value.getSeasonId(), value.getEpisodeId(), sourceFrom);
                    if (StringUtils.isBlank(jumpUrl)) {
                        return null;
                    }
                    return AppletShortBo.Candidate.builder()
                            .appId(appId)
                            .onlineStatus(Integer.valueOf(baseInfoDto.getOffline()))
                            .authTime(info.getAuthTime())
                            .defaultTag(info.isDefaultTag())
                            .jumpUrl(jumpUrl)
                            .build();
                }).filter(Objects::nonNull).collect(Collectors.toList());
                builder.candidates(candidates);
            }
            result.add(builder.build());
            stopWatch.stop();
        }
        log.info("[AppletService] assembleResult Performance metrics:\n{}", stopWatch.prettyPrint());
        return result;
    }

    private Map<String, MiniAppBaseInfoDto> extractAppInfos(Map<Long, List<SeasonAuthorizationResultBo>> longListMap) {
        // 获取所有去重的appId
        List<String> allAppIds = longListMap.values().stream().flatMap(List::stream).map(SeasonAuthorizationResultBo::getAppId).distinct().collect(Collectors.toList());
        // 获取所有的小程序信息
        return miniAppRemoteService.queryAppInfosWithinCache(allAppIds);
    }

    private Map<Long, List<SeasonAuthorizationResultBo>> extractSeasonAuth(List<EpisodeBo> episodeBos) {
        if (CollectionUtils.isEmpty(episodeBos)) {
            return Collections.emptyMap();
        }
        List<Long> seasonIds = episodeBos.stream().map(EpisodeBo::getSeasonId).collect(Collectors.toList());

        // 取出所有的season_id查询是否授权
        return seasonAuthorizationExtService.getAuthorizedAppIdsWithCache(seasonIds);
    }

    private CompletableFuture<List<EpisodeBo>> asyncGetEpisodes(List<Long> aids) {
        return CompletableFuture.supplyAsync(() -> ogvSeasonService.queryEpisodeByAids(aids), parallelExecutor);
    }

    private String buildJumpUrl(AppletCustomizedContext context, String appId, Integer appletVersion, Long seasonId, Long episodeId, Integer sourceFrom) {
        Map<String, Object> params = new HashMap<>();
        addIfNotNull(params, "sourcefrom", sourceFrom);
        addIfNotNull(params, "bl_episode_id", episodeId);
        addIfNotNull(params, "bl_album_id", seasonId);

        if (Objects.isNull(context)) {
            return null;
        }

        mergeParams(params, context.getParams());

        return appletUrlService.getSeasonPageUrl(
                appId,
                appletVersion,
                context.getPath(),
                params
        );
    }

    private AppletCustomizedContext getCustomizedContext(Map<String, String> pathMapConfig, String appId) {
        MiniAppCustomLinkBo customLink = miniAppCustomLinkService.getCustomLinkFromCache(appId);
        return getCustomizedContext(pathMapConfig, appId, customLink);
    }

    private AppletCustomizedContext getCustomizedContext(Map<String, String> pathMapConfig, String appId, MiniAppCustomLinkBo customLink) {
        Map<String, Object> params = new HashMap<>();
        String path;
        if (customLink == null) {
            String pathConfigStr = pathMapConfig.get(appId);
            if (StringUtils.isBlank(pathConfigStr)) {
                log.warn("[AppletService] Path config is missing for appId: {}", appId);
                return null;
            }

            AppPathConfigCenter.AppPathConfig pathConfig = parsePathConfig(pathConfigStr);
            if (pathConfig == null || StringUtils.isBlank(pathConfig.getPath())) {
                return null;
            }

            mergeParams(params, pathConfig.getParams());
            path = pathConfig.getPath();
        } else {
            String pathPrefix = "/pages";
            if (StringUtils.isBlank(customLink.getCustomPath())) {
                log.warn("[AppletService] Custom path is missing for appId: {}", appId);
                return null;
            }
            if (customLink.getCustomPath().startsWith("/pages")) {
                pathPrefix = "";
            }
            path = pathPrefix + customLink.getCustomPath();
            addCustomParamsIfNecessary(params, customLink.getCustomParams());
        }
        return AppletCustomizedContext.builder()
                .path(path)
                .params(params)
                .build();
    }

    private Map<String, AppletCustomizedContext> getCustomizedContextMap(List<String> appIds) {
        Map<String, MiniAppCustomLinkBo> customLinkFromCacheByAppIds = miniAppCustomLinkService.getCustomLinkFromCacheByAppIds(appIds);
        Map<String, AppletCustomizedContext> result = new HashMap<>();
        Map<String, String> pathMapConfig = pathConfigCenter.getP();
        appIds.forEach(appId -> result.put(appId, getCustomizedContext(pathMapConfig, appId, customLinkFromCacheByAppIds.get(appId))));
        return result;
    }

    private void addCustomParamsIfNecessary(Map<String, Object> params, String customParams) {
        if (StringUtils.isNotBlank(customParams)) {
            Arrays.stream(customParams.split("&"))
                    .forEach(customParam -> {
                        String[] split = customParam.split("=");
                        if (split.length == 2) {
                            String key = split[0];
                            String value = split[1];
                            addIfNotNull(params, key, value);
                        }
                    });
        }
    }

    private void addIfNotNull(Map<String, Object> params, String key, Object value) {
        if (value != null) {
            params.put(key, value);
        }
    }

    private AppPathConfigCenter.AppPathConfig parsePathConfig(String pathConfigStr) {
        try {
            return JSON.parseObject(pathConfigStr, AppPathConfigCenter.AppPathConfig.class);
        } catch (Exception e) {
            log.error("[AppletService] Failed to parse path config", e);
            return null;
        }
    }

    private void mergeParams(Map<String, Object> target, Map<String, Object> source) {
        if (MapUtils.isNotEmpty(source)) {
            for (Map.Entry<String, Object> entry : source.entrySet()) {
                target.putIfAbsent(entry.getKey(), entry.getValue());
            }
        }
    }
}
