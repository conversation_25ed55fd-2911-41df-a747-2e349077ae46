package com.bilibili.miniapp.open.service.biz.applet.impl;

import com.bilibili.miniapp.open.service.biz.applet.IAppletUrlService;
import com.bilibili.miniapp.open.service.common.MiniAppLinkBuilder;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/

@Service
@Slf4j
public class AppletUrlService implements IAppletUrlService {

    @Resource
    private ConfigCenter configCenter;

    public static final int APPLET_VERSION_0 = 0;
    public static final int APPLET_VERSION_1 = 1;

    @Override
    public String getMainPageUrl(String appId, Integer appletVersion, Map<String, Object> params) {
        return new MiniAppLinkBuilder()
                .baseUrl(getBaseUrl(appletVersion))
                .appId(String.valueOf(appId))
                .withParams(params)
                .build();
    }

    @Override
    public String getSeasonPageUrl(String appId, Integer appletVersion, String path, Map<String, Object> params) {
        return new MiniAppLinkBuilder()
                .baseUrl(getBaseUrl(appletVersion))
                .appId(appId)
                .path(path)
                .withParams(params)
                .build();
    }

    private String getBaseUrl(Integer appletVersion) {
        // 默认新版本框架
        if (appletVersion == null) {
            appletVersion = APPLET_VERSION_1;
        }
        String jumpUrlBase = configCenter.getUserAccess().getJumpUrlBase_1();
        if (Objects.equals(appletVersion, APPLET_VERSION_0)) {
            jumpUrlBase = configCenter.getUserAccess().getJumpUrlBase_0();
        }
        return jumpUrlBase;
    }
}
