package com.bilibili.miniapp.open.service.biz.auth;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.service.bo.auth.PreAuthInfo;
import com.bilibili.miniapp.open.service.bo.up_info.UserDetailBo;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 授权验证服务
 * @Date 2025/3/13
 **/
@Component
public class AuthValidationService {

    public void validateUserState(UserDetailBo userDetail) {
        AssertUtil.notNull(userDetail, ErrorCodeType.NO_DATA);
        AssertUtil.isTrue(userDetail.isBindTel(), ErrorCodeType.NO_BIND_TEL);
    }
    
    public void validatePreAuthCode(String code, PreAuthInfo authInfo) {
        AssertUtil.isTrue(authInfo != null && code.equals(authInfo.getPreAuthCode()),
            ErrorCodeType.PRE_AUTH_CODE_EXPIRED);
        AssertUtil.isTrue(Objects.equals(PreAuthInfo.STATUS_UNUSED, authInfo.getStatus()),
            ErrorCodeType.PRE_AUTH_CODE_USED);
    }
}
