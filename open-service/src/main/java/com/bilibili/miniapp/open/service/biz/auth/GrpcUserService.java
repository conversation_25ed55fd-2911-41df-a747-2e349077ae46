package com.bilibili.miniapp.open.service.biz.auth;

import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.service.bo.up_info.UserDetailBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserSensitiveInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.grpc.client.MainSitePassportUserServiceGrpcClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class GrpcUserService {
    private final MainSitePassportUserServiceGrpcClient grpcClient;
    private final ConfigCenter configCenter;

    public UserDetailBo fetchUserDetail(Long mid) {
        try {
            GrpcCallContext context = buildContext(configCenter.getMainSite().getUserDetailTimeout());
            return grpcClient.getUserDetail(mid, context);
        } catch (ServiceException e) {
            log.error("[GrpcUserService] 获取用户详情系统异常 mid={}, errorCode={}", mid, e.getCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("[GrpcUserService] 获取用户详情系统异常 mid={}", mid, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }
    }

    public UserSensitiveInfoBo fetchSensitiveInfo(Long mid, String appId) {
        try {
            GrpcCallContext context = buildContext(configCenter.getMainSite().getUserSensitiveInfoTimeout());
            return grpcClient.getUserSensitiveInfo(mid, appId, context);
        } catch (ServiceException e) {
            log.error("[GrpcUserService] 获取敏感信息服务异常 mid={}, appId={}, code={}", mid, appId, e.getCode(), e);
            throw e;
        } catch (Exception e) {
            log.error("[GrpcUserService] 获取敏感信息系统异常 mid={}, appId={}", mid, appId, e);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }
    }

    private GrpcCallContext buildContext(Long timeout) {
        return GrpcCallContext.builder().timeout(timeout).build();
    }
}
