package com.bilibili.miniapp.open.service.biz.auth;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppExchangeInfoDTO;
import com.bilibili.miniapp.open.service.bo.auth.EncryptedEntity;
import com.bilibili.miniapp.open.service.bo.auth.MiniAppAuthBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/


public interface IMiniAppAuthService {


    MiniAppAuthBo getPreAuthCode(String appId, Long mid);

    EncryptedEntity authLogin(String appId, Long mid, String preAuthCode) throws Exception;

    EncryptedEntity authLogin(String appId, String openId, String preAuthCode) throws Exception;

    /**
     * 根据openId查询当前登录信息包括app_id和mid
     * <p>
     * open_id = md5(app_id + mid)，且数据库表mini_app_login中open_id并不是唯一约束，且从目前数据来看存在open_id重复的case，因此需要app_id尽可能精准匹配
     */
    MiniAppExchangeInfoDTO getMiniAppExchangeInfoSafely(String appId, String openId);
}
