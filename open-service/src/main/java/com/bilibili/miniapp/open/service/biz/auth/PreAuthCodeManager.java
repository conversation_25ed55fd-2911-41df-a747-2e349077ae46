package com.bilibili.miniapp.open.service.biz.auth;

import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.bo.auth.PreAuthInfo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
public class PreAuthCodeManager {
    private final ICacheRepository cacheRepository;
    private final ConfigCenter configCenter;

    private static final String PRE_AUTH_CODE_PREFIX = "PRE_AUTH_";

    public String generateCode() {
        return PRE_AUTH_CODE_PREFIX + System.currentTimeMillis() + "_" 
               + UUID.randomUUID().toString().replace("-", "");
    }

    public void cacheCode(String appId, Long mid, String code) {
        PreAuthInfo authInfo = PreAuthInfo.builder().preAuthCode(code).build();
        cacheRepository.setObject(
            RedisKeyPattern.getUserPreAuthInfoKey(mid, appId),
            authInfo,
            configCenter.getMainSite().getPreAuthCodeExpire(),
            TimeUnit.SECONDS
        );
    }
}
