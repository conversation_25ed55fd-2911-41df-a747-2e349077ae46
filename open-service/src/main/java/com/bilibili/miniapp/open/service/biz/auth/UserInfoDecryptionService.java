package com.bilibili.miniapp.open.service.biz.auth;

import com.alibaba.fastjson2.JSON;
import com.bilibili.miniapp.open.common.entity.SecretKeyPair;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.EncryptUtil;
import com.bilibili.miniapp.open.common.util.GsonUtil;
import com.bilibili.miniapp.open.service.bo.auth.EncryptedEntity;
import com.bilibili.miniapp.open.service.bo.up_info.UserSensitiveInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.MainSiteConfig;
import com.bilibili.miniapp.open.service.config.MiniAppConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户信息解密服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserInfoDecryptionService {
    private final ConfigCenter configCenter;

    public UserSensitiveInfoBo decrypt(UserSensitiveInfoBo encryptedInfo) {
        try {
            MainSiteConfig config = configCenter.getMainSite();
            
            SecretKeyPair pair = SecretKeyPair.builder()
                .key(config.getSensitiveEncryptKey())
                .iv(config.getSensitiveEncryptIv())
                .build();

            String decrypted = EncryptUtil.decryptWithUrlSafe(encryptedInfo.getPayload(), pair);
            UserSensitiveInfoBo result = GsonUtil.toObject(decrypted, UserSensitiveInfoBo.class);
            result.setPayload(encryptedInfo.getPayload()); // Preserve original
            
            log.debug("[UserInfoDecryptionService] 用户信息解密成功 mid={}, info={}", result.getMid(), JSON.toJSONString(result));
            return result;
        } catch (Exception ex) {
            log.error("[UserInfoDecryptionService] 用户信息解密失败 msg={}", ex.getMessage(), ex);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }
    }

    public EncryptedEntity encryptPhoneInfo(UserSensitiveInfoBo userInfo) throws Exception {
        MiniAppConfig config = configCenter.getMiniAppConfig();
        final String secretIv = EncryptUtil.generateSecretIv();
        final SecretKeyPair pair = SecretKeyPair.builder()
                .key(config.getPhoneEncryptKey())
                .iv(secretIv)
                .build();

        return EncryptedEntity.builder()
                .encryptedData(EncryptUtil.encrypt(userInfo.getTel(), pair))
                .iv(secretIv)
                .build();
    }
}
