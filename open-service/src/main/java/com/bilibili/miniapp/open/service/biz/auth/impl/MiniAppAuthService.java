package com.bilibili.miniapp.open.service.biz.auth.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppExchangeInfoDTO;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.auth.*;
import com.bilibili.miniapp.open.service.bo.auth.EncryptedEntity;
import com.bilibili.miniapp.open.service.bo.auth.MiniAppAuthBo;
import com.bilibili.miniapp.open.service.bo.auth.PreAuthInfo;
import com.bilibili.miniapp.open.service.bo.up_info.UserDetailBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserSensitiveInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.MainSiteConfig;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppRemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class MiniAppAuthService extends AbstractOpenService implements IMiniAppAuthService {

    private final GrpcUserService userService;
    private final PreAuthCodeManager codeManager;
    private final AuthValidationService validationService;
    private final UserInfoDecryptionService decryptionService;
    private final IMiniAppRemoteService remoteService;
    private final ConfigCenter configCenter;

    private final ICacheRepository cacheRepository;

    @Override
    public MiniAppAuthBo getPreAuthCode(String appId, Long mid) {
        UserDetailBo userDetail = userService.fetchUserDetail(mid);
        validationService.validateUserState(userDetail);
        String code = codeManager.generateCode();
        codeManager.cacheCode(appId, mid, code);
        return buildAuthResponse(code, userDetail, configCenter.getMainSite());
    }

    @Override
    public EncryptedEntity authLogin(String appId, Long mid, String preAuthCode) throws Exception {
        validateAuthPermission(appId);

        RLock lock = cacheRepository.tryLock(String.format(RedisKeyPattern.OPEN_LOCK_USER_PRE_AUTH_INFO.getPattern(), mid, appId));
        try {
            return processAuthLoginFlow(appId, mid, preAuthCode);
        } finally {
            if (lock.isHeldByCurrentThread()) lock.unlock();
        }
    }

    @Override
    public EncryptedEntity authLogin(String appId, String openId, String preAuthCode) throws Exception {
        final MiniAppExchangeInfoDTO exchangeInfo = getMiniAppExchangeInfoSafely(appId, openId);
        return authLogin(appId, exchangeInfo.getMid(), preAuthCode);
    }

    @Override
    public MiniAppExchangeInfoDTO getMiniAppExchangeInfoSafely(String appId, String openId) {
        MiniAppExchangeInfoDTO info = call("获取ExchangeInfo",
                () -> remoteService.getExchangeInfo(appId, null, openId)
        );
        if (info != null) {
            boolean isValid = openId.equals(info.getOpenId()) && appId.equals(info.getAppId());
            AssertUtil.isTrue(isValid, ErrorCodeType.BAD_DATA.getCode(), "open_id非法");
        }
        return info;
    }

    private MiniAppAuthBo buildAuthResponse(String code, UserDetailBo user, MainSiteConfig config) {
        return MiniAppAuthBo.builder()
                .preauthCode(code)
                .maskedPhone(Objects.requireNonNullElse(user.getHideTel(), ""))
                .expiresIn(config.getPreAuthCodeExpire())
                .build();
    }

    private void validateAndUpdateCode(String code, String cacheKey) {
        PreAuthInfo authInfo = cacheRepository.getObject(cacheKey, PreAuthInfo.class);
        log.info("[MiniAppAuthService] validateAndUpdateCode code={}, authInfo={}", code, authInfo);
        validationService.validatePreAuthCode(code, authInfo);
        authInfo.markAsUsed();
        cacheRepository.setObject(cacheKey, authInfo,
                configCenter.getMainSite().getPreAuthCodeExpire(), TimeUnit.SECONDS);
    }

    private void validateAuthPermission(String appId) {
        if (!configCenter.getMiniAppConfig().getAuthPhoneWhiteList().contains(appId)) {
            log.warn("[MiniAppAuthService] 非法访问尝试 appId={}", appId);
            throw new ServiceException(ErrorCodeType.UNAUTHORIZED.getCode(), "无访问权限");
        }
    }

    private EncryptedEntity processAuthLoginFlow(String appId, Long mid, String code) throws Exception {
        validateAndUpdateCode(code, RedisKeyPattern.getUserPreAuthInfoKey(mid, appId));
        UserSensitiveInfoBo sensitiveInfo = userService.fetchSensitiveInfo(mid, appId);
        sensitiveInfo = decryptionService.decrypt(sensitiveInfo);
        return decryptionService.encryptPhoneInfo(sensitiveInfo);
    }
}
