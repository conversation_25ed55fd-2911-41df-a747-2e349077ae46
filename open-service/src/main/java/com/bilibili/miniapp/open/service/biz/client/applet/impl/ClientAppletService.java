package com.bilibili.miniapp.open.service.biz.client.applet.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.miniapp.open.service.biz.client.applet.IClientAppletService;
import com.bilibili.miniapp.open.service.bo.client.applet.ClientAppletInfoBo;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Service
public class ClientAppletService implements IClientAppletService {

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Override
    public ClientAppletInfoBo appInfo(String appId) {
        MiniAppDTO miniAppDTO = miniAppRemoteService.queryAppInfo(appId);
        if (miniAppDTO == null) {
            return null;
        }
        return ClientAppletInfoBo.builder()
                .miniAppName(miniAppDTO.getName())
                .miniAppLogo(miniAppDTO.getLogo())
                .build();
    }
}
