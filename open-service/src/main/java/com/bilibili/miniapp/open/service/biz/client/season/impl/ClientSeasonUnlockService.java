package com.bilibili.miniapp.open.service.biz.client.season.impl;

import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenClientSeasonUnlockDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenClientSeasonUnlockPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenClientSeasonUnlockPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.client.season.IClientSeasonUnlockService;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Service
public class ClientSeasonUnlockService implements IClientSeasonUnlockService {

    @Autowired
    private MiniAppOpenClientSeasonUnlockDao unlockDao;
    @Autowired
    private ICacheRepository cache;

    private final int cacheDay = 30;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlockSeason(String appId, String openId, Long seasonId, List<Long> epIds) {
        if (CollectionUtils.isEmpty(epIds)) {
            return;
        }
        String lockKey = String.format(RedisKeyPattern.CLIENT_APP_USER_UNLOCK_EPS.getPattern(), appId, openId, seasonId);
        RLock lock = cache.tryLock(lockKey);
        try {
            String epsKey = String.format(RedisKeyPattern.CLIENT_APP_GET_UNLOCK_EPS.getPattern(), appId, openId, seasonId);
            Set<Long> unlockEps = cache.getSet(epsKey, Long.class);

            if (unlockEps.containsAll(epIds)) {
                return;
            }

            Timestamp now = new Timestamp(System.currentTimeMillis());
            //c端接口，使用批量插入
            unlockDao.insertBatch(epIds.stream()
                    .map(epId -> MiniAppOpenClientSeasonUnlockPo.builder()
                            .appId(appId)
                            .openId(openId)
                            .seasonId(seasonId)
                            .epId(epId)
                            .isDeleted(0)
                            .ctime(now)
                            .mtime(now)
                            .build())
                    .collect(Collectors.toList()));

            cache.addAllSet(epsKey, new HashSet<>(epIds), cacheDay, TimeUnit.DAYS);
        }finally {
            lock.unlock();
        }
    }

    @Override
    public List<Long> queryAllUnlockEpIds(String appId, String openId, Long seasonId) {
        String epsKey = String.format(RedisKeyPattern.CLIENT_APP_GET_UNLOCK_EPS.getPattern(), appId, openId, seasonId);
        Set<Long> unlockEps = cache.getSet(epsKey, Long.class);
        if (unlockEps == null) {
            List<Long> unlockEpsInDb = queryUnlockEpsFromDb(appId, openId, seasonId);
            cache.addAllSet(epsKey, new HashSet<>(unlockEpsInDb), cacheDay, TimeUnit.DAYS);
            return unlockEpsInDb;
        }else{
            return new ArrayList<>(unlockEps);
        }
    }

    private List<Long> queryUnlockEpsFromDb(String appId, String openId, Long seasonId) {
        MiniAppOpenClientSeasonUnlockPoExample example = new MiniAppOpenClientSeasonUnlockPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andOpenIdEqualTo(openId)
                .andSeasonIdEqualTo(seasonId)
                .andIsDeletedEqualTo(0);
        List<MiniAppOpenClientSeasonUnlockPo> pos = unlockDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return new ArrayList<>();
        }
        return pos.stream().map(MiniAppOpenClientSeasonUnlockPo::getEpId).collect(Collectors.toList());
    }
}
