package com.bilibili.miniapp.open.service.biz.client.user.impl;

import cn.hutool.core.util.StrUtil;
import com.bilibili.mall.miniapp.dto.applet.Code2SessionDTO;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.service.biz.client.user.IClientUserService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppAppletRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Service
public class ClientUserServiceImpl implements IClientUserService {

    @Autowired
    private MiniAppAppletRemoteService appletRemoteService;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Override
    public String getUserOpenId(String appId, String jsCode) {
        ChannelMiniAppInfoDTO miniApp = miniAppRemoteService.queryAppInfoWithinCache(appId);
        Assert.isTrue(miniApp != null, StrUtil.format("小程序{}不存在", appId));
        String appSecret = miniApp.getAppSecret();
        String fixGrantType = "authorization_code";
        Code2SessionDTO code2Session = appletRemoteService.getCode2Session(appId, appSecret, jsCode, fixGrantType);
        return code2Session.getOpenid();
    }

}
