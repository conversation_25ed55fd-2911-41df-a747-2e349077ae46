package com.bilibili.miniapp.open.service.biz.comment.impl;

import cn.hutool.core.util.StrUtil;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.service.biz.comment.CommentConvertTitleService;
import com.bilibili.miniapp.open.service.bo.comment.CommentJumpUrlInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.enums.SeasonPaymentStatus;
import com.bilibili.miniapp.open.service.rpc.grpc.client.PgcSeasonServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Slf4j
@Service
public class BiliBiliCommentServiceImpl implements CommentConvertTitleService {

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;
    @Autowired
    private PgcSeasonServiceGrpcClient pgcSeasonServiceGrpcClient;
    @Autowired
    private ConfigCenter configCenter;

    @Override
    public boolean match(CommentJumpUrlInfoBo jumpUrlInfo) {
        return StringUtils.isNotBlank(jumpUrlInfo.getSeasonId());
    }

    @Override
    public String getConvertTitle(CommentJumpUrlInfoBo jumpUrlInfo) {
        String appName = getAppName(jumpUrlInfo.getAppId());
        SeasonBo season = getSeason(jumpUrlInfo.getSeasonId());
        return appendTitle(appName, season);
    }

    public String getAppName(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        try {
            ChannelMiniAppInfoDTO miniAppDTO = miniAppRemoteService.queryAppInfoWithinCache(appId);
            return miniAppDTO.getName();
        } catch (Exception e) {
            log.error("获取小程序时发生异常", e);
            return null;
        }
    }

    private SeasonBo getSeason(String seasonIdParam) {
        if (seasonIdParam == null) {
            return null;
        }

        long seasonId = Long.parseLong(seasonIdParam);
        List<SeasonBo> seasonBos = pgcSeasonServiceGrpcClient.querySeason4Short(
                List.of(seasonId),
                GrpcCallContext.builder().timeout(configCenter.getShortPlay().getSeasonTimeout()).build()
        );

        if (CollectionUtils.isEmpty(seasonBos)) {
            return null;
        }
        return seasonBos.get(0);
    }

    private String appendTitle(String appName, SeasonBo season) {

        String defaultDesc = "哔哩哔哩小程序";
        String seasonName = getSeasonName(season);

        if (StringUtils.isAllBlank(appName, seasonName)) {
            return defaultDesc;
        }
        String left;
        String right;
        if (StringUtils.isBlank(seasonName)) {
            left = appName;
            right = defaultDesc;
        } else {
            left = seasonName;
            right = appName;
            if (StringUtils.isBlank(right)) {
                right = defaultDesc;
            }
        }
        return StrUtil.format("{} | {}", left, right);
    }

    private String getSeasonName(SeasonBo season) {
        String seasonName;
        if (season == null) {
            seasonName = null;
        } else {
            if (season.getPaymentStatus() != SeasonPaymentStatus.IAP.getCode()) {
                seasonName = StrUtil.format("免费看《{}》", season.getTitle());
            } else {
                seasonName = StrUtil.format("《{}》", season.getTitle());
            }
        }
        return seasonName;
    }
}
