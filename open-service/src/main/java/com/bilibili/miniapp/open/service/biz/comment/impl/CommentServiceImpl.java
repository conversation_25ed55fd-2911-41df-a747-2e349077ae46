package com.bilibili.miniapp.open.service.biz.comment.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.comment.CommentConvertTitleService;
import com.bilibili.miniapp.open.service.biz.comment.CommentService;
import com.bilibili.miniapp.open.service.bo.comment.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=860448638">评论蓝链接入文档</a>
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
@Slf4j
@Service
public class CommentServiceImpl extends AbstractOpenService implements CommentService {

    private final static String URL_ALBUM_PARAM_NAME = "bl_album_id";
    private final static String URL_EP_PARAM_NAME = "bl_episode_id";

    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private List<CommentConvertTitleService> commentConvertTitleServices;

    @Override
    public BatchGetReplyBlueLinkInfoRespBo convertCommentBlueLinks(BatchGetReplyBlueLinkInfoReqBo req) {
        List<ReplyUrlBo> replyUrls = req.getReplyUrls();
        BatchGetReplyBlueLinkInfoRespBo result = new BatchGetReplyBlueLinkInfoRespBo();
        if (CollectionUtils.isEmpty(replyUrls)) {
            return result;
        }

        List<BlueLinkInfoBo> converted = replyUrls.stream()
                .filter(replyUrl -> CollectionUtils.isNotEmpty(replyUrl.getUrls()))
                .map(replyUrl -> buildBlueLinkInfoBo(req, replyUrl))
                .collect(Collectors.toList());

        result.setBlueLinkInfos(converted);
        return result;
    }

    private BlueLinkInfoBo buildBlueLinkInfoBo(BatchGetReplyBlueLinkInfoReqBo baseInfo, ReplyUrlBo replyUrlBo) {
        BlueLinkInfoBo blueLinkInfoBo = new BlueLinkInfoBo();
        blueLinkInfoBo.setRpid(replyUrlBo.getRpid());
        Map<String, BlueLinkMaterialBo> materialMap = replyUrlBo.getUrls().stream()
                .filter(StringUtils::isNotBlank)
                .map(url -> {
                    BlueLinkMaterialBo materialBo = new BlueLinkMaterialBo();

                    CommentJumpUrlInfoBo jumpUrlInfo = extractInfo(url);

                    String convertTitle = getConvertTitle(jumpUrlInfo);

                    if (StringUtils.isBlank(convertTitle)) {
                        return null;
                    }
                    materialBo.setTitle(convertTitle);
                    materialBo.setPrefixIcon(configCenter.getCommentBlueLink().getPrefixIconUrl());
                    materialBo.setOriginalUrl(url);

                    String processedJumpUrl = processJumpUrl(baseInfo, jumpUrlInfo);
                    String finalJumpUrl = appendReportParams(baseInfo, replyUrlBo, processedJumpUrl);
                    materialBo.setAppSchema(finalJumpUrl);

                    return materialBo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(BlueLinkMaterialBo::getOriginalUrl, Function.identity()));

        blueLinkInfoBo.setBlueLinkMaterials(materialMap);
        return blueLinkInfoBo;
    }

    private CommentJumpUrlInfoBo extractInfo(String originalUrl) {
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(originalUrl).build();

        String appId = extractAppId(uriComponents);
        MultiValueMap<String, String> queryParams = uriComponents.getQueryParams();
        String seasonId = queryParams.getFirst(URL_ALBUM_PARAM_NAME);
        String epId = queryParams.getFirst(URL_EP_PARAM_NAME);
        return CommentJumpUrlInfoBo.builder()
                .appId(appId)
                .seasonId(seasonId)
                .epId(epId)
                .originalUrl(originalUrl)
                .build();
    }

    private String processJumpUrl(BatchGetReplyBlueLinkInfoReqBo baseInfo, CommentJumpUrlInfoBo jumpUrlInfo) {
        if (!configCenter.getCommentBlueLink().isOpenOgvExperiment()) {
            return jumpUrlInfo.getOriginalUrl();
        }

        return applyExperimentLogic(baseInfo, jumpUrlInfo);
    }

    private String appendReportParams(BatchGetReplyBlueLinkInfoReqBo baseInfo, ReplyUrlBo replyInfo, String jumpUrl) {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(jumpUrl);
        uriComponentsBuilder.queryParam("track_id", baseInfo.getTrackId() == null ? "" : baseInfo.getTrackId());
        String sourceFromBlueLink = "100101";
        uriComponentsBuilder.queryParam("sourcefrom", sourceFromBlueLink);

        ReportExtraBo reportExtraBo = new ReportExtraBo();
        reportExtraBo.setFromAvid(getAvid(baseInfo));
        reportExtraBo.setIsUpReply(Objects.equals(baseInfo.getUpMid(), replyInfo.getMid()) ? "1" : "0");
        String jumpFromBlueLink = "10000";
        reportExtraBo.setJumpType(jumpFromBlueLink);

        uriComponentsBuilder.queryParam("reportExtra", JSON.toJSONString(reportExtraBo));
        return uriComponentsBuilder.toUriString();
    }

    private String applyExperimentLogic(BatchGetReplyBlueLinkInfoReqBo baseInfo, CommentJumpUrlInfoBo jumpUrlInfo) {

        String originalUrl = jumpUrlInfo.getOriginalUrl();

        Long mid = baseInfo.getMid();
        if (mid == null) {
            log.warn("[评论蓝链] mid为空，无法应用实验分组逻辑");
            return originalUrl;
        }

        boolean isExperimentGroup = (mid % 2 == 1);
        if (!isExperimentGroup) {
            return originalUrl;
        }

        String appId = jumpUrlInfo.getAppId();
        if (StringUtils.isBlank(appId)) {
            log.warn("[评论蓝链] 无法从URL中提取appId: {}", originalUrl);
            return originalUrl;
        }

        String ogvExperimentAppId = configCenter.getCommentBlueLink().getOgvExperimentAppId();
        if (!Objects.equals(ogvExperimentAppId, appId)) {
            log.warn("[评论蓝链] 非实验指定appId，实际：{}，实验appId：{}", appId, ogvExperimentAppId);
            return originalUrl;
        }

        String experimentUrlsMapJson = configCenter.getCommentBlueLink().getExperimentUrlsMapJson();
        if (StringUtils.isBlank(experimentUrlsMapJson)) {
            return originalUrl;
        }

        Map<String, String> experimentUrls = JSON.parseObject(experimentUrlsMapJson,
                new TypeReference<>() {
                });

        if (StringUtils.isAnyBlank(jumpUrlInfo.getSeasonId(), jumpUrlInfo.getEpId())) {
            log.warn("[评论蓝链] seasonId或者epId不存在,{}", jumpUrlInfo);
            return originalUrl;
        }

        String configKey = jumpUrlInfo.getSeasonId() + "_" + jumpUrlInfo.getEpId();

        if (experimentUrls == null
                || experimentUrls.isEmpty()
                || StringUtils.isBlank(experimentUrls.get(configKey))) {
            log.info("[评论蓝链] appId: {}，对应key:{}，没有配置实验组URL，使用原始URL", appId, configKey);
            return originalUrl;
        }

        String configuredUrl = experimentUrls.get(configKey);

        log.info("[评论蓝链] 已应用实验分组逻辑 mid: {}, appId: {}, 原始URL: {}, 新URL: {}",
                mid, appId, originalUrl, configuredUrl);
        return configuredUrl;
    }

    private String extractAppId(UriComponents uriComponents) {
        try {
            String path = uriComponents.getPath();
            if (StringUtils.isBlank(path)) {
                return null;
            }
            String[] split = path.split("/");
            return split[2];
        } catch (Exception e) {
            log.error("[评论蓝链] 从URL中提取appId时发生错误: {}", uriComponents.getPath(), e);
            return null;
        }
    }

    private String getAvid(BatchGetReplyBlueLinkInfoReqBo baseInfo) {
        SubjectIdBo subjectId = baseInfo.getSubjectId();
        //评论文档中没有具体对应的内容，需要咨询开发
        int archiveType = 1;
        if (subjectId == null || subjectId.getType() != archiveType) {
            return "";
        }
        return String.valueOf(subjectId.getOid());
    }

    private String getConvertTitle(CommentJumpUrlInfoBo jumpUrlInfo) {
        for (CommentConvertTitleService convertTitleService : commentConvertTitleServices) {
            if (convertTitleService.match(jumpUrlInfo)) {
                return convertTitleService.getConvertTitle(jumpUrlInfo);
            }
        }
        return null;
    }
}
