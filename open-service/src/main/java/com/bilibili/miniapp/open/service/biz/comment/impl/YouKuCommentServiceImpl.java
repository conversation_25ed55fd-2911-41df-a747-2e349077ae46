package com.bilibili.miniapp.open.service.biz.comment.impl;

import cn.hutool.core.util.StrUtil;
import com.bilibili.miniapp.open.repository.bo.youku.ShowInfo;
import com.bilibili.miniapp.open.repository.bo.youku.YouKuVideo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl.YouKuShowRepository;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.comment.CommentConvertTitleService;
import com.bilibili.miniapp.open.service.bo.comment.CommentJumpUrlInfoBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Service
public class YouKuCommentServiceImpl implements CommentConvertTitleService {

    private static final String SHOW_ID_PARAM = "showId";
    private static final String VIDEO_ID_PARAM = "videoId";
    @Autowired
    private YouKuShowRepository youKuShowRepository;
    @Autowired
    private ICacheRepository cache;

    private static final Random RANDOM = new Random();

    @Override
    public boolean match(CommentJumpUrlInfoBo jumpUrlInfo) {
        String uriString = jumpUrlInfo.getOriginalUrl();
        return StringUtils.isNotBlank(uriString)
                && (uriString.contains(SHOW_ID_PARAM) || uriString.contains(VIDEO_ID_PARAM));
    }

    @Override
    public String getConvertTitle(CommentJumpUrlInfoBo jumpUrlInfo) {
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(jumpUrlInfo.getOriginalUrl()).build();
        MultiValueMap<String, String> queryParams = uriComponents.getQueryParams();
        String showId = queryParams.getFirst(SHOW_ID_PARAM);
        String videoId = queryParams.getFirst(VIDEO_ID_PARAM);

        if (StringUtils.isAllBlank(showId, videoId)) {
            return null;
        }

        String showName = getShowName(showId, videoId);

        if (StringUtils.isBlank(showName)) {
            return "优酷视频 | 哔哩哔哩小程序";
        }
        return StrUtil.format("《{}》| 优酷视频", showName);
    }


    /**
     * 对于剧为空字符串的情况仍需要保存，防止缓存穿透
     */
    private String getShowName(String showId, String videoId) {

        String showName = doGetShowNameFromCache(showId, videoId);
        if (showName != null) {
            return showName;
        }

        showName = doGetShowNameFromDb(showId, videoId);

        cacheShowName(showName, showId, videoId);

        return showName;
    }

    private void cacheShowName(String showName, String showId, String videoId) {

        int expireDay = 90 + RANDOM.nextInt(10);

        if (StringUtils.isNotBlank(showId)) {
            cache.setObject(String.format(RedisKeyPattern.OPEN_COMMENT_YOUKU_SHOW_ID_GET_SHOW_NAME.getPattern(), showId), showName, expireDay, TimeUnit.DAYS);
        }
        if (StringUtils.isNotBlank(videoId)) {
            cache.setObject(String.format(RedisKeyPattern.OPEN_COMMENT_YOUKU_VIDEO_ID_GET_NAME.getPattern(), videoId), showName, expireDay, TimeUnit.DAYS);
        }
    }


    private String doGetShowNameFromCache(String showId, String videoId) {
        String showName = null;
        if (StringUtils.isNotBlank(showId)) {
            showName = cache.getObject(String.format(RedisKeyPattern.OPEN_COMMENT_YOUKU_SHOW_ID_GET_SHOW_NAME.getPattern(), showId), String.class);
        } else if (StringUtils.isNotBlank(videoId)) {
            showName = cache.getObject(String.format(RedisKeyPattern.OPEN_COMMENT_YOUKU_VIDEO_ID_GET_NAME.getPattern(), videoId), String.class);
        }
        return showName;
    }

    private String doGetShowNameFromDb(String showId, String videoId) {
        String showName = "";
        if (StringUtils.isNotBlank(showId)) {
            ShowInfo showInfo = youKuShowRepository.queryShowInfo(showId);
            if (showInfo != null) {
                showName = showInfo.getName();
            }
        } else if (StringUtils.isNotBlank(videoId)) {
            YouKuVideo youKuVideo = youKuShowRepository.queryVideo(videoId);
            if (youKuVideo != null) {
                ShowInfo showInfo = youKuShowRepository.queryShowInfo(youKuVideo.getShowId());
                showName = showInfo.getName();
            }
        }
        return showName;
    }
}
