package com.bilibili.miniapp.open.service.biz.company;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditListBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
public interface ICompanyService {

    void saveCompanyAdmission(CompanyInfoBo companyAdmissionInfoBo);

    CompanyDetailBo getDetail(long mid);

    CompanyDetailBo getCreatedCompanyDetail(long mid);

    PageResult<CompanyAdmissionAuditListBo> queryAdmissionAuditList(String companyName, AppCompanyAuditStatus auditStatus, Page page);

    CompanyAdmissionAuditInfoBo queryAdmissionAuditDetail(long id);

    void passOrRejectAdmission(long id, AppCompanyAuditStatus auditStatus, String failReason);
}
