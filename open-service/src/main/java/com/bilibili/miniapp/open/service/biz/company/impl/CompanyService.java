package com.bilibili.miniapp.open.service.biz.company.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyDetailDto;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyUserDetailDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyAddQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyDetailAddQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyQuery;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenCompanyAdmissionDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCompanyAdmissionPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCompanyAdmissionPoExample;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.access.impl.OpenAccessService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditInfoBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyAdmissionAuditListBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.company.CompanyInfoBo;
import com.bilibili.miniapp.open.service.mapper.CompanyBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppCompanyRemoteService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ConcurrencyThrottleSupport;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Slf4j
@Service
public class CompanyService implements ICompanyService {

    @Autowired
    private MiniAppCompanyRemoteService companyRemoteClient;
    @Autowired
    private OpenAccessService openAccessService;
    @Autowired
    private RedissonCacheRepository redis;
    @Autowired
    private MiniAppOpenCompanyAdmissionDao companyAdmissionDao;

    public static final Pattern CREDIT_CODE_PATTERN = Pattern.compile("^[A-Z0-9]{18}$");

    @Override
    public void saveCompanyAdmission(CompanyInfoBo companyInfo) {
        validateCompanyInfo(companyInfo);
        long mid = companyInfo.getMid();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_COMPANY_AUDIT.getPattern(), mid);
        RLock lock = redis.tryLock(lockKey);
        try {
            MiniAppOpenCompanyAdmissionPo existedAdmission = getAdmission(mid, null);
            Assert.isTrue(existedAdmission == null
                    || Objects.equals(existedAdmission.getAuditStatus(), AppCompanyAuditStatus.REJECTED.getCode()), "仅有审核不通过的企业信息能变更");

            if (existedAdmission == null) {
                insertAdmission(mid, companyInfo);
            } else {
                updateAdmission(existedAdmission, companyInfo);
            }
        } finally {
            lock.unlock();
        }
    }

    private void validateCompanyInfo(CompanyInfoBo companyInfoBo) {
        // 已经存在的主体不允许再次创建
        CompanyDetailBo createdCompanyDetail = getCreatedCompanyDetail(companyInfoBo.getMid());
        AssertUtil.isTrue(createdCompanyDetail == null, null, "企业信息已存在，不能重复创建");
        AssertUtil.isTrue(StringUtils.isNotBlank(companyInfoBo.getCreditCode()), null, "统一社会信用证代码不能为空");
        AssertUtil.isTrue(CREDIT_CODE_PATTERN.matcher(companyInfoBo.getCreditCode()).matches(), null, "统一社会信用证代码——必须是英文、数字格式（18位阿拉伯数字或大写英文字母）");
    }

    private void updateAdmission(MiniAppOpenCompanyAdmissionPo existedAdmission, CompanyInfoBo updateAdmission) {
        MiniAppOpenCompanyAdmissionPo updatePo = new MiniAppOpenCompanyAdmissionPo();
        updatePo.setId(existedAdmission.getId());
        updatePo.setCompanyName(updateAdmission.getCompanyName());
        updatePo.setEditInfo(JSON.toJSONString(updateAdmission));
        if (existedAdmission.getAuditStatus() == AppCompanyAuditStatus.REJECTED.getCode()) {
            updatePo.setAuditStatus(AppCompanyAuditStatus.PENDING.getCode());
            updatePo.setFailReason("");
        }
        companyAdmissionDao.updateByPrimaryKeySelective(updatePo);
    }

    private void insertAdmission(long mid, CompanyInfoBo companyInfo) {
        MiniAppOpenCompanyAdmissionPo po = MiniAppOpenCompanyAdmissionPo.builder()
                .mid(mid)
                .companyName(companyInfo.getCompanyName())
                .editInfo(JSON.toJSONString(companyInfo))
                .build();

        companyAdmissionDao.insertSelective(po);
    }

    @Override
    public CompanyDetailBo getDetail(long mid) {
        MiniAppOpenCompanyAdmissionPo admission = getAdmission(mid, null);
        boolean queryFromAdmission = admission != null && admission.getAuditStatus() != AppCompanyAuditStatus.PASSED.getCode();
        if (queryFromAdmission) {
            return CompanyBizMapper.MAPPER.toBo(admission);
        } else {
            return getCreatedCompanyDetail(mid);
        }
    }

    @Override
    public CompanyDetailBo getCreatedCompanyDetail(long mid) {
        MiniAppCompanyDetailDto companyDetailDto = getAlreadyExistedCompany(mid);
        if (companyDetailDto == null) {
            return null;
        }
        CompanyInfoBo companyInfoBo = CompanyBizMapper.MAPPER.toCompanyInfoBo(companyDetailDto);
        companyInfoBo.setMid(mid);
        CompanyDetailBo companyDetailBo = new CompanyDetailBo();
        companyDetailBo.setCompanyInfo(companyInfoBo);
        companyDetailBo.setAuditStatus(AppCompanyAuditStatus.PASSED.getCode());
        return companyDetailBo;
    }

    private MiniAppCompanyDetailDto getAlreadyExistedCompany(long mid) {
        MiniAppCompanyQuery miniAppCompanyQuery = new MiniAppCompanyQuery();
        miniAppCompanyQuery.setMid(mid);
        PageInfo<MiniAppCompanyUserDetailDTO> companies = companyRemoteClient.listCompanies(miniAppCompanyQuery);
        if (CollectionUtils.isEmpty(companies.getList())) {
            return null;
        }
        Assert.isTrue(companies.getList().size() == 1, "当前mid关联企业数量异常");
        MiniAppCompanyUserDetailDTO companyUserDetailDTO = companies.getList().get(0);
        String companyId = companyUserDetailDTO.getCompanyId();
        return companyRemoteClient.getCompanyDetail(companyId);
    }

    private MiniAppOpenCompanyAdmissionPo getAdmission(Long mid, Long id) {
        if (mid == null && id == null) {
            return null;
        }
        MiniAppOpenCompanyAdmissionPoExample example = new MiniAppOpenCompanyAdmissionPoExample();
        MiniAppOpenCompanyAdmissionPoExample.Criteria criteria = example.createCriteria()
                .andIsDeletedEqualTo(0);
        if (mid != null) {
            criteria.andMidEqualTo(mid);
        }
        if (id != null) {
            criteria.andIdEqualTo(id);
        }
        List<MiniAppOpenCompanyAdmissionPo> pos = companyAdmissionDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.get(0);
    }

    @Override
    public PageResult<CompanyAdmissionAuditListBo> queryAdmissionAuditList(String companyName, AppCompanyAuditStatus auditStatus, Page page) {
        MiniAppOpenCompanyAdmissionPoExample example = new MiniAppOpenCompanyAdmissionPoExample();
        MiniAppOpenCompanyAdmissionPoExample.Criteria condition = example.createCriteria();
        if (!StringUtils.isBlank(companyName)) {
            condition.andCompanyNameLike(companyName + "%");
        }
        if (Objects.nonNull(auditStatus)) {
            condition.andAuditStatusEqualTo(auditStatus.getCode());
        }

        long count = companyAdmissionDao.countByExample(example);
        if (count == 0) {
            return PageResult.emptyPageResult();
        }

        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        example.setOrderByClause("mtime desc");
        List<MiniAppOpenCompanyAdmissionPo> pos = companyAdmissionDao.selectByExample(example);
        return new PageResult<>(Math.toIntExact(count),
                pos.stream()
                        .map(CompanyBizMapper.MAPPER::toAuditListBo)
                        .collect(Collectors.toList()));
    }

    @Override
    public CompanyAdmissionAuditInfoBo queryAdmissionAuditDetail(long id) {
        MiniAppOpenCompanyAdmissionPo admission = getAdmission(null, id);
        return CompanyBizMapper.MAPPER.toAuditInfoBo(admission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void passOrRejectAdmission(long id, AppCompanyAuditStatus auditStatus, String failReason) {
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_COMPANY_AUDIT.getPattern(), id);
        RLock lock = redis.tryLock(lockKey);
        try {
            MiniAppOpenCompanyAdmissionPo admission = getAdmission(null, id);
            Assert.isTrue(admission != null, "记录不存在");
            doAudit(admission, auditStatus, failReason);
            if (auditStatus == AppCompanyAuditStatus.PASSED) {
                try {
                    createCompany(admission);
                } catch (Exception e) {
                    log.error("创建企业失败", e);
                    throw new ServiceException(e.getMessage());
                }
            }
        } finally {
            lock.unlock();
        }
    }

    private void doAudit(MiniAppOpenCompanyAdmissionPo admission, AppCompanyAuditStatus auditStatus, String failReason) {
        MiniAppOpenCompanyAdmissionPo updatePo = new MiniAppOpenCompanyAdmissionPo();
        updatePo.setId(admission.getId());
        updatePo.setAuditStatus(auditStatus.getCode());
        updatePo.setFailReason("");
        if (auditStatus == AppCompanyAuditStatus.REJECTED) {
            Assert.isTrue(!StringUtils.isBlank(failReason), "请填写失败原因");
            updatePo.setFailReason(failReason);
        }

        MiniAppOpenCompanyAdmissionPoExample example = new MiniAppOpenCompanyAdmissionPoExample();
        example.or()
                .andIdEqualTo(admission.getId())
                .andAuditStatusEqualTo(admission.getAuditStatus())
                .andIsDeletedEqualTo(0);
        companyAdmissionDao.updateByExampleSelective(updatePo, example);
    }

    private void createCompany(MiniAppOpenCompanyAdmissionPo admission) {
        long mid = admission.getMid();
        String editInfo = admission.getEditInfo();
        CompanyInfoBo companyInfoBo = JSON.parseObject(editInfo, CompanyInfoBo.class);
        String companyId = doCreateCompany(companyInfoBo);
        Assert.isTrue(StringUtils.isNoneBlank(companyId), "企业id不能为空");
        log.info("创建企业成功,companyId:{}", companyId);
        boolean bindSuccess = companyRemoteClient.bindCompanyUser(mid, companyId);
        Assert.isTrue(bindSuccess, "企业和mid绑定失败");
        log.info("绑定企业成功,companyId:{}，mid:{}", companyId, mid);
        openAccessService.generateAccess(companyId, companyInfoBo.getCompanyName());
    }

    private String doCreateCompany(CompanyInfoBo companyAdmissionInfoBo) {
        MiniAppCompanyAddQuery miniAppCompanyAddQuery = new MiniAppCompanyAddQuery();
        miniAppCompanyAddQuery.setCompanyName(companyAdmissionInfoBo.getCompanyName());
        byte defaultIsThree = 1;
        miniAppCompanyAddQuery.setThree(defaultIsThree);
        miniAppCompanyAddQuery.setCode(companyAdmissionInfoBo.getCreditCode());

        MiniAppCompanyDetailAddQuery detail = new MiniAppCompanyDetailAddQuery();
        detail.setLicense(companyAdmissionInfoBo.getBusinessLicense());
        detail.setEmail(companyAdmissionInfoBo.getContactEmail());
        detail.setOfficialWebsite(companyAdmissionInfoBo.getOfficialWebsite());
        detail.setAdditionalMaterials(companyAdmissionInfoBo.getAdditionalMaterials());
        detail.setContact(companyAdmissionInfoBo.getOperatorName());
        detail.setPhoneNumber(companyAdmissionInfoBo.getPhoneNumber());
        detail.setCode(companyAdmissionInfoBo.getCreditCode());
        miniAppCompanyAddQuery.setDetail(detail);
        return companyRemoteClient.createCompany(miniAppCompanyAddQuery);
    }

}
