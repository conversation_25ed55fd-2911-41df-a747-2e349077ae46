package com.bilibili.miniapp.open.service.biz.contract.impl;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.MiniAppContractStatusEnum;
import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenContractDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPoExample;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSettlementDetailRespBo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSignUrlBo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.enums.ContractCenterContractType;
import com.bilibili.miniapp.open.service.enums.ContractCenterEntityType;
import com.bilibili.miniapp.open.service.rpc.http.dto.*;
import com.bilibili.miniapp.open.service.rpc.http.impl.ContractCenterApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

/**
 * 合同结算服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class ContractService implements IContractService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private ContractCenterApiService contractCenterApiService;

    @Autowired
    private MiniAppOpenContractDao miniAppOpenContractDao;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private ConfigCenter configCenter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LockRequest(key = "'create_settlement_contract_' + #request.appId")
    public void saveContractSettlement(Long mid, SettlementContractBo request) {

        CompanyDetailBo company = companyService.getCreatedCompanyDetail(mid);

        AssertUtil.isTrue(accountService.hasPermission(mid, request.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenContractPo oldContract = getContract(request.getAppId());
        boolean allowCreation = oldContract == null || oldContract.getContractStatus() == MiniAppContractStatusEnum.REVIEW_FAILED.getCode();
        AssertUtil.isTrue(allowCreation, ErrorCodeType.EXISTS_DATA.getCode(), "合同已存在，请勿重复创建");

        validateContractTime(request);

        CreateContractResult contractResult = createContract(request, company);

        saveContract(request, contractResult, oldContract);
    }


    private CreateContractResult createContract(SettlementContractBo request, CompanyDetailBo company) {
        CreateContractRequest contractRequest = buildCreateContractRequest(request, company);
        return contractCenterApiService.createContract(contractRequest);
    }

    /**
     * 校验合同时间参数
     */
    private void validateContractTime(SettlementContractBo request) {
        Long startTime = request.getContractStartTime();
        Long endTime = request.getContractEndTime();

        AssertUtil.isTrue(startTime > 0 && endTime > 0,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同时间参数不正确");

        AssertUtil.isTrue(endTime > startTime,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同结束时间必须大于开始时间");
    }

    /**
     * 构建合同创建请求
     */
    private CreateContractRequest buildCreateContractRequest(SettlementContractBo request, CompanyDetailBo company) {

        ContractEntity contractEntity = ContractEntity.builder()
                .entityPartner("乙方")
                .entityType(ContractCenterEntityType.COMPANY.getCode())
                .entityName(company.getCompanyInfo().getCompanyName())
                .build();

        return CreateContractRequest.builder()
                .business("小程序结算")
                .entity(List.of("机构"))
                .contractType(ContractCenterContractType.COMPANY.getCode())
                .entities(Collections.singletonList(contractEntity))
                .oaInfo(new OaInfo())
                .requiredTplId(configCenter.getContractConfig().getContractTemplateId())
                .build();
    }

    private void saveContract(SettlementContractBo request, CreateContractResult contractResult, MiniAppOpenContractPo oldContract) {
        if (oldContract != null) {
            MiniAppOpenContractPo delete = new MiniAppOpenContractPo();
            delete.setId(oldContract.getId());
            delete.setIsDeleted(1);

            miniAppOpenContractDao.updateByPrimaryKeySelective(delete);
        }

        MiniAppOpenContractPo contractPo = buildPo(request, contractResult);

        miniAppOpenContractDao.insert(contractPo);
    }

    private MiniAppOpenContractPo buildPo(SettlementContractBo request, CreateContractResult contractResult){
        return MiniAppOpenContractPo.builder()
                .appId(request.getAppId())
                .signatoryName(request.getSignatoryName())
                .signatoryPhone(request.getSignatoryPhone())
                .signatoryEmail(request.getSignatoryEmail())
                .contactAddress(request.getContactAddress())
                .contractStartTime(new Timestamp(request.getContractStartTime()))
                .contractEndTime(new Timestamp(request.getContractEndTime()))
                .contractId(contractResult.getContractId())
                .contractStatus(contractResult.getState())
                .build();
    }


    @Override
    public ContractSettlementDetailRespBo getContractDetail(Long mid, String appId) {
        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenContractPo contract = getContract(appId);
        if (contract == null) {
            return null;
        }

        return ContractSettlementDetailRespBo.builder()
                .contractId(contract.getContractId())
                .signatoryName(contract.getSignatoryName())
                .signatoryPhone(contract.getSignatoryPhone())
                .signatoryEmail(contract.getSignatoryEmail())
                .signatoryAddress(contract.getContactAddress())
                .contractStartTime(contract.getContractStartTime().getTime())
                .contractEndTime(contract.getContractEndTime().getTime())
                .contractStatus(contract.getContractStatus())
                .contractAuditReason(contract.getContractAuditReason())
                .build();
    }

    private MiniAppOpenContractPo getContract(String appId) {
        MiniAppOpenContractPoExample example = new MiniAppOpenContractPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenContractPo> contracts = miniAppOpenContractDao.selectByExample(example);
        if (CollectionUtils.isEmpty(contracts)) {
            return null;
        }
        return contracts.get(0);
    }

    @Override
    public ContractSignUrlBo getContractSignUrl(Long mid, String appId) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenContractPo contract = getContract(appId);
        if (contract == null) {
            return null;
        }

        ContractSignUrlResult signUrlResult = contractCenterApiService.getContractSignUrl(contract.getContractId());
        return ContractSignUrlBo.builder()
                .signUrl(signUrlResult.getSignUrl())
                .build();
    }

    @Override
    public void updateContractStatus(String contractId, MiniAppContractStatusEnum status, long sendMsgTimeForSecond) {

        MiniAppOpenContractPoExample example = new MiniAppOpenContractPoExample();
        example.createCriteria()
                .andContractIdEqualTo(contractId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenContractPo> contracts = miniAppOpenContractDao.selectByExample(example);
        if (CollectionUtils.isEmpty(contracts)) {
            return;
        }

        MiniAppOpenContractPo existedPo = contracts.get(0);

        Timestamp updateTime = new Timestamp(sendMsgTimeForSecond * 1000);
        MiniAppOpenContractPo updatePo = new MiniAppOpenContractPo();
        updatePo.setContractStatus(status.getCode());
        updatePo.setContractMtime(updateTime);

        MiniAppOpenContractPoExample updateExample = new MiniAppOpenContractPoExample();
        updateExample.createCriteria()
                .andContractIdEqualTo(contractId)
                .andContractStatusEqualTo(existedPo.getContractStatus())
                .andContractMtimeLessThan(updateTime)
                .andIsDeletedEqualTo(0);

        miniAppOpenContractDao.updateByExampleSelective(updatePo, updateExample);
    }
}
