package com.bilibili.miniapp.open.service.biz.data;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.service.bo.data.EcpmEventLogBo;

/**
 * <AUTHOR>
 * @date 2025/4/28
 */
public interface EcpmEventLogService {

    PageResult<EcpmEventLogBo> queryEventLog(String appId, String openId, String dateHour, Page page);

}
