package com.bilibili.miniapp.open.service.biz.data.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.data.EcpmEventLogService;
import com.bilibili.miniapp.open.service.bo.data.EcpmEventLogBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.http.impl.EsSearchApiService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/28
 */
@Service
public class EcpmEventLogServiceImpl implements EcpmEventLogService {

    @Autowired
    private ICacheRepository redis;
    @Autowired
    private EsSearchApiService esSearchApi;
    @Autowired
    private ConfigCenter configCenter;

    //匹配yyyy-MM-dd
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
    //匹配yyyy-MM-dd HH
    private static final Pattern DATE_HOUR_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}$");

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter INDEX_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    public PageResult<EcpmEventLogBo> queryEventLog(String appId, String openId, String dateHour, Page page) {

        int maxPageSize = 500;
        AssertUtil.isTrue(page.getPageSize() <= maxPageSize, ErrorCodeType.BAD_PARAMETER.getCode(), "分页大小不能超过500");

        TimeRange timeRange = parseTimeRange(dateHour);

        String request = buildQuery(appId, openId, dateHour, page, timeRange);

        String index = buildIndex(timeRange.getStartTimestamp());

        SearchResponse searchResponse = esSearchApi.searchEventLog(index, request);

        TotalHits totalHits = searchResponse.getHits().getTotalHits();
        if (totalHits == null || totalHits.value == 0) {
            return PageResult.emptyPageResult();
        }

        long total = totalHits.value;
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits.length == 0) {
            return new PageResult<>(Math.toIntExact(total), new ArrayList<>());
        }

        if (hits.length == page.getPageSize()) {
            SearchHit lastOne = hits[hits.length - 1];
            Object[] lastOneSortValues = lastOne.getSortValues();
            recordLastOne(appId, openId, dateHour, page, new SpecifyPositionUniqueKey(lastOneSortValues));
        }

        List<EcpmEventLogBo> logs = Arrays.stream(hits)
                .map(hit -> {
                    String sourceAsString = hit.getSourceAsString();
                    return JSON.parseObject(sourceAsString, EcpmEventLogBo.class);
                }).collect(Collectors.toList());

        return PageResult.<EcpmEventLogBo>builder()
                .total(Math.toIntExact(total))
                .records(logs)
                .build();
    }

    private String buildQuery(String appId, String openId, String dateHour, Page page, TimeRange timeRange) {

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("app_id", appId))
                .must(QueryBuilders.rangeQuery("event_time")
                        .gte(timeRange.getStartTimestamp())
                        .lte(timeRange.getEndTimestamp()));
        if (StringUtils.isNotBlank(openId)) {
            query.must(QueryBuilders.termQuery("open_id", openId));
        }
        sourceBuilder.query(query);
        sourceBuilder.sort(new FieldSortBuilder("event_time").order(SortOrder.ASC));
        sourceBuilder.sort(new FieldSortBuilder("unique_key").order(SortOrder.ASC));

        if (page.getPage() > 1) {
            SpecifyPositionUniqueKey previousPageInfo = getPreviousPageInfo(appId, openId, dateHour, page);
            AssertUtil.isTrue(previousPageInfo != null, ErrorCodeType.ECPM_NO_DATA_OR_PAGE_ERROR);

            sourceBuilder.searchAfter(previousPageInfo.getSearchAfterValues());
        }
        sourceBuilder.size(page.getPageSize());
        return sourceBuilder.toString();
    }

    private String buildIndex(long queryTimeStamp) {
        LocalDateTime localDateTime = new Timestamp(queryTimeStamp).toLocalDateTime();
        String date = INDEX_FORMATTER.format(localDateTime);
        return configCenter.getEsConfig().getEventLogIndexPrefix() + date;
    }

    private TimeRange parseTimeRange(String dateHour) {

        LocalDateTime startTime;
        LocalDateTime endTime;
        try {
            if (DATE_PATTERN.matcher(dateHour).matches()) {
                // yyyy-MM-dd 格式，整天
                LocalDate date = LocalDate.parse(dateHour);
                startTime = date.atStartOfDay();
                endTime = date.atTime(LocalTime.MAX);
            } else if (DATE_HOUR_PATTERN.matcher(dateHour).matches()) {
                // yyyy-MM-dd HH 格式，整小时
                LocalDateTime dateTime = LocalDateTime.parse(dateHour + ":00:00",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                startTime = dateTime.withMinute(0).withSecond(0).withNano(0);
                endTime = dateTime.withMinute(59).withSecond(59).withNano(999_999_999);
            } else {
                // yyyy-MM-dd HH:mm:ss 格式，解析到小时
                LocalDateTime dateTime = LocalDateTime.parse(dateHour, DATE_TIME_FORMATTER);
                startTime = dateTime.withMinute(0).withSecond(0).withNano(0);
                endTime = dateTime.withMinute(59).withSecond(59).withNano(999_999_999);
            }
        } catch (Exception e) {
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(),
                    "时间格式错误: " + dateHour + "，支持的格式: yyyy-MM-dd, yyyy-MM-dd HH, yyyy-MM-dd HH:mm:ss");
        }

        int maxQueryDays = 3;
        LocalDate now = LocalDate.now();
        LocalDate startDay = now.minusDays(maxQueryDays);
        LocalDate endDay = now.plusDays(1);
        AssertUtil.isTrue(startTime.toLocalDate().isAfter(startDay)
                && endTime.toLocalDate().isBefore(endDay), ErrorCodeType.BAD_PARAMETER.getCode(), "仅支持查询近3天数据");

        long startTimestamp = toTimestamp(startTime);
        long endTimestamp = toTimestamp(endTime);

        return new TimeRange(startTimestamp, endTimestamp);
    }

    private static long toTimestamp(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    private void recordLastOne(String appId, String openId, String queryTime, Page page, SpecifyPositionUniqueKey specifyPositionInfo) {
        int position = page.getPage() * page.getPageSize();
        String key = StrUtil.format(RedisKeyPattern.ECPM_EVENT_LOG_POSITION.getPattern(), appId, openId, queryTime, position);
        redis.setObject(key, specifyPositionInfo, 4, TimeUnit.DAYS);
    }

    private SpecifyPositionUniqueKey getPreviousPageInfo(String appId, String openId, String queryTime, Page page) {
        int position = (page.getPage() - 1) * page.getPageSize();
        if (position == 0) {
            return null;
        }
        String key = StrUtil.format(RedisKeyPattern.ECPM_EVENT_LOG_POSITION.getPattern(), appId, openId, queryTime, position);
        return redis.getObject(key, SpecifyPositionUniqueKey.class);
    }


    @Data
    @AllArgsConstructor
    private static class TimeRange {
        private long startTimestamp;
        private long endTimestamp;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class SpecifyPositionUniqueKey {
        private long eventTime;
        private String uniqueKey;

        public SpecifyPositionUniqueKey(Object[] lastOneSortValues) {
            this.eventTime = (long) lastOneSortValues[0];
            this.uniqueKey = (String) lastOneSortValues[1];
        }

        public Object[] getSearchAfterValues() {
            return new Object[]{eventTime, uniqueKey};
        }
    }
}
