package com.bilibili.miniapp.open.service.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 复制自 mall-miniapp的LogChannelOperationEnum
 * <AUTHOR>
 * @date 2025/3/18
 */
@Getter
@AllArgsConstructor
public enum MiniAppChannelEnum {

    BILIBILI_PINK("46f711c1974a468c99c5e79af5509685", "bilibili粉"),
    //盘古
    PAN_GU("de6cefc3a0094fc9aeb457c6e27365c2", "盘古"),
    //漫画
    MAN_HUA("d297b978be0e40c0b9703299ff5f6331", "漫画"),
    //miniappKit
    MINI_APP_KIT("46ad5fa207e049da90f7d8f5bf7a1bb4", "MiniAppKit"),
    //测试
    TEST("6f1dfcf6abd2462bacf42950d15bf04f", "测试"),
    //渠道A
    TEST_A("e0546fce5c904e36993eadf2988b7f86", "渠道_A"),
    //渠道B
    TEST_B("937abaf5c4e5424993a096f6302b4516", "渠道_B");

    private final String channelId;
    private final String channelName;
}
