package com.bilibili.miniapp.open.service.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/22
 */
@Getter
@AllArgsConstructor
public enum MiniAppStatusEnum {


    //0开发版 1审核中 2审核失败 3审核成功 4已发布 5被覆盖
    DEVELOP((byte) 0, "开发版"),
    AUDITING((byte) 1, "审核中"),
    AUDIT_FAIL((byte) 2, "审核失败"),
    AUDIT_SUCCESS((byte) 3, "审核成功"),
    RELEASED((byte) 4, "已发布"),
    ;

    private final byte code;
    private final String desc;
}
