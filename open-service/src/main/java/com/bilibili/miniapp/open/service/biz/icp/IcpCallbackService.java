package com.bilibili.miniapp.open.service.biz.icp;

import com.bilibili.miniapp.open.service.bo.icp.IcpBeiAnResult;
import com.bilibili.miniapp.open.service.bo.icp.IcpConfig;
import com.bilibili.regulation.api.dto.IcpConfigDto;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/
public interface IcpCallbackService {

    /**
     * 处理备案配置
     *
     * @param icpConfig 备案配置
     */
    void handleIcpConfig(IcpConfig icpConfig);

    /**
     * 处理备案结果
     *
     * @param icpBeiAnResult 备案结果
     */
    void handleIcpBeiAnResult(IcpBeiAnResult icpBeiAnResult);

    void handleIcpFlowStatus(Long flowId);

    void handleIcpGovAudit(long flowId);
}
