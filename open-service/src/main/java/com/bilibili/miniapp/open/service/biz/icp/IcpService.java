package com.bilibili.miniapp.open.service.biz.icp;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpAttachment;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpPlatformAudit;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportInfo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.service.bo.icp.*;
import com.bilibili.regulation.api.dto.IcpConfigDto;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/
public interface IcpService {

    void saveIcpReportInfo(IcpReportInfo icpReportInfo);

    IcpReportState getIcpReportState(String appId);

    IdentityAuthResp identityAuth(IcpReportInfo icpReportInfo);

    IcpConfigBo getIcpConfigBo();

    IcpConfigBo getIcpConfigBo(IcpConfig configDto);

    IcpReportInfo getIcpReportInfo(Long flowId);

    void saveIcpPlatformAuditResult(IcpPlatformAudit platformAudit);

    IdentityResult syncIdentityAuthInfo(Long identityId);

    PageInfo<IcpPlatformAuditListBo> queryIcpAuditList(IcpPlatformAuditQueryBo queryBo);

    void reportIcpInfo(Long flowId);

    void retry(Long flowId);

    /**
     * 回刷补充文件，然后自动审核(仅审核中状态)
     */
    void saveAdditionAttachmentThenAutoAudit(List<String> appId, List<IcpAttachment> attachments, boolean isAutoAudit);
}
