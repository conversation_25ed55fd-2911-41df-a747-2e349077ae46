package com.bilibili.miniapp.open.service.biz.icp.impl;

import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.bilibili.miniapp.open.common.enums.IcpReportResultStatusEnum;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpPlatformAudit;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportInfo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpConfigRepository;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpRepository;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl.MiniAppIcpRepository;
import com.bilibili.miniapp.open.service.biz.icp.IcpCallbackService;
import com.bilibili.miniapp.open.service.biz.icp.IcpService;
import com.bilibili.miniapp.open.service.bo.icp.IcpBeiAnResult;
import com.bilibili.miniapp.open.service.bo.icp.IcpConfig;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.mapper.IcpBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRegulationRemoteService;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.bilibili.regulation.api.dto.BeiAnStatusDto;
import com.bilibili.regulation.api.dto.IcpConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/

@Service
@Slf4j
public class IcpCallbackServiceImpl implements IcpCallbackService {

    @Resource
    private IMiniAppIcpRepository miniAppIcpRepository;

    @Resource
    private IcpService icpService;

    @Resource
    private IMiniAppIcpConfigRepository miniAppIcpConfigRepository;

    @Resource
    private MiniAppRegulationRemoteService regulationRemoteService;

    @Resource
    private ConfigCenter configCenter;

    @Override
    public void handleIcpConfig(IcpConfig icpConfig) {
        miniAppIcpConfigRepository.saveIcpConfig(IcpBizMapper.MAPPER.toMiniAppIcpConfig(icpConfig));
        icpService.getIcpConfigBo(icpConfig);
    }

    @Override
    public void handleIcpBeiAnResult(IcpBeiAnResult icpBeiAnResult) {
        handleAudit(icpBeiAnResult.getAuditStatus());
        handleResult(icpBeiAnResult.getBeiAnStatus());
    }

    @Override
    public void handleIcpFlowStatus(Long flowId) {
        IcpReportState icpReportState = miniAppIcpRepository.getIcpReportStateByFlowId(flowId);
        if (Objects.isNull(icpReportState)) {
            log.warn("[IcpCallbackServiceImpl] handleIcpFlowStatus flowId:{} not exist", flowId);
            return;
        }
        // 流转到短信核验
        icpReportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT.getStatus());
        icpReportState.setReportStatus(0);
        IcpPlatformAudit platformAudit = icpReportState.getPlatformAudit();
        platformAudit.setAuditStatus(0);
        platformAudit.setOperator("");
        platformAudit.setFailReasons(List.of());
        miniAppIcpRepository.saveIcpReportState(icpReportState);
    }

    @Override
    public void handleIcpGovAudit(long flowId) {
        IcpReportState icpReportState = miniAppIcpRepository.getIcpReportStateByFlowId(flowId);
        if (Objects.isNull(icpReportState)) {
            log.warn("[IcpCallbackServiceImpl] handleIcpGovAudit flowId:{} not exist", flowId);
            return;
        }
        // 流转到管局审核
        icpReportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_AUDIT.getStatus());
        miniAppIcpRepository.saveIcpReportState(icpReportState);
    }

    private void handleResult(List<IcpBeiAnResult.BeiAnStatus> beiAnStatuses) {
        if (CollectionUtils.isEmpty(beiAnStatuses)) {
            return ;
        }
        for (IcpBeiAnResult.BeiAnStatus beiAnStatus : beiAnStatuses) {
            // 查询
            IcpReportState icpReportState = miniAppIcpRepository.getIcpReportStateByFlowId(beiAnStatus.getFlowId());
            if (Objects.isNull(icpReportState)) {
                // 不存在非法数据
                log.warn("handleResult flowId:{} not exist", beiAnStatus.getFlowId());
                continue;
            }
            // 短信核验中才能操作结果
            if (!IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus().equals(icpReportState.getFlowStatus())) {
                log.warn("[IcpCallbackServiceImpl] handleResult flowId:{} status not match", beiAnStatus.getFlowId());
                continue;
            }
            List<Long> abortGovCodes = configCenter.getRegulation().getAbortGovCodes();
            if (abortGovCodes.contains(beiAnStatus.getJgdm())) {
                // 保证幂等
                log.info("[IcpCallbackServiceImpl] handleResult flowId:{} abortGovCodes", beiAnStatus.getFlowId());
                continue;
            }
            IcpReportResultStatusEnum resultStatusEnum = IcpReportResultStatusEnum.getByCode(beiAnStatus.getJgdm());
            if (resultStatusEnum == null) {
                // 流转到平台审核失败
                IcpPlatformAudit platformAudit = icpReportState.getPlatformAudit();
                IcpPlatformAudit.IcpAuditResult auditResult = new IcpPlatformAudit.IcpAuditResult();
                auditResult.setReason(beiAnStatus.getDmms());
                platformAudit.setFailReasons(List.of(auditResult));
//                platformAudit.setOperator("系统");
                platformAudit.setAuditStatus(2);
                icpReportState.setReportStatus(0);
                icpReportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT_FAIL.getStatus());
            } else {
                switch (resultStatusEnum) {
                    case ICP_REPORT_RESULT_STATUS_9001:
                        icpReportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus());
                        break;
                    case ICP_REPORT_RESULT_STATUS_1090:
                        icpReportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY_TIMEOUT.getStatus());
                        icpReportState.setReportStatus(0);
                        break;
                    case ICP_REPORT_RESULT_STATUS_9002:
                        icpReportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_AUDIT.getStatus());
                        break;
                }
            }
            log.info("[IcpCallbackServiceImpl] handleResult flowId:{} icpReportState:{}", beiAnStatus.getFlowId(), JsonUtil.writeValueAsString(icpReportState));
            miniAppIcpRepository.saveIcpReportState(icpReportState);
        }
    }

    private void handleAudit(List<IcpBeiAnResult.AuditStatus> auditStatuses) {
        if (CollectionUtils.isEmpty(auditStatuses)) {
            return ;
        }
        for (IcpBeiAnResult.AuditStatus auditStatus : auditStatuses) {
            // 查询
            IcpReportState reportState = miniAppIcpRepository.getIcpReportStateByFlowId(auditStatus.getFlowId());
            if (Objects.isNull(reportState)) {
                // 不存在非法数据
                log.warn("[IcpCallbackServiceImpl] handleAudit flowId:{} not exist", auditStatus.getFlowId());
                continue;
            }
            // 管局审核中才能操作审核
            if (!IcpFlowStatusEnum.ICP_STATUS_GOV_AUDIT.getStatus().equals(reportState.getFlowStatus())) {
                log.warn("[IcpCallbackServiceImpl] handleAudit flowId:{} status not match", auditStatus.getFlowId());
                continue;
            }
            Integer shjg = auditStatus.getShjg();
            if (Objects.equals(shjg, 0)) {
                // 审核拒绝
                reportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_AUDIT_FAIL.getStatus());
                reportState.setReportStatus(0);
                reportState.setGovAuditState(3);
                reportState.setGovAuditDescription(auditStatus.getShyj());
                reportState.setGovAuditTime(TimeUtil.getTimestampByStr(auditStatus.getShsj(), "yyyy-MM-dd HH:mm:ss"));
            } else if (Objects.equals(shjg, 1)) {
                // 审核通过
                reportState.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_REGISTER_SUCCESS.getStatus());
                reportState.setGovAuditState(2);
                reportState.setGovAuditTime(TimeUtil.getTimestampByStr(auditStatus.getShsj(), "yyyy-MM-dd HH:mm:ss"));
                // 调用备案服务查询备案号 这里不一定能查到备案号 先不查了，后面由定时任务更新
//                IcpReportInfo reportInfo = miniAppIcpRepository.getIcpReportInfoByFlowId(auditStatus.getFlowId(), true);
//                BeiAnStatusDto beiAnStatusDto = regulationRemoteService.queryBeiAnStatus(reportInfo.getCompany().getLicenseNo(), reportInfo.getApp().getName());
//                if (beiAnStatusDto.isBazt()) {
//                    reportState.setRecordNumber(beiAnStatusDto.getWzbah());
//                }
            }
            log.info("[IcpCallbackServiceImpl] handleAudit flowId:{} reportState:{}", auditStatus.getFlowId(), JsonUtil.writeValueAsString(reportState));
            miniAppIcpRepository.saveIcpReportState(reportState);
        }
    }
}
