package com.bilibili.miniapp.open.service.biz.icp.impl;

import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportInfo;
import com.bilibili.miniapp.open.service.biz.icp.IcpReportValidateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/28
 **/

@Service
@Slf4j
public class IcpReportValidateServiceImpl implements IcpReportValidateService {



    @Override
    public void validate(IcpReportInfo reportInfo) {
        // 校验基础信息
        reportInfo.validate();
        //
    }
}
