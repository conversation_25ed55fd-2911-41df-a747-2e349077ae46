package com.bilibili.miniapp.open.service.biz.icp.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppQuery;
import com.bilibili.miniapp.open.common.entity.IcpConstant;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.IcpApproveTypeEnum;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.ImageConverter;
import com.bilibili.miniapp.open.common.util.ImageUtil;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.*;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpConfigRepository;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.icp.IcpReportValidateService;
import com.bilibili.miniapp.open.service.biz.icp.IcpService;
import com.bilibili.miniapp.open.service.biz.resource.IBossService;
import com.bilibili.miniapp.open.service.bo.icp.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.bilibili.miniapp.open.service.mapper.IcpBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRegulationRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.bilibili.regulation.api.dto.*;
import com.bilibili.regulation.common.constant.BeiAnTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@Service
@Slf4j
public class IcpServiceImpl implements IcpService {

    @Resource
    private IMiniAppIcpRepository miniAppIcpRepository;

    @Resource
    private MiniAppRegulationRemoteService regulationRemoteService;

    @Resource
    private MiniAppRemoteService miniAppRemoteService;

    @Resource
    private ConfigCenter configCenter;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private RedissonCacheRepository redissonCacheRepository;

    @Resource
    private IBossService bossService;

    @Resource
    private IcpReportValidateService validateService;

    @Resource
    private IMiniAppIcpConfigRepository miniAppIcpConfigRepository;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveIcpReportInfo(IcpReportInfo icpReportInfo) {
        validateService.validate(icpReportInfo);
        // 保存备案记录
        Long flowId = icpReportInfo.getFlowId();
        if (Objects.nonNull(flowId) && flowId > 0) {
            // 更新备案记录
            // 校验只有审核失败 和管局审核失败才能编辑
            IcpReportState flowState = miniAppIcpRepository.getIcpReportStateByFlowId(flowId);
            if (flowState == null) {
                log.error("[IcpServiceImpl] saveIcpReportInfo flowState is null, flow_id: {}", flowId);
                throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "备案记录不存在");
            }
            Integer flowStatus = flowState.getFlowStatus();
            if (!IcpFlowStatusEnum.canEditStatus().contains(flowStatus)) {
                log.error("[IcpServiceImpl] saveIcpReportInfo flowState status is not editable, flow_id: {}, status: {}", flowId, flowStatus);
                throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "当前流程单不支持编辑");
            }
            flowState.flowReportEdit();
            miniAppIcpRepository.saveIcpReportState(flowState);
            miniAppIcpRepository.updateIcpReportInfo(icpReportInfo);
        } else {
            MiniAppDTO miniAppDTO = miniAppRemoteService.queryAppInfo(icpReportInfo.getAppId());
            AssertUtil.notNull(miniAppDTO, ErrorCodeType.NO_DATA.getCode(), "小程序信息不存在");
            AssertUtil.isTrue(StringUtils.equals(icpReportInfo.getApp().getName(), miniAppDTO.getName()), ErrorCodeType.BAD_DATA.getCode(), "小程序名称不一致");
            icpReportInfo.getCompany().setCompanyId(miniAppDTO.getCompanyId());
            icpReportInfo.getApp().setWzid(getAppWzid(Long.valueOf(miniAppDTO.getId())));
            // 新增备案记录
            flowId = miniAppIcpRepository.saveIcpReportState(IcpReportState.builder()
                    .appId(icpReportInfo.getAppId())
                    .flowStatus(IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT.getStatus())
                    .platformAudit(IcpPlatformAudit.builder()
                            .appId(icpReportInfo.getAppId())
                            .failReasons(List.of())
                            .build())
                    .build());
            icpReportInfo.setFlowId(flowId);
            miniAppIcpRepository.saveIcpReportInfo(icpReportInfo);
        }
    }

    @Override
    public IcpReportState getIcpReportState(String appId) {
        return miniAppIcpRepository.getIcpReportState(appId);
    }

    @Override
    public IdentityAuthResp identityAuth(IcpReportInfo icpReportInfo) {
        // 获取appId对应的appIntId
        String appId = icpReportInfo.getAppId();
        MiniAppDTO appInfoDTO = miniAppRemoteService.queryAppInfo(appId);
        IdentityAuthInfo identityAuthInfo = covertToIdentityAuthInfoDto(icpReportInfo, appInfoDTO);
        // 保存人证核验信息
        miniAppIcpRepository.saveIcpIdentityAuth(identityAuthInfo);
        IdentityAuthInfoDto authInfoDto = IcpBizMapper.MAPPER.toIdentityAuthInfoDto(identityAuthInfo);
        String qrCode = regulationRemoteService.queryQrCodeV2(authInfoDto);
        return IdentityAuthResp.builder()
                .identityId(identityAuthInfo.getIspWzid())
                .qrcode(qrCode)
                .build();
    }

    @Override
    public IcpConfigBo getIcpConfigBo() {
        IcpConfigBo object = redissonCacheRepository.getObject(RedisKeyPattern.OPEN_PLATFORM_ICP_CONFIG_KET.getPattern(), IcpConfigBo.class);
        if (Objects.nonNull(object)) {
            return object;
        }
        // 查询数据库
        MiniAppIcpConfig icpConfig = miniAppIcpConfigRepository.getIcpConfig();
        IcpConfig config = IcpBizMapper.MAPPER.toIcpConfig(icpConfig);
        return getIcpConfigBo(config);
    }

    @Override
    public IcpConfigBo getIcpConfigBo(IcpConfig config) {
        IcpConfigBo icpConfigBo = new IcpConfigBo();
        icpConfigBo.setAreaCode(config.parseAreaCodeTree());
        icpConfigBo.setOrgType(config.parseOrgTypeTree());
        icpConfigBo.setServiceType(config.parseServiceTypeTree());
        redissonCacheRepository.setObject(RedisKeyPattern.OPEN_PLATFORM_ICP_CONFIG_KET.getPattern(), icpConfigBo, 30, TimeUnit.DAYS);
        return icpConfigBo;
    }

    @Override
    public IcpReportInfo getIcpReportInfo(Long flowId) {
        return miniAppIcpRepository.getIcpReportInfoByFlowId(flowId, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveIcpPlatformAuditResult(IcpPlatformAudit platformAudit) {
        // 通过flowId获取对应的appId
        Long flowId = platformAudit.getFlowId();
        IcpReportState icpReportState = miniAppIcpRepository.getIcpReportStateByFlowId(flowId);
        if (icpReportState == null) {
            log.error("[IcpServiceImpl] saveIcpPlatformAuditResult icpReportState is null, flow_id: {}", flowId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "审批数据不存在");
        }
        // 仅平台审核中状态的流程单才能被审核
        Integer flowStatus = icpReportState.getFlowStatus();
        if (!IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT.getStatus().equals(flowStatus)) {
            log.error("[IcpServiceImpl] saveIcpPlatformAuditResult icpReportState status is not platform audit, flow_id: {}, status: {}", flowId, icpReportState.getFlowStatus());
            throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "当前流程单不支持审核");
        }
        icpReportState.flowPlatformAudit(platformAudit);
        // 审核通过等待发送工信部（目前是未发送状态，通过eventbus发送，之后更新状态已发送，对于发送失败的定时任务调用）
        icpReportState.setReportStatus(0);
        miniAppIcpRepository.saveIcpReportState(icpReportState);
    }

    @Override
    public IdentityResult syncIdentityAuthInfo(Long identityId) {
        IdentityResultDto identityResultDto = regulationRemoteService.sync(identityId, 0);
        if (identityResultDto == null) {
            log.error("[IcpServiceImpl] syncIdentityAuthInfo identityResultDto is null, identityId: {}", identityId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "核验结果不存在");
        }
        IdentityResult identityResult = new IdentityResult();
        IdentityAuthInfo authInfo = miniAppIcpRepository.getIcpIdentityAuthByWzid(identityId);
        identityResult.setFzrName(authInfo.getWzFzrXm());
        identityResult.setFzrCardNo(authInfo.getWzFzrZjhm());
        identityResult.setFzrLicenseType(authInfo.getWzFzrZjlx());
        identityResult.setFzrCardBegin(authInfo.getWzFzrZjyxqStart().getTime());
        identityResult.setFzrCardEnd(authInfo.getWzFzrZjyxqEnd() == null ? null : authInfo.getWzFzrZjyxqEnd().getTime());
        identityResult.setFzrCardLongEffect(authInfo.getWzFzrZjyxqCqyx());
        List<AttachmentDto> attachments = identityResultDto.getAttachments();
        if (CollectionUtils.isNotEmpty(attachments)) {
            List<IcpAttachment> icpAttachments = attachments.stream().map(attach -> {
                IcpAttachment icpAttachment = new IcpAttachment();
                //  附件文件格式，1 为.jpg，2 为.png，3 为.mp4
                IcpAttachment.AttachmentFormat format = IcpAttachment.AttachmentFormat.fromCode(Math.toIntExact(attach.getFjwjgs()));
                String fileName = String.format("%s_%s_%s", identityId, attach.getFjyt(), System.currentTimeMillis());
                if (Objects.nonNull(format)) {
                    fileName = String.format("%s.%s", fileName, format.getDesc());
                }
                String content = bossService.upload("icp", fileName, attach.getFjnr());
                Long fjyt = attach.getFjyt();
                if (Objects.equals(fjyt, 102L)) {
                    // 电子化核验平台返回的 互联网信息服务备案承诺书，上报省管局系统时附件用途键值建议为 10
                    fjyt = 10L;
                } else if (Objects.equals(fjyt, 5L)) {
                    // 活体采集人脸照片
                    identityResult.setVerifyPhoto(content);
                } else if (Objects.equals(fjyt, 4L)) {
                    // 负责人身份证照片
                    identityResult.setFzrCard(content);
                }
                icpAttachment.setContent(content);
                icpAttachment.setFormat(Math.toIntExact(attach.getFjwjgs()));
                icpAttachment.setType(Math.toIntExact(fjyt));
                return icpAttachment;
            }).collect(Collectors.toList());
            miniAppIcpRepository.saveIdentityMaterial(icpAttachments, identityId);
        }
        return identityResult;
    }

    @Override
    public PageInfo<IcpPlatformAuditListBo> queryIcpAuditList(IcpPlatformAuditQueryBo queryBo) {
        // 根据审核状态查询
        IcpPlatformAuditCondition.IcpPlatformAuditConditionBuilder conditionBuilder = IcpPlatformAuditCondition.builder();
        if (CollectionUtils.isNotEmpty(queryBo.getAppId())) {
            conditionBuilder.appIdList(queryBo.getAppId());
        }
        if (Objects.nonNull(queryBo.getAuditStatus())) {
            conditionBuilder.auditStatusList(List.of(queryBo.getAuditStatus()));
        }
        conditionBuilder.orderBy("submit_time desc");
        List<IcpPlatformAudit> icpPlatformAudits = miniAppIcpRepository.queryIcpPlatformAuditByCondition(conditionBuilder.build());
        if (CollectionUtils.isEmpty(icpPlatformAudits)) {
            return new PageInfo<>();
        }
        Map<String, IcpPlatformAudit> platformAuditMap = icpPlatformAudits.stream().collect(Collectors.toMap(IcpPlatformAudit::getAppId, Function.identity(), (v1, v2) -> v1));
        MiniAppQuery.MiniAppQueryBuilder builder = MiniAppQuery.builder();
        builder.pageNum(queryBo.getPageNum());
        builder.pageSize(queryBo.getPageSize());
        builder.appIdList(Lists.newArrayList(platformAuditMap.keySet()));
        builder.name(queryBo.getAppName());
        builder.companyName(queryBo.getCompanyName());
        PageInfo<MiniAppDTO> pageInfo = miniAppRemoteService.listMiniApps(builder.build());
        List<MiniAppDTO> appDtoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(appDtoList)) {
            return new PageInfo<>();
        }
        List<IcpPlatformAuditListBo> platformAuditListBos = appDtoList.stream().map(app -> {
            IcpPlatformAudit platformAudit = platformAuditMap.get(app.getAppId());
            return IcpPlatformAuditListBo.builder()
                    .id(platformAudit.getId())
                    .flowId(platformAudit.getFlowId())
                    .appId(app.getAppId())
                    .appLogo(app.getLogo())
                    .appName(app.getName())
                    .companyName(app.getCompanyName())
                    .auditStatus(platformAudit.getAuditStatus())
                    .submitTime(platformAudit.getSubmitTime())
                    .auditTime(platformAudit.getAuditTime())
                    .operator(platformAudit.getOperator())
                    .build();
        }).collect(Collectors.toList());
        PageInfo<IcpPlatformAuditListBo> page = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, page);
        page.setList(platformAuditListBos);
        return page;
    }

    @Override
    public void reportIcpInfo(Long flowId) {
        IcpReportState state = miniAppIcpRepository.getIcpReportStateByFlowId(flowId);
        if (!state.needReportRegulation()) {
            log.error("[IcpServiceImpl] reportIcpInfo flowId: {} needReportRegulation is false", flowId);
            throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "当前流程单不支持");
        }
        IcpReportInfo reportInfo = miniAppIcpRepository.getIcpReportInfoByFlowId(flowId, true);
        if (reportInfo == null) {
            log.error("[IcpServiceImpl] reportIcpInfo reportInfo is null, flow_id: {}", flowId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "备案信息不存在");
        }
        ReportBeiAnRespDto reportBeiAnRespDto = regulationRemoteService.reportBeiAnInfo(covertToBeiAnInfoDto(reportInfo));
        // 更新备案状态
        state.setReportStatus(1);
        state.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus());
        if (Objects.equals(reportBeiAnRespDto.getBeiAnType(), BeiAnTypeEnum.BEIAN_TYPE_ADD_II.getType())) {
            // 新增服务不会有短信核验
            state.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_AUDIT.getStatus());
        }
        miniAppIcpRepository.saveIcpReportState(state);
    }

    @Override
    public void retry(Long flowId) {
        IcpReportState state = miniAppIcpRepository.getIcpReportStateByFlowId(flowId);
        if (state == null) {
            log.error("[IcpServiceImpl] retry state is null, flow_id: {}", flowId);
            throw new ServiceException(ErrorCodeType.NO_DATA.getCode(), "备案信息不存在");
        }
        if (!Objects.equals(state.getFlowStatus(), IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY_TIMEOUT.getStatus())) {
            log.error("[IcpServiceImpl] retry state status is not gov sms verify timeout, flow_id: {}, status: {}", flowId, state.getFlowStatus());
            throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "当前流程单不支持重新发送短信");
        }
        // 设置发送状态 设置流程状态 等待定时任务发送
        state.setReportStatus(0);
        state.setFlowStatus(IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus());
        miniAppIcpRepository.saveIcpReportState(state);
    }

    @Override
    public void saveAdditionAttachmentThenAutoAudit(List<String> appId, List<IcpAttachment> attachments, boolean isAutoAudit) {
        List<Long> flowIds = miniAppIcpRepository.saveIcpAttachments(appId, attachments, List.of(IcpFlowStatusEnum.ICP_STATUS_PLATFORM_AUDIT.getStatus()));
        if (CollectionUtils.isEmpty(flowIds)) {
            return ;
        }
        // 发送审核消息
        IcpServiceImpl icpService = (IcpServiceImpl) AopContext.currentProxy();
        if (isAutoAudit) {
            for (Long flowId : flowIds) {
                try {
                    icpService.saveIcpPlatformAuditResult(IcpPlatformAudit.builder().flowId(flowId).auditStatus(1).operator("SYSTEM").build());
                } catch (Exception e) {
                    log.error("[IcpServiceImpl] saveAdditionAttachmentThenAutoAudit error, flow_id: {}", flowId, e);
                    // 失败不影响
                }
            }
        }
    }

    private Long getAppWzid(Long id) {
        // 这里因为工信部那边只有正式环境 而所有的环境都共用一个isp_wzid
        // 所以这里区分环境生成(uat使用ISP_WZID_BASE_UAT+id， pre和prod使用id)
        Long ispWzid = id;
        if ("uat".equals(configCenter.getEnv())) {
            ispWzid = IcpConstant.ISP_WZID_BASE_UAT + id;
        }
        return ispWzid;
    }

    private IdentityAuthInfo covertToIdentityAuthInfoDto(IcpReportInfo icpReportInfo, MiniAppDTO appInfoDTO) {
        IdentityAuthInfo identityAuthInfoDto = new IdentityAuthInfo();
        Long ispWzid = getAppWzid(Long.valueOf(appInfoDTO.getId()));
        identityAuthInfoDto.setIspWzid(ispWzid);
        // 主体
        IcpCompany company = icpReportInfo.getCompany();
        identityAuthInfoDto.setDwmc(company.getName());
        identityAuthInfoDto.setShengid(company.getLicenseProvince());
        identityAuthInfoDto.setDwxz(company.getType());
        identityAuthInfoDto.setZjlx(company.getLicenseType());
        identityAuthInfoDto.setZjhm(company.getLicenseNo());
        identityAuthInfoDto.setFzrXm(company.getFzrName());
        identityAuthInfoDto.setFzrZjlx(company.getFzrLicenseType());
        identityAuthInfoDto.setFzrZjhm(company.getFzrCardNo());
        identityAuthInfoDto.setFzrZjyxqStart(company.getFzrCardBegin());
        identityAuthInfoDto.setFzrZjyxqEnd(Objects.equals(company.getFzrCardLongEffect(), 1) ? null : company.getFzrCardEnd());
        identityAuthInfoDto.setFzrZjyxqCqyx(company.getFzrCardLongEffect());
        // 服务
        IcpApp app = icpReportInfo.getApp();
        identityAuthInfoDto.setWzmc(app.getName());
        // 互联网信息服务类型，1 为网站，6 为 APP，7 为小程序，8 为快应用
        identityAuthInfoDto.setFwlx(7);
//        identityAuthInfoDto.setFwlx(app.getServiceType());
        // 是否存在前置审批
        identityAuthInfoDto.setNrlx(app.getApprovalPre());
        identityAuthInfoDto.setWzFzrXm(app.getFzrName());
        identityAuthInfoDto.setWzFzrZjlx(app.getFzrLicenseType());
        identityAuthInfoDto.setWzFzrZjhm(app.getFzrCardNo());
        identityAuthInfoDto.setWzFzrZjyxqStart(app.getFzrCardBegin());
        identityAuthInfoDto.setWzFzrZjyxqEnd(Objects.equals(app.getFzrCardLongEffect(), 1) ? null : app.getFzrCardEnd());
        identityAuthInfoDto.setWzFzrZjyxqCqyx(app.getFzrCardLongEffect());
        return identityAuthInfoDto;
    }

    private BeiAnInfoDto covertToBeiAnInfoDto(IcpReportInfo icpReportInfo) {
        BeiAnInfoDto beiAnInfoDto = new BeiAnInfoDto();
        beiAnInfoDto.setBizId(icpReportInfo.getFlowId());
        beiAnInfoDto.setBizType(1);
        // 备案主体信息
        IcpCompany companyInfo = icpReportInfo.getCompany();
        CompanyInfoDto companyInfoDto = new CompanyInfoDto();
        // company_id 格式 mac10149 取mac后面的
        Long ztid = Long.valueOf(companyInfo.getCompanyId().substring(3));
        companyInfoDto.setLspZtid(ztid);
        companyInfoDto.setDwmc(companyInfo.getName());
        companyInfoDto.setDwxz(Long.valueOf(companyInfo.getType()));
        companyInfoDto.setZjlx(Long.valueOf(companyInfo.getLicenseType()));
        companyInfoDto.setZjhm(companyInfo.getLicenseNo());
        companyInfoDto.setZjzs(companyInfo.getLicenseDetailAddress());
        companyInfoDto.setShengId(Long.valueOf(companyInfo.getLicenseProvince()));
        companyInfoDto.setShiId(Long.valueOf(companyInfo.getLicenseCity()));
        companyInfoDto.setXianId(Long.valueOf(companyInfo.getLicenseCounty()));
        companyInfoDto.setXxdz(companyInfo.getContactDetailAddress());
        companyInfoDto.setFzrXm(companyInfo.getFzrName());
        companyInfoDto.setFzrZjlx(Long.valueOf(companyInfo.getFzrLicenseType()));
        companyInfoDto.setFzrZjhm(companyInfo.getFzrCardNo());
        companyInfoDto.setFzrDzyj(companyInfo.getFzrEmail());
        companyInfoDto.setFzrLxfs1(companyInfo.getFzrPhone());
        companyInfoDto.setYjdh(companyInfo.getFzrEmergency());
        companyInfoDto.setBbfs(1);
        companyInfoDto.setFzrZjyxqStart(TimeUtil.timestampToString(companyInfo.getFzrCardBegin(), "yyyy.MM.dd"));
        companyInfoDto.setFzrZjyxqEnd(Objects.equals(companyInfo.getFzrCardLongEffect(), 1) ? "长期" : TimeUtil.timestampToString(companyInfo.getFzrCardEnd(), "yyyy.MM.dd"));
        companyInfoDto.setFzrZjyxq(companyInfo.getFzrCardLongEffect() == 1);
        beiAnInfoDto.setCompany(companyInfoDto);
        // 备案小程序信息
        IcpApp appInfo = icpReportInfo.getApp();
        MiniAppInfoDto appInfoDto = new MiniAppInfoDto();
        appInfoDto.setLspWzid(appInfo.getWzid());
        appInfoDto.setWzmc(appInfo.getName());
        appInfoDto.setXcxId(appInfo.getAppId());
        Integer approvalPre = appInfo.getApprovalPre();
        if (Objects.equals(approvalPre, 1)) {
            // 需要审批
            IcpApproveTypeEnum approveTypeEnum = IcpApproveTypeEnum.getByType(appInfo.getApprovalType());
            if (approveTypeEnum == null) {
                log.error("[IcpServiceImpl] covertToBeiAnInfoDto approveTypeEnum is null, approval_type: {}", appInfo.getApprovalType());
                throw new ServiceException(ErrorCodeType.BAD_DATA.getCode(), "备案小程序类型错误");
            }
            appInfoDto.setNrlxId(approveTypeEnum.getServiceType());
            appInfoDto.setQzsph(appInfo.getApprovalIsbnNo());
            appInfoDto.setSpwj(appInfo.getApprovalAttachment().stream().map(img -> {
                try {
                    return ImageConverter.optimizeImage(img);
                } catch (Exception e) {
                    log.error("[IcpServiceImpl] covertToBeiAnInfoDto optimizeImage error", e);
                    throw new ServiceException(ErrorCodeType.SYSTEM_ERROR.getCode(), "图片转换base64失败");
                }
            }).collect(Collectors.toList()));
        }
        appInfoDto.setFwnrId(Long.valueOf(appInfo.getServiceType()));
        appInfoDto.setFwlx((short) 7);
        appInfoDto.setYylbId(1);
        appInfoDto.setFzrXm(appInfo.getFzrName());
        appInfoDto.setFzrZjlx(appInfo.getFzrLicenseType());
        appInfoDto.setFzrZjhm(appInfo.getFzrCardNo());
        appInfoDto.setFzrDzyj(appInfo.getFzrEmail());
        appInfoDto.setFzrLxfs1(appInfo.getFzrPhone());
        appInfoDto.setYjdh(appInfo.getFzrEmergency());
        appInfoDto.setFzrZjyxqStart(TimeUtil.timestampToString(appInfo.getFzrCardBegin(), "yyyy.MM.dd"));
        appInfoDto.setFzrZjyxqEnd(Objects.equals(appInfo.getFzrCardLongEffect(), 1) ? "长期" : TimeUtil.timestampToString(appInfo.getFzrCardEnd(), "yyyy.MM.dd"));
        appInfoDto.setFzrZjyxq(appInfo.getFzrCardLongEffect() == 1);
        beiAnInfoDto.setMiniApp(appInfoDto);
        // 人证核验材料
        List<IcpAttachment> attachments = miniAppIcpRepository.queryIdentityMaterialById(appInfo.getWzid());
        List<AttachmentDto> attachmentDtos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(attachments)) {
            attachmentDtos.addAll(attachments.stream().filter(a -> !Objects.equals(a.getType(), 103)).map(attach -> {
                AttachmentDto attachmentDto = new AttachmentDto();
                attachmentDto.setFjnr(ImageUtil.convertImageToBase64(attach.getContent()));
                attachmentDto.setFjwjgs(Long.valueOf(attach.getFormat()));
                attachmentDto.setFjyt(Long.valueOf(attach.getType()));
                return attachmentDto;
            }).collect(Collectors.toList()));
        }
        List<IcpAttachment> attachment = icpReportInfo.getAttachment();
        if (CollectionUtils.isNotEmpty(attachment)) {
            attachmentDtos.addAll(attachment.stream().map(attach -> {
                AttachmentDto attachmentDto = new AttachmentDto();
                try {
                    attachmentDto.setFjnr(ImageConverter.optimizeImage(attach.getContent(), IcpAttachment.AttachmentFormat.JPG.getDesc()));
                } catch (Exception e) {
                    log.error("[IcpServiceImpl] covertToBeiAnInfoDto additional optimizeImage error", e);
                    throw new ServiceException(ErrorCodeType.SYSTEM_ERROR.getCode(), "图片转换base64失败");
                }
                attachmentDto.setFjwjgs(Long.valueOf(IcpAttachment.AttachmentFormat.JPG.getCode()));
                attachmentDto.setFjyt(Long.valueOf(attach.getType()));
                return attachmentDto;
            }).collect(Collectors.toList()));
        }
        beiAnInfoDto.setAttachments(attachmentDtos);
        return beiAnInfoDto;
    }
}
