package com.bilibili.miniapp.open.service.biz.miniapp;

import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCategoryCertificationBo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/7
 */
public interface IMiniAppCategoryService {

    void saveMiniAppCategory(String appId, int categoryId, List<String> certifications);

    void updateMiniAppCategory(String appId, int categoryId, List<String> certifications);

    MiniAppCategoryCertificationBo getMiniAppCategoryInfo(String appId);

    Map<String, MiniAppCategoryCertificationBo> getMiniAppCategoryInfo(List<String> appIds);

    String getCategoryNameByMappingId(Integer mappingId);

}
