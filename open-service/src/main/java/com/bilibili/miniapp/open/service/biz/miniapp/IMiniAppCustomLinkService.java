package com.bilibili.miniapp.open.service.biz.miniapp;

import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
public interface IMiniAppCustomLinkService {

    void saveCustomLink(MiniAppCustomLinkBo miniAppCustomLink);

    MiniAppCustomLinkBo getCustomLinkFromDb(String appId);

    MiniAppCustomLinkBo getCustomLinkFromCache(String appId);

    Map<String, MiniAppCustomLinkBo> getCustomLinkFromCacheByAppIds(List<String> appIds);

}
