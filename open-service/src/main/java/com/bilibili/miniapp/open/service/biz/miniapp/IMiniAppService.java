package com.bilibili.miniapp.open.service.biz.miniapp;

import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.AppCompanyAuditStatus;
import com.bilibili.miniapp.open.common.enums.MiniAppModifyType;
import com.bilibili.miniapp.open.service.bo.miniapp.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
public interface IMiniAppService {
    void saveMiniAppAdmission(long mid, MiniAppBo miniAppBo);

    PageResult<MiniAppListBo> queryMiniAppList(long mid, String name, Integer page, Integer size);

    MiniAppDetailBo getMiniAppDetail(long mid, String appId, Long admissionId);

    PageResult<MiniAppAdmissionAuditListBo> queryAdmissionAuditList(String appId,
                                                                    AppCompanyAuditStatus auditStatus,
                                                                    MiniAppModifyType type,
                                                                    Page page);

    MiniAppAdmissionAuditDetailBo queryAdmissionAuditDetail(Long admissionId);

    void passOrRejectAdmission(long id, AppCompanyAuditStatus auditStatus, String failReason);

    /**
     * 初始化积木小程序种草配置
     */
    void initDefaultMiniAppPlantingConfig(List<String> appIds);


    String checkName(String appId, String name);

    List<MiniAppCategoryTree> getCategoryTree();

    void initDefaultMiniAppCategory();

    Boolean updateName(String appId, String name);
}