package com.bilibili.miniapp.open.service.biz.miniapp.impl;

import com.bilibili.miniapp.open.common.enums.CategoryType;
import com.bilibili.miniapp.open.common.util.EnvUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenCategoryCertificationDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCategoryCertificationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCategoryCertificationPoExample;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCategoryService;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCategoryCertificationBo;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppCatRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/7
 */
@Service
public class MiniAppCategoryService implements IMiniAppCategoryService {
    @Autowired
    private MiniAppOpenCategoryCertificationDao dao;
    @Autowired
    private MiniAppCatRemoteService miniAppCatRemoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMiniAppCategory(String appId, int categoryId, List<String> certifications) {

        if (CollectionUtils.isEmpty(certifications)) {
            return;
        }
        int mappingId = CategoryType.getMappingId(categoryId);
        certifications.forEach(certification -> {
            MiniAppOpenCategoryCertificationPo po = MiniAppOpenCategoryCertificationPo.builder()
                    .appId(appId)
                    .categoryId(mappingId)
                    .certification(certification)
                    .build();
            dao.insertSelective(po);
        });
        boolean updated = miniAppCatRemoteService.updateMiniAppCats(appId, List.of(mappingId));
        if (!updated) {
            throw new RuntimeException("更新小程序分类失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMiniAppCategory(String appId, int categoryId, List<String> certifications) {

        int mappingId = CategoryType.getMappingId(categoryId);
        List<MiniAppOpenCategoryCertificationPo> existedPos = queryList(appId);
        if (needUpdate(mappingId, certifications, existedPos)) {
            if (!CollectionUtils.isEmpty(existedPos)) {
                MiniAppOpenCategoryCertificationPoExample example = new MiniAppOpenCategoryCertificationPoExample();
                example.or()
                        .andAppIdEqualTo(appId)
                        .andCategoryIdEqualTo(mappingId)
                        .andIsDeletedEqualTo(0);
                MiniAppOpenCategoryCertificationPo deletePo = MiniAppOpenCategoryCertificationPo.builder()
                        .isDeleted(1)
                        .build();
                dao.updateByExampleSelective(deletePo, example);
            }

            saveMiniAppCategory(appId, categoryId, certifications);
        }
    }

    private boolean needUpdate(int mappingId, List<String> certifications, List<MiniAppOpenCategoryCertificationPo> existedPos) {
        if (certifications.size() != existedPos.size()) {
            return true;
        }
        for (MiniAppOpenCategoryCertificationPo existedPo : existedPos) {
            if (existedPo.getCategoryId() != mappingId) {
                return true;
            }
            if (!certifications.contains(existedPo.getCertification())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, MiniAppCategoryCertificationBo> getMiniAppCategoryInfo(List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return new HashMap<>();
        }
        MiniAppOpenCategoryCertificationPoExample example = new MiniAppOpenCategoryCertificationPoExample();
        example.or()
                .andAppIdIn(appIds)
                .andIsDeletedEqualTo(0);
        List<MiniAppOpenCategoryCertificationPo> pos = dao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return new HashMap<>();
        }else{

            Map<String, List<MiniAppOpenCategoryCertificationPo>> map = pos.stream()
                    .collect(Collectors.groupingBy(MiniAppOpenCategoryCertificationPo::getAppId));

            Map<String, MiniAppCategoryCertificationBo> result = new HashMap<>();
            map.forEach((appId, categoryList) -> {
                MiniAppCategoryCertificationBo certificationBo = MiniAppCategoryCertificationBo.builder()
                        .appId(appId)
                        .categoryId(categoryList.get(0).getCategoryId())
                        .certifications(categoryList.stream().map(MiniAppOpenCategoryCertificationPo::getCertification).collect(Collectors.toList()))
                        .build();
                result.put(appId, certificationBo);
            });
            return result;
        }
    }

    @Override
    public String getCategoryNameByMappingId(Integer mappingId) {
        return CategoryType.getByMappingId(mappingId).getDesc();
    }


    private int getMappingId(CategoryType categoryType) {
        return EnvUtil.isProdOrPre() ? categoryType.getProdMappingId() : categoryType.getUatMappingId();
    }


    @Override
    public MiniAppCategoryCertificationBo getMiniAppCategoryInfo(String appId) {

        List<MiniAppOpenCategoryCertificationPo> pos = queryList(appId);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        MiniAppOpenCategoryCertificationPo anyOne = pos.get(0);
        return MiniAppCategoryCertificationBo.builder()
                .appId(appId)
                .categoryId(anyOne.getCategoryId())
                .certifications(pos.stream().map(MiniAppOpenCategoryCertificationPo::getCertification).collect(Collectors.toList()))
                .build();
    }

    private List<MiniAppOpenCategoryCertificationPo> queryList(String appId) {
        MiniAppOpenCategoryCertificationPoExample example = new MiniAppOpenCategoryCertificationPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andIsDeletedEqualTo(0);
        List<MiniAppOpenCategoryCertificationPo> pos = dao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return List.of();
        }else{
            return pos;
        }
    }

}