package com.bilibili.miniapp.open.service.biz.miniapp.impl;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenCustomLinkDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCustomLinkPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenCustomLinkPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.bo.miniapp.MiniAppCustomLinkBo;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Service
public class MiniAppCustomLinkService implements IMiniAppCustomLinkService {

    @Autowired
    private MiniAppOpenCustomLinkDao customLinkDao;
    @Autowired
    private ICacheRepository redis;

    private static final Pattern KV_PATTERN = Pattern.compile("^(?:[^=&]+=[^=&]+)(?:&[^=&]+=[^=&]+)*$");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCustomLink(MiniAppCustomLinkBo miniAppCustomLink) {
        AssertUtil.isTrue(miniAppCustomLink.getCustomPath().length() <= 200, ErrorCodeType.BAD_PARAMETER.getCode(), "路径过长");
        AssertUtil.isTrue(miniAppCustomLink.getCustomPath().startsWith("/"), ErrorCodeType.BAD_PARAMETER.getCode(), "路径必须以/开头");
        if (StringUtils.isNotBlank(miniAppCustomLink.getCustomParams())) {
            AssertUtil.isTrue(miniAppCustomLink.getCustomParams().length() <= 200, ErrorCodeType.BAD_PARAMETER.getCode(), "参数过长");
            AssertUtil.isTrue(KV_PATTERN.matcher(miniAppCustomLink.getCustomParams()).matches(), ErrorCodeType.BAD_PARAMETER.getCode(), "参数格式不正确，应为k1=v1或k1=v1&k2=v2……");
        }

        String appId = miniAppCustomLink.getAppId();
        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_APP_CUSTOM_LINK.getPattern(), appId);
        RLock lock = redis.tryLock(lockKey);
        try {
            MiniAppOpenCustomLinkPo po = getPo(appId);
            if (po == null) {
                po = new MiniAppOpenCustomLinkPo();
                po.setAppId(appId);
                po.setCustomPath(miniAppCustomLink.getCustomPath());
                po.setCustomParams(miniAppCustomLink.getCustomParams());
                customLinkDao.insertSelective(po);
            } else {
                po.setCustomPath(miniAppCustomLink.getCustomPath());
                po.setCustomParams(miniAppCustomLink.getCustomParams());
                po.setCtime(null);
                po.setMtime(null);
                customLinkDao.updateByPrimaryKeySelective(po);
            }

            redis.setObject(getCacheKey(appId), miniAppCustomLink);
        } finally {
            lock.unlock();
        }
    }

    private String getCacheKey(String appId) {
        return String.format(RedisKeyPattern.OPEN_APP_CUSTOM_LINK.getPattern(), appId);
    }

    @Override
    public MiniAppCustomLinkBo getCustomLinkFromDb(String appId) {
        MiniAppOpenCustomLinkPo po = getPo(appId);
        if (po == null) {
            return null;
        }
        return MiniAppCustomLinkBo.builder()
                .appId(po.getAppId())
                .customPath(po.getCustomPath())
                .customParams(po.getCustomParams())
                .build();
    }

    private MiniAppOpenCustomLinkPo getPo(String appId) {
        MiniAppOpenCustomLinkPoExample example = new MiniAppOpenCustomLinkPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andIsDeleteEqualTo(0);
        List<MiniAppOpenCustomLinkPo> pos = customLinkDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        } else {
            return pos.get(0);
        }
    }

    @Override
    public MiniAppCustomLinkBo getCustomLinkFromCache(String appId) {
        return redis.getObject(getCacheKey(appId), MiniAppCustomLinkBo.class);
    }

    @Override
    public Map<String, MiniAppCustomLinkBo> getCustomLinkFromCacheByAppIds(List<String> appIds) {
        return redis.multiGetObject(
                appIds.stream()
                        .map(this::getCacheKey)
                        .collect(Collectors.toList()),
                MiniAppCustomLinkBo.class
        );
    }
}
