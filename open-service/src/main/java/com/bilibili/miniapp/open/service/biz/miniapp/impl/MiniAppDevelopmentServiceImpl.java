package com.bilibili.miniapp.open.service.biz.miniapp.impl;

import cn.hutool.core.util.StrUtil;
import com.bilibili.mall.miniapp.cmd.miniapp.ImmediatePublishRequest;
import com.bilibili.mall.miniapp.cmd.miniapp.MiniAppStorageUpdateCmd;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppAllStorageDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppStorageDTO;
import com.bilibili.mall.miniapp.enums.MiniAppStorageStatusEnum;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppStorageCheckUpdateQuery;
import com.bilibili.miniapp.open.common.enums.AppDevelopType;
import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.bilibili.miniapp.open.common.util.AppIdValidator;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppIcpRepository;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.enums.TemplateMiniAppVersionEnum;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppDevelopmentService;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.http.dto.UploadBuildPackageRequest;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppStorageRemoteService;
import com.bilibili.miniapp.open.service.util.BotUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/18
 */
@Service
@Slf4j
public class MiniAppDevelopmentServiceImpl implements IMiniAppDevelopmentService {

    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private MiniAppStorageRemoteService miniAppStorageRemoteService;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;
    @Autowired
    private ICacheRepository redis;
    @Autowired
    private IMiniAppIcpRepository miniAppIcpRepository;

    private static final int lengthPerMsg = 100;

    /**
     * 单个消息头长度
     * **[积木模板x.x.x发布结果]** \n
     */
    private static final int msgBaseLength = 30;
    private static final int markDownMsgMaxLength = 4096;
    private static final int msgSize = (markDownMsgMaxLength - msgBaseLength) / lengthPerMsg;

    @Override
    public int uploadTemplatePackage(long mid, String appId) {
        return doUploadTemplatePackage(mid, appId, configCenter.getAppTemplateConfig().getVersion());
    }

    private int doUploadTemplatePackage(long mid, String appId, String version) {
        UploadBuildPackageRequest req = new UploadBuildPackageRequest();
        req.setAppId(appId);
        req.setMid(mid);
        req.setDescription("积木小程序");
        req.setSubUrl(configCenter.getAppTemplateConfig().getTemplatePackageUrl());
        req.setUrl("");
        req.setVersion(version);
        req.setNewVersion(version);
        req.setDescription("");
        return miniAppStorageRemoteService.uploadBuildPackageV4(req);
    }

    @Override
    public void publishTemplateMiniApp(String newVersion) {
        new Thread(() -> {
            RLock lock = redis.tryLock(RedisKeyPattern.OPEN_LOCK_TEMPLATE_MINI_APP_PUBLISH.getPattern());
            sendStartBotMsg(newVersion);
            List<String> msgList = new ArrayList<>();
            try {
                int page = 1;
                int finalPage = page;
                do {
                    PageInfo<MiniAppDTO> templateMiniAppsPage = miniAppRemoteService.listMiniApps(MiniAppQuery.builder()
                            .pageNum(page++)
                            .pageSize(100)
                            .developType(AppDevelopType.TEMPLATE.getCode())
                            .build());

                    if (templateMiniAppsPage.getPages() > finalPage) {
                        finalPage = templateMiniAppsPage.getPages();
                    }

                    for (MiniAppDTO miniApp : templateMiniAppsPage.getList()) {
                        if (AppIdValidator.isApplet(miniApp.getAppId())) {
                            String msg = doPublishTemplateMiniApp(miniApp, newVersion);
                            msgList.add(msg);
                        }
                    }
                } while (page <= finalPage);

                sendFinishedBotMsg(newVersion, msgList);
            } finally {
                lock.unlock();
            }
        }).start();
    }

    private void sendStartBotMsg(String version) {
        BotUtil.sendWithMarkdown(configCenter.getRobotConfig().getTemplateMiniAppPublishBotUrl(), StrUtil.format("**[积木模板小程序发布任务开始，版本号：{}]**", version));
    }

    private void sendFinishedBotMsg(String version, List<String> msgList) {
        String title = StrUtil.format("**[积木模板{}发布结果]** \n ", version);
        List<List<String>> partition = Lists.partition(msgList, msgSize);
        for (List<String> strings : partition) {
            StringBuilder sb = new StringBuilder(title);
            for (String s : strings) {
                sb.append(s).append("\n");
            }
            BotUtil.sendWithMarkdown(configCenter.getRobotConfig().getTemplateMiniAppPublishBotUrl(), sb.toString());
        }

        BotUtil.sendWithMarkdown(configCenter.getRobotConfig().getTemplateMiniAppPublishBotUrl(),
                StrUtil.format("**[积木模板{}发布结果，总计{}个，成功{}个]**", version, msgList.size(), msgList.stream().filter(msg -> StrUtil.contains(msg, "成功")).count()));
    }

    private String doPublishTemplateMiniApp(MiniAppDTO miniApp, String newVersion) {

        String msg;
        try {
            String appId = miniApp.getAppId();
            MiniAppAllStorageDTO storage = miniAppStorageRemoteService.getAllStorage(appId);
            long mid = miniApp.getMid();
            boolean icpRegistered = isIcpRegistered(appId);
            if (!icpRegistered) {
                return StrUtil.format("小程序：[{}] 未完成备案，跳过发布", appId);
            }

            boolean hasReleaseVersion = storage.getLastProduct() != null
                    || storage.getProduct() != null;

            if (!hasReleaseVersion) {
                return StrUtil.format("小程序：[{}] 没有已发布的版本，跳过发布", appId);
            }


            TemplateMiniAppVersionEnum currentState = determineCurrentState(storage);
            switch (currentState) {
                case DEVELOP:
                    handleDevelopVersion(storage.getDevelopment(), mid, appId, newVersion);
                    break;
                case AUDIT:
                    handleAuditVersion(storage.getCheck(), mid, appId, newVersion);
                    break;
                case RELEASED:
                    handleReleaseVersion(storage.getProduct(), mid, appId, newVersion);
                    break;
                default:
                    log.warn("小程序：[{}] 未找到对应版本程序包", appId);
            }
            msg = StrUtil.format("小程序：[{}] 发布成功]", appId);
        } catch (Exception e) {
            msg = StrUtil.format("小程序：[{}] 发布失败异常：[{}]", miniApp.getAppId(), ExceptionUtils.getMessage(e));
        }
        return msg;
    }

    private boolean isIcpRegistered(String appId) {
        if (configCenter.getIcpWhiteList().contains(appId)) {
            return true;
        }
        IcpReportState icpReportState = miniAppIcpRepository.getIcpReportState(appId);
        return icpReportState != null &&
                Objects.equals(icpReportState.getFlowStatus(), IcpFlowStatusEnum.ICP_STATUS_REGISTER_SUCCESS.getStatus());
    }

    private void handleReleaseVersion(MiniAppStorageDTO product, long mid, String appId, String newVersion) {
        if (!product.getVersion().equals(newVersion)) {
            handleDevelopVersion(null, mid, appId, newVersion);
        }
    }


    private void handleDevelopVersion(MiniAppStorageDTO development, long mid, String appId, String newVersion) {
        int storageId;
        if (development == null || !development.getVersion().equals(newVersion)) {
            storageId = doUploadTemplatePackage(mid, appId, newVersion);
        } else {
            storageId = development.getStorageId();
        }
        submitAudit(appId, storageId);
        passAudit(appId, storageId);
        publishToProduction(appId, storageId, newVersion);
    }

    private void handleAuditVersion(MiniAppStorageDTO auditVersion, long mid, String appId, String newVersion) {
        if (!auditVersion.getVersion().equals(newVersion)
                || Objects.equals(auditVersion.getStatus(), MiniAppStorageStatusEnum.CHECK_FAIL.getStatus())) {
            handleDevelopVersion(null, mid, appId, newVersion);
        } else {
            if (Objects.equals(auditVersion.getStatus(), MiniAppStorageStatusEnum.CHECK.getStatus())) {
                int storageId = auditVersion.getStorageId();
                passAudit(appId, storageId);
                publishToProduction(appId, storageId, newVersion);
            }
        }
    }

    private void submitAudit(String appId, int storageId) {
        miniAppStorageRemoteService.updateDevToCheck(MiniAppStorageUpdateCmd.builder()
                .appId(appId)
                .storageId(storageId)
                .build());
    }

    private void passAudit(String appId, int storageId) {
        MiniAppStorageCheckUpdateQuery query = new MiniAppStorageCheckUpdateQuery();
        query.setAppId(appId);
        query.setStorageId(storageId);
        byte passStatus = 3;
        query.setStatus(passStatus);
        miniAppStorageRemoteService.passOrRejectAppPublish(query);
    }

    private void publishToProduction(String appId, int storageId, String version) {
        miniAppStorageRemoteService.immediatePublish(ImmediatePublishRequest.builder()
                .appId(appId)
                .storageId(storageId)
                .version(version)
                .build());
    }


    private TemplateMiniAppVersionEnum determineCurrentState(MiniAppAllStorageDTO storage) {
        if (storage.getProduct() != null) {
            return TemplateMiniAppVersionEnum.RELEASED;
        }
        if (storage.getCheck() != null) {
            return TemplateMiniAppVersionEnum.AUDIT;
        }
        return TemplateMiniAppVersionEnum.DEVELOP;
    }
}
