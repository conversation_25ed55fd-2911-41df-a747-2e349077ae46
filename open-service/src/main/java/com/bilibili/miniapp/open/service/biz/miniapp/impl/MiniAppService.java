package com.bilibili.miniapp.open.service.biz.miniapp.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCheckDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppStorageCheckDTO;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.mall.miniapp.dto.miniapp.channel.MiniAppChannelMinBaseVersionUpdateRequest;
import com.bilibili.mall.miniapp.enums.MiniAppOfflineEnum;
import com.bilibili.mall.miniapp.enums.MiniAppOriginEnum;
import com.bilibili.mall.miniapp.query.miniapp.*;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.PatternConstant;
import com.bilibili.miniapp.open.common.enums.*;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.common.util.MemoryPagination;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAppAdmissionDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAppAdmissionPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAppAdmissionPoExample;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.mini_app.MiniAppCategoryBo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IMiniAppCategoryRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.enums.MiniAppChannelEnum;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCategoryService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppCustomLinkService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppDevelopmentService;
import com.bilibili.miniapp.open.service.biz.miniapp.IMiniAppService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.miniapp.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.mapper.MiniAppBizMapper;
import com.bilibili.miniapp.open.service.rpc.http.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Service
@Slf4j
public class MiniAppService implements IMiniAppService {

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;
    @Autowired
    private MiniAppMemberRemoteService miniAppMemberRemoteService;
    @Autowired
    private MiniAppOpenAppAdmissionDao miniAppAdmissionDao;
    @Autowired
    private MiniAppStorageRemoteService miniAppStorageRemoteService;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private RedissonCacheRepository redis;
    @Autowired
    private IMiniAppCategoryService categoryService;
    @Autowired
    private IOpenAccessService openAccessService;
    @Autowired
    private MiniAppChannelRemoteService channelRemoteService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private MiniAppUrlRemoteService miniAppUrlRemoteService;
    @Autowired
    private IMiniAppDevelopmentService miniAppPublishService;

    @Autowired
    private IMiniAppCustomLinkService customLinkService;

    @Autowired
    private IMiniAppCategoryRepository categoryRepository;
    @Autowired
    private IAccountService accountService;



    @Override
    public void saveMiniAppAdmission(long mid, MiniAppBo miniAppBo) {

        CompanyDetailBo detail = companyService.getDetail(mid);
        Assert.isTrue(detail != null, "无企业信息不可新建小程序");
        String companyId = detail.getCompanyInfo().getCompanyId();
        Assert.isTrue(StringUtils.isNotBlank(companyId), "没有企业id无法创建小程序");

        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_APP_ADMISSION_CREATION.getPattern(), mid);
        RLock lock = redis.tryLock(lockKey);
        try {
            if (miniAppBo.getAdmissionId() == null) {
                doSaveAdmission(mid, miniAppBo);
            } else {
                doUpdateAdmission(mid, miniAppBo);
            }
        } finally {
            lock.unlock();
        }
    }

    private void doUpdateAdmission(long mid, MiniAppBo miniAppBo) {
        MiniAppOpenAppAdmissionPo existedAdmission = getAdmission(mid, miniAppBo.getAdmissionId());
        Assert.isTrue(existedAdmission != null, "该mid没有对应admissionId的信息");
        AssertUtil.isTrue(existedAdmission.getAuditStatus() == AppCompanyAuditStatus.REJECTED.getCode(), null, "只有审核不通过的小程序信息才能变更");

        MiniAppOpenAppAdmissionPo updatePo = new MiniAppOpenAppAdmissionPo();
        updatePo.setId(existedAdmission.getId());
        updatePo.setEditInfo(JSON.toJSONString(miniAppBo));
        updatePo.setAppName(miniAppBo.getAppName());
        if (existedAdmission.getAuditStatus() == AppCompanyAuditStatus.REJECTED.getCode()) {
            updatePo.setAuditStatus(AppCompanyAuditStatus.PENDING.getCode());
            updatePo.setFailReason("");
        }
        miniAppAdmissionDao.updateByPrimaryKeySelective(updatePo);
    }

    private void doSaveAdmission(long mid, MiniAppBo miniAppBo) {
        MiniAppOpenAppAdmissionPo po = MiniAppOpenAppAdmissionPo.builder()
                .editInfo(JSON.toJSONString(miniAppBo))
                .appId(miniAppBo.getAppId())
                .auditStatus(AppCompanyAuditStatus.PENDING.getCode())
                .mid(mid)
                .appName(miniAppBo.getAppName())
                .build();
        miniAppAdmissionDao.insertSelective(po);
    }

    private MiniAppOpenAppAdmissionPo getAdmission(Long mid, long admissionId) {
        MiniAppOpenAppAdmissionPoExample example = new MiniAppOpenAppAdmissionPoExample();
        MiniAppOpenAppAdmissionPoExample.Criteria condition = example.createCriteria();
        if (mid != null) {
            condition.andMidEqualTo(mid);
        }
        condition.andIdEqualTo(admissionId)
                .andIsDeletedEqualTo(0);
        List<MiniAppOpenAppAdmissionPo> pos = miniAppAdmissionDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        } else {
            return pos.get(0);
        }
    }

    @Override
    public PageResult<MiniAppListBo> queryMiniAppList(long mid, String appName, Integer page, Integer size) {

        List<MiniAppDTO> mainApps = miniAppRemoteService.queryMainMiniApps(mid);

        List<MiniAppMemberDTO> miniAppMemberRoles = miniAppMemberRemoteService.queryMemberInfo(mid);

        List<String> appIds = miniAppMemberRoles.stream()
                .map(MiniAppMemberDTO::getAppId)
                .collect(Collectors.toList());
        List<MiniAppDTO> memberMiniApps = miniAppRemoteService.listMiniAppsByAppIds(appIds);

        MiniAppOpenAppAdmissionPoExample example = new MiniAppOpenAppAdmissionPoExample();
        example.or()
                .andMidEqualTo(mid)
                .andAuditStatusIn(List.of(AppCompanyAuditStatus.PENDING.getCode(), AppCompanyAuditStatus.REJECTED.getCode()))
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAppAdmissionPo> notPassAuditMiniApps = miniAppAdmissionDao.selectByExample(example);

        List<MiniAppListBo> result = mergeAllList(mid, appName, mainApps, memberMiniApps, notPassAuditMiniApps);

        List<MiniAppListBo> paginationResult = MemoryPagination.pageList(result, page, size);

        appendNecessaryInfo(mainApps, paginationResult, miniAppMemberRoles);

        return new PageResult<>(result.size(), paginationResult);
    }

    private void appendNecessaryInfo(List<MiniAppDTO> mainApps, List<MiniAppListBo> paginationResult, List<MiniAppMemberDTO> miniAppMembers) {
        Map<String, MiniAppMemberDTO> roleMap = miniAppMembers.stream()
                .collect(Collectors.toMap(MiniAppMemberDTO::getAppId, Function.identity()));

        Map<String, MiniAppDTO> mainAppMap = mainApps.stream()
                .collect(Collectors.toMap(MiniAppDTO::getAppId, Function.identity()));

        List<String> appIds = paginationResult.stream()
                .map(MiniAppListBo::getAppId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, MiniAppStorageCheckDTO> miniAppProdStorageInfo;
        if (CollectionUtils.isNotEmpty(appIds)) {
            miniAppProdStorageInfo = miniAppStorageRemoteService.getMiniAppProdStorageInfo(appIds);
        } else {
            miniAppProdStorageInfo = Map.of();
        }

        Map<String, MiniAppCategoryCertificationBo> certificationMap = categoryService.getMiniAppCategoryInfo(appIds);

        paginationResult.stream()
                .filter(miniApp -> StringUtils.isNotBlank(miniApp.getAppId()))
                .forEach(miniApp -> {
                    String appId = miniApp.getAppId();

                    if (certificationMap.containsKey(appId)) {
                        MiniAppCategoryCertificationBo certificationBo = certificationMap.get(appId);
                        miniApp.setCategoryName(categoryService.getCategoryNameByMappingId(certificationBo.getCategoryId()));
                    }
                    miniApp.setPermission(accountService.getPermission(appId, mainAppMap, roleMap));
                    if (StringUtils.isNotBlank(appId)) {
                        miniApp.setStatus(miniAppProdStorageInfo.containsKey(appId) ? MiniAppStatus.PUBLISHED.getCode() : MiniAppStatus.UNPUBLISHED.getCode());
                    }
                });
    }

    private List<MiniAppListBo> mergeAllList(long mid,
                                             String appName,
                                             List<MiniAppDTO> mainApps,
                                             List<MiniAppDTO> memberMiniApps,
                                             List<MiniAppOpenAppAdmissionPo> miniAppAdmissions) {

        List<MiniAppListBo> result = new ArrayList<>();

        result.addAll(processMiniAppList(mainApps, appName, MiniAppBizMapper.MAPPER::toBo));
        result.addAll(processMiniAppList(memberMiniApps, appName, MiniAppBizMapper.MAPPER::toBo));
        CompanyDetailBo company = companyService.getCreatedCompanyDetail(mid);
        result.addAll(processMiniAppList(miniAppAdmissions, appName, po -> this.buildNotPassAuditMiniApp(company, po)));

        result = result.stream()
                .sorted(this::orderByCtimeDesc)
                .collect(Collectors.toList());
        return result;
    }

    private MiniAppListBo buildNotPassAuditMiniApp(CompanyDetailBo company, MiniAppOpenAppAdmissionPo po) {
        MiniAppBo miniAppBo = JSON.parseObject(po.getEditInfo(), MiniAppBo.class);
        MiniAppListBo miniAppListBo = MiniAppBizMapper.MAPPER.toMiniAppListBo(po, miniAppBo);
        miniAppListBo.setCompanyName(company.getCompanyInfo().getCompanyName());
        miniAppListBo.setCategoryName(CategoryType.getByCode(miniAppBo.getCategoryId()).getDesc());

        return miniAppListBo;
    }

    private <T> List<MiniAppListBo> processMiniAppList(List<T> sourceList, String name, Function<T, MiniAppListBo> mapper) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }

        return sourceList.stream()
                .map(mapper)
                .filter(miniApp -> StringUtils.isBlank(name) || StringUtils.contains(miniApp.getAppName(), name))
                .collect(Collectors.toList());
    }


    private int orderByCtimeDesc(MiniAppListBo o1, MiniAppListBo o2) {
        return Long.compare(o2.getCtime(), o1.getCtime());
    }

    @Override
    public MiniAppDetailBo getMiniAppDetail(long mid, String appId, Long admissionId) {

        if (admissionId != null) {
            return queryDetailFromAdmission(mid, admissionId);
        }

        if (appId != null) {
            return queryDetailFromRemote(appId);
        }
        return null;
    }

    private MiniAppDetailBo queryDetailFromAdmission(Long mid, long id) {
        MiniAppOpenAppAdmissionPo admission = getAdmission(mid, id);
        if (admission == null) {
            return null;
        }

        MiniAppBo miniAppBo = JSON.parseObject(admission.getEditInfo(), MiniAppBo.class);
        miniAppBo.setAdmissionId(id);
        MiniAppDetailBo result = new MiniAppDetailBo();
        result.setMiniAppInfo(miniAppBo);
        return result;
    }

    private MiniAppDetailBo queryDetailFromRemote(String appId) {
        MiniAppDTO miniapp = miniAppRemoteService.queryAppInfo(appId);
        MiniAppDetailBo miniAppDetailBo = new MiniAppDetailBo();
        miniAppDetailBo.setMiniAppInfo(MiniAppBizMapper.MAPPER.toMiniAppBo(miniapp));
        MiniAppCategoryCertificationBo miniAppCategoryInfo = categoryService.getMiniAppCategoryInfo(appId);
        if (miniAppCategoryInfo != null) {
            miniAppDetailBo.getMiniAppInfo().setCategoryId(miniAppCategoryInfo.getCategoryId());
            miniAppDetailBo.getMiniAppInfo().setCategoryCertifications(miniAppCategoryInfo.getCertifications());
        }
        if (configCenter.getIcpWhiteList().contains(appId)) {
            miniAppDetailBo.getMiniAppInfo().setIcpSwitch(1);
        } else {
            miniAppDetailBo.getMiniAppInfo().setIcpSwitch(0);
        }
        return miniAppDetailBo;
    }

    @Override
    public PageResult<MiniAppAdmissionAuditListBo> queryAdmissionAuditList(String appId,
                                                                           AppCompanyAuditStatus auditStatus,
                                                                           MiniAppModifyType type,
                                                                           Page page) {

        MiniAppOpenAppAdmissionPoExample example = new MiniAppOpenAppAdmissionPoExample();
        MiniAppOpenAppAdmissionPoExample.Criteria condition = example.createCriteria();
        if (StringUtils.isNotBlank(appId)) {
            condition.andAppIdEqualTo(appId);
        }
        if (auditStatus != null) {
            condition.andAuditStatusEqualTo(auditStatus.getCode());
        }
        if (type != null) {
            condition.andModifyTypeEqualTo(type.getCode());
        }
        long count = miniAppAdmissionDao.countByExample(example);
        if (count == 0) {
            return PageResult.emptyPageResult();
        }

        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        example.setOrderByClause("mtime desc");
        List<MiniAppOpenAppAdmissionPo> pos = miniAppAdmissionDao.selectByExample(example);

        return new PageResult<>(Math.toIntExact(count), pos.stream()
                .map(MiniAppBizMapper.MAPPER::toAuditListBo)
                .collect(Collectors.toList()));
    }

    @Override
    public MiniAppAdmissionAuditDetailBo queryAdmissionAuditDetail(Long admissionId) {

        MiniAppOpenAppAdmissionPo admissionPo = getAdmission(null, admissionId);
        AssertUtil.isTrue(admissionPo != null, ErrorCodeType.NO_DATA);

        MiniAppAdmissionAuditDetailBo result = MiniAppBizMapper.MAPPER.toMiniAppAuditBaseDetail(admissionPo);
        CompanyDetailBo company = companyService.getCreatedCompanyDetail(admissionPo.getMid());
        result.setCompanyName(company.getCompanyInfo().getCompanyName());

        MiniAppBo miniAppBo = JSON.parseObject(admissionPo.getEditInfo(), MiniAppBo.class);
        MiniAppAuditBo miniAppAuditBo = buildMiniAppAuditBo(miniAppBo);
        result.setNewInfo(miniAppAuditBo);

        //开平一期走不到这个逻辑
        //String appId = admissionPo.getAppId();
        //if (StringUtils.isNotBlank(appId)) {
        //    MiniAppDetailBo miniAppDetailBo = queryDetailFromRemote(appId);
        //    result.setOldInfo(buildMiniAppAuditBo(miniAppDetailBo.getMiniAppInfo()));
        //}
        return result;
    }

    private MiniAppAuditBo buildMiniAppAuditBo(MiniAppBo miniAppBo) {
        MiniAppAuditBo miniAppAuditBo = MiniAppBizMapper.MAPPER.toMiniAppAuditBo(miniAppBo);
        miniAppAuditBo.setCategoryName(CategoryType.getByCode(miniAppBo.getCategoryId()).getDesc());
        return miniAppAuditBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void passOrRejectAdmission(long id, AppCompanyAuditStatus auditStatus, String failReason) {

        String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_APP_AUDIT.getPattern(), id);
        RLock lock = redis.tryLock(lockKey);
        try {
            MiniAppOpenAppAdmissionPo admissionPo = getAdmission(null, id);
            AssertUtil.isTrue(admissionPo != null, ErrorCodeType.NO_DATA);
            String appId = admissionPo.getAppId();
            boolean create = admissionPo.getModifyType() == MiniAppModifyType.NEW.getCode();
            //boolean update = !create;
            if (auditStatus == AppCompanyAuditStatus.PASSED) {
                if (create) {
                    appId = doCreateMiniApp(admissionPo);
                }
                //if (update) {
                //开平一期需求暂无此能力
                //doUpdateMiniApp(admissionPo);
                //}
            }
            if (auditStatus == AppCompanyAuditStatus.REJECTED) {
                if (create) {
                    String appIdFromCache = getAlreadyCreatedAppIdFromCache(admissionPo.getMid(), id);
                    AssertUtil.isTrue(StringUtils.isBlank(appIdFromCache), null, "小程序已创建，不能拒审，请再次点击审核通过");
                }
            }

            doAudit(id, admissionPo.getAuditStatus(), auditStatus, failReason, appId);

        } catch (Exception e) {
            log.error("过审小程序时发生异常", e);
            throw new ServiceException(e, e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void initDefaultMiniAppPlantingConfig(List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }
        for (String appId : appIds) {
            MiniAppDetailBo miniAppDetail = getMiniAppDetail(0, appId, null);
            if (miniAppDetail == null) {
                continue;
            }
            createCustomizeLinkIfNecessary(appId, miniAppDetail.getMiniAppInfo());
        }
    }

    @Override
    public String checkName(String appId, String name) {
        if (!validateAppName(name)) {
            return "名称格式错误，请参考命名规则重新输入";
        }
        List<MiniAppCheckDTO> miniAppCheckDTOS = miniAppRemoteService.nameRepeat(name);
        if (CollectionUtils.isEmpty(miniAppCheckDTOS)) {
            return null;
        }
        if (StringUtils.isNotBlank(appId)) {
            miniAppCheckDTOS.removeIf(app -> StringUtils.equals(app.getAppId(), appId));
        }
        if (CollectionUtils.isNotEmpty(miniAppCheckDTOS)) {
            return "当前小程序名称已被占用，请重新输入";
        }
        return null;
    }

    @Override
    public List<MiniAppCategoryTree> getCategoryTree() {
        List<MiniAppCategoryBo> allCategoryList = categoryRepository.getAllCategoryList();
        if (CollectionUtils.isEmpty(allCategoryList)) {
            return Collections.emptyList();
        }
        return buildCategoryTree(allCategoryList);
    }

    @Override
    public void initDefaultMiniAppCategory() {
        List<MiniAppCategoryBo> categoryBos = getDefaultCategory();
        categoryRepository.batchInsertCategory(categoryBos);
    }

    @Override
    public Boolean updateName(String appId, String name) {
        // 校验小程序合法性
        MiniAppInfoQuery miniAppInfoQuery = new MiniAppInfoQuery();
        miniAppInfoQuery.setAppId(appId);
        ChannelMiniAppInfoDTO appInfoDTO = miniAppRemoteService.getMiniAppInfoDTO(miniAppInfoQuery);
        AssertUtil.notNull(appInfoDTO, ErrorCodeType.NO_DATA.getCode(), "小程序不存在");
        AssertUtil.isTrue(!MiniAppOfflineEnum.OFFLINE.getType().equals(appInfoDTO.getOffline()), ErrorCodeType.BAD_DATA.getCode(), "小程序已下架，请联系管理员");
        AssertUtil.isTrue(MiniAppOriginEnum.APP.getType().equals(appInfoDTO.getOrigin()), ErrorCodeType.BAD_DATA.getCode(), "小程序不可操作");
        return miniAppRemoteService.updateName(appId, name);
    }

    @NotNull
    private static List<MiniAppCategoryBo> getDefaultCategory() {
        return List.of(
                MiniAppCategoryBo.builder()
                        .id(1L)
                        .name("网络小说")
                        .level(3)
                        .parentId(5L)
                        .build(),
                MiniAppCategoryBo.builder()
                        .id(2L)
                        .name("长视频")
                        .level(3)
                        .parentId(5L)
                        .build(),
                MiniAppCategoryBo.builder()
                        .id(3L)
                        .name("微短剧")
                        .level(3)
                        .parentId(5L)
                        .build(),
                MiniAppCategoryBo.builder()
                        .id(4L)
                        .name("文娱")
                        .level(1)
                        .parentId(0L)
                        .build(),
                MiniAppCategoryBo.builder()
                        .id(5L)
                        .name("文娱")
                        .level(2)
                        .parentId(4L)
                        .build());
    }

    private static List<MiniAppCategoryTree> buildCategoryTree(List<MiniAppCategoryBo> allCategoryList) {
        Map<Long, MiniAppCategoryTree> categoryMap = new HashMap<>();
        for (MiniAppCategoryBo category : allCategoryList) {
            MiniAppCategoryTree tree = new MiniAppCategoryTree();
            tree.setValue(category.getId());
            tree.setLabel(category.getName());
            tree.setChildren(new ArrayList<>());
            categoryMap.put(category.getId(), tree);
        }

        List<MiniAppCategoryTree> result = new ArrayList<>();
        for (MiniAppCategoryBo category : allCategoryList) {
            if (category.getParentId() == null || category.getParentId() == 0) {
                result.add(categoryMap.get(category.getId()));
            } else {
                MiniAppCategoryTree parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    parent.addChild(categoryMap.get(category.getId()));
                }
            }
        }
        return result;
    }

    private void doAudit(long id,
                         int oldStatus,
                         AppCompanyAuditStatus newStatus,
                         String failReason,
                         String appId) {

        MiniAppOpenAppAdmissionPo updatePo = new MiniAppOpenAppAdmissionPo();
        updatePo.setAuditStatus(newStatus.getCode());
        updatePo.setAppId(appId);
        updatePo.setFailReason(failReason);
        MiniAppOpenAppAdmissionPoExample example = new MiniAppOpenAppAdmissionPoExample();
        example.or()
                .andIdEqualTo(id)
                .andAuditStatusEqualTo(oldStatus)
                .andIsDeletedEqualTo(0);
        miniAppAdmissionDao.updateByExampleSelective(updatePo, example);
    }

    private void doUpdateMiniApp(MiniAppOpenAppAdmissionPo admissionPo) {
        MiniAppBo miniAppBo = JSONObject.parseObject(admissionPo.getEditInfo(), MiniAppBo.class);
        MiniAppInfoUpdateQuery updateQuery = new MiniAppInfoUpdateQuery();
        updateQuery.setAppId(admissionPo.getAppId());
        updateQuery.setIntroduction(miniAppBo.getAppDescription());
        updateQuery.setLogo(miniAppBo.getAppLogo());
        updateQuery.setName(miniAppBo.getAppName());
        miniAppRemoteService.updateMiniApp(updateQuery);
        categoryService.updateMiniAppCategory(admissionPo.getAppId(), miniAppBo.getCategoryId(), miniAppBo.getCategoryCertifications());
    }

    private String doCreateMiniApp(MiniAppOpenAppAdmissionPo admissionPo) {
        MiniAppBo miniAppBo = JSONObject.parseObject(admissionPo.getEditInfo(), MiniAppBo.class);
        CompanyDetailBo company = companyService.getDetail(admissionPo.getMid());
        AssertUtil.isTrue(company != null, ErrorCodeType.NO_DATA);
        String companyId = company.getCompanyInfo().getCompanyId();
        miniAppBo.setAdmissionId(admissionPo.getId());
        String appId = doCreateMiniAppByRemote(companyId, admissionPo.getMid(), miniAppBo);
        createOtherInfo(appId, company, miniAppBo);
        return appId;
    }

    private void createOtherInfo(String appId, CompanyDetailBo company, MiniAppBo miniAppBo) {
        allowPay(appId);
        categoryService.saveMiniAppCategory(appId, miniAppBo.getCategoryId(), miniAppBo.getCategoryCertifications());
        channelRemoteService.updateChannelBindInfo(MiniAppChannelMinBaseVersionUpdateRequest.builder()
                .appId(appId)
                .channelKeys(Collections.singletonList(MiniAppChannelEnum.BILIBILI_PINK.getChannelId()))
                .minBaseVersionNumber(configCenter.getMiniAppConfig().getMinVersion())
                .build());
        openAccessService.generateAccess(company.getCompanyInfo().getCompanyId(), company.getCompanyInfo().getCompanyName());

        createTemplateInfoIfNecessary(appId, company, miniAppBo);
        // 积木小程序初始化种草链接
        createCustomizeLinkIfNecessary(appId, miniAppBo);
    }

    private void createCustomizeLinkIfNecessary(String appId, MiniAppBo miniAppBo) {
        try {
            // 理论上不会保存失败，但是保存失败也不能影响主流程
            if (miniAppBo.miniAppTemplate()) {
                // 积木小程序才能设置默认的自定义链接
                customLinkService.saveCustomLink(MiniAppCustomLinkBo.builder()
                        .appId(appId)
                        .customPath("/pages/detail/detail")
                        .build());
            }
        } catch (Exception e) {
            log.error("[MiniAppService] 创建小程序种草链接失败: {}", appId, e);
        }
    }

    private void createTemplateInfoIfNecessary(String appId, CompanyDetailBo company, MiniAppBo miniAppBo) {
        if (!Objects.equals(miniAppBo.getDevelopType(), AppDevelopType.TEMPLATE.getCode())) {
            return;
        }
        addUrlWhiteList(appId, miniAppBo);
        miniAppPublishService.uploadTemplatePackage(company.getCompanyInfo().getMid(), appId);
    }

    private void addUrlWhiteList(String appId, MiniAppBo miniAppBo) {
        MiniAppUrlUpdateQuery miniAppUrlUpdateQuery = new MiniAppUrlUpdateQuery();
        miniAppUrlUpdateQuery.setLimitType(MiniAppUrlType.SERVER_DOMAIN.getCode());
        miniAppUrlUpdateQuery.setAppId(appId);
        String defaultWhiteDomain = configCenter.getMiniAppConfig().getDefaultWhiteDomains();
        List<String> domainList = JSON.parseArray(defaultWhiteDomain, String.class);
        miniAppUrlUpdateQuery.setDownloadFile(domainList);
        miniAppUrlUpdateQuery.setBusiness(domainList);
        miniAppUrlUpdateQuery.setRequest(domainList);
        miniAppUrlUpdateQuery.setUploadFile(domainList);
        miniAppUrlRemoteService.updateServer(miniAppUrlUpdateQuery);
    }

    private String doCreateMiniAppByRemote(String companyId, long mid, MiniAppBo miniAppBo) {
        String appId = getAlreadyCreatedAppIdFromCache(mid, miniAppBo.getAdmissionId());

        if (StringUtils.isNotBlank(appId)) {
            return appId;
        }
        MiniAppAddQuery miniAppAddQuery = MiniAppAddQuery.builder()
                .companyId(companyId)
                .mid(mid)
                .name(miniAppBo.getAppName())
                .introduction(miniAppBo.getAppDescription())
                .logo(miniAppBo.getAppLogo())
                .type((byte) 0)
                .developType(miniAppBo.getDevelopType())
                .build();
        MiniAppDTO createdMiniApp = miniAppRemoteService.createMiniApp(miniAppAddQuery);
        appId = createdMiniApp.getAppId();
        redis.setObject(getAppIdCacheKey(mid, miniAppBo.getAdmissionId()), appId, 15, TimeUnit.DAYS);
        return appId;
    }

    private String getAlreadyCreatedAppIdFromCache(long mid, long admissionId) {
        String key = getAppIdCacheKey(mid, admissionId);
        return redis.getObject(key, String.class);
    }

    private String getAppIdCacheKey(long mid, long admissionId) {
        return String.format(RedisKeyPattern.OPEN_APP_CREATING.getPattern(), mid, admissionId);
    }

    private void allowPay(String appId) {
        try {
            MiniAppPayUpdateQuery miniAppPayUpdateQuery = new MiniAppPayUpdateQuery();
            miniAppPayUpdateQuery.setAppId(appId);
            byte supportPay = 1;
            miniAppPayUpdateQuery.setPaymentStatus(supportPay);
            miniAppRemoteService.updateMiniAppAllowPay(miniAppPayUpdateQuery);
        } catch (Exception e) {
            //重复开通下可以忽略
            log.warn("开通支付时异常", e);
        }
    }

    private static boolean validateAppName(String appName) {
        // 支持大小写字母、数字和中文，字符长度为4～24个，一个汉字对应两个字符
        if (appName == null || appName.isEmpty()) {
            return false;
        }

        // 检查是否只包含允许的字符
        if (!PatternConstant.ALLOWED_CHARS_PATTERN.matcher(appName).matches()) {
            return false;
        }

        // 计算长度（汉字算2个字符）
        int length = calculateEffectiveLength(appName);

        // 检查长度是否在4-24之间
        return length >= 4 && length <= 24;

    }

    /**
     * 计算字符串的有效长度（汉字算2个字符，其他算1个）
     * @param str 输入字符串
     * @return 有效长度
     */
    private static int calculateEffectiveLength(String str) {
        int length = 0;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (isChinese(c)) {
                length += 2;
            } else {
                length += 1;
            }
        }
        return length;
    }

    /**
     * 判断字符是否是汉字
     * @param c 要检查的字符
     * @return 是汉字返回true，否则返回false
     */
    private static boolean isChinese(char c) {
        // 汉字的Unicode范围
        return (c >= 0x4e00 && c <= 0x9fa5);
    }
}