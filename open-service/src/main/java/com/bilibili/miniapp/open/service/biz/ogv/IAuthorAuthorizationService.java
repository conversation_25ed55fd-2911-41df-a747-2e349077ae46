package com.bilibili.miniapp.open.service.biz.ogv;

import com.bilibili.miniapp.open.common.annotations.ReadFlag;
import com.bilibili.miniapp.open.service.bo.ogv.AuthorAuthorizationBo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/3 16:29
 */
public interface IAuthorAuthorizationService {
    /**
     * 为某个MINI_APP授权某内容创作者
     * <p>
     * {@link AuthorAuthorizationBo#getMidList()} must be positive
     * <p>
     * {@link AuthorAuthorizationBo#getAppId()} must have text
     */
    void authorize(AuthorAuthorizationBo authorization);

    /**
     * 为某个MINI_APP取消对某创作者的授权
     * <p>
     * {@link AuthorAuthorizationBo#getMidList()} must be positive
     * <p>
     * {@link AuthorAuthorizationBo#getAppId()} must have text
     */
    void cancel(AuthorAuthorizationBo authorization);

    /**
     * 查询某MINI_APP所有授权创作者
     * <p>
     * {@param appId} must have text
     */
    @ReadFlag({ReadFlag.F.DB})
    AuthorAuthorizationBo getAuthorization(String appId);

    AuthorAuthorizationBo getAuthorizationWithMidInfo(String appId);

    /**
     * 查询某MINI_APP是否授权指定的创作者
     * <p>
     * 1、如果MINI_APP不存在，则返回null
     * <p>
     * 2、存在授权关系的mid会返回，不存在授权关系的mid不返回，调用方可以根据返回的mid和传入的mid做diff判断业务逻辑
     * {@param appId} must have text
     * {@param midList} must be not empty，如果有empty的诉求，则请调用{@link #getAuthorization(String)}
     */
    @ReadFlag({ReadFlag.F.DB})
    AuthorAuthorizationBo getAuthorization(String appId, List<Long> midList) throws Exception;

    /**
     * 判断某创作者是否对某MINI_APP有授权
     * <p>依赖缓存</p>
     * {@param seasonId} must be positive
     * <p>
     * {@param appId} must have text
     */
    @ReadFlag({ReadFlag.F.REDIS})
    boolean isAuthorized(String appId, Long mid);

    /**
     * 刷新某APP关联的所有创作者（被授权的）
     * <p>
     * {@param appId} must have text
     */
    void refresh(String appId) throws Exception;


}
