package com.bilibili.miniapp.open.service.biz.ogv;

import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/28
 **/
public interface ISeasonAuthorizationExtService {

    /**
     * 获取指定短剧的所有授权的APP_ID
     * @param seasonIds
     * @return
     */
    Map<Long, List<SeasonAuthorizationResultBo>> getAuthorizedAppIdsFromDb(List<Long> seasonIds);

    /**
     * 获取指定短剧的所有授权的APP_ID (带缓存)
     * @param seasonIds
     * @return
     */
    Map<Long, List<SeasonAuthorizationResultBo>> getAuthorizedAppIdsWithCache(List<Long> seasonIds);

    /**
     * 预热缓存
     * @param seasonIds
     */
    void warmUpCache(List<Long> seasonIds);

    /**
     * 获取所有授权的APP_ID
     * @return
     */
    List<String> getAllAuthorizedAppIds();


}
