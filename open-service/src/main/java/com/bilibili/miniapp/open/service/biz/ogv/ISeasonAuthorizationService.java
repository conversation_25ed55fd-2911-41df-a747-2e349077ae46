package com.bilibili.miniapp.open.service.biz.ogv;

import com.bilibili.miniapp.open.common.annotations.ReadFlag;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonAuthorizationBo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;

import java.util.List;
import java.util.Map;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/2 20:04
 */
public interface ISeasonAuthorizationService {
    /**
     * 为某个MINI_APP授权某短剧
     * <p>
     * {@link SeasonAuthorizationBo#getSeasonIdList()} must be positive
     * <p>
     * {@link SeasonAuthorizationBo#getAppId()} must have text
     */
    void authorize(SeasonAuthorizationBo authorization) throws Exception;

    /**
     * 为某个MINI_APP取消对某短剧的授权
     * <p>
     * {@link SeasonAuthorizationBo#getSeasonIdList()} must be positive
     * <p>
     * {@link SeasonAuthorizationBo#getAppId()} must have text
     */
    void cancel(SeasonAuthorizationBo authorization);

    /**
     * 判断某短剧是否对某MINI_APP有授权
     * <p>依赖缓存</p>
     * {@param seasonId} must be positive
     * <p>
     * {@param appId} must have text
     */
    @ReadFlag({ReadFlag.F.REDIS})
    boolean isAuthorized(String appId, Long seasonId) throws Exception;

    /**
     * 刷新某APP关联的所有短剧（被授权的）
     * <p>
     * {@param appId} must have text
     */
    void refresh(String appId) throws Exception;

    /**
     * 获取某短剧的所有授权的APP_ID
     * <p>
     * {@param seasonId} must be positive
     */
    List<SeasonAuthorizationResultBo> getAuthorizedAppIds(Long seasonId);

    List<Long> findUnSupportSeasonIds(List<Long> seasonIds);

    List<SeasonBo> querySeason4Short(List<Long> seasonIds);

}
