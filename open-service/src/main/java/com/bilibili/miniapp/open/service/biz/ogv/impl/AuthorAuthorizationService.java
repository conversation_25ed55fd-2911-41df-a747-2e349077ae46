package com.bilibili.miniapp.open.service.biz.ogv.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppInfoQuery;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.enums.AuthorizationStatus;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAuthorAuthorizationDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAuthorAuthorizationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAuthorAuthorizationPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonTabService;
import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.AuthorAuthorizationBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonAuthorizationBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.rpc.grpc.client.MainSiteAccountServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/3 16:44
 */
@Slf4j
@Service
public class AuthorAuthorizationService extends AbstractOpenService implements IAuthorAuthorizationService {
    @Autowired
    private MiniAppOpenAuthorAuthorizationDao authorAuthorizationDao;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;
    @Autowired
    private MainSiteAccountServiceGrpcClient mainSiteAccountServiceGrpcClient;
    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;
    @Autowired
    private ISeasonAuthorizationService seasonAuthorizationService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private ISeasonTabService seasonTabService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void authorize(AuthorAuthorizationBo authorization) {
        AuthorizeContext authorizeContext = validateAuthorization(authorization);
        for (Long mid : authorization.getMidList()) {
            authorAuthorizationDao.insertUpdateSelective(MiniAppOpenAuthorAuthorizationPo.builder()
                    .mid(mid)
                    .appId(authorization.getAppId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .status(AuthorizationStatus.VALID.getCode())
                    .build());
        }
        addAppAuthorAuthorizationCache(authorization.getAppId(), authorization.getMidList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancel(AuthorAuthorizationBo authorization) {
        AuthorizeContext authorizeContext = validateAuthorization(authorization);
        for (Long mid : authorization.getMidList()) {
            authorAuthorizationDao.insertUpdateSelective(MiniAppOpenAuthorAuthorizationPo.builder()
                    .mid(mid)
                    .appId(authorization.getAppId())
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .status(AuthorizationStatus.INVALID.getCode())
                    .build());
            seasonTabService.removeMidSeason(authorization.getAppId(), mid);
        }
        //取消对应创作者的剧集授权
        seasonAuthorizationService.cancel(SeasonAuthorizationBo.builder()
                .appId(authorization.getAppId())
                .midList(authorization.getMidList())
                .build());
        removeAppAuthorAuthorizationCache(authorization.getAppId(), authorization.getMidList());
    }

    @Override
    public AuthorAuthorizationBo getAuthorization(String appId) {
        Assert.hasText(appId, "appId must have text");
        //数据量很少，全查
        MiniAppOpenAuthorAuthorizationPoExample example = new MiniAppOpenAuthorAuthorizationPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStatusEqualTo(AuthorizationStatus.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenAuthorAuthorizationPo> list = authorAuthorizationDao.selectByExample(example);
        List<Long> midList = list.stream().map(MiniAppOpenAuthorAuthorizationPo::getMid).collect(Collectors.toList());
        return AuthorAuthorizationBo.builder()
                .appId(appId)
                .midList(midList)
                .build();
    }

    @Override
    public AuthorAuthorizationBo getAuthorizationWithMidInfo(String appId) {
        AuthorAuthorizationBo authorization = getAuthorization(appId);
        List<AccountInfoBo> accounts = accountService.getAccountInfos(authorization.getMidList());
        authorization.setAccounts(accounts);
        return authorization;
    }

    @Override
    public AuthorAuthorizationBo getAuthorization(String appId, List<Long> midList) throws Exception {
        Assert.hasText(appId, "appId must have text");
        Assert.notEmpty(midList, "midList must not be empty");
        MiniAppOpenAuthorAuthorizationPoExample example = new MiniAppOpenAuthorAuthorizationPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andMidIn(midList)
                .andStatusEqualTo(AuthorizationStatus.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenAuthorAuthorizationPo> list = authorAuthorizationDao.selectByExample(example);
        List<Long> authorizedMidList = list.stream().map(MiniAppOpenAuthorAuthorizationPo::getMid).collect(Collectors.toList());
        return AuthorAuthorizationBo.builder()
                .appId(appId)
                .midList(authorizedMidList)
                .build();
    }

    @Override
    public boolean isAuthorized(String appId, Long mid) {
        return cacheRepository.containsSetValue(getAppAuthorAuthorizationKey(appId), mid);
    }

    @Override
    public void refresh(String appId) throws Exception {
        AuthorAuthorizationBo authorization = getAuthorization(appId);
        cacheRepository.clearAndAddAllSet(getAppAuthorAuthorizationKey(appId), Sets.newHashSet(authorization.getMidList()));
    }

    private void addAppAuthorAuthorizationCache(String appId, List<Long> midList) {
        cacheRepository.addAllSet(getAppAuthorAuthorizationKey(appId), Sets.newHashSet(midList));
    }

    private void removeAppAuthorAuthorizationCache(String appId, List<Long> midList) {
        cacheRepository.removeAllSet(getAppAuthorAuthorizationKey(appId), Sets.newHashSet(midList));
    }

    private String getAppAuthorAuthorizationKey(String appId) {
        return String.format(RedisKeyPattern.APP_AUTHOR_AUTHORIZATION.getPattern(), appId);
    }

    private AuthorizeContext validateAuthorization(AuthorAuthorizationBo authorization) {
        Assert.hasText(authorization.getAppId(), "app_id不可为空");
        Assert.notEmpty(authorization.getMidList(), "剧id不可为空");
        MiniAppInfoQuery miniAppInfoQuery = new MiniAppInfoQuery();
        miniAppInfoQuery.setAppId(authorization.getAppId());
        ChannelMiniAppInfoDTO channelMiniAppInfo = miniAppRemoteService.queryAppInfoWithinCache(authorization.getAppId());
        Assert.notNull(channelMiniAppInfo, "小程序不存在");
        Map<Long, UserInfoBo> userMap = mainSiteAccountServiceGrpcClient.queryUser(authorization.getMidList(),
                GrpcCallContext.builder().timeout(2000).build());
        List<Long> unsupportedMidList = authorization.getMidList()
                .stream()
                .filter(mid -> !userMap.containsKey(mid))
                .collect(Collectors.toList());
        Assert.isTrue(unsupportedMidList.isEmpty(), String.format("当前用户[%s]不支持小程序授权", JSON.toJSONString(unsupportedMidList)));
        return AuthorizeContext.builder()
                .userMap(userMap)
                .miniAppInfo(channelMiniAppInfo)
                .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class AuthorizeContext {
        private ChannelMiniAppInfoDTO miniAppInfo;
        private Map<Long, UserInfoBo> userMap;
    }
}
