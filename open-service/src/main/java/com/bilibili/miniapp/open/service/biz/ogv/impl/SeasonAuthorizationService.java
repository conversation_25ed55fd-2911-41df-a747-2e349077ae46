package com.bilibili.miniapp.open.service.biz.ogv.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppInfoQuery;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.enums.AuthorizationStatus;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonAuthorizationDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonAuthorizationPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonAuthorizationPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.ogv.IAuthorAuthorizationService;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonAuthorizationService;
import com.bilibili.miniapp.open.service.bo.ogv.AuthorAuthorizationBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonAuthorizationBo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.ogv.SeasonAuthorizationResultBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.grpc.client.PgcSeasonServiceGrpcClient;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppRemoteService;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/2 20:09
 */
@Slf4j
@Service
public class SeasonAuthorizationService extends AbstractOpenService implements ISeasonAuthorizationService {
    @Autowired
    private MiniAppOpenSeasonAuthorizationDao seasonAuthorizationDao;
    @Autowired
    private IMiniAppRemoteService miniAppRemoteService;
    @Autowired
    private PgcSeasonServiceGrpcClient seasonServiceGrpcClient;
    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;
    @Autowired
    private IAuthorAuthorizationService authorAuthorizationService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void authorize(SeasonAuthorizationBo authorization) throws Exception {
        AuthorizeContext authorizeContext = validateAuthorization(authorization);
        Map<Long, SeasonBo> seasonMap = authorizeContext.getSeasonMap();
        for (Long ssid : authorization.getSeasonIdList()) {
            seasonAuthorizationDao.insertUpdateSelective(MiniAppOpenSeasonAuthorizationPo.builder()
                    .seasonId(ssid)
                    .seasonMid(Objects.requireNonNull(seasonMap.get(ssid).getAuthor(), "author not exists").getMid())
                    .appId(authorization.getAppId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .status(AuthorizationStatus.VALID.getCode())
                    .build());
        }
        addAppSeasonAuthorizationCache(authorization.getAppId(), authorization.getSeasonIdList());
        // 新增授权所有剧对应的小程序缓存失效
        authorization.getSeasonIdList().forEach(this::removeSeasonAppletAuthorizationCache);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancel(SeasonAuthorizationBo authorization) {
        AuthorizeContext authorizeContext = validateCancelAuthorization(authorization);

        MiniAppOpenSeasonAuthorizationPoExample example = new MiniAppOpenSeasonAuthorizationPoExample();
        MiniAppOpenSeasonAuthorizationPoExample.Criteria criteria = example.createCriteria()
                .andAppIdEqualTo(authorization.getAppId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        FunctionUtil.notEmpty(authorization.getSeasonIdList(), criteria::andSeasonIdIn);
        FunctionUtil.notEmpty(authorization.getMidList(), criteria::andSeasonMidIn);
        List<MiniAppOpenSeasonAuthorizationPo> authorizationPoList = seasonAuthorizationDao.selectByExample(example);

        if (!CollectionUtils.isEmpty(authorizationPoList)) {
            List<Long> idList = authorizationPoList.stream().map(MiniAppOpenSeasonAuthorizationPo::getId).collect(Collectors.toList());
            example = new MiniAppOpenSeasonAuthorizationPoExample();
            example.createCriteria().andIdIn(idList);
            seasonAuthorizationDao.updateByExampleSelective(MiniAppOpenSeasonAuthorizationPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .status(AuthorizationStatus.INVALID.getCode())
                    .build(), example);
            List<Long> seasonIdList = authorizationPoList.stream().map(MiniAppOpenSeasonAuthorizationPo::getSeasonId).collect(Collectors.toList());
            removeAppSeasonAuthorizationCache(authorization.getAppId(), seasonIdList);
            // 取消授权所有剧对应的小程序缓存失效
            authorization.getSeasonIdList().forEach(this::removeSeasonAppletAuthorizationCache);
        }
    }

    @Override
    public boolean isAuthorized(String appId, Long seasonId) throws Exception {
        List<SeasonBo> seasonBos = seasonServiceGrpcClient.querySeason4Short(List.of(seasonId));
        if (CollectionUtils.isEmpty(seasonBos)) {
            return false;
        }
        SeasonBo seasonBo = seasonBos.get(0);
        UserInfoBo author = seasonBo.getAuthor();
        if (author == null) {
            return false;
        }
        return authorAuthorizationService.isAuthorized(appId, author.getMid());
    }

    @Override
    public void refresh(String appId) throws Exception {
        //数据量很少，全查
        MiniAppOpenSeasonAuthorizationPoExample example = new MiniAppOpenSeasonAuthorizationPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStatusEqualTo(AuthorizationStatus.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenSeasonAuthorizationPo> list = seasonAuthorizationDao.selectByExample(example);
        List<Long> seasonIdList = list.stream().map(MiniAppOpenSeasonAuthorizationPo::getSeasonId).collect(Collectors.toList());
        cacheRepository.clearAndAddAllSet(buildAppSeasonAuthorizationKey(appId), Sets.newHashSet(seasonIdList));
    }


    @Override
    public List<SeasonAuthorizationResultBo> getAuthorizedAppIds(Long seasonId) {
        MiniAppOpenSeasonAuthorizationPoExample example = new MiniAppOpenSeasonAuthorizationPoExample();
        example.createCriteria()
                .andSeasonIdEqualTo(seasonId)
                .andStatusEqualTo(AuthorizationStatus.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<MiniAppOpenSeasonAuthorizationPo> list = seasonAuthorizationDao.selectByExample(example);
        return list.stream().map(po -> SeasonAuthorizationResultBo.builder()
                .seasonId(po.getSeasonId())
                .appId(po.getAppId())
                .seasonMid(po.getSeasonMid())
                .authTime(po.getMtime())
                .build()).collect(Collectors.toList());
    }

    private void removeSeasonAppletAuthorizationCache(Long seasonId) {
        cacheRepository.delete(buildAppSeasonAppletKey(seasonId));
    }

    private void addAppSeasonAuthorizationCache(String appId, List<Long> seasonIdList) {
        cacheRepository.addAllSet(buildAppSeasonAuthorizationKey(appId), Sets.newHashSet(seasonIdList));
    }

    private void removeAppSeasonAuthorizationCache(String appId, List<Long> seasonIdList) {
        cacheRepository.removeAllSet(buildAppSeasonAuthorizationKey(appId), Sets.newHashSet(seasonIdList));
    }

    private String buildAppSeasonAuthorizationKey(String appId) {
        return String.format(RedisKeyPattern.APP_SEASON_AUTHORIZATION.getPattern(), appId);
    }

    @Override
    public List<Long> findUnSupportSeasonIds(List<Long> seasonIds) {
        List<SeasonBo> seasonList = seasonServiceGrpcClient.querySeason4Short(seasonIds);
        return findUnSupportSeasonIds(seasonIds, seasonList);
    }

    @Override
    public List<SeasonBo> querySeason4Short(List<Long> seasonIds) {
        return seasonServiceGrpcClient.querySeason4Short(seasonIds);
    }

    private List<Long> findUnSupportSeasonIds(List<Long> seasonIds, List<SeasonBo> seasons) {
        Map<Long, SeasonBo> seasonMap = seasons.stream()
                .collect(Collectors.toMap(SeasonBo::getSeasonId, Function.identity(), FunctionUtil.override()));
        return seasonIds.stream()
                .filter(ssid -> !seasonMap.containsKey(ssid))
                .collect(Collectors.toList());
    }
    private String buildAppSeasonAppletKey(Long seasonId) {
        return RedisKeyPattern.buildAppSeasonAppletKey(seasonId);
    }


    private AuthorizeContext validateAuthorization(SeasonAuthorizationBo authorization) throws Exception {
        Assert.hasText(authorization.getAppId(), "app_id不可为空");
        Assert.notEmpty(authorization.getSeasonIdList(), "剧id不可为空");

        //小程序检查（可能是非法小程序id）
        MiniAppInfoQuery miniAppInfoQuery = new MiniAppInfoQuery();
        miniAppInfoQuery.setAppId(authorization.getAppId());
        ChannelMiniAppInfoDTO channelMiniAppInfo = call("getMiniAppInfoDTO",
                () -> miniAppRemoteService.getMiniAppInfoDTO(toRequestBody(miniAppInfoQuery)));
        Assert.notNull(channelMiniAppInfo, "小程序不存在");

        //剧集检查（可能是非法的ssid或者不支持小程序渠道的ssid）
        List<SeasonBo> seasonList = seasonServiceGrpcClient.querySeason4Short(authorization.getSeasonIdList(),
                GrpcCallContext.builder().timeout(2000).build());
        Map<Long, SeasonBo> seasonMap = seasonList.stream()
                .collect(Collectors.toMap(SeasonBo::getSeasonId, Function.identity(), FunctionUtil.override()));
        List<Long> unsupportedSeasonIdList = findUnSupportSeasonIds(authorization.getSeasonIdList(), seasonList);
        Assert.isTrue(unsupportedSeasonIdList.isEmpty(), String.format("当前剧[%s]不支持小程序分发", JSON.toJSONString(unsupportedSeasonIdList)));

        //创作者授权检查（可能某些ssid不是当前app_id已授权的创作者的内容）
        List<Long> midList = seasonList.stream()
                .map(season -> {
                    Long mid = Objects.requireNonNull(season.getAuthor(), "author not exists").getMid();
                    Assert.isTrue(NumberUtil.isPositive(mid), "author is invalid");
                    return mid;
                })
                .distinct()
                .collect(Collectors.toList());
        AuthorAuthorizationBo authorAuthorization = authorAuthorizationService.getAuthorization(authorization.getAppId(), midList);
        List<Long> authorizedMidList = authorAuthorization.getMidList();
        List<Long> unauthorizedSeasonIdList = seasonList.stream()
                .filter(season -> !authorizedMidList.contains(Objects.requireNonNull(season.getAuthor()).getMid()))
                .map(SeasonBo::getSeasonId)
                .collect(Collectors.toList());
        Assert.isTrue(unauthorizedSeasonIdList.isEmpty(), String.format("当前剧[%s]不支持授权", JSON.toJSONString(unauthorizedSeasonIdList)));
        return AuthorizeContext.builder()
                .seasonMap(seasonMap)
                .miniAppInfo(channelMiniAppInfo)
                .authorAuthorization(authorAuthorization)
                .build();
    }

    private AuthorizeContext validateCancelAuthorization(SeasonAuthorizationBo authorization) {
        Assert.hasText(authorization.getAppId(), "app_id不可为空");

        //小程序检查（可能是非法小程序id）
        MiniAppInfoQuery miniAppInfoQuery = new MiniAppInfoQuery();
        miniAppInfoQuery.setAppId(authorization.getAppId());
        ChannelMiniAppInfoDTO channelMiniAppInfo = call("getMiniAppInfoDTO",
                () -> miniAppRemoteService.getMiniAppInfoDTO(toRequestBody(miniAppInfoQuery)));
        Assert.notNull(channelMiniAppInfo, "小程序不存在");
        return AuthorizeContext.builder()
                .miniAppInfo(channelMiniAppInfo)
                .build();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class AuthorizeContext {
        private ChannelMiniAppInfoDTO miniAppInfo;
        private Map<Long, SeasonBo> seasonMap;
        private AuthorAuthorizationBo authorAuthorization;
    }
}
