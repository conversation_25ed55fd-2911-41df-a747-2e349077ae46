package com.bilibili.miniapp.open.service.biz.ogv.impl;

import cn.hutool.core.util.StrUtil;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.SeasonTabType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonTabDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSeasonTabExtDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonTabPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSeasonTabPoExample;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.ogv.ISeasonTabService;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Service
public class SeasonTabServiceImpl implements ISeasonTabService {

    @Autowired
    private MiniAppOpenSeasonTabDao tabDao;
    @Autowired
    private MiniAppOpenSeasonTabExtDao tabExtDao;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private ICacheRepository cache;

    @Override
    public List<Long> getAlreadyPublishedSeasonIds(String appId, List<Long> seasonIds) {
        if (CollectionUtils.isEmpty(seasonIds)) {
            return new ArrayList<>();
        }

        List<Long> hotSeasonIds = tabExtDao.queryAlreadyInTabSeasons(appId, SeasonTabType.HOT.getCode(), seasonIds);
        List<Long> newSeasonIds = tabExtDao.queryAlreadyInTabSeasons(appId, SeasonTabType.NEW.getCode(), seasonIds);

        return Stream.concat(hotSeasonIds.stream(), newSeasonIds.stream())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public void addSeasonToTab(String appId, int tabType, Map<Long, SeasonBo> seasonMap) {

        String lockKey = String.format(RedisKeyPattern.APP_SEASON_ADD_TAB.getPattern(), appId);
        RLock lock = cache.tryLock(lockKey);
        try {
            List<Long> seasonIdList = new ArrayList<>(seasonMap.keySet());

            List<Long> needAddSeasonIds = findNeedAddSeasons(appId, tabType, seasonIdList);

            if (CollectionUtils.isEmpty(needAddSeasonIds)) {
                return;
            }

            if (tabType == SeasonTabType.HOT.getCode()) {

                PageResult<Long> pageResult = queryTabSeasonIds(appId, tabType, Page.valueOf(1, 10));

                AssertUtil.isTrue(pageResult.getTotal() + needAddSeasonIds.size() <= configCenter.getSeasonConfig().getHotTabSeasonMaxCount(),
                        null,
                        StrUtil.format("热门tab下最多只能有{}部剧", configCenter.getSeasonConfig().getHotTabSeasonMaxCount()));
            }

            needAddSeasonIds.forEach(seasonId -> {
                SeasonBo seasonBo = seasonMap.get(seasonId);
                AssertUtil.isTrue(seasonBo.getAuthor() != null, ErrorCodeType.NO_DATA.getCode(), StrUtil.format("剧{}的作者信息不存在", seasonId));
                MiniAppOpenSeasonTabPo po = MiniAppOpenSeasonTabPo.builder()
                        .appId(appId)
                        .seasonId(seasonId)
                        .tabType(tabType)
                        .upMid(seasonBo.getAuthor().getMid())
                        .build();
                tabDao.insertSelective(po);
            });
        } finally {
            lock.unlock();
        }
    }

    private List<Long> findNeedAddSeasons(String appId, int tabType, List<Long> seasonIdList) {
        MiniAppOpenSeasonTabPoExample example = new MiniAppOpenSeasonTabPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andTabTypeEqualTo(tabType)
                .andSeasonIdIn(seasonIdList)
                .andIsDeletedEqualTo(0);
        List<MiniAppOpenSeasonTabPo> theseSeasonsExistedPos = tabDao.selectByExample(example);

        return seasonIdList.stream()
                .filter(seasonId -> theseSeasonsExistedPos.stream().noneMatch(po -> po.getSeasonId().equals(seasonId)))
                .collect(Collectors.toList());
    }

    @Override
    public void removeMidSeason(String appId, long mid) {
        MiniAppOpenSeasonTabPo update = new MiniAppOpenSeasonTabPo();
        update.setIsDeleted(1);

        MiniAppOpenSeasonTabPoExample example = new MiniAppOpenSeasonTabPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andUpMidEqualTo(mid)
                .andIsDeletedEqualTo(0);
        tabDao.updateByExampleSelective(update, example);
    }

    @Override
    public void removeSeasonFromTab(String appId, int tabType, Long seasonId) {
        MiniAppOpenSeasonTabPo update = new MiniAppOpenSeasonTabPo();
        update.setIsDeleted(1);

        MiniAppOpenSeasonTabPoExample example = new MiniAppOpenSeasonTabPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andTabTypeEqualTo(tabType)
                .andSeasonIdEqualTo(seasonId)
                .andIsDeletedEqualTo(0);
        tabDao.updateByExampleSelective(update, example);
    }

    @Override
    public PageResult<Long> queryTabSeasonIds(String appId, int tabType, Page page) {
        MiniAppOpenSeasonTabPoExample example = new MiniAppOpenSeasonTabPoExample();
        example.or()
                .andAppIdEqualTo(appId)
                .andTabTypeEqualTo(tabType)
                .andIsDeletedEqualTo(0);

        long count = tabDao.countByExample(example);
        if (count == 0) {
            return PageResult.emptyPageResult();
        }


        example.setOffset(page.getOffset());
        example.setLimit(page.getLimit());
        example.setOrderByClause("season_id desc");
        List<MiniAppOpenSeasonTabPo> pos = tabDao.selectByExample(example);


        return PageResult.<Long>builder()
                .total(Math.toIntExact(count))
                .records(pos.stream()
                        .map(MiniAppOpenSeasonTabPo::getSeasonId)
                        .collect(Collectors.toList()))
                .build();
    }
}
