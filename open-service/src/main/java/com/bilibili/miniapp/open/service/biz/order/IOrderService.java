package com.bilibili.miniapp.open.service.biz.order;

import com.bilibili.miniapp.open.common.annotations.ReadFlag;
import com.bilibili.miniapp.open.service.bo.order.*;

/**
 * <AUTHOR>
 * @date 2025/1/14 20:44
 */
public interface IOrderService {

    /**
     * 创建订单
     *
     * @param req must not be null
     */
    OrderCreateRes createOrder(OrderCreateReq req) throws Exception;

    /**
     * 根据开平订单查询订单明细
     *
     * @param orderId must not be null
     */
    @ReadFlag({ReadFlag.F.DB})
    Order getOrder(Long orderId);

    /**
     * 根据开发者订单查询订单明细
     *
     * @param appId      must not be null
     * @param devOrderId must not be null
     */
    @ReadFlag({ReadFlag.F.DB})
    Order getOrderByDevOrderId(String appId, String devOrderId);


    /**
     * 根据开发者订单查询订单明细
     */
    @ReadFlag({ReadFlag.F.DB})
    Order getOrderByOrderId(String appId, Long orderId);

    /**
     * 根据开平订单拓展信息
     *
     * @param orderId must not be null
     */
    @ReadFlag({ReadFlag.F.DB})
    OrderExtra getOrderExtra(Long orderId);

    /**
     * 更新订单信息
     *
     * @param order must not be null,
     *              and {@param order#order_id} must not be null,
     *              and {@param order#extra} may be null but not null in most cases
     */
    void updateOrder(Order order) throws Exception;
}
