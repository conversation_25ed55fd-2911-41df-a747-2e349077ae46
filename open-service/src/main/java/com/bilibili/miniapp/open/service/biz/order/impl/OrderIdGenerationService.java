package com.bilibili.miniapp.open.service.biz.order.impl;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.IpUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenOrderIdSegmentDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderIdSegmentPo;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.order.IOrderIdGenerationService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2025/1/16 11:10
 */
@Slf4j
@Service
public class OrderIdGenerationService extends AbstractOpenService implements IOrderIdGenerationService {
    @Autowired
    private MiniAppOpenOrderIdSegmentDao openOrderIdSegmentDao;
    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository cacheRepository;
    //偏移量，不至于id过小
    private static final long OFFSET = 1000000L;
    //每个号段的步长
    private static final long DELTA = 1000L;
    private static final AtomicLong TEST_SEGMENT = new AtomicLong(0);
    @Autowired
    private RedissonClient redissonClient;

    private static final String GENERATOR_SCRIPT = "local key1 = KEYS[1]\n" +
            "local key2 = KEYS[2]\n" +
            "\n" +
            "local m_id = redis.call('get', key2)\n" +
            "if not m_id then\n" +
            "    return 0\n" +
            "end\n" +
            "\n" +
            "local c_id = redis.call('incr', key1)\n" +
            "\n" +
            "if tonumber(c_id) > tonumber(m_id) then\n" +
            "    return 0\n" +
            "end\n" +
            "\n" +
            "return c_id";

    private static final String RESET_SCRIPT = "redis.call('mset', KEYS[1], ARGV[1], KEYS[2], ARGV[2])\n" +
            "return 0";

    /**
     * 非事务执行，屏蔽上游事务回滚可能导致的号段回拨问题
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public Long generateOrderId() throws Exception {
        RScript script = redissonClient.getScript();
        long id = script.eval(RScript.Mode.READ_WRITE, GENERATOR_SCRIPT, RScript.ReturnType.INTEGER,
                Lists.newArrayList(RedisKeyPattern.OPEN_ORDER_ID.getPattern(), RedisKeyPattern.OPEN_ORDER_ID_MAX.getPattern()));
        if (id <= 0) {
            RLock lock = null;
            try {
                lock = cacheRepository.getLock(RedisKeyPattern.OPEN_LOCK_ORDER_ID_SEGMENT.getPattern(), null);
                long segmentId = nextSegmentId();
                script.eval(RScript.Mode.READ_WRITE, RESET_SCRIPT, RScript.ReturnType.INTEGER,
                        Lists.newArrayList(RedisKeyPattern.OPEN_ORDER_ID.getPattern(), RedisKeyPattern.OPEN_ORDER_ID_MAX.getPattern()),
                        (segmentId - 1) * DELTA, segmentId * DELTA);
                id = script.eval(RScript.Mode.READ_WRITE, GENERATOR_SCRIPT, RScript.ReturnType.INTEGER,
                        Lists.newArrayList(RedisKeyPattern.OPEN_ORDER_ID.getPattern(), RedisKeyPattern.OPEN_ORDER_ID_MAX.getPattern()));
            } catch (Exception e) {
                log.error("[OrderIdGenerationService] generateId error", e);
            } finally {
                if (Objects.nonNull(lock)) {
                    try {
                        lock.unlock();
                    } catch (Exception e) {
                        log.error("[OrderIdGenerationService] generateId release lock error", e);
                    }
                }
            }
        }

        if (id <= 0) {
            log.error("[OrderIdGenerationService] failed to generate order id");
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR);
        }

        return OFFSET + id;
    }

    private long nextSegmentId() {
        MiniAppOpenOrderIdSegmentPo segment = MiniAppOpenOrderIdSegmentPo.builder()
                .ip(IpUtil.getLocalIp())
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
        openOrderIdSegmentDao.insertSelective(segment);
        return segment.getId();
//        return TEST_SEGMENT.incrementAndGet();
    }
}
