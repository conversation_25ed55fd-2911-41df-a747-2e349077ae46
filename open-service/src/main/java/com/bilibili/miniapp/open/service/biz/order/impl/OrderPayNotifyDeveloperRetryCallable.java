package com.bilibili.miniapp.open.service.biz.order.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.entity.SignParameterEntity;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.NotifyStatus;
import com.bilibili.miniapp.open.common.enums.PayStatus;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.common.util.OkHttpUtil;
import com.bilibili.miniapp.open.common.util.SignUtil;
import com.bilibili.miniapp.open.service.biz.access.IOpenAccessService;
import com.bilibili.miniapp.open.service.biz.order.IOrderService;
import com.bilibili.miniapp.open.service.biz.retry.OpenRetryCallable;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.bo.order.PayNotifyDeveloperInfo;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 【订单支付回调开发者】重试执行器
 *
 * <AUTHOR>
 * @date 2025/1/18 14:39
 */
@Slf4j
@Component
public class OrderPayNotifyDeveloperRetryCallable implements OpenRetryCallable {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IOpenAccessService accessService;

    @Override
    public RetryBizType bizType() {
        return RetryBizType.ORDER_PAY_NOTIFY_DEVELOPER;
    }

    @Override
    public boolean call(OpenRetryContext ctx) {
        String ctxStr = JSON.toJSONString(ctx);
        log.info("[OrderPayNotifyRetryCallable] prepare that notify pay info to developer, ctx={}", ctxStr);
        long orderId = ctx.getBizId();
        try {
            Order order = orderService.getOrder(orderId);
            if (Objects.isNull(order)) {
                //如果查不到订单，则直接置为成功
                return true;
            }
            PayStatus payStatus = PayStatus.getByCodeWithoutEx(order.getPayStatus());
            NotifyStatus notifyStatus = NotifyStatus.getByCodeWithoutEx(order.getNotifyStatus());

            //回调条件：
            // 1、传了回调地址
            // 2、已支付成功但未回调开发者时(失败或者未回调)
            boolean needNotify = StringUtils.hasText(order.getNotifyUrl())
                    && Objects.equals(payStatus, PayStatus.SUCCESS)
                    && !Objects.equals(notifyStatus, NotifyStatus.SUCCESS);
            if (!needNotify) {
                log.info("[OrderPayNotifyRetryCallable] give up that notify pay info to developer, because of NoNeedNotify, ctx={}", ctxStr);
                return true;
            }

            PayNotifyDeveloperInfo payNotifyDeveloperInfo = PayNotifyDeveloperInfo.builder()
                    .order_id(Objects.toString(order.getOrderId()))
                    .dev_order_id(order.getDevOrderId())
                    .amount(order.getAmount())
                    .pay_amount(order.getPayAmount())
                    .pay_time(order.getPayTime().getTime())
                    .pay_status(order.getPayStatus())
                    .pay_channel(order.getPayChannel())
                    .extra_data(Objects.nonNull(order.getExtra()) ? order.getExtra().getDevExtraData() : null)
                    .build();

            SignParameterEntity signParameterEntity = SignParameterEntity.builder()
                    .param2(payNotifyDeveloperInfo)
                    .ts(System.currentTimeMillis())
                    .build();

            OpenAccessBo access = accessService.getAccess(order.getAccessKey());
            String sign = SignUtil.sign(signParameterEntity, access.getAccessToken());

            String notifyUrl = String.format("%s?ts=%d&sign=%s", order.getNotifyUrl(), signParameterEntity.getTs(), sign);
            String body = JSON.toJSONString(payNotifyDeveloperInfo);
            log.info("[OrderPayNotifyRetryCallable] start that notify pay info to developer, notifyUrl={}, body={}",
                    notifyUrl, body);
            Response<?> response = OkHttpUtil.bodyPost(notifyUrl)
                    .json(JSON.toJSONString(payNotifyDeveloperInfo))
                    .callForObject(Response.class);
            log.info("[OrderPayNotifyRetryCallable] end that notify pay info to developer, notifyUrl={}, body={}, response={}",
                    notifyUrl, body, JSON.toJSONString(response));

            boolean notifySuccessful = Objects.equals(response.getCode(), ErrorCodeType.SUCCESS.getCode());
            NotifyStatus curNotifyStatus = notifySuccessful ? NotifyStatus.SUCCESS : NotifyStatus.FAILED;

            //如果本次回调的状态没有发生变化，则无需更新订单，防止无效的变更消息
            if (!Objects.equals(curNotifyStatus, notifyStatus)) {
                Order updateOrder = Order.builder()
                        .orderId(order.getOrderId())
                        .appId(order.getAppId())
                        .devOrderId(order.getDevOrderId())
                        .notifyStatus(notifySuccessful ? NotifyStatus.SUCCESS.getCode() : NotifyStatus.FAILED.getCode())
                        .build();
                orderService.updateOrder(updateOrder);
            }

            return notifySuccessful;
        } catch (Exception e) {
            log.error("[OrderPayNotifyRetryCallable] process pay notify to developer error,ctx={}", ctxStr, e);
        }
        return false;
    }
}
