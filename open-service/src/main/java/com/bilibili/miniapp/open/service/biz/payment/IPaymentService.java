package com.bilibili.miniapp.open.service.biz.payment;

import com.bilibili.miniapp.open.service.bo.order.Refund;
import com.bilibili.miniapp.open.service.bo.order.RefundReq;
import com.bilibili.miniapp.open.service.bo.payment.*;

import java.util.List;

/**
 * 支付Service
 * <p>
 * 注意：将退款作为Payment Domain流程更清晰，不会产生Order和Payment循环依赖
 *
 * <AUTHOR>
 * @date 2025/1/14 20:44
 */
public interface IPaymentService {

    /**
     * 交换支付信息（端上发起）
     *
     * @param req must not be null, and {@param req#order} must not be null
     */
    ExchangePayInfo exchangePayInfo(ExchangePayInfoReq req) throws Exception;

    /**
     * 确认支付信息（确认消费）（端上发起）
     *
     * @param req must not be null, and {@param req#order} must not be null
     */
    void confirmPayInfo(OpenPayConfirmInfo req) throws Exception;

    /**
     * 支付回调通知
     *
     * @param notifyInfo must not be null
     */
    void notify(PayNotifyInfo notifyInfo) throws Exception;

    /**
     * 发起退款订单（开发者发起）
     *
     * @param req must not be null
     */
    Refund refund(RefundReq req) throws Exception;

    /**
     * 更新退款信息
     *
     * @param orderRefund must not be null
     */
    void updateRefund(Refund orderRefund) throws Exception;

    /**
     * 查看退款信息
     *
     * @param orderId must not be null
     * @return 如果没有退款记录，则返回空列表emptyList
     */
    List<Refund> getRefundList(Long orderId) throws Exception;

    /**
     * 查看特定退款信息
     *
     * @param orderId     must not be null
     * @param devRefundId must not be null
     * @return 如果没有退款记录，则返回null
     */
    Refund getRefund(Long orderId, String devRefundId) throws Exception;

    /**
     * 发起支付中台退款
     *
     * @param orderId     must not be null
     * @param devRefundId must not be null
     */
    void refundWithPayPlatform(Long orderId, String devRefundId) throws Exception;

    /**
     * 支付中台退款回调通知
     *
     * @param notifyInfo must not be null
     */
    void refundNotify(RefundNotifyInfo notifyInfo) throws Exception;
}
