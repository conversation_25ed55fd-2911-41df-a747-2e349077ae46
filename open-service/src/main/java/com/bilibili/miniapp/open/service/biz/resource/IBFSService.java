package com.bilibili.miniapp.open.service.biz.resource;

import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.exception.ServiceException;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2024/9/6 20:09
 */
public interface IBFSService {
    BFSUploadResult upload(String category, String fileName, byte[] data) throws ServiceException;

    BFSUploadResult upload(String category, File file) throws ServiceException;
}
