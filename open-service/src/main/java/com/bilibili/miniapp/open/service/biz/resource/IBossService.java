package com.bilibili.miniapp.open.service.biz.resource;

import java.io.File;
import java.io.InputStream;

public interface IBossService {

    //通用
    String upload(String filePath, String fileName, File file);

    String upload(String filePath, String fileName, String base64Str);

    String upload(String filePath, String fileName, InputStream inputStream, long len);

    String uploadWithoutExp(String filePath, String fileName, InputStream inputStream, long len);

    //兼容具体业务
    String upload4biz(String fileName, InputStream inputStream, long len, boolean isOverride);

    boolean exist(String key);

    boolean delete(String key);
}