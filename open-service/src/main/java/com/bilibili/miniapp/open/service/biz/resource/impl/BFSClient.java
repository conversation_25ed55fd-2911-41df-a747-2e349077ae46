package com.bilibili.miniapp.open.service.biz.resource.impl;


import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.HttpClientUtil;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.util.Base64Utils;

import javax.activation.MimetypesFileTypeMap;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024/9/6 20:44
 */
@Slf4j
public class BFSClient implements Closeable {
    private final String accessKey;
    private final String accessSecret;
    private final String bucketName;
    private final CloseableHttpClient httpClient;
    private final String baseUrl;
    private final Mac mac;
    private final String domain;
    private final String hostname;
    private final AtomicInteger rotate = new AtomicInteger();
    private static final String MAC_NAME = "HmacSHA1";
    private static final String ENCODING = "utf8";
    private static final MimetypesFileTypeMap mimetypesFileTypeMap = new MimetypesFileTypeMap();
    private static final HttpClientUtil.HttpConfig defaultConfig = HttpClientUtil.HttpConfig.builder().maxPoolSize(50).build();

    static {
        mimetypesFileTypeMap.addMimeTypes("image/png png PNG");
        mimetypesFileTypeMap.addMimeTypes("image/webp webp WEBP");

        mimetypesFileTypeMap.addMimeTypes("application/javascript js JS");
        mimetypesFileTypeMap.addMimeTypes("text/css css CSS");
        mimetypesFileTypeMap.addMimeTypes("text/html html HTML");

        mimetypesFileTypeMap.addMimeTypes("application/x-font-ttf ttf TTF");
        mimetypesFileTypeMap.addMimeTypes("image/svg+xml svg SVG");
    }


    /**
     * BFS文件客户端
     *
     * @param host         BFS主机名
     * @param port         BFS端口 0为不设置
     * @param accessKey    应用accessKey
     * @param accessSecret 应用accessSecret
     * @param bucketName   应用Bucket名
     */
    public BFSClient(String host, int port, String accessKey, String accessSecret, String bucketName) {
        this(host, port, accessKey, accessSecret, bucketName, defaultConfig);
    }

    /**
     * BFS文件客户端
     *
     * @param host         BFS主机名
     * @param port         BFS端口 0为不设置
     * @param accessKey    应用accessKey
     * @param accessSecret 应用accessSecret
     * @param bucketName   应用Bucket名
     * @param config       HTTP配置
     */
    public BFSClient(String host, int port, String accessKey, String accessSecret, String bucketName, HttpClientUtil.HttpConfig config) {
        Preconditions.checkNotNull(host, "Host can't be null or empty!");
        Preconditions.checkNotNull(accessKey, "AcessKey can't be null or empty!");
        Preconditions.checkNotNull(accessSecret, "AccessSecret can't be null or empty!");
        Preconditions.checkNotNull(bucketName, "Bucketname can't be null or empty!");
        this.accessKey = accessKey;
        this.accessSecret = accessSecret;
        this.bucketName = bucketName;
        this.hostname = getHostname();
        this.domain = port <= 0 ? host : String.format("%s:%d", host, port);
        this.baseUrl = String.format("%s://%s/%s", "http", domain, bucketName);
        this.httpClient = HttpClientUtil.buildHttpClient(config);
        byte[] data;
        try {
            data = accessSecret.getBytes(ENCODING);
            mac = Mac.getInstance(MAC_NAME);
            SecretKey secretKey = new SecretKeySpec(data, MAC_NAME);
            mac.init(secretKey);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(String.format("Encoding error for secret %s", accessSecret));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(String.format("Mac algorithm not found for name %s", MAC_NAME));
        } catch (InvalidKeyException e) {
            throw new RuntimeException(String.format("Invalid secret key %s", accessSecret));
        }
    }

    /**
     * 上传文件
     *
     * @param category 文件分类，影响生成URL，由字母、数字、下划线组成
     * @param file     上传的文件
     * @return 上传结果
     * @throws ServiceException 上传失败
     */
    public BFSUploadResult upload(String category, File file) throws ServiceException {
        try {
            String fileName = file.getName();
            byte[] fileData = Files.readAllBytes(file.toPath());
            return upload(category, fileName, fileData);
        } catch (IOException e) {
            throw new ServiceException(e, "");
        }
    }

    public BFSUploadResult uploadWithOriginalFilename(String category, File file) throws ServiceException {
        try {
            String fileName = file.getName();
            byte[] fileData = Files.readAllBytes(file.toPath());
            return this.uploadWithOriginalFilename(category, fileName, fileData);
        } catch (IOException e) {
            throw new ServiceException(e, "");
        }
    }

    /**
     * 上传二进制数据
     *
     * @param category 文件分类，影响生成URL，由字母、数字、下划线组成
     * @param fileName 文件名
     * @param data     文件二进制数据
     * @return 上传结果
     * @throws ServiceException 上传失败
     */
    public BFSUploadResult upload(String category, String fileName, byte[] data) throws ServiceException {
        return this.upload(category, fileName, data, false);
    }

    public BFSUploadResult uploadWithOriginalFilename(String category, String fileName, byte[] data) throws ServiceException {
        return this.upload(category, fileName, data, true);
    }

    public BFSUploadResult upload(String category, String fileName, byte[] data, boolean fixedName) throws ServiceException {
        String fileNameWithPath = fixedName ? String.format("%s/%s", category, fileName) : this.buildFileNameWithPath(category, fileName);
        String url = String.format("%s/%s", baseUrl, fileNameWithPath);
        HttpPut put = this.getHttpPut(fileName, data, fileNameWithPath, url);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(put, new HttpClientContext());
            if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                throw new ServiceException(String.format("Upload return code %d,url=%s", response.getStatusLine().getStatusCode(), url));
            }
            Header[] headers = response.getHeaders("Location");
            if (headers == null || headers.length == 0) {
                throw new ServiceException(String.format("Failed to get location of uploaded file, url=%s", url));
            }
            String location = headers[0].getValue();
            log.info("[BFSClient] upload url={}, image_url={}", url, location);
            return BFSUploadResult.builder()
                    .url(location.replace("http://", "https://"))
                    .token("")
                    .hashCode(Base64Utils.encodeToString(url.getBytes()))
                    .md5(DigestUtils.md5Hex(data))
                    .build();
        } catch (IOException e) {
            throw new ServiceException(e, url);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.warn("Close response error", e);
                }
            }
        }
    }

    private HttpPut getHttpPut(String fileName, byte[] data, String fileNameWithPath, String url) {
        DateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        HttpPut put = new HttpPut(url);
        Date now = new Date();
        put.addHeader("Authorization", buildAuth(HttpPut.METHOD_NAME, fileNameWithPath, now.getTime() / 1000));
        put.addHeader("Date", dateFormat.format(now));
        String mimeType = mimetypesFileTypeMap.getContentType(fileName);
        put.addHeader("Content-Type", mimeType);
        put.addHeader("Host", domain);
        put.setEntity(new ByteArrayEntity(data));
        // milliseconds
        int httpConnectTimeout = 3000;
        // milliseconds
        int httpSocketTimeout = 3000;
        // milliseconds;
        int httpConnectionRequestTimeout = 3000;
        put.setConfig(RequestConfig.custom()
                .setConnectTimeout(httpConnectTimeout)
                .setSocketTimeout(httpSocketTimeout)
                .setConnectionRequestTimeout(httpConnectionRequestTimeout)
                .build());
        return put;
    }

    private String buildDomain(String host, int port) {
        if (port <= 0) {
            return host;
        } else {
            return String.format("%s:%d", host, port);
        }
    }

    private String buildFileNameWithPath(String category, String fileName) {
        Date now = new Date();
        String md5 = DigestUtils.md5Hex(fileName + now.getTime() + hostname + getRotate());
        String suffix = getFileExtend(fileName);

        if (StringUtils.isNotBlank(suffix)) {
            DateFormat filePathDateFormat = new SimpleDateFormat("yyyyMM");
            return String.format("%s/%s/%s.%s", category, filePathDateFormat.format(now), md5, suffix);
        }
        return String.format("%s/%s", category, md5);
    }

    private String buildAuth(String method, String fileNameWithPath, long expires) {
        return String.format("%s:%s:%d",
                accessKey,
                new String(
                        base64(
                                hmacShar1(
                                        accessSecret, String.format("%s\n%s\n%s\n%d\n", method, bucketName, fileNameWithPath, expires)
                                )
                        )
                ),
                expires
        );
    }

    byte[] hmacShar1(String key, String text) {
        byte[] data;
        try {
            data = text.getBytes(ENCODING);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(String.format("Encoding error for text %s", key));
        }

        return mac.doFinal(data);
    }

    byte[] base64(byte[] text) {
        return Base64.getEncoder().encode(text);
    }

    @Override
    public void close() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }

    private String getFileExtend(String fileName) {
        if (fileName == null) {
            return null;
        }
        int pos = fileName.lastIndexOf('.');
        if (pos < 0) {
            return null;
        }

        return fileName.substring(pos + 1);
    }

    private String getHostname() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            return "UNKNOWN";
        }
    }

    private int getRotate() {
        return rotate.accumulateAndGet(100000, (left, right) -> {
            left++;
            if (left >= right) {
                return 0;
            }
            return left;
        });
    }
}

