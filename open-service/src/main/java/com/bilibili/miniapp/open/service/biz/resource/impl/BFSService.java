package com.bilibili.miniapp.open.service.biz.resource.impl;

import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @date 2024/9/6 20:09
 */
@Slf4j
@Service
public class BFSService extends AbstractOpenService implements IBFSService {
    @Autowired
    private BFSClient bFSClient;

    @Override
    public BFSUploadResult upload(String category, String fileName, byte[] data) throws ServiceException {
        log.info("upload param category-{} fileName-{}", category, fileName);
        if (Strings.isNullOrEmpty(category) || Strings.isNullOrEmpty(fileName) || data == null || data.length < 1) {
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(), "参数错误");
        }
        return bFSClient.upload(category, fileName.toLowerCase(), data);
    }

    @Override
    public BFSUploadResult upload(String category, File file) throws ServiceException {
        log.info("upload param category-{} file-{}", category, file);
        if (Strings.isNullOrEmpty(category) || file == null) {
            throw new ServiceException(ErrorCodeType.BAD_PARAMETER.getCode(), "参数错误");
        }
        try {
            String fileName = file.getName();
            byte[] fileData = Files.readAllBytes(file.toPath());
            return bFSClient.upload(category, fileName, fileData);
        } catch (IOException e) {
            log.error("BFS upload failed", e);
            throw new ServiceException(e, "BFS upload failed");
        }
    }
}
