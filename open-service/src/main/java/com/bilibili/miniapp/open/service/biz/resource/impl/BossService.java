package com.bilibili.miniapp.open.service.biz.resource.impl;

import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.service.biz.resource.IBossService;
import com.bilibili.miniapp.open.service.configuration.BossConfiguration;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.transfer.s3.S3TransferManager;
import software.amazon.awssdk.transfer.s3.model.CopyRequest;
import software.amazon.awssdk.transfer.s3.model.Upload;
import software.amazon.awssdk.transfer.s3.model.UploadFileRequest;
import software.amazon.awssdk.transfer.s3.progress.LoggingTransferListener;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Service
public class BossService implements IBossService {
    private final BossConfiguration.BossConfigurationProperties properties;
    private final S3Client s3Client;
    private final S3AsyncClient s3AsyncClient;

    public BossService(BossConfiguration.BossConfigurationProperties properties, S3Client s3Client, S3AsyncClient s3AsyncClient) {
        this.properties = properties;
        this.s3Client = s3Client;
        this.s3AsyncClient = s3AsyncClient;
    }

    @Override
    public String upload(String filePath, String fileName, File file) {
        Transaction tx = Cat.getProducer().newTransaction("Boss", "upload4file");
        String url = "";
        filePath = StringUtils.hasText(filePath) ? filePath : "default";
        String key = Joiner.on("/").join(filePath, fileName);
        Assert.isTrue(!exist(key), "存在同名文件：" + key);
        try {
            url = upload(key, file);
            tx.setStatus(Transaction.SUCCESS);
        } catch (Throwable e) {
            tx.setStatus("1");
            log.error("[BossService] failed to upload file, filePath={}, fileName={}", filePath, fileName, e);
            throw new ServiceException(e, "文件上传失败");
        } finally {
            tx.complete();
            log.info("[BossService] upload to boss end. file boss url:{}, cost:{}", url, tx.getDurationInMillis() / 1000);
        }
        return url;
    }

    @Override
    public String upload(String filePath, String fileName, String base64Str) {
        // 通过base64字符串上传文件
        byte[] bytes = Base64Utils.decodeFromString(base64Str);
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            return upload(filePath, fileName, inputStream, bytes.length);
        } catch (Exception e) {
            log.error("[BossService] failed to create input stream from base64 string", e);
            return null;
        }
    }

    @Override
    public String upload(String filePath, String fileName, InputStream inputStream, long len) {
        String url = "";
        filePath = StringUtils.hasText(filePath) ? filePath : "default";
        String key = Joiner.on("/").join(filePath, fileName);
        Assert.isTrue(!exist(key), "存在同名文件：" + key);
        return uploadWithTx(fileName, inputStream, len, url, key);
    }

    @Override
    public String uploadWithoutExp(String filePath, String fileName, InputStream inputStream, long len) {
        String url = "";
        filePath = StringUtils.hasText(filePath) ? filePath : "default";
        String key = Joiner.on("/").join(filePath, fileName);
        if (exist(key)) {
            log.warn("[BossService] file already exists, key={}", key);
            return Joiner.on("/").join(properties.getDownloadHost(), properties.getBucket(), key);
        }
        return uploadWithTx(fileName, inputStream, len, url, key);
    }

    @NotNull
    private String uploadWithTx(String fileName, InputStream inputStream, long len, String url, String key) {
        Transaction tx = Cat.getProducer().newTransaction("Boss", "upload4stream");
        try {
            url = upload(key, inputStream, len);
            tx.setStatus(Transaction.SUCCESS);
        } catch (Throwable e) {
            tx.setStatus("1");
            log.error("[BossService] failed to upload file, fileName={}", fileName, e);
            throw e;
        } finally {
            tx.complete();
            log.info("[BossService] upload to boss end. file boss url:{}, cost:{}", url, tx.getDurationInMillis() / 1000);
        }
        return url;
    }

    @Override
    public String upload4biz(String fileName, InputStream inputStream, long len, boolean isOverride) {
        Transaction tx = Cat.getProducer().newTransaction("Boss", "upload4biz");
        String url = "";
        try {
            if (exist(fileName)) {
                Assert.isTrue(isOverride, "存在同名文件：" + fileName);
                String temp = System.currentTimeMillis() + "_" + fileName;
                String tempUrl = upload(temp, inputStream, len);
                log.info("[BossService] temp file url [{}] for {}", tempUrl, fileName);
                url = copy(temp, fileName);
            } else {
                url = upload(fileName, inputStream, len);
            }
            tx.setStatus(Transaction.SUCCESS);
        } catch (Throwable e) {
            tx.setStatus("1");
            log.error("[BossService] failed to upload file, fileName={}", fileName, e);
            throw e;
        } finally {
            tx.complete();
            log.info("[BossService] upload to boss end. file boss url:{}, cost:{}", url, tx.getDurationInMillis() / 1000);
        }
        return url;
    }

    @Override
    public boolean exist(String key) {
        if (!StringUtils.hasText(key)) {
            //非法的数据，一并认为存在
            return true;
        }
        try {
            HeadObjectRequest request = HeadObjectRequest.builder()
                    .bucket(properties.getBucket())
                    .key(key)
                    .build();
            s3Client.headObject(request);
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        }
    }

    @Override
    public boolean delete(String key) {
        if (!StringUtils.hasText(key)) {
            //非法的数据，一并认为删除成功
            return true;
        }

        try {
            DeleteObjectRequest request = DeleteObjectRequest.builder()
                    .bucket(properties.getBucket())
                    .key(key)
                    .build();
            s3Client.deleteObject(request);
            return true;
        } catch (Throwable e) {
            log.error("[BossService] failed to delete file,key={}", key, e);
            throw new ServiceException(e, "删除文件失败:" + key);
        }
    }

    private String upload(String key, File file) {
        S3TransferManager transferManager = S3TransferManager.builder()
                .s3Client(s3AsyncClient)
                .build();

        // 创建上传请求
        UploadFileRequest uploadRequest = UploadFileRequest.builder()
                .putObjectRequest(b -> b.bucket(properties.getBucket()).key(key))
                .addTransferListener(LoggingTransferListener.create()) // 可选的进度监听器
                .source(Paths.get(file.getAbsolutePath()))
                .build();

        // 执行上传
        transferManager.uploadFile(uploadRequest).completionFuture().join();

        // 关闭Transfer Manager
        //transferManager.close();

        return Joiner.on("/").join(properties.getDownloadHost(), properties.getBucket(), key);
    }

    private String upload(String key, InputStream inputStream, long len) {
        S3TransferManager transferManager = S3TransferManager.builder()
                .s3Client(s3AsyncClient)
                .build();

        ThreadPoolExecutor executor = ThreadPoolUtil.getExecutor(ThreadPoolType.BOSS);
        AsyncRequestBody asyncRequestBody = AsyncRequestBody.fromInputStream(inputStream, len, executor);
        Upload upload = transferManager.upload(builder -> builder
                .requestBody(asyncRequestBody)
                .putObjectRequest(req -> req.bucket(properties.getBucket()).key(key))
                .build());
        upload.completionFuture().join();

        return Joiner.on("/").join(properties.getDownloadHost(), properties.getBucket(), key);
    }

    private String uploadBackup(String key, InputStream inputStream, long len) {
        PutObjectRequest request = PutObjectRequest.builder().bucket(properties.getBucket()).key(key).build();
        RequestBody requestBody = RequestBody.fromInputStream(inputStream, len);
        s3Client.putObject(request, requestBody);
        return Joiner.on("/").join(properties.getDownloadHost(), properties.getBucket(), key);
    }

    private String copy(String srcKey, String destKey) {
        S3TransferManager transferManager = S3TransferManager.builder()
                .s3Client(s3AsyncClient)
                .build();

        CopyObjectRequest request = CopyObjectRequest.builder()
                .sourceBucket(properties.getBucket())
                .sourceKey(srcKey)
                .destinationBucket(properties.getBucket())
                .destinationKey(destKey)
                .build();
        CopyRequest copyRequest = CopyRequest.builder()
                .copyObjectRequest(request)
                .build();
        transferManager.copy(copyRequest).completionFuture().join();

        return Joiner.on("/").join(properties.getDownloadHost(), properties.getBucket(), destKey);
    }
}