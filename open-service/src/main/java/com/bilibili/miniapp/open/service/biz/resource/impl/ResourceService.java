package com.bilibili.miniapp.open.service.biz.resource.impl;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import com.bilibili.miniapp.open.common.entity.BFSKey;
import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.bilibili.miniapp.open.service.biz.resource.IResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Slf4j
@Service
public class ResourceService implements IResourceService {
    @Autowired
    private IBFSService bfsService;

    @Override
    public String uploadPic(MultipartFile pic, Integer width, Integer height) {
        try(InputStream inputStream = pic.getInputStream()) {
            check(inputStream, width, height);
            BFSUploadResult upload = bfsService.upload(BFSKey.CATEGORY_MINIAPP_OPEN_RESOURCE, pic.getOriginalFilename(), pic.getBytes());
            return upload.getUrl();
        } catch (IOException e) {
            log.error("上传文件时发生异常", e);
            throw new RuntimeException(e);
        }
    }

    private void check(InputStream picStream,Integer width, Integer height) {
        if (width != null && height != null) {
            BufferedImage image = ImgUtil.read(picStream);
            Assert.isTrue(image.getWidth() == width, StrUtil.format("图片宽不符合尺寸，应该为:{}，实际为:{}", width, image.getWidth()));
            Assert.isTrue(image.getHeight() == height, StrUtil.format("图片高不符合尺寸，应该为:{}，实际为:{}", height, image.getHeight()));
        }
    }
}
