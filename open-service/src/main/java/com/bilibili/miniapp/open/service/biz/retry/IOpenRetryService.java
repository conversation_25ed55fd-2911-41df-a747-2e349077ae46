package com.bilibili.miniapp.open.service.biz.retry;

import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;

/**
 * <AUTHOR>
 * @date 2025/1/17 21:12
 */
public interface IOpenRetryService {

    /**
     * 发起重试任务请求，调用方：业务方主动调用和任务调度
     *
     * @param ctx, 重试任务的上下文数据
     * @return true/false，分别表示任务成功和失败
     * @see OpenRetryCallable
     */
    boolean retry(OpenRetryContext ctx);

    /**
     * 重试所有可重试的任务，调用方：任务调度
     *
     * @see OpenRetryCallable
     */
    void retry();
}
