package com.bilibili.miniapp.open.service.biz.retry;

import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;

/**
 * 真正完成【重试业务逻辑执行器】
 *
 * <AUTHOR>
 * @date 2025/1/17 21:50
 */
public interface OpenRetryCallable {

    /**
     * 业务类型
     * <p>
     * 必须返回非null实例，【重试框架】会根据此类型分发【重试业务逻辑执行器】
     */
    RetryBizType bizType();

    /**
     * 【重试业务逻辑执行器】的业务逻辑
     * <p>
     * 备注：请不要抛出任何异常，成功与失败通过返回值标识，否则【重试框架】会默认为失败
     * <p>
     *
     * @param ctx must not be null，重试所需要的上下文数据
     * @return true/false，分别代表重试成功和失败，
     * <p>
     * 1、如果返回失败则【重试框架】会
     * <p>
     * 1.1、未达到重试上限，持久化上下文和失败状态，且下次重试
     * <p>
     * 1.2、达到重试上限，持久化上下文以及失败终态，且不再重试
     * <p>
     * 2、如果返回成功则【重试框架】会
     * <p>
     * 2.1、首次调用（一般是业务主动调用），【重试框架】会直接忽略即首次调用的任务如果成功则不会持久化
     * <p>
     * 2.3、非首次调用，标记当前重试任务成功且后续不在重试
     */
    boolean call(OpenRetryContext ctx);
}
