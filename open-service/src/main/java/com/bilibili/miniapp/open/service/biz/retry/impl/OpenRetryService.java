package com.bilibili.miniapp.open.service.biz.retry.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.common.enums.RetryStatus;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.common.util.TraceUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenRetryDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRetryPoExample;
import com.bilibili.miniapp.open.service.biz.retry.IOpenRetryService;
import com.bilibili.miniapp.open.service.biz.retry.OpenRetryCallable;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/17 21:51
 */
@Slf4j
@Component
public class OpenRetryService implements IOpenRetryService {
    @Autowired
    private MiniAppOpenRetryDao openRetryDao;
    @Autowired
    private List<OpenRetryCallable> consumers;
    private Map<RetryBizType, OpenRetryCallable> bizTypeConsumerMap;

    @PostConstruct
    private void init() {
        bizTypeConsumerMap = Optional.ofNullable(consumers)
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(OpenRetryCallable::bizType, Function.identity()));
    }

    @Override
    public boolean retry(OpenRetryContext ctx) {
        OpenRetryCallable openRetryCallable = bizTypeConsumerMap.get(ctx.getBizType());
        if (Objects.isNull(openRetryCallable)) {
            return true;
        }
        boolean success = false;
        try {
            if (!StringUtils.hasText(ctx.getReqId())) {
                //避免业务首次主动调用时必须设置req_id的繁琐
                ctx.setReqId(TraceUtil.genTraceId());
            }
            success = openRetryCallable.call(ctx);
        } catch (Exception e) {
            log.error("[OpenRetryService] retry openRetryCallable call error, ctx={}", JSON.toJSONString(ctx), e);
        }
        //无论saveRetry是否成功，只要openRetryCallable成功即为成功
        try {
            saveRetry(ctx, success);
        } catch (Exception e) {
            log.error("[OpenRetryService] retry saveRetry error, callableSuccess={}, ctx={}", success, JSON.toJSONString(ctx), e);
        }
        return success;
    }

    @Override
    public void retry() {
        Timestamp curTime = new Timestamp(System.currentTimeMillis());
        int limit = 200;
        long id = 0;
        while (true) {
            MiniAppOpenRetryPoExample example = new MiniAppOpenRetryPoExample();
            example.createCriteria()
                    .andRetryStatusEqualTo(RetryStatus.FAILED.getCode())
                    .andNextTimeLessThanOrEqualTo(curTime)
                    .andIdGreaterThan(id);
            example.setLimit(limit);
            example.setOrderByClause("id");
            List<MiniAppOpenRetryPo> miniAppOpenRetryPos = openRetryDao.selectByExample(example);
            if (CollectionUtils.isEmpty(miniAppOpenRetryPos)) {
                return;
            }
            id = miniAppOpenRetryPos.get(miniAppOpenRetryPos.size() - 1).getId();
            for (MiniAppOpenRetryPo miniAppOpenRetryPo : miniAppOpenRetryPos) {
                RetryBizType retryBizType = RetryBizType.getByCodeWithoutEx(miniAppOpenRetryPo.getBizType());
                //为了防止数据不存在导致saveRetry重复插入数据导致的问题，因此要求三元组[retryBizType,biz_id,req_id]必须存在
                boolean isRetry = StringUtils.hasText(miniAppOpenRetryPo.getReqId())
                        && !Objects.equals(retryBizType, RetryBizType.UNKNOWN)
                        && NumberUtil.isPositive(miniAppOpenRetryPo.getBizId());
                if (isRetry) {
                    try {
                        /**
                         * 解析RetryBizType时不要报错，因为很可能开发过程新增了BizType枚举，但还没上线，此时线上的job也会捞取到对应的数据，
                         * {@link #retry(OpenRetryContext)}方法找不到匹配的consumer则直接终止，这个很重要！！！
                         */
                        OpenRetryContext ctx = OpenRetryContext.builder()
                                .bizType(retryBizType)
                                .bizId(miniAppOpenRetryPo.getBizId())
                                .reqId(miniAppOpenRetryPo.getReqId())
                                .bizData(JSON.parseObject(miniAppOpenRetryPo.getBizData()))
                                .retryCount(miniAppOpenRetryPo.getRetryCount())
                                .build();
                        retry(ctx);
                    } catch (Exception e) {
                        //ignore
                        //多个业务逻辑，互不影响
                    }
                }
            }
        }
    }

    private void saveRetry(OpenRetryContext ctx, boolean isSuccess) {
        RetryBizType bizType = ctx.getBizType();
        Integer[] timeRetryStrategy = bizType.getTimeRetryStrategy();
        MiniAppOpenRetryPoExample example = new MiniAppOpenRetryPoExample();
        example.createCriteria()
                .andBizTypeEqualTo(ctx.getBizType().getCode())
                .andBizIdEqualTo(ctx.getBizId())
                .andReqIdEqualTo(ctx.getReqId());
        List<MiniAppOpenRetryPo> miniAppOpenRetryPos = openRetryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(miniAppOpenRetryPos) && isSuccess) {
            //如果不存在重试记录但此次成功（即首次成功），则无需插入数据
            return;
        }

        if (CollectionUtils.isEmpty(miniAppOpenRetryPos)) {
            //首次执行就失败了。。。
            MiniAppOpenRetryPo miniAppOpenRetryPo = MiniAppOpenRetryPo.builder()
                    .bizType(ctx.getBizType().getCode())
                    .bizId(ctx.getBizId())
                    .reqId(StringUtils.hasText(ctx.getReqId()) ? ctx.getReqId() : TraceUtil.genTraceId())//最差情况下的兜底
                    .bizData(Objects.isNull(ctx.getBizData()) ? "{}" : ctx.getBizData().toJSONString())
                    .retryStatus(RetryStatus.FAILED.getCode())
                    .retryCount(0)
                    .nextTime(Timestamp.valueOf(LocalDateTime.now().plusSeconds(timeRetryStrategy[0])))
                    .isDeleted(IsDeleted.VALID.getCode())
                    .ctime(TimeUtil.curTimestamp())
                    .mtime(TimeUtil.curTimestamp())
                    .build();
            openRetryDao.insertUpdate(miniAppOpenRetryPo);
        } else {
            //多次重试了，此次可能成功、可能失败
            MiniAppOpenRetryPo miniAppOpenRetryPo = miniAppOpenRetryPos.get(0);
            RetryStatus retryStatus = isSuccess ? RetryStatus.SUCCESS : RetryStatus.FAILED;
            if (miniAppOpenRetryPo.getRetryCount() >= timeRetryStrategy.length - 1 && !isSuccess) {
                //如果已经执行了maxCount-1次，此次又失败了，则标记为失败最终态
                retryStatus = RetryStatus.FAILED_TERMINAL;
            }

            Timestamp nextTime = null;//如果已经重试达到最大次数了，则下次执行时间不再更新了
            if (miniAppOpenRetryPo.getRetryCount() < timeRetryStrategy.length - 1) {
                nextTime = Timestamp.valueOf(LocalDateTime.now().plusSeconds(timeRetryStrategy[miniAppOpenRetryPo.getRetryCount() + 1]));
            }

            MiniAppOpenRetryPo updateMiniAppOpenRetryPo = MiniAppOpenRetryPo.builder()
                    .id(miniAppOpenRetryPo.getId())
                    .bizData(Objects.isNull(ctx.getBizData()) ? "{}" : ctx.getBizData().toJSONString())
                    .retryStatus(retryStatus.getCode())
                    .retryCount(miniAppOpenRetryPo.getRetryCount() + 1)
                    .nextTime(nextTime)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .build();
            openRetryDao.updateByPrimaryKeySelective(updateMiniAppOpenRetryPo);
        }

    }
}
