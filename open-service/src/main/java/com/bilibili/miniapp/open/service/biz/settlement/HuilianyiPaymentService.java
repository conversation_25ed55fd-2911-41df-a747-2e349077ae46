package com.bilibili.miniapp.open.service.biz.settlement;

import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams;
import com.bilibili.miniapp.open.service.rpc.http.model.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
public interface HuilianyiPaymentService {


    /**
     * 创建预提单。由每半月的定时任务驱动
     * @param businessName
     * @param withdrawAmt
     * @return
     */
    HuilianyiAccrualCreateResult createAccrual(AccrualCreateMandatoryParams withdrawAmt);


    /**
     * 发票ocr。 由用户在业务侧的体现入口发起
     * @param invoiceImgUrl
     * @return
     */
    HuilianyiOcrResult receiptOcr(String invoiceImgUrl);

    Map<String, HuilianyiInvoiceWrapper> batchProcessInvoice(List<String> invoiceImgUrls);


    HuilianyiExpenseCreateResult createExpense(
           ExpenseCreateMandatoryParams expenseCreateMandatoryParams
    );
}


