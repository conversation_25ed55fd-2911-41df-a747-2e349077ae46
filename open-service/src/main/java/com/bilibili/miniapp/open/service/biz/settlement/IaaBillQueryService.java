package com.bilibili.miniapp.open.service.biz.settlement;

import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillRecordDTO;
import java.util.List;

/**
 * 账单查询服务， 查询内容包括结算账单、提现账单、提现账单中日结明细。
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
public interface IaaBillQueryService {


    IaaWithdrawBillRecordDTO queryById(Long billId);


    List<IaaWithdrawBillRecordDTO> batchQueryById(List<Long> billIds);
}
