package com.bilibili.miniapp.open.service.biz.settlement;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/24
 */
public interface IaaSettlementDailyCheck {


    /**
     * 每日调度对系统中未结算的账单尝试进行结算，对于目前来说，本质是轮询检查合同是否发生了续签
     */
    void onDailyRecoverCheck4InvalidSettlements();

    /**
     * 每日调度对crm中未结算的账单尝试进行结算，对于目前来说，本质是轮询检查app是否绑定了商业广告主
     */
    void onDailyRecoverCheck4InvalidCrmChargeBills();

    /**
     * 尝试恢复turn_withdrawable_failed的账单
     */
    void onDailyRecoverCheck4InvalidWithdrawBills();

    /**
     * 每日对账，检查withdraw_bill中的金额是否与settlement中匹配
     */

    void onDailyCheckBillAmt();



}
