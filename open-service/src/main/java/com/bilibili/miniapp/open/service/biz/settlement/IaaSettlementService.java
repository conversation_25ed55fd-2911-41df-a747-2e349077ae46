package com.bilibili.miniapp.open.service.biz.settlement;

import com.bilibili.miniapp.open.service.biz.settlement.model.IaaDailyIncomeEvent;
import com.bilibili.miniapp.open.service.biz.settlement.vo.WithdrawApplyRequest;
import com.bilibili.miniapp.open.service.biz.settlement.vo.WithdrawApplyResult;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCallback;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
public interface IaaSettlementService {

    void processHistoricalSettlements(String lastLogdate);


    void onDailySettlementsReady(String readyLogdate);



    /**
     * 每日收入汇总，被berserker吐出的databus事件驱动
     *
     * 只结算某单个app
     *
     * @param dailySum
     */
    void onDailySettlement(IaaDailyIncomeEvent dailySum);


    /**
     * 每半月收入汇总
     * 账单日未每月16号和次月1号，该事件一般为本日的日收入结算完成后触发
     *
     */

    void onMonthlyWithdrawDateSchedule(String withdrawDateLte);

    /**
     * 提现账单状态更新
     * @param callback
     */
    void listenHuilianyiCallback(HuilianyiExpenseCallback callback);



    /**
     * 业务侧用户发起提现。
     * @param applyRequest 申请提现请求， 包含app信息，账单信息，发票
     * @return
     */
    WithdrawApplyResult applyWithdraw(WithdrawApplyRequest applyRequest);
}
