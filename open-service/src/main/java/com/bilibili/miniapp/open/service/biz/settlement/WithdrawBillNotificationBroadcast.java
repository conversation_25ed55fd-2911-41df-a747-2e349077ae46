package com.bilibili.miniapp.open.service.biz.settlement;

import com.bilibili.miniapp.open.repository.mysql.settlement.mapper.IaaWithdrawBillMapper;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPoExample;
import com.bilibili.miniapp.open.service.biz.settlement.event.WithdrawBillStatusUpdateEvent;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBillStatus;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillRecordDTO;
import com.bilibili.miniapp.open.service.databus.entity.IaaWithdrawBillEventMsg;
import com.bilibili.miniapp.open.service.databus.producer.IaaWithdrawBillStatusUpdateEventProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/24
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class WithdrawBillNotificationBroadcast {


    private final IaaWithdrawBillStatusUpdateEventProducer eventProducer;


    private final IaaBillQueryService billQueryService;

    @Autowired
    private IaaWithdrawBillMapper iaaWithdrawBillMapper;

    @EventListener(WithdrawBillStatusUpdateEvent.class)
    public void onWithdrawBillStatusUpdateEvent(WithdrawBillStatusUpdateEvent event){

        try {
            IaaWithdrawBillRecordDTO bill = billQueryService.queryById(
                    event.getWithdrawBillId());

            eventProducer.publishBillEvent(new IaaWithdrawBillEventMsg().setData(bill));
        }
        catch (Throwable t){
            log.error("Fail to broadcast withdraw bill status update event, event={}", event.getWithdrawBillId(), t);
        }
    }

    public void publishBills(List<Integer> billIds) {
        billIds.forEach(id -> {
            IaaWithdrawBillRecordDTO bill = billQueryService.queryById(Long.valueOf(id));

            eventProducer.publishBillEvent(new IaaWithdrawBillEventMsg().setData(bill));
        });
    }

    /**
     * 根据月份和状态推送账单消息
     *
     * @param month  月份，格式为YYYYMM，例如202501表示2025年1月
     * @param status 账单状态
     * @return 成功推送的账单数量
     */
    public int publishBillsByMonthAndStatus(String month, IaaWithdrawBillStatus status) {
        log.info("Start publishing bills by month={} and status={}", month, status);

        try {
            // 构建withdrawDate前缀，例如"202501_"
            String withdrawDatePrefix = month + "_";

            // 查询符合条件的账单
            IaaWithdrawBillPoExample example = new IaaWithdrawBillPoExample();
            IaaWithdrawBillPoExample.Criteria criteria = example.createCriteria();
            criteria.andBillStatusEqualTo(status.name());
            criteria.andWithdrawDateLike(withdrawDatePrefix + "%");

            List<IaaWithdrawBillPo> bills = iaaWithdrawBillMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(bills)) {
                log.info("No bills found for month={} and status={}", month, status);
                return 0;
            }

            log.info("Found {} bills for month={} and status={}", bills.size(), month, status);

            // 获取账单ID列表
            List<Long> billIds = bills.stream()
                    .map(IaaWithdrawBillPo::getId)
                    .collect(Collectors.toList());

            // 批量查询完整账单信息
            List<IaaWithdrawBillRecordDTO> billRecords = billQueryService.batchQueryById(billIds);

            // 推送消息
            int successCount = 0;
            for (IaaWithdrawBillRecordDTO bill : billRecords) {
                try {
                    eventProducer.publishBillEvent(new IaaWithdrawBillEventMsg().setData(bill));
                    successCount++;
                } catch (Exception e) {
                    log.error("Failed to publish bill event for billId={}", bill.getBillId(), e);
                }
            }

            log.info("Successfully published {}/{} bills for month={} and status={}",
                    successCount, billRecords.size(), month, status);

            return successCount;
        } catch (Exception e) {
            log.error("Error publishing bills by month={} and status={}", month, status, e);
            return 0;
        }
    }
}
