package com.bilibili.miniapp.open.service.biz.settlement.event;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 *
 * 提现账单状态变更实现，目前不发送init创建的事件；
 *
 * <AUTHOR>
 * @desc
 * @date 2025/3/24
 */
@Getter
@Setter
@Accessors(chain = true)
public class WithdrawBillStatusUpdateEvent extends ApplicationEvent {


    private Long withdrawBillId;


    public WithdrawBillStatusUpdateEvent(Object source) {
        super(source);
    }
}
