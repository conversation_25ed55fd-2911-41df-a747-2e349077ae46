package com.bilibili.miniapp.open.service.biz.settlement.exception;

import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlementStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/18
 */

@Getter
@Setter
@Accessors(chain = true)
public class SettlementRetryableException extends IllegalArgumentException{


    private IaaSettlementStatus settlementStatus;


    public SettlementRetryableException(String s) {
        super(s);
    }

    public SettlementRetryableException(String message, Throwable cause) {
        super(message, cause);
    }
}
