package com.bilibili.miniapp.open.service.biz.settlement.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Getter
@Setter
@Accessors(chain = true)
public class WithdrawRetryableException extends IllegalArgumentException{


    private String expenseErrorCode;

    private String expenseMessage;



    public WithdrawRetryableException(String s) {
        super(s);
    }

    public WithdrawRetryableException(String message, Throwable cause) {
        super(message, cause);
    }
}
