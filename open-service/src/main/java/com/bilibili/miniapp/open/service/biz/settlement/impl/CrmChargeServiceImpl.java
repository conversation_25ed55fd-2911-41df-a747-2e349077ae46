package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.service.biz.settlement.CrmChargeService;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/18
 */
@Slf4j
@Service
public class CrmChargeServiceImpl implements CrmChargeService {

    @Override
    public void charge(Long accountId, BigDecimal amount, String reason) {

        log.info("Success to charge accountId={}, amount={}, reason={}", accountId, amount, reason);
        throw new IllegalArgumentException("CRM广告金充值服务未就绪");

    }
}
