package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.repository.mysql.settlement.IaaCrmChargeBillRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaSettlementRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaWithdrawBillRepository;
import com.bilibili.miniapp.open.service.biz.settlement.IaaBillQueryService;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaCrmChargeBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlement;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.SettlementModelConvertor;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillRecordDTO;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IaaBillQueryServiceImpl implements IaaBillQueryService {


    private final IaaSettlementRepository iaaSettlementRepository;

    private final IaaWithdrawBillRepository iaaWithdrawBillRepository;

    private final IaaCrmChargeBillRepository iaaCrmChargeBillRepository;



    @Override
    public IaaWithdrawBillRecordDTO queryById(Long billId) {

        IaaWithdrawBill bill = iaaWithdrawBillRepository.selectByPrimaryKey(
                        billId)
                .map(SettlementModelConvertor.convertor::fromPo)
                .orElseThrow(() -> new IllegalArgumentException("找不到对应的提现账单"));

        List<IaaSettlement> withdrawBills = iaaSettlementRepository.selectByWithdrawBillIdEq(billId)
                .stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());

        List<IaaCrmChargeBill> crmChargeBills = iaaCrmChargeBillRepository.selectByWithdrawBillIdEq(billId)
                .stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());

        return IaaWithdrawBillRecordDTO.toDTO(billId, bill, withdrawBills, crmChargeBills);


    }

    @Override
    public List<IaaWithdrawBillRecordDTO> batchQueryById(List<Long> billIds) {

        if(billIds.isEmpty()){
            return List.of();
        }

        Map<Long, IaaWithdrawBill> bills = iaaWithdrawBillRepository.selectAllByIdIn(billIds)
                .stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toMap(
                        IaaWithdrawBill::getId, b -> b,
                        (a, b) -> b
                ));

        Map<Long, List<IaaSettlement>> withdrawBills = iaaSettlementRepository.selectByWithdrawBillIdIn(billIds)
                .stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.groupingBy(
                        IaaSettlement::getWithdrawBillId,
                        Collectors.toList()
                ));

        Map<Long, List<IaaCrmChargeBill>> crmChargeBills = iaaCrmChargeBillRepository.selectByWithdrawBillIdIn(billIds)
                .stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.groupingBy(
                        IaaCrmChargeBill::getWithdrawBillId,
                        Collectors.toList()
                ));

        return bills.entrySet().stream()
                .map(entry -> IaaWithdrawBillRecordDTO.toDTO(entry.getKey(), entry.getValue(),
                        withdrawBills.getOrDefault(entry.getKey(), List.of()),
                        crmChargeBills.getOrDefault(entry.getKey(), List.of())))
                .collect(Collectors.toList());



    }
}
