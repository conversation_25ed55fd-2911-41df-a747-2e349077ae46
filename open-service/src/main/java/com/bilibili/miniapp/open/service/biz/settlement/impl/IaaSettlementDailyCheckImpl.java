package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.common.util.IteratorHelper;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaCrmChargeBillRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaSettlementRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.IaaWithdrawBillRepository;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo;
import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementDailyCheck;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaCrmChargeBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaCrmChargeBillStatus;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaDailyIncomeEvent;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlement;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlementStatus;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBillStatus;
import com.bilibili.miniapp.open.service.biz.settlement.model.SettleRule;
import com.bilibili.miniapp.open.service.biz.settlement.model.SettlementModelConvertor;
import com.bilibili.miniapp.open.service.biz.settlement.model.WithdrawDate;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BindingBusinessAccount;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BusinessEntityInfoMiniGameImpl;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IaaSettlementDailyCheckImpl implements IaaSettlementDailyCheck {

    private final IaaSettlementRepository iaaSettlementRepository;

    private final IaaCrmChargeBillRepository iaaCrmChargeBillRepository;

    private final IaaWithdrawBillRepository iaaWithdrawBillRepository;

    private final IaaSettlementServiceImpl iaaSettlementService;

    private final IaaSettlementConfig iaaSettlementConfig;

    private final BusinessEntityInfoMiniGameImpl miniGameBusinessInfoProvider;

    private final SettleRule settleRule;




    @Override
    public void onDailyRecoverCheck4InvalidSettlements() {

        // scan all invalid settlements and retry
        log.info("Start to re-settle for uncompleted settlements, processing...");

        Iterator<List<IaaSettlement>> uncompletedSettlementIterator = IteratorHelper.buildIterator(
                (id, limit) -> {
                    return Optional.ofNullable(iaaSettlementRepository.selectAllByIdGtAndLimitAndStatusIn(
                                    IaaSettlementStatus.fetchStatusCanReSettle()
                                            .stream()
                                            .map(IaaSettlementStatus::name)
                                            .collect(Collectors.toList()),
                                    id, limit
                            )).orElse(new ArrayList<>())
                            .stream().map(SettlementModelConvertor.convertor::fromPo)
                            .collect(Collectors.toList());
                }, IaaSettlement::getId,

                iaaSettlementConfig.getReSettleBatchSize());

        AtomicInteger resettleCnt=  new AtomicInteger(0);

        AtomicInteger needResettleCnt = new AtomicInteger(0);

        while (uncompletedSettlementIterator.hasNext()) {

            List<IaaSettlement> batch = uncompletedSettlementIterator.next();

            needResettleCnt.addAndGet(batch.size());

//            Map<Long, IaaWithdrawBill> bills = iaaWithdrawBillRepository.selectAllByIdIn(
//                    batch.stream().map(iaaSettlement -> iaaSettlement.getWithdrawBillId())
//                            .distinct().filter(Objects::nonNull).collect(Collectors.toList())
//            ).stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toMap(
//                    IaaWithdrawBill::getId, bill -> bill
//            ));


            batch.forEach(uncompletedSettlement->{

                IaaDailyIncomeEvent dailyEvent = uncompletedSettlement.recoverDailyEventFromUncompletedSettlement();

//                // 尽量使用原有的账期
//                Optional.ofNullable(bills.get(uncompletedSettlement.getWithdrawBillId())).ifPresent(bill -> {
//                    if (bill.getWithdrawDate() != null) {
//                        dailyEvent.setWithdrawDate(bill.getWithdrawDate());
//                    }
//                });
                try {
                    iaaSettlementService.onDailySettlement(dailyEvent);
                } catch (Exception e) {
                    log.error("Fail to re-settle for uncompleted settlement, data={}", uncompletedSettlement, e);
                    return;
                }

                log.info("Complete re-settle for uncompleted settlement, data={}", uncompletedSettlement);

            });
        }

        log.info("Complete all re-settle for uncompleted settlements, resettle cnt={}, need resettle cnt={}",
                resettleCnt.get(), needResettleCnt.get());

    }


    @Override
    public void onDailyRecoverCheck4InvalidCrmChargeBills() {

        // 有charge记录的，必然是settle完成的， 仅需补偿charge任务即可

        log.info("Start to re-charge for uncompleted crm charge bills, processing...");

        Iterator<List<IaaCrmChargeBill>> billIterator = IteratorHelper
                .buildIterator(
                        (id, limit) -> iaaCrmChargeBillRepository.selectAllByStatusInAndIdGtAndLimit(
                                        IaaCrmChargeBillStatus.fetchAllReChargeStatus(),

                                        id, limit).stream().map(SettlementModelConvertor.convertor::fromPo)
                                .collect(Collectors.toList()),
                        IaaCrmChargeBill::getId,
                        iaaSettlementConfig.getReSettleBatchSize()

                );

        long startTs = System.currentTimeMillis();

        while (billIterator.hasNext()) {

            List<IaaCrmChargeBill> batch = billIterator.next();

            batch.forEach(bill -> {

                Try.run(() -> {
                    BigDecimal chargeAmt = bill.getSettleRuleSnapshotFromDeserializeExtra()
                            .orElse(settleRule.snapshot())
                            .calculateBusinessCrmCharge(bill.getChargeAmt());

                    BindingBusinessAccount account = miniGameBusinessInfoProvider.queryBindingBusinessAccountByAppId(
                            bill.getAppId()).orElseThrow(() -> {
                        log.error("Fail to query business account by appId={}", bill.getAppId());
                        return new RuntimeException("Fail to query business account by appId");
                    });

                    iaaSettlementService.chargeCrmBill(chargeAmt,
                            bill.getLogdate(),
                            Optional.of(account.getAccountId()),
                            bill
                    );
                    log.info("Complete re-charge for uncompleted crm charge bill, data={}", bill);
                }).onFailure(t -> {

                    log.error("Fail to re-charge crm bill for uncompleted crm charge bill, data={}", bill, t);

                });

            });
        }

        log.info("Complete all recover for uncompleted crm charge bills, cost={} mills",
                System.currentTimeMillis() - startTs);


    }


    /**

     com.bilibili.miniapp.open.service.biz.settlement.impl.IaaSettlementDailyCheckImpl#onDailyRecoverCheck4InvalidWithdrawBills()

     */
    @Override
    public void onDailyRecoverCheck4InvalidWithdrawBills() {


        log.info("Start to re-settle for uncompleted withdraw bills, processing...");

        WithdrawDate withdrawDate = WithdrawDate.generateNextNearestWithdrawDateByDateTime(
                LocalDateTime.now().minusDays(1));

        Iterator<List<IaaWithdrawBillPo>> billIterator = IteratorHelper.buildIterator(
                (id, limit) -> iaaWithdrawBillRepository.selectAllByIdGtAndLimitAndStatusInAndWithdrawDateLte(
                        id, limit, Lists.newArrayList(IaaWithdrawBillStatus.turn_withdrawable_failed.name()),
                        withdrawDate.getWithdrawDate()
                ),
                IaaWithdrawBillPo::getId,
                iaaSettlementConfig.getReSettleBatchSize()
        );


        long startTs= System.currentTimeMillis();
        while (billIterator.hasNext()) {

            List<IaaWithdrawBill> batch = billIterator.next()
                    .stream().map(SettlementModelConvertor.convertor::fromPo).collect(Collectors.toList());

            batch.forEach(bill -> {

                Try.run(() -> {

                    iaaSettlementService.turnWithdrawable(bill);
                    log.info("Complete turn withdrawable for uncompleted withdraw-bill, data={}", bill);
                }).onFailure(t -> {

                    log.error("Fail to turn withdrawable for uncompleted withdraw-bill, data={}", bill, t);

                });

            });
        }

        log.info("Complete all recover for uncompleted withdraw bills, cost={} mills",
                System.currentTimeMillis() - startTs);

    }





    @Override
    public void onDailyCheckBillAmt() {

        log.info("Start to scan and check withdraw bill amt by settlement detail, processing...");



    }



}
