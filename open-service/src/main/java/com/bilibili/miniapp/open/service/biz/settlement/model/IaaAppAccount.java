package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
public class IaaAppAccount implements SnakeCaseBody {

    /**
     * 账户自增id
     */
    private Long id;

//    /**
//     * 流量类型 0 自然 1 商业流量
//     *
//     * {@link IaaTrafficType#getCode()}
//     */
//    private IaaTrafficType trafficType;

    /**
     * 账户类型 mini_game/mini_app
     */
    private IaaAppType appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 累计收入,分
     */
    private BigDecimal incomeAmt;

    /**
     * 累计广告收入,分
     */
    private BigDecimal incomeBusinessPartAmt;

    /**
     * 累计自然收入,分
     */
    private BigDecimal incomeNaturalPartAmt;

    /**
     * 累计提现,分，不一定到账，只要预提就发生
     */
    private BigDecimal withdrawAmt;

    /**
     * 累计提现,分，不一定到账，只要预提就发生
     */
    private BigDecimal withdrawBusinessPartAmt;

    /**
     * 累计提现,分，不一定到账，只要预提就发生
     */
    private BigDecimal withdrawNaturalPartAmt;

    /**
     * 累计CRM扣款,分
     */
    private BigDecimal crmChargeAmt;

    private Integer settleTimes;

    /**
     * 最近一次结算时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestSettleTime;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ctime;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mtime;

    private Boolean deleted;

    private String extra;





}



