package com.bilibili.miniapp.open.service.biz.settlement.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * iaa收入来源
 *
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Getter
@Deprecated(since = "仅在小游戏结算中会使用到")
@RequiredArgsConstructor
public enum IaaAppType {

    unknown(0, "未知"),

    mini_app(1, "小程序"),

    mini_game(2, "小游戏")



    ;


    private final int code;

    private final String desc;


    public static IaaAppType of(Integer code) {
        for (IaaAppType value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return unknown;
    }

    public static IaaAppType fromName(String name) {
        for (IaaAppType value : values()) {
            if (value.name().equalsIgnoreCase(name)) {
                return value;
            }
        }
        return unknown;
    }

}
