package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */

@Data
@Accessors(chain = true)
public class IaaCrmChargeBill implements SnakeCaseBody {

    /**
     * 提现单自增id
     */
    private Long id;

    /**
     * 账户类型 mini_game/mini_app
     */
    private IaaAppType appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 流量类型 0 自然 1 商业流量
     */
    private IaaTrafficType trafficType;


    private String logdate;
    /**
     * CRM充值金额,分
     */
    private BigDecimal chargeAmt;

    /**
     * 收入金额，分
     */
    private BigDecimal incomeAmt;


    /**
     * 关联结算单号
     */
    private Long settlementId;

    /**
     * 归属于哪个结算单
     */
    private Long withdrawBillId;

    /**
     * 充值日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date chargeTime;


    /**
     * 充值状态，理论上插入就应该只有成功
     */
    private IaaCrmChargeBillStatus billStatus;


    /**
     * 充值失败原因
     */
    private String reason;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ctime;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mtime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    private String extra;


    public Optional<SettleRule> getSettleRuleSnapshotFromDeserializeExtra() {

        return Optional.ofNullable(
                JsonUtil.readValue(extra, CrmChargeBillExtra.class)).map(CrmChargeBillExtra::getRule);
    }


    public boolean isChargeSuccess(){

        return billStatus == IaaCrmChargeBillStatus.success;
    }


    @Data
    @Accessors(chain = true)
    public static class CrmChargeBillExtra implements SnakeCaseBody{

        private SettleRule rule;

    }
}