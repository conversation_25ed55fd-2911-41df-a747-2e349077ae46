package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@Getter
@RequiredArgsConstructor
public enum IaaCrmChargeBillStatus {

    // 实际不存在
    init(0, "初始化"),

    success(1, "成功"),

    failed(-1, "失败"),




    ;


    private final int code;


    private final String desc;


    public static List<String> fetchAllReChargeStatus(){

        return Lists.newArrayList(init, failed)
                .stream()
                .map(IaaCrmChargeBillStatus::name)
                .collect(Collectors.toList());

    }
}
