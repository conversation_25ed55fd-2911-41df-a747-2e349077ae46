package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.google.common.collect.Lists;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * {"trafficType": "natural", "appId": "123", "appType": "mini_game", "incomeAmt": 100.00, "logdate": "20220317"}
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
public class IaaDailyIncomeEvent implements SnakeCaseBody {

    private static DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");

//
//    /**
//     * 收入来源 mini_game/mini_app
//     */
//    private String source;


    /**
     * 流量类型 0 自然 1 商业流量
     */
    private IaaTrafficType trafficType;

    /**
     * 账户唯一键
     */
    private String appId;

    private IaaAppType appType;

    /**
     * 收入,分
     */
    private BigDecimal incomeAmt;

    /**
     * 日志日期, 流水发生日期
     */
    private String logdate;


    /**
     * 归属的账单日期，一般为空。如果为空，表示是选择结算当时的账单日.
     *
     * 另外对于村来那个数据的结算，需要使用这个字段。因为存量的数据可能要分在每个半月里，然后可能部分是已经线下结算了的，如果一张单子会难以区分
     */
    @Nullable
    private String withdrawDate;


    private boolean generateWithdrawDateByLogdate = true;




    public void validate(){





    }


    /**
     * 范围当月的第一天到今天的logdate的范围 如果当天就是第一天，那么直接返回empty
     *
     *

     * 比如1-17 在结算的时候，及时1-15。1-16 当时还没有结算。 1-17的累计流水的时候。 也是要考虑1-15 1-16的。
     * @return
     */
    public Optional<Tuple2<String, String>> logdateRangeBeforeThisDayOfCurrentMonth() {

        LocalDate today = LocalDate.parse(logdate, df);
        LocalDate firstDay = today.withDayOfMonth(1);
        if (today.equals(firstDay)) {
            return Optional.empty();
        }
        return Optional.of(Tuple.of(firstDay.format(df), today.minusDays(1).format(df)));
    }


    public List<WithdrawDate> getCandidateWithdrawDateInOrder() {

        List<WithdrawDate> result = new ArrayList<>();

        if (withdrawDate != null) {

            Optional.ofNullable(Try.of(() -> new WithdrawDate(withdrawDate)).getOrNull()).ifPresent(result::add);

        }

        WithdrawDate logdateWithdrawDate = Try.of(() -> {
            return WithdrawDate.generateNextNearestWithdrawDateByDateTime(
                    LocalDate.parse(logdate, df).atStartOfDay());
        }).get();

        // 结算日期在最新的产品口径中不会被使用到了
//        WithdrawDate currentTimeWithdrawDate = WithdrawDate.generateNextNearestWithdrawDateByDateTime(
//                LocalDateTime.now().minusDays(1));

        result.add(logdateWithdrawDate);
        WithdrawDate next = logdateWithdrawDate.generateNext();
        result.add(next);
        // 理论上不会用到了
        result.add(next.generateNext());

        return result;

    }
}