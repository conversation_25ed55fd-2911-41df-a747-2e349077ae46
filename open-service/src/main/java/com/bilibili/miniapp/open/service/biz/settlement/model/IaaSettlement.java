package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 *
 * 该表同时有清分和结算的语义。
 * 清分： 交易处理中用于“分类、匹配、核对”的中间数据，决定资金如何分配。
 * 结算：资金或证券实际划转的“执行指令”与结果记录，完成最终交割。
 * 转账单数据： 用户视角的资金流转记录，包含交易双方、金额、时间等明细信息。
 *
 *
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
public class IaaSettlement implements SnakeCaseBody {

    /**
     * 结算自增id
     */
    private Long id;

    /**
     * 结算的唯一键保证事务性 UniqueKey:app_id,aggre_logdate,flow_type
     */
    private String settleKey;

    /**
     * 聚合日期 2025-01-01
     */
    private String logdate;

    /**
     * 流量类型 0 自然 1 商业流量
     */
    private IaaTrafficType trafficType;

    /**
     * 账户类型 mini_game/mini_app
     */
    private IaaAppType appType;

    /**
     * 账户唯一键
     */
    private String appId;

    /**
     * 结算时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date settleTime;

    /**
     * 是否已处理 0 未处理 1 已处理
     */
    private IaaSettlementStatus settleStatus;

    private String settleReason;


    private Long withdrawBillId;



    /**
     * 收入,分
     */
    private BigDecimal incomeAmt;


    /**
     * 提现金额,分
     */
    private BigDecimal withdrawAmt;

    /**
     * CRM充值金额,分
     */
    private BigDecimal crmChargeAmt;



    /**
     * 结算额外信息， json格式，例如结算规则等
     */
    private String extra;



    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ctime;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mtime;

    /**
     * 是否删除, 预留
     */
    private Boolean deleted;



    @Data
    public static class SettleExtra implements SnakeCaseBody{


        private SettleRule rule;


    }


    public IaaDailyIncomeEvent recoverDailyEventFromUncompletedSettlement(){

        return SettlementModelConvertor.convertor.dailySettlement2DailyEvent(this);
    }
}