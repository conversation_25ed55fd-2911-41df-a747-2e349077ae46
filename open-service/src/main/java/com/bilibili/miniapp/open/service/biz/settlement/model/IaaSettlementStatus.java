package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.google.common.collect.Lists;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Getter
@RequiredArgsConstructor
public enum IaaSettlementStatus {


    init(0, "初始化"),

    /**
     * 处理中, 理论上这个状态不可见（RR下），只是事务中的中间状态
     */
    settling(1, "处理中"),



    settled(2, "已结算"),


//    settled_withdraw(3, "已结算且，已提现"),


    /**
     * 允许合同补齐后进行补偿结算,需要注意补偿事件的触发
      */
    failed(-1, "失败"),



    ;

    private final int code;

    private final String desc;


    /**
     * 当前实际只有无效合同可以重新结算
     * @return
     */
    public static List<IaaSettlementStatus> fetchStatusCanReSettle(){

        return Lists.newArrayList(failed);
    }



}
