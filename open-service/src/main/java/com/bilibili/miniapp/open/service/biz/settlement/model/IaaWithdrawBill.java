package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
public class IaaWithdrawBill implements SnakeCaseBody {

    /**
     * 提现单自增id
     */
    private Long id;

    /**
     * 账单抬头
     */
    private String title;

    /**
     * 账单状态:
     * 初始化
     * 预提现（待补充发票并确认体现）
     */
    private IaaWithdrawBillStatus billStatus;

    private String failReason;

    /**
     * 账户类型 mini_game/mini_app
     */
    private IaaAppType appType;


    /**
     * 账户唯一键
     */
    private String appId;


    /**
     * 账单开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billStartTime;

    /**
     * 账单结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billEndTime;


    /**
     * 提现账单日 格式202501_1 2025年一月上半月
     */
    private String withdrawDate;

    /**
     * 最近一次结算时间
     */
    private Date latestSettleTime;

    /**
     * 用户发起提现的时间
     */
    private Date withdrawApplyTime;

    /**
     * 提现到账时间
     */
    private Date withdrawArrivalTime;


    /**
     * 当前账单周期内的累计金额
     */
    private BigDecimal incomeAmt;




    private Integer settleTimes;



    /**
     * 当前账单周期内的累计金额,
     * Q：使用bill中每天累计的金额进行提现还是使用每15天重新累计sum值？
     * A：使用体现bill中的累计值，因为15天累积无法考虑做到每天清分，无法处理当天的提成协议、状态等等的变更。
     *
     * 那是否需要15天的合计值？ 可以保留，多一道对账工序。如果和累计值有误差也可以及时进行告警。
     */
    private BigDecimal withdrawAmt;

    private BigDecimal crmChargeAmt;


//    private List<IaaWithdrawBillPartDetail> partDetails;

    /**
     * 当前账单周期内的自然收入部分
     */
    private BigDecimal incomeNaturalPartAmt;

    /**
     * 当前账单周期内的商业收入部分
     */
    private BigDecimal incomeBusinessPartAmt;

    private BigDecimal withdrawNaturalPartAmt;

    private BigDecimal withdrawBusinessPartAmt;


    /**
     * 业务实体名称
     */
    private String businessEntityName;

    /**
     * 提现请求金额，注意因为允许批量合并提现，所以体现请求金额可能大于该bill的金额，是多个bill的汇总
     */
    private BigDecimal withdrawApplyAmt;

    /**
     * 发票图片地址
     */
    private String invoiceImgUrl;


    /**
     * 汇联易预提单id
     */
    private String accrualId;

    /**
     * 汇联易预提单附加信息
     */
    private String accrualExtra;

    /**
     * 汇联易付款单id
     */
    private String expenseId;

    /**
     * 汇联易付款单附加信息,员工号等
     */
    private String expenseExtra;

    private String expenseCode;

    private String expenseMessage;

    /**
     * 预留字段，额外信息 例如发生失败需要重试等，
     * 此处可以保留一些快照等信息。
     */
    private String extra;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ctime;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date mtime;


    private Boolean deleted;




    @Data
    @Accessors(chain = true)
    public static class AccrualExtra{

        private AccrualCreateMandatoryParams params;

        private String accrualCode;

        private String accrualMessage;

    }


    @Data
    @Accessors(chain = true)
    public static class ExpenseExtra{


        private ExpenseCreateMandatoryParams params;


        private String expenseCode;

        private String expenseMessage;



        public String serialize(){
            return JsonUtil.writeValueAsString(this);
        }

        public static ExpenseExtra deserialize(String json){
            return Optional.ofNullable(JsonUtil.readValue(json, ExpenseExtra.class))
                    .orElse(new ExpenseExtra());
        }

    }



    @Data
    @Accessors(chain = true)
    public static class BillExtra{


        private String taxRate;

        private BigDecimal taxAmt;

        private BigDecimal withdrawAmtAfterTax;

    }







}