package com.bilibili.miniapp.open.service.biz.settlement.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Getter
@RequiredArgsConstructor
public enum IaaWithdrawBillStatus {


    /**
     * 未提现
     */
    init(1, "未提现"),



    turn_withdrawable_failed(-3, "转为可提现失败"),



    withdrawable(2, "可提现"),



    /**
     * 提现中
     */
    withdrawing(3, "提现中"),



    /**
     * 提现成功
     *
     *
     */
    success(4, "提现成功"),



    error(-1, "提现失败"),



    failed(-2, "可重试失败"),


    ;


    private final int code;

    private final String desc;



    public boolean canWithdraw(){

        return this == withdrawable || this == failed;

    }

    public boolean skipWithdraw(){

        return this == withdrawing || this == success;
    }

    public boolean forbidWithdraw(){

        return this == init || this == error;
    }

    public boolean canTurnWithdrawable(){

        return this == init || this == turn_withdrawable_failed;

    }


}
