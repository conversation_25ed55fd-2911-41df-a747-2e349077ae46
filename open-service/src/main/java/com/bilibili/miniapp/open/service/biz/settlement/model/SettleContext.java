package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.service.biz.settlement.spi.BindingBusinessAccount;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BusinessEntityInfo;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/20
 */
@Data
@Accessors(chain = true)
public class SettleContext {

    private IaaDailyIncomeEvent dailySum;

    private IaaAppAccount appAccount;

    private IaaSettlement settlement;

    /**
     * 需要是状态为init的账单。
     */
    private IaaWithdrawBill nearestSettlingWithdrawBill;

//    private IaaWithdrawBill pairBillOfThisMonth;

    private Optional<BusinessEntityInfo> businessEntity;

    private Optional<BindingBusinessAccount> bindingBusinessAccount;


    private Optional<IaaCrmChargeBill> chargeBill;

    private SettleRule settleRule;



}
