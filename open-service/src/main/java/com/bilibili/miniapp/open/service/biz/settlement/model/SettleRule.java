package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.vavr.Lazy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import pleiades.venus.config.WatchedProperties;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
@WatchedProperties
@Configuration
@ConfigurationProperties(prefix = "iaa.settlement.rule")
public class SettleRule {


    /**
     * 商业流量提现比例， 万分比, 90%
     */
    @JsonProperty("b_pct")
    private String businessCashPercent = "90";

    /**
     * 商业流量crm充值比例， 万分比, 12%
     */
    @JsonProperty("b_crm_pct")
    private String businessCrmChargePercent = "12";

    /**
     * 自然流量阈值以下提现比例， 万分比, 60%
     */
    @JsonProperty("n_below_pct")
    private String naturalCashPercentBelowThreshold = "60";

    /**
     * 自然流量阈值以上提现比例， 万分比, 50% ,注意和阈值以下部分是梯度比例。这个万分比只作用于超过阈值以上的部分。然后总计就是求和。
     */
    @JsonProperty("n_above_pct")
    private String naturalCashPercentAboveThreshold = "50";


    /**
     * 月流水阈值 50W元
     */
    @JsonProperty("n_threshold")
    private Integer monthIncomeThreshold = 500000;


    public SettleRule snapshot(){

        return new SettleRule()
                .setBusinessCashPercent(businessCashPercent)
                .setBusinessCrmChargePercent(businessCrmChargePercent)
                .setNaturalCashPercentBelowThreshold(naturalCashPercentBelowThreshold)
                .setNaturalCashPercentAboveThreshold(naturalCashPercentAboveThreshold)
                .setMonthIncomeThreshold(monthIncomeThreshold)
                ;

    }



    /**
     * 计算商业流量提现金额
     * @param income
     * @return
     */
    public BigDecimal calculateBusinessWithdraw(BigDecimal income, String logdate) {
        // 2025年4月1日（不包括）之前的数据分成规则为100%
        // logdate格式 yyyyMMdd
        if (logdate.compareTo("20250401") < 0) {
            businessCashPercent = "100";
        }

        return income.multiply(new BigDecimal(businessCashPercent))
                .divide(new BigDecimal(100) , 4, RoundingMode.HALF_UP);

    }


    public BigDecimal calculateBusinessCrmCharge(BigDecimal income) {

        return income.multiply(new BigDecimal(businessCrmChargePercent))
                .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)
                ;
    }

    /**
     * 计算自然流量提现金额,
     * currentTotal是当前积累的不做计算，分成计算income，低于阈值部分以naturalCashPermyriadBelowThreshold计算，
     * 高于阈值部分以naturalCashPermyriadAboveThreshold计算，
     * 如果两步都有，最后结果是两部分的和
     *
     * @param income 当前这笔自然流量收入
     * @param currentTotal 目前累积的自然流量收入
     * @return
     */
    public BigDecimal calculateNaturalWithdraw(BigDecimal income, BigDecimal currentTotal) {

        BigDecimal nextTotal = currentTotal.add(income);

        // 统一按照分计算
        BigDecimal threshold = new BigDecimal(monthIncomeThreshold).multiply(new BigDecimal("100"));



        BigDecimal belowPart;
        BigDecimal abovePart;


        if (threshold.compareTo(currentTotal) <= 0) {
            belowPart = BigDecimal.ZERO;
            abovePart = income;
        }
        else {
            if (threshold.compareTo(nextTotal) <= 0) {
                belowPart = threshold.subtract(currentTotal);
                abovePart = nextTotal.subtract(threshold);
            }
            else {
                belowPart = income;
                abovePart = BigDecimal.ZERO;
            }
        }

        return belowPart.multiply(new BigDecimal(naturalCashPercentBelowThreshold))
                .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP)
                .add(abovePart.multiply(new BigDecimal(naturalCashPercentAboveThreshold))
                        .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP));

    }



    public DailyIncreasingBenefit calculateDailyIncreasingBenefit(IaaDailyIncomeEvent daily,
            Lazy<BigDecimal> naturalTrafficIncomeThisMonth
    ){


        BigDecimal withdrawBusinessPart = BigDecimal.ZERO;
        BigDecimal withdrawNaturalPart = BigDecimal.ZERO;
        BigDecimal crmCharge = BigDecimal.ZERO;
        BigDecimal incomeBusinessPart = BigDecimal.ZERO;
        BigDecimal incomeNaturalPart = BigDecimal.ZERO;
        switch (daily.getTrafficType()){

            case business:{

                incomeBusinessPart = daily.getIncomeAmt();
                withdrawBusinessPart = calculateBusinessWithdraw(daily.getIncomeAmt(), daily.getLogdate());
                crmCharge = calculateBusinessCrmCharge(daily.getIncomeAmt());

                break;

            }
            case natural:{
                incomeNaturalPart = daily.getIncomeAmt();
                withdrawNaturalPart = calculateNaturalWithdraw(
                        daily.getIncomeAmt(),
                        naturalTrafficIncomeThisMonth.get()
                );

                break;

            }
            case unknown:
            default:{
                throw new IllegalArgumentException("unknown traffic type");
            }

        }

        return new DailyIncreasingBenefit()
                .setIncome(daily.getIncomeAmt())
                .setIncomeNaturalPart(incomeNaturalPart)
                .setIncomeBusinessPart(incomeBusinessPart)
                .setWithdraw(withdrawBusinessPart.add(withdrawNaturalPart))
                .setCrmCharge(crmCharge)
                .setWithdrawBusinessPart(withdrawBusinessPart)
                .setWithdrawNaturalPart(withdrawNaturalPart)
                ;
    }



    @Data
    @Accessors(chain = true)
    public static class DailyIncreasingBenefit {

        /**
         * 收入,分
         */
        private BigDecimal income;

        private BigDecimal incomeBusinessPart;

        private BigDecimal incomeNaturalPart;

        /**
         * 提现
         */
        private BigDecimal withdraw;

        /**
         * CRM扣款
         */
        private BigDecimal crmCharge;

        /**
         * 商业流量提现
         */
        private BigDecimal withdrawBusinessPart;

        /**
         * 自然流量提现
         */
        private BigDecimal withdrawNaturalPart;



    }
}
