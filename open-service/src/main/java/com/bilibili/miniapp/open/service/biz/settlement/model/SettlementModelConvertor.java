package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaAppAccountPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaCrmChargeBillPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.IaaWithdrawBillPo;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaCrmChargeBillDTO;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaSettlementDTO;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Mapper
public interface SettlementModelConvertor {

    SettlementModelConvertor convertor = Mappers.getMapper(SettlementModelConvertor.class);


    @Mapping(target = "deleted", expression = "java(byte2Boolean(po.getDeleted()))")
    IaaSettlement fromPo(IaaSettlementPo po);


    @Mapping(target = "deleted", expression = "java(byte2Boolean(po.getDeleted()))")
    IaaAppAccount fromPo(IaaAppAccountPo po);

    @Mapping(target = "deleted", expression = "java(byte2Boolean(po.getDeleted()))")

    IaaWithdrawBill fromPo(IaaWithdrawBillPo po);

//    IaaWithdrawBillPartDetail fromPo(IaaWithdrawBillPartDetailPo po);
    @Mapping(target = "deleted", expression = "java(byte2Boolean(po.getDeleted()))")
    IaaCrmChargeBill fromPo(IaaCrmChargeBillPo po);


    @Mapping(target = "deleted", expression = "java(boolean2Byte(po.getDeleted()))")
    IaaSettlementPo toPo(IaaSettlement po);

    @Mapping(target = "deleted", expression = "java(boolean2Byte(po.getDeleted()))")
    IaaAppAccountPo toPo(IaaAppAccount po);

    @Mapping(target = "deleted", expression = "java(boolean2Byte(po.getDeleted()))")
    IaaWithdrawBillPo toPo(IaaWithdrawBill po);


    @Mapping(target = "deleted", expression = "java(boolean2Byte(po.getDeleted()))")
    IaaCrmChargeBillPo toPo(IaaCrmChargeBill po);

    IaaDailyIncomeEvent dailySettlement2DailyEvent(IaaSettlement iaaSettlement);


    IaaWithdrawMonthlyEvent4SingleApp bill2MonthlyEvent(IaaWithdrawBill iaaWithdrawBill);


    IaaSettlementDTO toDTO(IaaSettlement iaaSettlement);


    IaaWithdrawBillDTO toDTO(IaaWithdrawBill iaaWithdrawBill);


    IaaCrmChargeBillDTO toDTO(IaaCrmChargeBill iaaCrmChargeBill);




    default Boolean byte2Boolean(Byte b){
        return b != null && b == 1;
    }


    default Byte boolean2Byte(Boolean b){
        return b != null && b ? (byte) 1 : 0;
    }

}
