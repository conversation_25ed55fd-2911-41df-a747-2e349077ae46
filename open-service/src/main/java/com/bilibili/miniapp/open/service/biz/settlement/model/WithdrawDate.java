package com.bilibili.miniapp.open.service.biz.settlement.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.base.Splitter;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@Data
@Accessors(chain = true)
public class WithdrawDate {

    private static DateTimeFormatter yyyyMMDf = DateTimeFormatter.ofPattern("yyyyMM");


    private static DateTimeFormatter logdateDf = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static DateTimeFormatter  df = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");


    /**
     * 202501_1 2025年一月上半月
     * 202501_2 2025年一月下半月
     */
    private String withdrawDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginTime;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;


    /**
     * 是否是上半月
     */
    private Boolean firstHalf;


    private String pairWithdrawDateOfThisMonth;


    public WithdrawDate(String withdrawDate) {
        this.withdrawDate = withdrawDate;

        List<String> split = Splitter.on("_").splitToList(withdrawDate);

        String yyyyMM = split.get(0);
        String half = split.get(1);

        LocalDateTime beginTime = LocalDateTime.parse(yyyyMM + "01 00:00:00", df);

        if ("1".equals(half)) {
            this.beginTime = beginTime;
            this.endTime = beginTime.plusDays(15).minusSeconds(1);
            this.firstHalf = true;
            this.pairWithdrawDateOfThisMonth = yyyyMM + "_2";
        } else {
            this.beginTime = beginTime.plusDays(15);
            this.endTime = beginTime.plusMonths(1).minusSeconds(1);
            this.firstHalf = false;
            this.pairWithdrawDateOfThisMonth = yyyyMM + "_1";
        }

    }



    public WithdrawDate generateNext(){


        if(this.firstHalf) {
            return new WithdrawDate(this.pairWithdrawDateOfThisMonth);
        }

        return generateNextNearestWithdrawDateByDateTime(this.endTime.plusSeconds(1));

    }

    public static WithdrawDate generateWithdrawDateByLogdate(String logdate) {

        return generateNextNearestWithdrawDateByDateTime(
                LocalDate.parse(logdate, logdateDf).atStartOfDay());
    }

    public static  WithdrawDate generateNextNearestWithdrawDateByDateTime(LocalDateTime dateTime) {

        String yyyyMM = dateTime.format(yyyyMMDf);
        int dayOfMonth = dateTime.getDayOfMonth();
        String half = dayOfMonth <= 15 ? "1" : "2";

        return new WithdrawDate(yyyyMM + "_" + half);

    }

    /**
     * 获取当前日内的上一个账期， 4.9 => 202503_2 4.16 => 202504_1
     */
    public static WithdrawDate generatePreviousDateOfCurrentDate(LocalDate today) {


        String yyyyMM = today.format(yyyyMMDf);
        int dayOfMonth = today.getDayOfMonth();
        boolean firstPart = dayOfMonth <= 15;

        if (firstPart) {
            return new WithdrawDate(today.minusMonths(1).format(yyyyMMDf) + "_2");
        }
        else {
            return new WithdrawDate(yyyyMM + "_1");
        }

    }










}
