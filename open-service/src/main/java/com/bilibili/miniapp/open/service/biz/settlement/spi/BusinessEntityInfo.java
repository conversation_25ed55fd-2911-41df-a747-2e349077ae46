package com.bilibili.miniapp.open.service.biz.settlement.spi;

import com.bilibili.miniapp.open.service.biz.settlement.exception.SettlementRetryableException;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlementStatus;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 经营主体信息
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
public class BusinessEntityInfo {

    /**
     * 经营主体名称
     */
    private String businessEntityName;


    private String contractNumber;


    private String bankAccountNumber;

    /**
     * 收款方id
     */
    private String payee;


    /**
     * 经营主体合同开始时间
     */
    private LocalDateTime contractStartTime;

    /**
     * 经营主体合同结束时间
     */
    private LocalDateTime contractEndTime;


    public void validateContractTime(LocalDateTime current) throws SettlementRetryableException {

        if (current.isBefore(contractStartTime)){
            throw new SettlementRetryableException("合同未生效" )
                .setSettlementStatus(IaaSettlementStatus.failed);
        }

        if (current.isAfter(contractEndTime)){
            throw new SettlementRetryableException("合同已过期" )
                .setSettlementStatus(IaaSettlementStatus.failed);
        }

    }

}
