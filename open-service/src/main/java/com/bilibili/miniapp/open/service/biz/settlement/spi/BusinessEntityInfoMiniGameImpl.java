package com.bilibili.miniapp.open.service.biz.settlement.spi;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaAppType;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig;
import com.bilibili.miniapp.open.service.rpc.http.GameOpenPlatformApi;
import com.bilibili.miniapp.open.service.rpc.http.model.GameBindAdAccountInfo;
import com.bilibili.miniapp.open.service.rpc.http.model.GameContractInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import io.vavr.control.Try;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Slf4j
@Service("miniGameBusinessInfoProvider")
public class BusinessEntityInfoMiniGameImpl extends AbstractOpenService
        implements BusinessEntityInfoProvider{

    @Resource
    private GameOpenPlatformApi gameOpenPlatformApi;
    @Resource
    private IaaSettlementConfig iaaSettlementConfig;

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 注意此cache会缓存空
     */
    private LoadingCache<String, Optional<BusinessEntityInfo>> businessEntityInfoCache;
    private LoadingCache<String, Optional<BindingBusinessAccount>> bindingAccountInfoCache;

    @PostConstruct
    public void init(){


        this.businessEntityInfoCache = CacheBuilder.newBuilder()
                .expireAfterWrite(iaaSettlementConfig.getMiniGameBusinessEntityCacheSeconds(), TimeUnit.SECONDS)
                .build(new CacheLoader<String,  Optional<BusinessEntityInfo>>() {
                    @Override
                    public Optional<BusinessEntityInfo> load(String key) throws Exception {
                        return BusinessEntityInfoMiniGameImpl.this.doQueryBusinessEntityInfoByAppId(key);
                    }
                });

        this.bindingAccountInfoCache = CacheBuilder.newBuilder()
                .expireAfterWrite(iaaSettlementConfig.getMiniGameBindingAccountCacheSeconds(), TimeUnit.SECONDS)
                .build(new CacheLoader<String,  Optional<BindingBusinessAccount>>() {
                    @Override
                    public Optional<BindingBusinessAccount> load(String key) throws Exception {
                        return BusinessEntityInfoMiniGameImpl.this.doQueryBindingBusinessAccountByAppId(key);
                    }
                });

    }





    @Override
    public IaaAppType supportedAppType() {
        return IaaAppType.mini_game;
    }

    @Override
    public Optional<BusinessEntityInfo> queryBusinessEntityInfoByAppId(String appId) {
        if (iaaSettlementConfig.getMiniGameBusinessCacheEnabled()) {
            return Try.of(() -> {
                return businessEntityInfoCache.get(appId);

            }).onFailure(t->{
                log.error("Fail to queryBusinessEntityInfoByAppId by cache, return empty, appId={} ", appId, t);
            }).getOrElse(Optional.empty());
        }

        return doQueryBusinessEntityInfoByAppId(appId);
    }


    public Optional<BusinessEntityInfo> doQueryBusinessEntityInfoByAppId(String appId) {

        return Try.of(() -> {

            Response<GameContractInfo> r = this.retry(() -> {
                        Response<GameContractInfo> rsp = this.callWithoutWrap(
                                "getContractInfo",
                                () -> {
                                    Map<String, String> params = new HashMap<>();
                                    params.put("app_id", appId);
                                    params.put("appkey", iaaSettlementConfig.getGameOpenPlatformParams().getAppkey());
                                    params.put("ts", String.valueOf(System.currentTimeMillis()));
                                    params.put("sign", sign(params));
                                    return gameOpenPlatformApi.getContractInfo(params);
                                }
                        );

                        if (rsp == null || rsp.getData() == null) {
                            throw new RuntimeException(
                                    "获取商业主体信息失败: " + Optional.ofNullable(rsp).map(Response::getMessage).orElse(""));
                        }

                        return rsp;
                    },
                    iaaSettlementConfig.getGameOpenPlatformApiRetryIntervalMillis(),
                    iaaSettlementConfig.getGameOpenPlatformApiRetryTimes());

            return Optional.of(
                    new BusinessEntityInfo()
                            .setPayee(r.getData().getSupplierCode())
                            .setContractNumber(r.getData().getEContractId())
                            .setBusinessEntityName(r.getData().getCompanyName())
                            .setBankAccountNumber(r.getData().getBankAccount())
                            .setContractStartTime(r.getData().getStartTimeAsLocalDate().atStartOfDay())
                            .setContractEndTime(r.getData().getEndTimeAsLocalDate().atTime(23, 59, 59))
            );
        }).onFailure(t -> {
            log.error("Fail to queryBusinessEntityInfoByAppId, return empty, appId={} ", appId, t);
        }).getOrElse(Optional.empty());

    }





    @Override
    public Optional<BindingBusinessAccount> queryBindingBusinessAccountByAppId(String appId) {
        if( iaaSettlementConfig.getMiniGameBusinessCacheEnabled()) {
            return Try.of(() -> {
                return bindingAccountInfoCache.get(appId);

            }).onFailure(t->{
                log.error("Fail to queryBindingBusinessAccountByAppId by cache, return empty, appId={} ", appId, t);
            }).getOrElse(Optional.empty());
        }

        return doQueryBindingBusinessAccountByAppId(appId);

    }
    public Optional<BindingBusinessAccount> doQueryBindingBusinessAccountByAppId(String appId) {

        return Try.of(() -> {
            Response<GameBindAdAccountInfo> r = this.retry(() -> {
                        Response<GameBindAdAccountInfo> resp = this.callWithoutWrap(
                                "getBindAdAccountInfo",
                                () -> {
                                    Map<String, String> params = new HashMap<>();
                                    params.put("app_id", appId);
                                    params.put("appkey", iaaSettlementConfig.getGameOpenPlatformParams().getAppkey());
                                    params.put("ts", String.valueOf(System.currentTimeMillis()));
                                    params.put("sign", sign(params));
                                    return gameOpenPlatformApi.getBindAdAccountInfo(
                                            params
                                    );
                                }
                        );

                        if (resp == null || resp.getData() == null) {
                            throw new RuntimeException(
                                    "获取绑定商业账号信息失败: " + Optional.ofNullable(resp).map(Response::getMessage).orElse(""));
                        }
                        return resp;
                    },
                    iaaSettlementConfig.getGameOpenPlatformApiRetryIntervalMillis(),
                    iaaSettlementConfig.getGameOpenPlatformApiRetryTimes()
            );

            Long accountId = Try.of(() -> {
                return Long.parseLong(r.getData().getAdAccountId());
            }).getOrElseThrow(e -> {
                throw new RuntimeException("获取绑定商业账号格式错误: " + r.getData().getAdAccountId());
            });

            return Optional.of(
                    new BindingBusinessAccount()
                            .setAccountId(accountId)
            );

        }).onFailure(t->{
            log.error("Fail to queryBindingBusinessAccountByAppId, return empty, appId={} ", appId, t);
        }).getOrElse(Optional.empty());
        
    }



    private String sign(Map<String, String> paramsTreeMap) {

        AtomicReference<String> signCalc = new AtomicReference<>("");

        paramsTreeMap.entrySet().stream()
                .filter(entry -> !"file".equals(entry.getKey()))
                .sorted(Comparator.comparing(Entry::getKey))
                .forEach(entry -> {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    try {
                        signCalc.set(String.format("%s%s=%s&", signCalc.get(), key,
                                encodedFix(URLEncoder.encode(value, "UTF-8"))));
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                });

        if (!signCalc.get().isEmpty()) {
            signCalc.set(signCalc.get().substring(0, signCalc.get().length() - 1));
        }
        signCalc.set(DigestUtils.md5Hex(String.format("%s%s", signCalc.get(),
                iaaSettlementConfig.getGameOpenPlatformParams().getAppsecret())));

        return signCalc.get();

    }


    private static String encodedFix(String encoded) {
        // required
        encoded = encoded.replace("+", "%20");
        encoded = encoded.replace("*", "%2A");
        encoded = encoded.replace("%7E", "~");

        // optional
        encoded = encoded.replace("!", "%21");
        encoded = encoded.replace("(", "%28");
        encoded = encoded.replace(")", "%29");
        encoded = encoded.replace("'", "%27");
        return encoded;
    }


    private <T> T retry(Supplier<T> T, long intervalMillis, long maxTimes){


        long times = 0;
        while (true) {
            try {
                return T.get();
            } catch (Exception e) {
                if (times >= maxTimes) {
                    throw e;
                }
                times++;
                try {
                    Thread.sleep(intervalMillis);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(ie);
                }
            }
        }
    }
}
