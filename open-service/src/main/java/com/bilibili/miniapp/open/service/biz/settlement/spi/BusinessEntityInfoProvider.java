package com.bilibili.miniapp.open.service.biz.settlement.spi;

import com.bilibili.miniapp.open.service.biz.settlement.model.IaaAppType;
import java.util.Optional;

/**
 * 商业经营主体信息查询器
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
public interface BusinessEntityInfoProvider {


    IaaAppType supportedAppType();



    Optional<BusinessEntityInfo> queryBusinessEntityInfoByAppId(String appId);



    Optional<BindingBusinessAccount> queryBindingBusinessAccountByAppId(String appId);


}
