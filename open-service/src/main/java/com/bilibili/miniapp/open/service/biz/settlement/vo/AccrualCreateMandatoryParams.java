package com.bilibili.miniapp.open.service.biz.settlement.vo;

import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig.HuilianyiAccrualParams;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest.AccrualCreateInput;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest.CustomFormValueDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest.OpenCreateAccrualHeadDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest.OpenCreateAccrualLineDTO;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class AccrualCreateMandatoryParams {


    private String payee;

    private BigDecimal amtInCny;

    private String businessCode;

    private String period;

    public AccrualCreateMandatoryParams() {
    }

    public HuilianyiAccrualCreateRequest toHuilianyiAccrualCreateRequest(HuilianyiAccrualParams params) {

        return new HuilianyiAccrualCreateRequest()
                .setAccrualCreateInput(
                        new AccrualCreateInput().setAccrualHead(
                                new OpenCreateAccrualHeadDTO()
                                        .setBusinessCode(businessCode)
                                        .setFormCode(params.getFormCode())
                                        .setApplicantEmployeeId(params.getApplicantEmployeeId4MiniGame())
                                        .setStatus(params.getStatus())
                                        .setCustomFormValues(Lists.newArrayList(
                                                new CustomFormValueDTO("company", params.getCompany()),
                                                new CustomFormValueDTO("department", params.getDepartment()),
                                                new CustomFormValueDTO("costCentre", params.getCostCentre4MiniGame()),
                                                new CustomFormValueDTO("reason", params.getReason()),
                                                new CustomFormValueDTO("payee", payee),
                                                new CustomFormValueDTO("taxrate", params.getTaxrate()),
                                                new CustomFormValueDTO("period", period),
                                                new CustomFormValueDTO("prepay", params.getPrepay())
//                                                new CustomFormValueDTO("remark",""),
//                                                new CustomFormValueDTO("contract","")

                                        ))

                        ).setAccrualLines(Lists.newArrayList(
                                new OpenCreateAccrualLineDTO()
                                        .setAmount(amtInCny)
                                        .setExpenseTypeCode(params.getExpenseTypeCode())

                        ))
                );


    }


}
