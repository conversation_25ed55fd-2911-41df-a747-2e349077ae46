package com.bilibili.miniapp.open.service.biz.settlement.vo;

import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig.HuilianyiExpenseParams;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.CorporatePaymentCreateRequestDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.OpenAccrualWriteOffRequestDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.OpenContractRelationRequestDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.OpenCustomFormValueRequestDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.OpenInvoiceCreateDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.OpenPaymentScheduleRequestDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest.OpenReceiptRequestDTO;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiInvoiceWrapper;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrResult;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class ExpenseCreateMandatoryParams {


    private static DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");


    private List<String> invoiceImgUrls;

    private Map<String, SimpleOcrResult> ocrResults;

    private BigDecimal withdrawAmtInCny;

    private Map<String, BigDecimal> accrualId2AmtInCny;

    /**
     * 对应供应商的合同编号
     */
    private String contractNumber;

    /**
     * 对应供应商的银行账号
     */
    private String bankNumber;

    /**
     * 对应收款方id
     */
    private String payee;


    private String businessCode;


    @Data
    @Accessors(chain = true)
    public static class SimpleOcrResult{

        private String attachId;

        private String billDate;

        private String billCode;

        private String billNo;

        private BigDecimal fee;

        private BigDecimal feeWithoutTax;

        private String receiptTypeNo;

        private BigDecimal tax;

        private String checkCode;




        public static SimpleOcrResult fromInvoiceWrapper(HuilianyiInvoiceWrapper invoiceWrapper){
            HuilianyiOcrResult.OcrResult ocrResult = invoiceWrapper.getOcrResult().getData().getIdentify_results().get(0);
            return new SimpleOcrResult()
                    .setAttachId(invoiceWrapper.getUploadOid())
                    .setBillDate(ocrResult.getDetails().getDate())
                    .setBillNo(ocrResult.getDetails().getNumber())
                    .setBillCode(ocrResult.getDetails().getCode())
                    .setCheckCode(ocrResult.getDetails().getCheckCode())
                    .setFee(new BigDecimal(ocrResult.getDetails().getTotal()))
                    .setFeeWithoutTax(new BigDecimal(ocrResult.getDetails().getPretaxAmount()))
                    .setReceiptTypeNo(ocrResult.getDetails().getInvoiceTypeNo())
                    .setTax(new BigDecimal(ocrResult.getDetails().getTax()));

        }





    }




    public HuilianyiExpenseCreateRequest toHuilianyiExpenseCreateRequest(
            HuilianyiExpenseParams params) {

        List<OpenReceiptRequestDTO> openReceipts = invoiceImgUrls.stream().map(url -> ocrResults.get(url))
                .map(detail -> {
                    return new OpenReceiptRequestDTO()
                            .setBillingCode(detail.getBillCode())
                            .setCheckCode(detail.getCheckCode())
                            .setAttachmentOID(detail.getAttachId())
                            .setBillingDate(
                                    LocalDate.parse(detail.getBillDate(), formatter2).format(formatter1))
                            .setBillingNo(detail.getBillNo())
                            .setFee(detail.getFee())
                            .setFeeWithoutTax(detail.getFeeWithoutTax())
                            .setReceiptTypeNo(detail.getReceiptTypeNo())
                            .setTax(detail.getTax());

                }).collect(Collectors.toList());

        return new HuilianyiExpenseCreateRequest()
                .setExpenseCreateInput(new CorporatePaymentCreateRequestDTO()
                        .setFormCode(params.getFormCode())
                        .setAccrualBusinessCodeList(new ArrayList<>(accrualId2AmtInCny.keySet()))
                        .setContractNumberList(Lists.newArrayList(contractNumber))
                        .setEmployeeId(params.getApplicantEmployeeId4MiniGame())
                        .setAccountNumber(bankNumber)
                        .setBusinessCode(businessCode)
                        .setPaymentMethodCode(params.getPaymentMethodCode())
                        .setAutoSubmit(params.getAutoSubmit())
                        .setOpenCustomFormValueRequestDTOList(
                                Lists.newArrayList(
                                        new OpenCustomFormValueRequestDTO("department",
                                                params.getDepartment4MiniGame()),
                                        new OpenCustomFormValueRequestDTO("costCentre",
                                                params.getCostCentre4MiniGame()),
//                                        new OpenCustomFormValueRequestDTO("costcenter",
//                                                params.getCostCentre4MiniGame()),
                                        new OpenCustomFormValueRequestDTO("PastPeriod", params.getPastPeriod()),
                                        new OpenCustomFormValueRequestDTO("period", ""),
                                        new OpenCustomFormValueRequestDTO("company", params.getCompany()),
                                        new OpenCustomFormValueRequestDTO("currency", params.getCurrency()),
                                        new OpenCustomFormValueRequestDTO("reason", ""),
                                        new OpenCustomFormValueRequestDTO("remark", ""),
                                        new OpenCustomFormValueRequestDTO("prepay", params.getPrepay()),
                                        new OpenCustomFormValueRequestDTO("fullPayout", params.getFullPayout()),
                                        new OpenCustomFormValueRequestDTO("payee", payee)
                                ))
                        .setOpenPaymentScheduleRequestDTOList(
                                Lists.newArrayList(
                                        new OpenPaymentScheduleRequestDTO()
                                                .setPaymentScheduleType(params.getPaymentScheduleType())
                                                .setPaymentMethodCode(params.getPaymentMethodCode())
                                                .setRealPaymentAmount(withdrawAmtInCny)
                                                .setOpenAccrualWriteOffRequestDTOList(
                                                        Lists.newArrayList(
                                                                accrualId2AmtInCny.entrySet().stream()
                                                                        .map(entry -> new OpenAccrualWriteOffRequestDTO()
                                                                                .setAccrualWriteOffAmount(
                                                                                        entry.getValue())
                                                                                .setAccrualWriteOffBusinessCode(
                                                                                        entry.getKey())
                                                                        ).collect(Collectors.toList())
                                                        )
                                                )
                                                .setOpenInvoiceCreateDTO(
                                                        new OpenInvoiceCreateDTO()
                                                                .setAmount(withdrawAmtInCny)
                                                                .setExpenseTypeCode(params.getExpenseTypeCode())
                                                                .setInvoiceCurrencyCode(params.getInvoiceCurrencyCode())
                                                                .setOccurrenceDate(ZonedDateTime.now().format(
                                                                        DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                                                                .setOpenReceiptRequestDTOList(openReceipts)
                                                )
                                                .setOpenContractRelationRequestDTOList(
                                                        Lists.newArrayList(new OpenContractRelationRequestDTO()
                                                                        .setContractLineNumber(1)
                                                                        .setContractNumber(contractNumber)
                                                                        .setAssignedAmount(withdrawAmtInCny)
                                                                // TODO
                                                        ))

                                )
                        )
                        .setPaymentMethodCode(params.getPaymentMethodCode())

                );
    }




}
