package com.bilibili.miniapp.open.service.biz.settlement.vo;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/26
 */
@Data
@Accessors(chain = true)
public class IaaWithdrawBillDTO extends IaaWithdrawBill implements SnakeCaseBody {

}
