package com.bilibili.miniapp.open.service.biz.settlement.vo;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaCrmChargeBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlement;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill;
import com.bilibili.miniapp.open.service.biz.settlement.model.SettlementModelConvertor;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */

@Data
@Accessors(chain = true)
public class IaaWithdrawBillRecordDTO implements SnakeCaseBody {


    private Long billId;

    private IaaWithdrawBillDTO bill;

    private List<IaaSettlementDTO> settlementDetails;

    private List<IaaCrmChargeBillDTO> crmChargeBillDetails;


    public static IaaWithdrawBillRecordDTO toDTO(Long billId, IaaWithdrawBill bill, List<IaaSettlement> withdrawBills,
            List<IaaCrmChargeBill> crmChargeBills){

        return new IaaWithdrawBillRecordDTO()
                .setBillId(billId)
                .setBill(SettlementModelConvertor.convertor.toDTO(bill))
                .setSettlementDetails(withdrawBills.stream().map(SettlementModelConvertor.convertor::toDTO).collect(
                        Collectors.toList()))
                .setCrmChargeBillDetails(crmChargeBills.stream().map(SettlementModelConvertor.convertor::toDTO).collect(
                        Collectors.toList()));
    }

}
