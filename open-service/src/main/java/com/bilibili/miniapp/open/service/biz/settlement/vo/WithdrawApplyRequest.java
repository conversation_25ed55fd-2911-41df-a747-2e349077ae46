package com.bilibili.miniapp.open.service.biz.settlement.vo;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaAppType;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaSettlement;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill;
import com.bilibili.miniapp.open.service.biz.settlement.spi.BusinessEntityInfo;
import com.bilibili.miniapp.open.service.common.CustomGrpcException;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiInvoiceWrapper;
import com.google.common.base.Joiner;
import io.vavr.Lazy;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Slf4j
@Data
@Accessors(chain = true)
public class WithdrawApplyRequest implements SnakeCaseBody {

    private IaaAppType appType;

    private String appId;
    /**
     * 批量申请提现的id， 无法指定金额，只能指定账单id，会提取里面所有的金额。
     * 账单id必须是合法的id，
     * 如果是已经提现中、成功的id的会进行跳过，
     * 如果是可重试的失败的会进行重试，
     * 如果是不可重试的失败会拒绝整个请求
     */
    private List<Long> billIds;

    /**
     * 申请提现的原因
     */
    private String applyReason;

    /**
     * 申请提现发票地址
     */
    private List<String> invoiceImgUrls;

    // 是否跳过发票校验， 生产禁用
    private Boolean skipCheckInvoice;


    /**
     * @param lazy
     * @return tuple._1 可提现的账单, tuple._2 跳过提现的账单
     */
    public Tuple3<List<IaaWithdrawBill>, List<IaaWithdrawBill>, Map<String, HuilianyiInvoiceWrapper>> validate(
            Lazy<List<IaaWithdrawBill>> lazy,
            Lazy<Map<String, HuilianyiInvoiceWrapper>> ocrLazy,
            Lazy<BusinessEntityInfo> businessEntityInfoLazy,
            Lazy<Map<Long, List<IaaSettlement>>> settlementsLazy,
            IaaSettlementConfig config
    ) {


        Assert.isTrue(!CollectionUtils.isEmpty(billIds), "提现单id不能为空");
        Assert.isTrue(billIds.size() <= 20, "提现单id数量不能超过20");
        Assert.isTrue(!CollectionUtils.isEmpty(invoiceImgUrls), "发票地址不能为空");
        Assert.isTrue(invoiceImgUrls.stream().noneMatch(StringUtils::isBlank), "发票地址不能为空");


        List<IaaWithdrawBill> bills = Optional.ofNullable(lazy.get()).orElse(new ArrayList<>());

        List<Long> absentIds = billIds.stream()
                .filter(id -> bills.stream().noneMatch(bill -> bill.getId().equals(id)))
                .collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(absentIds), "提现单id不存在: " + absentIds);

        Assert.isTrue(bills.stream().noneMatch(
                        bill -> !Objects.equals(bill.getAppType(), appType) || !Objects.equals(bill.getAppId(), appId)),
                "提现单不匹配app类型或appId");


        List<IaaWithdrawBill> forbidWithdrawableBills = bills
                .stream()
                .filter(bill -> billIds.contains(bill.getId()))
                .filter(bill -> {
                    return bill.getBillStatus().forbidWithdraw();
                })
                .collect(Collectors.toList());

        Assert.isTrue(CollectionUtils.isEmpty(forbidWithdrawableBills),
                "提现单不可提现: " + forbidWithdrawableBills.stream()
                        .map(IaaWithdrawBill::getId)
                        .collect(Collectors.toList())
        );

        List<IaaWithdrawBill> skipWithdrawableBills = bills
                .stream()
                .filter(bill -> billIds.contains(bill.getId()))
                .filter(bill -> {
                    return bill.getBillStatus().skipWithdraw();
                })
                .collect(Collectors.toList());


        List<IaaWithdrawBill> withdrawableBills = bills
                .stream()
                .filter(bill -> billIds.contains(bill.getId()))
                .filter(bill -> {
                    return bill.getBillStatus().canWithdraw();
                })
                .collect(Collectors.toList());

        Assert.isTrue(withdrawableBills.stream().noneMatch(bill -> StringUtils.isEmpty(bill.getAccrualId())),
                "存在可提现的提现单未绑定预提单，请联系服务管理员处理");

        Map<String, HuilianyiInvoiceWrapper> checkInvoice = ocrLazy.get();


        BusinessEntityInfo entity = businessEntityInfoLazy.get();

        if (!Optional.ofNullable(skipCheckInvoice).orElse(config.getSkipCheckInvoiceBusinessEntity())
                && checkInvoice.values().stream().flatMap(ocr -> ocr.getOcrResult().getBusinessName().stream())
                .distinct()
                .anyMatch(businessName -> !Objects.equals(entity.getBusinessEntityName(), businessName))) {

            throw new IllegalArgumentException("发票销售方名称与商业主体不一致");
        }

        if (appType == IaaAppType.mini_game) {
            BigDecimal totalAmount = checkInvoice.values().stream()
                    .map(ocr -> ocr.getOcrResult().getData().getIdentify_results())
                    .flatMap(Collection::stream)
                    .filter(ocr -> StringUtils.isNotBlank(ocr.getDetails().getTotal()))
                    .map(ocr -> new BigDecimal(ocr.getDetails().getTotal()))
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

            BigDecimal totalBillAmount = withdrawableBills.stream()
                    .map(IaaWithdrawBill::getWithdrawAmt)
                    .map(amt -> amt.divide(new BigDecimal(100), RoundingMode.HALF_UP))
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

            String maxDiff = config.getMiniGameInvoiceAndActualAmtMaxDiff();
            BigDecimal maxDiffAmt = new BigDecimal(maxDiff);
            if (StringUtils.isBlank(maxDiff)) {
                maxDiffAmt = BigDecimal.ZERO;
            }
            if (totalBillAmount.subtract(totalAmount).abs().compareTo(maxDiffAmt) > 0) {
                log.error("[WithdrawApplyRequest] 发票金额和账单金额差异过大, appId={}, billAmt={}, invoiceAmt={}",
                        appId, totalBillAmount, totalAmount);
                throw new CustomGrpcException(ErrorCodeType.WITHDRAW_AMOUNT_ERROR);
            }
        }

        if (config.getCheckSettlementAndBillAmtWhenApplyWithdraw()) {
            withdrawableBills.forEach(bill -> {

                Map<Long, List<IaaSettlement>> settlements = settlementsLazy.get();
                BigDecimal sumAmt = Optional.ofNullable(settlements.get(bill.getId()))
                        .map(s -> s.stream().map(IaaSettlement::getWithdrawAmt)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)).orElse(BigDecimal.ZERO);

                if (sumAmt.subtract(bill.getWithdrawAmt()).abs().compareTo(BigDecimal.ONE) > 0) {
                    //相差超过1分

                    log.error("Dangerous!! settlement and bill amt not match, billId={}, billAmt={}, settlementAmt={}",
                            bill.getId(), bill.getWithdrawAmt(), sumAmt);

                    if (config.getForbidWithdrawWhenAmtNotMatch()) {
                        throw new IllegalArgumentException("提现金额和结算金额不一致，请联系服务管理员处理");
                    }

                }
            });
        }


        return Tuple.of(withdrawableBills, skipWithdrawableBills, checkInvoice);

    }


    public String getJoinedInvoiceImgUrl() {

        return Joiner.on(",").join(invoiceImgUrls);
    }
}
