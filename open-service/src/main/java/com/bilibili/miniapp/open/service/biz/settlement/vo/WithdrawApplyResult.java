package com.bilibili.miniapp.open.service.biz.settlement.vo;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class WithdrawApplyResult implements SnakeCaseBody {


    /**
     * 申请成功的账单id, 不存在申请失败的id， 申请失败会同步直接返回失败。
     */
    private List<Long> applySuccessBillIds;


    /**
     * 跳过的账单id
     */
    private List<Long> skipBillIds;



}
