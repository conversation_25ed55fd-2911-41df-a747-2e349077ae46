package com.bilibili.miniapp.open.service.biz.user;

import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/
public interface IUserAccessCacheService {

    /**
     * 获取用户最近访问记录
     * @param mid 用户id
     * @return
     */
    List<UserAccessRecord> getUserAccessRecord(Long mid);

    /**
     * 获取用户最近访问记录(带分页)
     * @param mid 用户id
     * @return
     */
    List<UserAccessRecord> getUserAccessRecord(Long mid, Integer pageNum, Integer pageSize);

    /**
     * 缓存用户访问记录
     * @param mid 用户id
     * @param userAccessRecord 访问记录
     */
    void cacheUserAccessRecord(Long mid, List<UserAccessRecord> userAccessRecord);

    /**
     * 缓存用户访问记录
     * @param userAccessRecord 访问记录
     */
    void cacheUserAccessRecord(UserAccessRecord userAccessRecord);

    void invalidateUserAccessRecord(Long mid);
}
