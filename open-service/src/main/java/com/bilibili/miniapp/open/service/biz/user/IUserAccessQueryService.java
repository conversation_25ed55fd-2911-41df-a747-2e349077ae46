package com.bilibili.miniapp.open.service.biz.user;

import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 用户访问查询服务
 * @Date 2025/2/26
 **/
public interface IUserAccessQueryService {

    /**
     * 获取指定用户最近访问的小程序列表
     * @param mid 用户id
     * @return
     */
    List<UserAccessRecord> getUserAccessRecord(Long mid);

    /**
     * 判断用户是否访问过小程序
     * @param mid 用户id
     * @param appId 小程序id
     * @return
     */
    boolean existUserAccess(Long mid, String appId);

    /**
     * 获取指定用户访问的小程序列表(带分页)
     * @param mid
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<UserAccessRecord> getUserAccessRecord(Long mid, Integer pageNum, Integer pageSize);
}
