package com.bilibili.miniapp.open.service.biz.user.impl;

import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.service.biz.user.IUserAccessCacheService;
import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.UserAccessConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.client.RedisException;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Slf4j
@Service
public class UserAccessCacheService implements IUserAccessCacheService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ConfigCenter configCenter;

    private static final String LUA_SCRIPT = "local key = KEYS[1] " +
            "local expireSeconds = ARGV[1] " +
            "local maxRecent = ARGV[2] " +
            "redis.call('DEL', key) " +
            "local i = 3 " +
            "while i <= #ARGV do " +
                "local member = ARGV[i] " +
                "local score = ARGV[i+1] " +
                "redis.call('ZADD', key, score, member) " +
                "i = i + 2 " +
            "end " +
            "redis.call('ZREMRANGEBYRANK', key, 0, -(maxRecent +1)) " +
            "redis.call('EXPIRE', key, expireSeconds) " +
            "return 1";

    @Override
    public List<UserAccessRecord> getUserAccessRecord(Long mid) {
        UserAccessConfig userAccess = configCenter.getUserAccess();
        Integer maxRecent = userAccess.getMaxRecent();
        return getUserAccessRecordRange(mid, 0, maxRecent - 1, userAccess);
    }

    @Override
    public List<UserAccessRecord> getUserAccessRecord(Long mid, Integer pageNum, Integer pageSize) {
        UserAccessConfig userAccess = configCenter.getUserAccess();
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 20;
        }
        // 根据pageNum pageSize 获取 from 和 to
        Integer from = (pageNum - 1) * pageSize;
        int to = from + pageSize - 1;
        return getUserAccessRecordRange(mid, from, to, userAccess);
    }

    @Override
    public void cacheUserAccessRecord(Long mid, List<UserAccessRecord> userAccessRecord) {
        UserAccessConfig userAccess = configCenter.getUserAccess();
        Integer maxRecent = userAccess.getMaxRecent();
        long expireSeconds = Duration.ofDays(userAccess.getRecentDays()).getSeconds();
        redissonClient.getScript().eval(
                RScript.Mode.READ_WRITE,
                LUA_SCRIPT,
                RScript.ReturnType.VALUE,
                Collections.singletonList(String.format(RedisKeyPattern.API_USER_ACCESS.getPattern(), mid)),
                Stream.concat(
                        Stream.concat(Stream.of(expireSeconds), Stream.of(maxRecent)),
                        userAccessRecord.stream().flatMap(record -> {
                            String appId = record.getAppId();
                            double score = record.getMtime().getTime();
                            return Stream.of(appId, score);
                        })
                ).toArray(Object[]::new)
        );
    }

    @Override
    public void cacheUserAccessRecord(UserAccessRecord userAccessRecord) {
        UserAccessConfig userAccess = configCenter.getUserAccess();
        RScoredSortedSet<String> accessSet = getAccessSet(userAccessRecord.getMid(), userAccess.getRecentDays());
        Integer maxRecent = userAccess.getMaxRecent();
        Timestamp mtime = userAccessRecord.getMtime();
        double score = mtime.getTime();
        // 添加或更新访问记录（原子化操作）
        accessSet.add(score, userAccessRecord.getAppId());
//        // 自动修剪旧数据
        accessSet.removeRangeByRankAsync(0, -maxRecent - 1);
    }

    @Override
    public void invalidateUserAccessRecord(Long mid) {
        RScoredSortedSet<String> accessSet = getAccessSet(mid, 0);
        accessSet.delete();
    }

    private RScoredSortedSet<String> getAccessSet(Long mid, Integer recentDays) {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(
                String.format(RedisKeyPattern.API_USER_ACCESS.getPattern(), mid));
        scoredSortedSet.expire(Duration.ofDays(recentDays));
        return scoredSortedSet;
    }

    private RScoredSortedSetAsync<String> getAccessSet(Long mid, Integer recentDays, RBatch batch) {
        RScoredSortedSetAsync<String> scoredSortedSet = batch.getScoredSortedSet(
                String.format(RedisKeyPattern.API_USER_ACCESS.getPattern(), mid)
        );
//        scoredSortedSet.expireIfSetAsync(Duration.ofDays(recentDays));
        return scoredSortedSet;
    }

    private List<UserAccessRecord> getUserAccessRecordRange(Long mid, Integer from, Integer to, UserAccessConfig userAccessConfig) {
        RScoredSortedSet<String> accessSet = getAccessSet(mid, userAccessConfig.getRecentDays());
        // 倒序获取指定数量的记录（带时间戳）
        Collection<String> appIds = accessSet.valueRangeReversed(from, to);

        return appIds.stream().map(appId -> {
            Double score = accessSet.getScore(appId);
            return UserAccessRecord.builder()
                    .appId(appId)
                    .mid(mid)
                    .mtime(new Timestamp(score.longValue()))
                    .build();
        }).collect(Collectors.toList());
    }
}
