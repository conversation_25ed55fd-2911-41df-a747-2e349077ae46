package com.bilibili.miniapp.open.service.biz.user.impl;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccessPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.dao.MiniAppUserAccessLogDao;
import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.IUserAccessRepository;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.user.IUserAccessCacheService;
import com.bilibili.miniapp.open.service.biz.user.IUserAccessQueryService;
import com.bilibili.miniapp.open.service.bo.access.OpenAccessBo;
import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.UserAccessConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RScript;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Service
@Slf4j
public class UserAccessQueryService implements IUserAccessQueryService {
    @Resource
    private IUserAccessCacheService userAccessCacheService;

    @Resource
    private IUserAccessRepository userAccessRepository;

    @Resource(type = RedissonCacheRepository.class)
    private ICacheRepository redissonCacheRepository;

    @Override
    public List<UserAccessRecord> getUserAccessRecord(Long mid) {
        List<UserAccessRecord> userAccessRecords = userAccessCacheService.getUserAccessRecord(mid);
        if (CollectionUtils.isEmpty(userAccessRecords)) {
            String lockKey = String.format(RedisKeyPattern.OPEN_LOCK_USER_ACCESS.getPattern(), mid);
            RLock lock = redissonCacheRepository.tryLock(lockKey);
            try {
                // 再次检查缓存
                userAccessRecords = userAccessCacheService.getUserAccessRecord(mid);
                if (!CollectionUtils.isEmpty(userAccessRecords)) {
                    return userAccessRecords;
                }
                // 查询数据库
                List<MiniAppUserAccessLogPo> userAccessLogPos = userAccessRepository.queryByMid(mid);
                userAccessRecords = userAccessLogPos.stream().map(UserAccessRecord::fromPo).collect(Collectors.toList());
                // 更新缓存
                userAccessCacheService.cacheUserAccessRecord(mid, userAccessRecords);
            } catch (Exception e) {
                log.error("[UserAccessQueryService] getUserAccessRecord error", e);
            } finally {
                lock.unlock();
            }
        }
        return userAccessRecords;
    }

    @Override
    public boolean existUserAccess(Long mid, String appId) {
        List<UserAccessRecord> record = getUserAccessRecord(mid);
        return record.stream().map(UserAccessRecord::getAppId).anyMatch(appId::equals);
    }

    @Override
    public List<UserAccessRecord> getUserAccessRecord(Long mid, Integer pageNum, Integer pageSize) {
        List<UserAccessRecord> userAccessRecord = userAccessCacheService.getUserAccessRecord(mid, pageNum, pageSize);
        if (CollectionUtils.isEmpty(userAccessRecord)) {
            PageResult<MiniAppUserAccessLogPo> records = userAccessRepository.queryByMid(mid, pageNum, pageSize);
            List<MiniAppUserAccessLogPo> userAccessLogPos = records.getRecords();
            userAccessRecord = userAccessLogPos.stream().map(UserAccessRecord::fromPo).collect(Collectors.toList());
            userAccessCacheService.cacheUserAccessRecord(mid, userAccessRecord);
        }
        return userAccessRecord;
    }
}
