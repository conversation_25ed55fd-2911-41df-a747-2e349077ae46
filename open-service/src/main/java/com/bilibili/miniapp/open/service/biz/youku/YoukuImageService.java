package com.bilibili.miniapp.open.service.biz.youku;

import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.bo.youku.ShowInfo;
import com.bilibili.miniapp.open.repository.bo.youku.YouKuVideo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl.YouKuShowRepository;
import com.bilibili.miniapp.open.service.biz.resource.IBossService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.net.ssl.*;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/22
 **/

@Service
@Slf4j
public class YoukuImageService {

    @Resource
    private IBossService bossService;

    @Resource
    private YouKuShowRepository youKuShowRepository;

    private static final String YOU_KU_IMAGE_URL_PREFIX_REG = "^https?://m\\.ykimg\\.com/.+";
    private static final Pattern YOU_KU_IMAGE_URL_PATTERN = Pattern.compile(YOU_KU_IMAGE_URL_PREFIX_REG);

    public void scanShowAndReplacement(Long fromId, Integer size) {
        AtomicLong totalCount = new AtomicLong();
        while(true) {
            StopWatch stopWatch = new StopWatch();
            // 开始时间
            stopWatch.start();
            // 查询节目信息
            List<ShowInfo> showInfos = youKuShowRepository.fastQueryShowInfosByPage(fromId, size);
            stopWatch.stop();
            log.info("查询节目信息成功>>>fromId={}, size={}, costTime={}", fromId, showInfos.size(), stopWatch.getTotalTimeMillis());
            if (CollectionUtils.isEmpty(showInfos)) {
                break;
            }
            List<ShowInfo> needSave = new ArrayList<>();
            FunctionUtil.batch(showInfos, showInfoList -> {
                showInfoList.forEach(showInfo -> {
                    // 替换节目图片
                    String showThumbUrlHuge = showInfo.getShowThumbUrlHuge();
                    String showW3H4ThumbUrlHuge = showInfo.getShowW3H4ThumbUrlHuge();
                    boolean needUpdate = false;
                    if (StringUtils.isNotBlank(showThumbUrlHuge) && YOU_KU_IMAGE_URL_PATTERN.matcher(showThumbUrlHuge).matches()) {
                        showInfo.setShowThumbUrlHuge(processImageUrls(showThumbUrlHuge));
                        needUpdate = true;
                    }
                    if (StringUtils.isNotBlank(showW3H4ThumbUrlHuge) && YOU_KU_IMAGE_URL_PATTERN.matcher(showW3H4ThumbUrlHuge).matches()) {
                        showInfo.setShowW3H4ThumbUrlHuge(processImageUrls(showW3H4ThumbUrlHuge));
                        needUpdate = true;
                    }
                    if (needUpdate) {
                        needSave.add(showInfo);
                        totalCount.incrementAndGet();
                    }
                });
                return showInfoList;
            }, 50, ThreadPoolUtil.getExecutor(ThreadPoolType.YOU_KU_IMAGE));
            youKuShowRepository.batchSave(needSave);
            // 更新fromId
            fromId = showInfos.get(showInfos.size() - 1).get_id();
        }
        log.info("优酷节目图片信息更新成功>>>共更新{}条节目信息", totalCount.get());
    }

    public void scanVideoAndReplacement(Long fromId, Integer size) {
        AtomicLong totalCount = new AtomicLong();
        while(true) {
            StopWatch stopWatch = new StopWatch();
            // 开始时间
            stopWatch.start();
            // 查询节目信息
            List<YouKuVideo> videos = youKuShowRepository.fastQueryVideosByPage(fromId, size);
            stopWatch.stop();
            log.info("查询视频信息成功>>>fromId={}, size={}, costTime={}", fromId, videos.size(), stopWatch.getTotalTimeMillis());
            if (CollectionUtils.isEmpty(videos)) {
                break;
            }
            List<YouKuVideo> needSave = new ArrayList<>();
            // 遍历节目信息
            FunctionUtil.batch(videos, videoList -> {
                videoList.forEach(video -> {
                    // 替换节目图片
                    String thumbnails = video.getThumbnails();
                    String verticalThumbnails = video.getVerticalThumbnails();
                    boolean needUpdate = false;
                    if (StringUtils.isNotBlank(thumbnails) && YOU_KU_IMAGE_URL_PATTERN.matcher(thumbnails).matches()) {
                        video.setThumbnails(processImageUrls(thumbnails));
                        needUpdate = true;
                    }
                    if (StringUtils.isNotBlank(verticalThumbnails) && YOU_KU_IMAGE_URL_PATTERN.matcher(verticalThumbnails).matches()) {
                        video.setVerticalThumbnails(processImageUrls(verticalThumbnails));
                        needUpdate = true;
                    }
                    if (needUpdate) {
                        needSave.add(video);
                        totalCount.incrementAndGet();
                    }
                });
                return videoList;
            }, 50, ThreadPoolUtil.getExecutor(ThreadPoolType.YOU_KU_IMAGE));
            youKuShowRepository.batchSaveVideos(needSave);
            // 更新fromId
            fromId = videos.get(videos.size() - 1).get_id();
        }
        log.info("优酷视频图片信息更新成功>>>共更新{}条视频信息", totalCount.get());
    }

    public String processImageUrls(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl)) {
            return imageUrl;
        }
        return uploadImageFromUrl(imageUrl);
    }

    private String uploadImageFromUrl(String imageUrl) {
        String filePath = "youku";
        String fileName = extractFileName(imageUrl);

        try {
            HttpURLConnection connection = createConnection(imageUrl);

            try (InputStream inputStream = connection.getInputStream()) {
                long len = connection.getContentLengthLong();
                return bossService.uploadWithoutExp(filePath, fileName, inputStream, len);
            } finally {
                connection.disconnect();
            }
        } catch (FileNotFoundException e) {
            log.error("[YoukuImageService] File not found: image_url={}, ex={}", imageUrl, e.getMessage());
            return imageUrl;
        } catch (Exception e) {
            log.error("[YoukuImageService] Failed to upload image: image_url={}", imageUrl, e);
            throw new RuntimeException(e);
        }
    }

    private HttpURLConnection createConnection(String imageUrl) throws Exception {
        URL url = new URL(imageUrl);
        HttpURLConnection connection;

        if (url.getProtocol().equalsIgnoreCase("https")) {
            HttpsURLConnection httpsConnection = (HttpsURLConnection) url.openConnection();
            httpsConnection.setSSLSocketFactory(createSSLFactory());
            httpsConnection.setHostnameVerifier(HttpsURLConnection.getDefaultHostnameVerifier());
            connection = httpsConnection;
        } else {
            connection = (HttpURLConnection) url.openConnection();
        }
        return connection;
    }

    // 添加SSL上下文工厂方法
    private SSLSocketFactory createSSLFactory() throws Exception {
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, new TrustManager[]{new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() { return null; }
            public void checkClientTrusted(X509Certificate[] certs, String authType) {}
            public void checkServerTrusted(X509Certificate[] certs, String authType) {}
        }}, new SecureRandom());
        return sslContext.getSocketFactory();
    }

    // 提取文件名的方法
    private String extractFileName(String url) {
        return url.substring(url.lastIndexOf("/") + 1);
    }
}
