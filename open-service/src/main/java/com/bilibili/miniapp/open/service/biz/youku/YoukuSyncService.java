package com.bilibili.miniapp.open.service.biz.youku;

import com.alibaba.fastjson2.JSON;
import com.bilibili.miniapp.open.common.enums.IsDeleted;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl.YouKuShowRepository;
import com.bilibili.miniapp.open.repository.bo.youku.YouKuVideo;
import com.bilibili.miniapp.open.service.bo.youku.MediaResource;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.processor.LargeJsonProcessor;
import com.bilibili.miniapp.open.service.rpc.http.YouKuRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import pleiades.component.http.client.BiliCall;
import pleiades.component.http.client.BiliHttpClient;
import pleiades.venus.breaker.exception.BiliBreakerRejectedException;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

// 数据同步核心实现类
@Service
@Slf4j
public class YoukuSyncService {
    @Resource
    private YouKuRemoteService youKuRemoteService;

    @Resource
    private LargeJsonProcessor largeJsonProcessor;

    @Resource
    private YoukuImageService imageService;

    @Resource
    private YouKuShowRepository youKuShowRepository;

    @Resource
    private ConfigCenter configCenter;

    // 主同步方法
    public void syncAllMediaData() {
        try {
            Map<String, String> params = buildBaseParams("上线");
            String sign = generateSignature(params, true);
            params.put("sign", sign);
            List<MediaResource> resources = fetchMediaResources(params);
            processResources(resources);
            log.info("[YoukuSyncService] syncAllMediaData finish.");
        } catch (Exception e) {
            log.error("[YoukuSyncService] syncAllMediaData error", e);
        }
    }

    public void syncIncrementMediaData(Integer hour, String category) {
        // 增量数据同步逻辑
        try {
            Map<String, String> params = buildBaseParams(category);
            params.put("hour", String.valueOf(hour));
            String sign = generateSignature(params, false);
            params.put("sign", sign);
            List<String> locs = fetchIncrMediaResources(params);
            processResourcesWithIncr(locs, category);
            log.info("[YoukuSyncService] syncIncrementMediaData finish.");
        } catch (Exception e) {
            log.error("[YoukuSyncService] syncIncrementMediaData error. [hour={}, category={}]", hour, category, e);
        }
    }

    // 构建基础参数
    private Map<String, String> buildBaseParams(String category) {
        Map<String, String> params = new LinkedHashMap<>();
        params.put("pid", configCenter.getYouKu().getPid());
        params.put("tm", String.valueOf(System.currentTimeMillis()));
        params.put("type", "json");
        params.put("category", category); // 全量接口需要此参数但不参与签名
        return params;
    }

    // 生成接口签名
    private String generateSignature(Map<String, String> params, boolean fullSync) throws UnsupportedEncodingException {
        TreeMap<String, String> sortedParams = new TreeMap<>(params);
        if (fullSync) {
            sortedParams.remove("category"); // 关键点：全量接口剔除category
        }
        
        StringJoiner rawString = new StringJoiner("&");
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            rawString.add(entry.getKey() + "=" + entry.getValue());
        }
        
        String encodedParams = URLEncoder.encode(rawString.toString(), StandardCharsets.UTF_8.name());
        String signContent = configCenter.getYouKu().getSecretKey() + "&" + encodedParams;
        return DigestUtils.md5Hex(signContent);
    }

    // 执行HTTP请求
    private List<MediaResource> fetchMediaResources(Map<String, String> params) throws IOException, URISyntaxException {
        BiliCall<List<MediaResource>> allAddress =
                youKuRemoteService.getAllAddress(params.get("pid"), Long.parseLong(params.get("tm")), params.get("type"), params.get("sign"), "上线");
        try {
            return allAddress.execute(new BiliHttpClient.Options(5000, 5000, 5000)).body();
        } catch (BiliBreakerRejectedException e) {
            throw new RuntimeException(e);
        }
    }

    private List<String> fetchIncrMediaResources(Map<String, String> params) throws IOException, URISyntaxException {
        BiliCall<List<String>> incrAddress =
                youKuRemoteService.getIncrementAddress(params.get("pid"), Long.parseLong(params.get("tm")), Integer.valueOf(params.get("hour")), params.get("type"), params.get("sign"), params.get("category"));
        try {
            return incrAddress.execute(new BiliHttpClient.Options(5000, 5000, 5000)).body();
        } catch (BiliBreakerRejectedException e) {
            throw new RuntimeException(e);
        }
    }

    // 处理资源数据
    private void processResources(List<MediaResource> resources) {
        log.info("[YoukuSyncService] 开始处理全量资源数据: {}", resources.size());
        resources.forEach(resource -> {
            try {
                processSingleResource(resource.getLoc(), "上线");
            } catch (Exception e) {
                log.error("[YoukuSyncService] 处理资源失败: {}", resource.getLoc(), e);
            }
        });
    }

    // 处理资源数据
    private void processResourcesWithIncr(List<String> locs, String category) {
        log.info("[YoukuSyncService] 开始处理增量资源数据: size={}, category={}", locs.size(), category);
        locs.forEach(loc -> {
            try {
                processSingleResource(loc, category);
            } catch (Exception e) {
                log.error("[YoukuSyncService] 处理资源失败: {}", loc, e);
            }
        });
    }

    // 处理单个资源文件
    private void processSingleResource(String fileUrl, String category) {
        try {
            StopWatch stopWatch = new StopWatch();
            largeJsonProcessor.process(new URL(fileUrl), batch -> {
                // 示例：并行处理每个条目
                FunctionUtil.batch(batch, entries -> {
                    // 开始时间
                    stopWatch.start();
                    entries.forEach(entry -> {
                        // 替换图片
                        try {
                            if (StringUtils.equals("下线", category)) {
                                entry.setIsDeleted(IsDeleted.DELETED.getCode());
                            }
                            entry.setShowThumbUrlHuge(imageService.processImageUrls(entry.getShowThumbUrlHuge()));
                            entry.setShowW3H4ThumbUrlHuge(imageService.processImageUrls(entry.getShowW3H4ThumbUrlHuge()));
                            // 处理video
                            List<YouKuVideo> videos = entry.getVideos();
                            if (CollectionUtils.isNotEmpty(videos)) {
                                videos.forEach(video -> {
                                    if (StringUtils.equals("下线", category)) {
                                        video.setIsDeleted(IsDeleted.DELETED.getCode());
                                    }
                                    video.setVerticalThumbnails(imageService.processImageUrls(video.getVerticalThumbnails()));
                                    video.setThumbnails(imageService.processImageUrls(video.getThumbnails()));
                                });
                            }
                        } catch (Exception e) {
                            log.error("处理图片资源失败: {}", e.getMessage(), e);
                            // 这里图片未替换成功，可以走补偿
                        }
                        try {
                            // 保存数据库
                            youKuShowRepository.save(entry);
                        } catch (Exception e) {
                            log.error("[YoukuSyncService] 保存数据库失败: {}. [data={}]", e.getMessage(), JSON.toJSONString(entry), e);
                            // 数据库保存失败，手动调用后门接口进行补偿，没必要记录重试表
                        }
                    });
                    stopWatch.stop();
                    log.info("[YoukuSyncService] 「{}」数据处理完成: {} 条数据, 耗时: {}ms", fileUrl, entries.size(), stopWatch.getTotalTimeMillis());

                    return entries;
                }, 50, ThreadPoolUtil.getExecutor(ThreadPoolType.YOU_KU_IMAGE));
            });
        } catch (Exception e) {
            log.error("[YoukuSyncService] 处理资源文件失败: {}", fileUrl, e);
            throw new RuntimeException(e);
        }
    }

    // 处理图片URL
}

