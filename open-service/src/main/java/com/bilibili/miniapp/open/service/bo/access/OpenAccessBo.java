package com.bilibili.miniapp.open.service.bo.access;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/6 17:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OpenAccessBo implements Serializable {
    private static final long serialVersionUID = -1222751332945819714L;
    private String accessKey;
    private String accessToken;
    private String bizName;
}
