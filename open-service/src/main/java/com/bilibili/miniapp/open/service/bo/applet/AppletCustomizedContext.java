package com.bilibili.miniapp.open.service.bo.applet;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppletCustomizedContext {

    private String path;
    private Map<String, Object> params;
}
