package com.bilibili.miniapp.open.service.bo.applet;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppBaseInfoDto;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppletInfoBo {

    private String appId;
    /**
     * 小程序名称
     */
    private String title;

    /**
     * 小程序icon地址
     */
    private String icon;

    /**
     * 小程序基础链接
     * <a href="https://miniapp.bilibili.com/applet/bili60acc738c0e02ca1">老版本框架</a>
     * <a href="https://miniapp.bilibili.com/appletx/bili60acc738c0e02ca1">新版本框架</a>
     */
    private String appletBaseUrl;

    private Integer appletVersion;

    private String customizedPath;

    private Map<String, Object> customizedParams;

    private SeasonBo season;
}
