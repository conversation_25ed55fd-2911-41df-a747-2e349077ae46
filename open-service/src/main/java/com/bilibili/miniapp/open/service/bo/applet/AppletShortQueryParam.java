package com.bilibili.miniapp.open.service.bo.applet;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/9
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AppletShortQueryParam {
    private String appId;
    private Long seasonId;
    /**
     * 是否返回ep
     */
    private boolean appendEpisode;
    /**
     * 是否返回ep的稿件宽高
     * 如果为true，则前提是{@link #appendEpisode} = true才生效
     */
    private boolean appendVideoDimension;
    /**
     * 是否返回作者详细信息（头像和昵称）
     */
    private boolean appendAuthorDetail;
}
