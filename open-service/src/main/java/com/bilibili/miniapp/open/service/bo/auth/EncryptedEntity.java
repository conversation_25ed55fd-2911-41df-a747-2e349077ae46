package com.bilibili.miniapp.open.service.bo.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/14
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EncryptedEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String encryptedData;

    private String iv;
}
