package com.bilibili.miniapp.open.service.bo.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppAuthBo {

    private String maskedPhone;

    private String preauthCode;

    // 有效期(单位: 秒)
    private long expiresIn;
}
