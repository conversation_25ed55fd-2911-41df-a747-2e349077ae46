package com.bilibili.miniapp.open.service.bo.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreAuthInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private String preAuthCode;

    private int status = STATUS_UNUSED;

    public static final int STATUS_UNUSED = 0;
    public static final int STATUS_USED = 1;

    public void markAsUsed() {
        status = STATUS_USED;
    }
}
