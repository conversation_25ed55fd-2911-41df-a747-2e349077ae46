package com.bilibili.miniapp.open.service.bo.client.season;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientUserEpBo {
    private Long epId;
    private Integer order;
    private Integer paymentStatus;
    private Boolean unlock;
    private Integer width;
    private Integer height;
    private Long duration;
}
