package com.bilibili.miniapp.open.service.bo.client.season;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientUserSeasonBo {

    private String cover;
    private List<ClientUserEpBo> epList;
    private Long seasonId;
    private String title;

}
