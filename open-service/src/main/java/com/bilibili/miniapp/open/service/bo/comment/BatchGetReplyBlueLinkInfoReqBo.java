package com.bilibili.miniapp.open.service.bo.comment;

import lombok.Data;

import java.util.List;

@Data
public class BatchGetReplyBlueLinkInfoReqBo {
    /**
     * 回复链接列表
     */
    private List<ReplyUrlBo> replyUrls;
    /**
     * 主题ID
     */
    private SubjectIdBo subjectId;
    /**
     * 用户ID
     */
    private Long mid;

    /**
     * 稿件up的mid
     */
    private Long upMid;

    private String trackId;

    /**
     * 当前页面的唯一标识
     */
    private String spmid;
}