package com.bilibili.miniapp.open.service.bo.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/27 17:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArchiveBo implements Serializable {
    private static final long serialVersionUID = -4524173239325936229L;
    private long aid;
    private long cid;
    private String cover;
    private int width;
    private int height;
    private String title;
    private String desc;
    private int state;
    //单位：秒
    private long duration;
    private String firstFrame;
}
