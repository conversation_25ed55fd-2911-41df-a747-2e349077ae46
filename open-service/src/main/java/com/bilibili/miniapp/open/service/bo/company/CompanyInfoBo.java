package com.bilibili.miniapp.open.service.bo.company;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfoBo {
    private String companyId;
    private Long mid;
    private List<String> additionalMaterials;
    private String businessLicense;
    private String companyName;
    private String contactEmail;
    private String creditCode;
    private String officialWebsite;
    private String operatorName;
    private String phoneNumber;
}