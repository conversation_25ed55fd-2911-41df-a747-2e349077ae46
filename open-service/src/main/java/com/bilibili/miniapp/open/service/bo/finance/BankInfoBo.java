package com.bilibili.miniapp.open.service.bo.finance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 银行信息BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankInfoBo {
    
    /**
     * 银行账户号
     */
    private String bankAccountNumber;
    
    /**
     * 银行名称
     */
    private String bankName;
    
    /**
     * 银行支行名称
     */
    private String bankBranchName;
}
