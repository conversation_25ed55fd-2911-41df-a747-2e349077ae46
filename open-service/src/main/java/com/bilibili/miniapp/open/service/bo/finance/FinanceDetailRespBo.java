package com.bilibili.miniapp.open.service.bo.finance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 财务信息详情响应BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinanceDetailRespBo {
    
    /**
     * 银行信息
     */
    private BankInfoBo bankInfo;
    
    /**
     * 发票信息
     */
    private InvoiceInfoBo invoiceInfo;
}
