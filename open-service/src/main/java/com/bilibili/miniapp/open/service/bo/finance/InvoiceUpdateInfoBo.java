package com.bilibili.miniapp.open.service.bo.finance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票更新信息BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceUpdateInfoBo {
    
    /**
     * 开票项目类别：1-信息技术服务*信息服务费,2-广告服务*广告发布费
     */
    private Integer invoiceItemCategory;
}
