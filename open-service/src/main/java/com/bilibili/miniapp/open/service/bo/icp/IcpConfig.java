package com.bilibili.miniapp.open.service.bo.icp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpConfig {

    private List<IcpDocumentType> documentTypeList;
    private List<IcpAreaCode> areaCodeList;
    private List<IcpOrgType> orgTypeList;
    private List<IcpAppServiceType> appServiceTypeList;


    /**
     * 证件类型
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class IcpDocumentType {
        private Long code;
        private String name;
        private Long orgCode;
        private Integer status;
    }

    /**
     * 区域列表
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class IcpAreaCode {
        private Long code;
        private String name;
        private Integer type;
        private Integer status;
    }

    /**
     * 单位性质
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class IcpOrgType {
        private Long code;
        private String name;
        private Integer status;
    }

    /**
     * APP服务类型
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class IcpAppServiceType {
        private Long code;
        private String name;
        private Long parentCode;
        private Integer status;
    }

    public List<IcpConfigTree> parseAreaCodeTree() {
        Map<Long, IcpConfigTree> codeToTreeNode = new HashMap<>();
        // Filter out invalid data and create tree nodes
        for (IcpAreaCode areaCode : areaCodeList) {
            if (areaCode.getStatus() == 0) continue;
            codeToTreeNode.put(areaCode.getCode(), new IcpConfigTree(areaCode.getCode(), areaCode.getName()));
        }

        List<IcpConfigTree> trees = new ArrayList<>();

        // Build tree structure
        for (IcpAreaCode areaCode : areaCodeList) {
            if (areaCode.getStatus() == 0) continue;
            IcpConfigTree currentNode = codeToTreeNode.get(areaCode.getCode());

            if (areaCode.getType() == 1) {
                // Province level
                trees.add(currentNode);
            } else if (areaCode.getType() == 2) {
                // City level
                Long parentCode = (areaCode.getCode() / 10000) * 10000;
                IcpConfigTree parentNode = codeToTreeNode.get(parentCode);
                if (parentNode != null) {
                    parentNode.addChild(currentNode);
                }
            } else if (areaCode.getType() == 3) {
                // District level
                Long parentCode = (areaCode.getCode() / 100) * 100;
                IcpConfigTree parentNode = codeToTreeNode.get(parentCode);
                if (parentNode != null) {
                    parentNode.addChild(currentNode);
                }
            }
        }

        return trees;

    }

    public List<IcpConfigTree> parseOrgTypeTree() {
        Map<Long, IcpConfigTree> orgMap = new HashMap<>();

        // Create tree nodes for valid org types
        for (IcpOrgType orgType : orgTypeList) {
            if (orgType.getStatus() == 0) continue;
            orgMap.put(orgType.getCode(), new IcpConfigTree(orgType.getCode(), orgType.getName()));
        }

        // Add document types as children of org types
        for (IcpDocumentType documentType : documentTypeList) {
            if (documentType.getStatus() == 0) continue;
            IcpConfigTree orgNode = orgMap.get(documentType.getOrgCode());
            if (orgNode != null) {
                orgNode.addChild(new IcpConfigTree(documentType.getCode(), documentType.getName()));
            }
        }

        return new ArrayList<>(orgMap.values());
    }

    public List<IcpConfigTree> parseServiceTypeTree() {
        Map<Long, IcpConfigTree> codeToTreeNode = new HashMap<>();
        List<IcpConfigTree> rootNodes = new ArrayList<>();

        // Create tree nodes for valid service types
        for (IcpAppServiceType serviceType : appServiceTypeList) {
            if (serviceType.getStatus() == 0) continue;
            codeToTreeNode.put(serviceType.getCode(), new IcpConfigTree(serviceType.getCode(), serviceType.getName()));
        }

        // Build tree structure
        for (IcpAppServiceType serviceType : appServiceTypeList) {
            if (serviceType.getStatus() == 0) continue;
            IcpConfigTree currentNode = codeToTreeNode.get(serviceType.getCode());

            if (serviceType.getParentCode() == null || serviceType.getParentCode() == 0) {
                // Root node (no parentCode means it's a root)
                rootNodes.add(currentNode);
            } else {
                // Non-root node, find parent and add as child
                IcpConfigTree parentNode = codeToTreeNode.get(serviceType.getParentCode());
                if (parentNode != null) {
                    parentNode.addChild(currentNode);
                }
            }
        }
        return rootNodes;
    }

    public static void main(String[] args) {
        List<IcpAreaCode> areaCodes = List.of(
                new IcpAreaCode(100000L, "北京", 1, 1),
                new IcpAreaCode(100100L, "市辖区", 2, 1),
                new IcpAreaCode(100101L, "东城区", 3, 1),
                new IcpAreaCode(100102L, "西城区", 3, 1),
                new IcpAreaCode(200000L, "上海", 1, 1),
                new IcpAreaCode(200100L, "市辖区", 2, 1),
                new IcpAreaCode(200101L, "黄浦区", 3, 1)
        );
        IcpConfig icpConfig = new IcpConfig();
        icpConfig.setAreaCodeList(areaCodes);
        List<IcpConfigTree> result = icpConfig.parseAreaCodeTree();

        for (IcpConfigTree tree : result) {
            System.out.println("省: " + tree.getLabel());
            for (IcpConfigTree city : tree.getChildren()) {
                System.out.println("\t市: " + city.getLabel());
                for (IcpConfigTree district : city.getChildren()) {
                    System.out.println("\t\t区: " + district.getLabel());
                }
            }
        }
    }
}