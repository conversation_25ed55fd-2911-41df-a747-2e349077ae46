package com.bilibili.miniapp.open.service.bo.icp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IcpConfigTree {

    private Long value;
    private String label;
    private List<IcpConfigTree> children = new ArrayList<>();

    public IcpConfigTree(Long value, String label) {
        this.value = value;
        this.label = label;
    }

    public void addChild(IcpConfigTree child) {
        children.add(child);
    }
}
