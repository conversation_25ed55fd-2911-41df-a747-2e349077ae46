package com.bilibili.miniapp.open.service.bo.icp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IcpPlatformAuditListBo {

    private Long id;

    private Long flowId;

    private String appName;

    private String appLogo;

    private String appId;

    private String companyName;

    private Timestamp submitTime;

    private Integer auditStatus;

    private String operator;

    private Timestamp auditTime;
}
