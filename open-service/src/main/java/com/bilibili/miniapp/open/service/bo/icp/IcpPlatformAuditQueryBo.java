package com.bilibili.miniapp.open.service.bo.icp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IcpPlatformAuditQueryBo {
    // 模糊匹配
    private String appName;

    private List<String> appId;

    // 模糊匹配
    private String companyName;

    private Integer auditStatus;

    private Integer pageNum;

    private Integer pageSize;
}
