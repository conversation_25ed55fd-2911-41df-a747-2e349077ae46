package com.bilibili.miniapp.open.service.bo.icp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/25
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IdentityResult {

    private String verifyPhoto;
    private String fzrCard;
    private Integer fzrLicenseType;
    private String fzrCardNo;
    private String fzrName;
    private Long fzrCardBegin;
    private Long fzrCardEnd;
    private Integer fzrCardLongEffect;
}
