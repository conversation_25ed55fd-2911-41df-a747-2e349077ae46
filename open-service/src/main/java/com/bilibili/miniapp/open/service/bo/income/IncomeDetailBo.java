package com.bilibili.miniapp.open.service.bo.income;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 收入明细响应BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeDetailBo {

    /**
     * 收入明细记录列表
     */
    private List<IncomeDetailItemBo> records;

    /**
     * 总记录数
     */
    private Long total;


    public static IncomeDetailBo emptyInstance() {
        return IncomeDetailBo.builder()
                .records(List.of())
                .total(0L)
                .build();
    }

}
