package com.bilibili.miniapp.open.service.bo.income;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 收入明细项BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeDetailItemBo {
    
    /**
     * 小程序名称
     */
    private String appName;
    
    /**
     * 小程序ID
     */
    private String appId;
    
    /**
     * 收入时间
     */
    private String incomeTime;
    
    /**
     * 流量类型
     */
    private Integer trafficType;
    
    /**
     * 收入金额（分）
     */
    private BigDecimal incomeAmount;
    
    /**
     * 通道费（分）
     */
    private BigDecimal channelFee;
    
    /**
     * 可分成收入金额（分）
     */
    private BigDecimal distributableIncomeAmount;
    
    /**
     * 实际收入金额（分）
     */
    private BigDecimal actualIncomeAmount;
    
    /**
     * 提现状态
     */
    private Integer withdrawStatus;
    
    /**
     * 通道费比例
     */
    private String channelFeeRatio;
    
    /**
     * 分成比例
     */
    private String distributableRatio;
}
