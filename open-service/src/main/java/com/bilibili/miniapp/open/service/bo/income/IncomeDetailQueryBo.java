package com.bilibili.miniapp.open.service.bo.income;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 收入明细查询请求BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeDetailQueryBo {
    
    /**
     * 小程序ID
     */
    private String appId;
    
    /**
     * 小程序名称
     */
    private String appName;
    
    /**
     * 开始时间
     */
    private String beginTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 流量类型
     */
    private Integer trafficType;
    
    /**
     * 提现状态
     */
    private Integer withdrawStatus;
    
    /**
     * 页码，默认1
     */
    private Integer page = 1;
    
    /**
     * 每页大小，默认10
     */
    private Integer size = 10;
}
