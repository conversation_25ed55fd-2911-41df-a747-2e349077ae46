package com.bilibili.miniapp.open.service.bo.miniapp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
public class MiniAppAuditBo {
    private Long admissionId;
    private String appDescription;
    private String appId;
    private String appLogo;
    private String appName;
    private List<String> categoryCertifications;
    /**
     * @see com.bilibili.miniapp.open.common.enums.CategoryType
     */
    private String categoryName;
}
