package com.bilibili.miniapp.open.service.bo.miniapp;

import com.bilibili.miniapp.open.common.enums.AppDevelopType;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
public class MiniAppBo {
    private Long admissionId;
    private String appDescription;
    private String appId;
    private String appLogo;
    private String appName;
    private List<String> categoryCertifications;
    /**
     * @see com.bilibili.miniapp.open.common.enums.CategoryType
     */
    private Integer categoryId;

    /**
     * @see com.bilibili.miniapp.open.common.enums.AppDevelopType
     */
    private Integer developType;

    private Integer appletVersion;

    //ICP备案拦截弹窗
    private Integer icpSwitch;

    public boolean miniAppTemplate() {
        return Objects.equals(developType, AppDevelopType.TEMPLATE.getCode());
    }

}
