package com.bilibili.miniapp.open.service.bo.miniapp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MiniAppCategoryCertificationBo {
    private String appId;
    private int categoryId;
    private List<String> certifications;

}
