package com.bilibili.miniapp.open.service.bo.miniapp;

import com.bilibili.miniapp.open.service.bo.icp.IcpConfigTree;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/19
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MiniAppCategoryTree {

    private Long value;
    private String label;
    private List<MiniAppCategoryTree> children = new ArrayList<>();

    public MiniAppCategoryTree(Long value, String label) {
        this.value = value;
        this.label = label;
    }

    public void addChild(MiniAppCategoryTree child) {
        children.add(child);
    }
}
