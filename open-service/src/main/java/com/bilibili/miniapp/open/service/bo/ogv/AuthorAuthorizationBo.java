package com.bilibili.miniapp.open.service.bo.ogv;

import com.bilibili.miniapp.open.service.bo.account.AccountInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/3 16:41
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthorAuthorizationBo implements Serializable {
    private static final long serialVersionUID = 666574871258417206L;
    //小程序app_id
    private String appId;
    //mid
    private List<Long> midList;

    private List<AccountInfoBo> accounts;
}
