package com.bilibili.miniapp.open.service.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/26 17:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EpisodeBo implements Serializable {
    private static final long serialVersionUID = -3553483973671627078L;
    private Long seasonId;
    private Long episodeId;
    private Long sectionId;
    //集顺序，比如第1集，第2集
    private Integer ord;
    //这个比较随意，比如第1集，也可能是第一集，也可能是首集，主要看投稿怎么设置
    private String title;
    //类似于集描述，展示在标题的右侧
    private String longTitle;
    //集封面
    private String cover;
    private Long aid;
    private Long cid;
    @Nullable
    private Integer width;
    @Nullable
    private Integer height;

    /**
     * @see com.bilibili.miniapp.open.service.enums.SeasonPaymentStatus
     */
    @Nullable
    private Integer paymentStatus;

    private Long duration;
}
