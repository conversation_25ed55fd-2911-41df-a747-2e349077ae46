package com.bilibili.miniapp.open.service.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformSeasonInfoBo {
    private String cover;
    private Long epCount;
    /**
     * 0-免费，1-IAA，2-IAP
     */
    private Long paymentStatus;
    private Long seasonId;
    private String subTitle;
    private String title;
    private Boolean alreadyPublished;
}
