package com.bilibili.miniapp.open.service.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/2 20:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeasonAuthorizationBo implements Serializable {
    private static final long serialVersionUID = 666574871258417206L;
    //小程序app_id
    private String appId;
    //剧id
    private List<Long> seasonIdList;
    //内容创作者的mid
    @Nullable
    private List<Long> midList;
}
