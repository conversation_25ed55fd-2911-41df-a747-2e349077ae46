package com.bilibili.miniapp.open.service.bo.ogv;

import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/26 17:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SeasonBo implements Serializable {
    private static final long serialVersionUID = -3628555946242855187L;
    private Long seasonId;
    //剧集封面
    private String cover;
    //剧集标题
    private String title;
    //剧集子标题
    private String subTitle;
    //媒资风格
    private List<String> styles;
    //是否已完结，1：已完结，0：未完结，-1：未知
    private Integer isFinish;

    /**
     * @see com.bilibili.miniapp.open.service.enums.SeasonPaymentStatus
     */
    private int paymentStatus;

    @Nullable
    private Integer epCount;

    //作者（投稿人）
    @Nullable
    private UserInfoBo author;
    //分节信息
    //同一个season，分节类型可能有多个
    @Nullable
    private List<SectionBo> sections;
}
