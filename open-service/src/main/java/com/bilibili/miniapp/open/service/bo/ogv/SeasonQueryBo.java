package com.bilibili.miniapp.open.service.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/27 15:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SeasonQueryBo implements Serializable {
    private static final long serialVersionUID = -6122081600094651666L;
    private List<Long> seasonIdList;
    /**
     * 是否返回ep
     */
    private boolean appendEpisode;
    /**
     * 是否返回ep的稿件宽高
     * 如果为true，则前提是{@link #appendEpisode} = true才生效
     */
    private boolean appendVideoDimension;
    /**
     * 是否返回作者详细信息（头像和昵称）
     */
    private boolean appendAuthorDetail;
}
