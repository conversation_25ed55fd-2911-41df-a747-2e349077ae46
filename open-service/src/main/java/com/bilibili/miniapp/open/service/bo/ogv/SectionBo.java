package com.bilibili.miniapp.open.service.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.List;

/**
 * 分节信息，同一个season：
 * 1、正片的分节类型只会有一个
 * 2、其他分节类型可能有多个
 * 3、无论何种分节，其sectionId均是全局唯一
 *
 * <AUTHOR>
 * @date 2024/12/27 10:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SectionBo implements Serializable {
    private static final long serialVersionUID = -6993469224824092427L;
    // 分节id，全局唯一
    private Long sectionId;
    /**
     * 分节类型
     *
     * @see com.bilibili.miniapp.open.common.enums.OgvSectionType
     */
    private Integer sectionType;

    //集信息
    @Nullable
    private List<EpisodeBo> episodes;
}
