package com.bilibili.miniapp.open.service.bo.ogv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShortPlayBo implements Serializable {

    private static final long serialVersionUID = -175919021740319054L;

    private boolean isMiniProgram;

    private String jumpUrl;

    private String errorInfo;
}
