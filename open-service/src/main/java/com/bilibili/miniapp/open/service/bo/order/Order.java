package com.bilibili.miniapp.open.service.bo.order;

import com.bilibili.miniapp.open.common.enums.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2025/1/14 20:46
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Order implements Serializable {
    private static final long serialVersionUID = 8404867013763159589L;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者订单id
     */
    private String devOrderId;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * mid
     */
    private Long mid;

    /**
     * 创建订单使用的ak
     */
    private String accessKey;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 订单金额
     */
    private Long amount;

    /**
     * 支付金额，回调时会回写
     */
    private Long payAmount;

    /**
     * 结算比，0-100，默认100即pay_amount均可结算
     * <p>
     * 可给开发者结算的比例，比如B币支付，实际可结算的金额等于payAmount * (settleRatio / 100)
     */
    private Integer settleRatio;

    /**
     * 分销比，0-100，表示平台的分账比例
     * <p>
     * 分销比，也是平台分账比，开发者实际结算提现金额=payAmount * (settleRatio / 100) * (1 - distRatio / 100)
     */
    private Integer distRatio;

    /**
     * 订单状态
     *
     * @see OrderStatus
     */
    private Integer orderStatus;

    /**
     * 支付状态
     *
     * @see PayStatus
     */
    private Integer payStatus;

    /**
     * 结算状态
     *
     * @see SettleStatus
     */
    private Integer settleStatus;

    /**
     * 回调状态
     *
     * @see NotifyStatus
     */
    private Integer notifyStatus;

    /**
     * 开发者支付回调接口
     */
    private String notifyUrl;

    /**
     * 支付中台的txid，回调时会回写
     */
    private String txId;

    /**
     * 平台类型，0：其他，1：iphone，2：android
     *
     * @see PlatformType
     */
    private Integer platform;

    /**
     * 支付渠道
     *
     * @see PayChannelType
     */
    private Integer payChannel;

    /**
     * 流量渠道
     *
     * @see SourceChannelType
     */
    private Integer sourceChannel;

    /**
     * trace_id，用于支付交易追踪
     */
    private String traceId;

    /**
     * 支付时间，回调时会回写
     */
    private Timestamp payTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 拓展信息
     */
    private OrderExtra extra;
}
