package com.bilibili.miniapp.open.service.bo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/14 20:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCreateReq implements Serializable {

    private static final long serialVersionUID = -6473116024979698999L;

    /**
     * 开发者订单id
     */
    private String devOrderId;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * 创建订单使用的ak
     */
    private String accessKey;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 订单金额
     */
    private Long amount;

    /**
     * 开发者支付回调接口
     */
    private String notifyUrl;

    /**
     * 开发者拓展信息
     */
    private String extraData;

//    /**
//     * 平台类型，0：其他，1：iphone，2：android
//     */
//    private Integer platform;
//
//    /**
//     * 流量渠道
//     */
//    private Integer sourceChannel;
}
