package com.bilibili.miniapp.open.service.bo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/14 22:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCreateRes implements Serializable {
    private static final long serialVersionUID = 2017247053424005215L;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者订单id
     */
    private String devOrderId;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * mid
     */
    private Long mid;
}
