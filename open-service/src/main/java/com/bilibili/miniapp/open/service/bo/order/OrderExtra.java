package com.bilibili.miniapp.open.service.bo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/16 21:11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderExtra implements Serializable {
    private static final long serialVersionUID = 507980914959405992L;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者透传的拓展信息
     */
    private String devExtraData;

    /**
     * trace信息
     */
    private String traceInfo;

    /**
     * 支付信息
     */
    private String payParamInfo;

    /**
     * 支付回调信息
     */
    private String payNotifyInfo;
}
