package com.bilibili.miniapp.open.service.bo.order;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.enums.PayStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付回调开发者的请求体，snake case规范，勿动！！！
 *
 * <AUTHOR>
 * @date 2025/1/18 15:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayNotifyDeveloperInfo implements Serializable {
    private static final long serialVersionUID = -8121485328858139706L;
    /**
     * 订单id
     */
    @Sign(key = "order_id")
    private String order_id;

    /**
     * 开发者订单id
     */
    @Sign(key = "dev_order_id")
    private String dev_order_id;

    /**
     * 订单金额
     */
    @Sign(key = "amount")
    private Long amount;

    /**
     * 支付金额，回调时会回写
     */
    @Sign(key = "pay_amount")
    private Long pay_amount;

    /**
     * 支付状态
     *
     * @see PayStatus
     */
    @Sign(key = "pay_status")
    private Integer pay_status;

    /**
     * 支付时间，回调时会回写
     */
    @Sign(key = "pay_time")
    private Long pay_time;

    /**
     * 支付渠道
     *
     * @see com.bilibili.miniapp.open.common.enums.PayChannelType
     */
    @Sign(key = "pay_channel")
    private Integer pay_channel;

    /**
     * 开发者透传的拓展信息
     * <p>
     * 需要签名
     */
    @Sign(key = "extra_data")
    private String extra_data;
}
