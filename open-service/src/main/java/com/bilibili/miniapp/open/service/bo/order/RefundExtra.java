package com.bilibili.miniapp.open.service.bo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/19 17:10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundExtra implements Serializable {
    private static final long serialVersionUID = 8505386392741439605L;

    /**
     * 退款批次id，如果后续支持部分退款，则同一订单的不同退款记录批次id不同
     */
    private Long refundId;

    /**
     * 开发者透传的拓展信息
     */
    private String devExtraData;

    /**
     * 退款请求信息
     */
    private String refundParamInfo;

    /**
     * 退款回调信息
     */
    private String refundNotifyInfo;
}
