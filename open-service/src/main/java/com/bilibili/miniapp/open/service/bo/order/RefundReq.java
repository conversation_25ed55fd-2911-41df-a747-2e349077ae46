package com.bilibili.miniapp.open.service.bo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/19 16:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundReq implements Serializable {
    private static final long serialVersionUID = 6324146696568417812L;
    /**
     * 开平订单id
     */
    private Long orderId;
    /**
     * 小程序id
     */
    private String appId;
    /**
     * 开发者订单号
     */
    private String devOrderId;
    /**
     * 开发者退款批次id
     */
    private String devRefundId;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 退款描述
     */
    private String refundDesc;

    /**
     * 退款回调地址
     */
    private String notifyUrl;

    /**
     * 拓展信息
     */
    private String extraData;

    /**
     * access info
     */
    private String accessKey;
}
