package com.bilibili.miniapp.open.service.bo.order;

import com.bilibili.miniapp.open.common.enums.RefundStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/19 16:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundRes implements Serializable {
    private static final long serialVersionUID = 3358215791708420178L;
    /**
     * 开平订单id
     */
    private Long orderId;
    /**
     * 开发者退款批次id
     */
    private String devRefundId;
    /**
     * 退款状态
     *
     * @see RefundStatus
     */
    private Integer refundStatus;
    /**
     * 退款金额，单位分
     */
    private Long refundAmount;

}
