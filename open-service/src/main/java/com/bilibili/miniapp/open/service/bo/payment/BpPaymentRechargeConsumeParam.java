package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.annotations.Sign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * B币支付接口
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=846771222">B币充值和消费</a>
 *
 * <AUTHOR>
 * @date 2025/1/16 17:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpPaymentRechargeConsumeParam implements Serializable {
    private static final long serialVersionUID = -7247465559494113633L;
    /**
     * mid
     */
    @Sign
    private Long uid;
    /**
     * 0.未知其他 1. IOS端 2. 安卓端 3.PC 4.H5 5.jsapi
     */
    @Sign
    private Integer platformType;
    /**
     * 业务方id
     */
    @Sign
    private Integer customerId;
    /**
     * 业务方订单号，最大长度64
     */
    @Sign
    private String orderId;
    /**
     * 业务订单创建时间，毫秒级时间戳
     */
    @Sign
    private Long orderCreateTime;
    /**
     * 交易失效时长，单位：秒
     */
    @Sign
    private Integer orderExpire;
    /**
     * 商品id
     */
    @Sign
    private String productId;
    /**
     * 需要消费的B币,
     * <p>
     * 注意：（单位:元）
     */
    @Sign
    private String payBp;
    /**
     * 支付订单显示标题
     * <p>
     * 最长128个字符
     */
    @Sign
    private String showTitle;
    /**
     * 交易扩展信息，业务方扩展json串，支付通知时原样返回
     * <p>
     * 该字段建议业务控制在128个字符内，否则支付侧保存时会报错
     */
    @Sign
    private String extData;
    /**
     * 设备标识， app uuid等
     */
    @Sign
    private String deviceInfo;
    /**
     * 用户端支付设备UA， pc/h5浏览器UA，ios-IOS、Android-ANDROID
     */
    @Sign
    private String createUa;
    /**
     * 支付完成后，异步通知商户服务支付信息
     */
    @Sign
    private String notifyUrl;
    //    private String returnUrl;
//    private String productUrl;
//    private String failUrl;
//    private String appName;
    /**
     * 请求标识id
     */
    @Sign
    private String traceId;
    /**
     * 时间戳，单位毫秒
     */
    @Sign
    private Long timestamp;
    /**
     * 签名校验类型，目前仅支持MD5
     */
    @Sign
    private String signType;
    /**
     * 接口版本，目前版本1.0
     */
    @Sign
    private String version;
    private String sign;
}
