package com.bilibili.miniapp.open.service.bo.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * B币快捷支付
 * <p>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957973">B币快捷支付</a>
 *
 * <AUTHOR>
 * @date 2025/1/18 21:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpQuickPayInfo implements Serializable {
    private static final long serialVersionUID = 6145965195992474107L;

    /**
     * 业务方id
     */
    private Integer customerId;

    /**
     * 业务方订单id
     */
    private String orderId;

    /**
     * 支付平台支付id
     */
    private Long txId;

    /**
     * 选择的支付渠道alipay-支付宝 wechat-微信 paypal-paypal
     */
    private String payChannel;

    /**
     * 支付设备渠道类型， 1 pc 2 webapp 3 app  4jsapi
     */
    private Integer deviceType;

    /**
     * 支付渠道参数串，支付渠道所需数据，app支付时取该参数调起支付渠道sdk
     */
    private String payChannelParam;

    /**
     * 支付渠道跳转url，pc、h5支付时取该参数跳转到支付渠道url
     */
    private String payChannelUrl;

    /**
     * 请求标识id
     */
    private String traceId;

    /**
     * 毫秒级服务器时间戳
     */
    private Long serverTime;

    /**
     * 非iOS余额，不包含券
     */
    private Long defaultBpAmount;

    /**
     * iOS余额
     */
    private Long iosBpAmount;

    /**
     * B币券余额
     */
    private Long payCouponAmount;

    /**
     * 可用余额
     */
    private Long availableBp;
}
