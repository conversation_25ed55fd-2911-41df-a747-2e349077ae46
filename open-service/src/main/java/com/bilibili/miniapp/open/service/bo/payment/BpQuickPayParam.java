package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.annotations.Sign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * B币快捷支付
 * <p>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957973">B币快捷支付</a>
 *
 * <AUTHOR>
 * @date 2025/1/18 20:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpQuickPayParam implements Serializable {
    private static final long serialVersionUID = 1196510098315613567L;
    /**
     * 业务方id
     */
    @Sign
    private Integer customerId;
    /**
     * 业务方订单号，max_length=30
     */
    @Sign
    private String orderId;

    /**
     * 用户mid
     */
    @Sign
    private Long uid;

    /**
     * 需要消费的B币
     * <p>
     * 注意：（单位:元）
     */
    @Sign
    private String payBp;

    /**
     * 业务订单创建时间，毫秒级时间戳
     */
    @Sign
    private String orderCreateTime;

    /**
     * 交易失效时长，秒为单位
     */
    @Sign
    private Integer orderExpire;

    /**
     * 商品id，max_length=64
     */
    @Sign
    private String productId;

    /**
     * 支付商品类型, 0： 为实物， 1 ：虚拟商品
     */
    @Sign
    private String productType;

    /**
     * 支付订单显示标题（用户可见，比如支付交易记录）不能使用特殊字符,  如果需要使用，请业务方自行测试验证
     * <p>
     * max_length=128
     */
    @Sign
    private String showTitle;

    /**
     * 是否支持使用使用b币券；默认会根据目前支付系统配置判断，除非明确传1不支持使用b币券
     */
    @Sign
    private Integer unsupportedBpCoupon;

    /**
     * Pc端-WEB，H5端-WAP，Ios端-IOS，Android端-ANDROID
     */
    @Sign
    private String device;

    /**
     * 设备标识， app uuid等
     */
    @Sign
    private String deviceInfo;

    /**
     * 支付设备渠道类型， 1 pc 2 H5 3 app 4公众号 5 业务方发起代扣扣款 6 微信小程序 7聚合二维码/OTT二维码 8:qq小程序
     */
    @Sign
    private Integer deviceType;

    /**
     * 请求标识id
     */
    @Sign
    private String traceId;

    /**
     * 交易扩展信息，业务方扩展json串，支付通知时原样返回
     */
    @Sign
    private String extData;

    /**
     * 毫秒级请求时间戳
     */
    @Sign
    private Long timestamp;

    /**
     * 支付完成后，异步通知商户服务支付信息
     * <p>
     * max_length=350
     */
    @Sign
    private String notifyUrl;

    /**
     * 支付货币类型，默认人民币CNY （币种表见文末）虚拟币币种请填写指定类型
     */
    @Sign
    private String feeType;

    /**
     * 签名校验类型，目前仅支持MD5
     */
    @Sign
    private String signType;

    /**
     * 接口版本，目前1.0
     */
    @Sign
    private String version;

    private String sign;
}
