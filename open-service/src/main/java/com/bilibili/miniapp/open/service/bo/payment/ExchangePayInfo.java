package com.bilibili.miniapp.open.service.bo.payment;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.enums.PayType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/14 22:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangePayInfo implements Serializable {
    private static final long serialVersionUID = -1303687507726806258L;

    /**
     * 支付参数，可能是支付参数也可能是充值参数(B币充值)
     * <p>
     * 注意：
     * 1、这个参数可能是充消一体接口返回的充值参数，后期可能会新增字段，并参与签名，因此我们不要使用具体类型去接收（可能导致丢数据），而是使用JSON接收然后透传给客户端
     * <p>
     * 2、实际字段数量>={@link PayParam}
     *
     * @see PayParam
     */
    private JSONObject payParam;

    /**
     * 支付类型
     * <p>
     * 0：SDK支付，此时pay_param即支付参数，此时pay_info用不到
     * <p>
     * 1：开平支付，此时用户确认后使用pay_info发起支付确认；
     * <p>
     * 2：需要充值后再支付，此时pay_param即充值参数，充值完成后，使用pay_info发起支付确认；
     *
     * @see PayType
     */
    private int payType;

    /**
     * 支付信息，端上确认支付时，需要透传给服务端的参数
     * <p>
     * 适用于【开平支付】
     */
    private OpenPayInfo payInfo;
}
