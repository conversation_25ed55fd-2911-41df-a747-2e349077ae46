package com.bilibili.miniapp.open.service.bo.payment;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.service.bo.order.OrderCreateRes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/14 22:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangePayInfoReq implements Serializable {
    private static final long serialVersionUID = -5237078348321524950L;
    /**
     * 创单后的结果
     */
    private OrderCreateRes order;
    /**
     * 小程序平台信息
     *
     * @see com.bilibili.miniapp.open.common.enums.PlatformType
     */
    private String platform;
    /**
     * 端上的代理信息
     */
    private String userAgent;

    private String buvid;
    /**
     * 支付上下文信息，包括一些归因信息
     */
    private JSONObject traceInfo;
}
