package com.bilibili.miniapp.open.service.bo.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 开平自身的支付确认信息（与支付中台无关）
 *
 * <AUTHOR>
 * @date 2025/1/20 15:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenPayConfirmInfo implements Serializable {
    private static final long serialVersionUID = 4932112011873574374L;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者订单id
     */
    private String devOrderId;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * mid
     */
    private Long mid;

    /**
     * 时间戳（毫秒），每次请求时，返回都不一样
     * <p>
     * 目前主要作用是散列加密的数据，不至于相同订单每次请求返回的加密数据一样
     */
    private Long ts;

    /**
     * 设备号，可为null/empty，
     * <p>
     * 目前主要作用是透传给B币支付
     */
    private String buvid;
}
