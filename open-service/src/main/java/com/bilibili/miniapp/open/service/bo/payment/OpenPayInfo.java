package com.bilibili.miniapp.open.service.bo.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 开平自身的支付信息（与支付中台无关）
 *
 * <AUTHOR>
 * @date 2025/1/20 15:46
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenPayInfo implements Serializable {
    private static final long serialVersionUID = 3655826251732377193L;
    /**
     * 提示标题
     */
    private String showTitle;

    /**
     * 提示内容
     */
    private String showContent;

    /**
     * 详细的支付订单信息
     */
    private OpenPayConfirmInfo confirmInfo;
}
