package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.enums.PayChannelType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付回调请求，驼峰规范，请勿轻易改动！！！
 * <p>
 * 这些参数只是目前开平用到的参数，实际参数可能比这个多
 * <p>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=*********">支付回调</a>
 *
 * <AUTHOR>
 * @date 2025/1/16 22:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PayNotifyInfo implements Serializable {
    private static final long serialVersionUID = 8138836003492941975L;
    /**
     * 业务id
     */
    private Integer customerId;
    /**
     * 业务方业务类型
     */
    private Integer serviceType;
    /**
     * 支付平台支付id
     */
    private String txId;
    /**
     * 业务方订单id，即开平订单id
     */
    private String orderId;
    /**
     * 支付设备渠道类型，  1 pc 2 h5 3 app 4jsapi 5 server 6小程序支付 7聚合二维码支付
     */
    private Integer deviceType;
    /**
     * 支付状态，SUCCESS（成功） | CONFIRMED（已确认）| FAIL (失败) ｜ PAY_CANCEL(取消)
     * <p>
     * 注：除了微信信用分用户确认后会通知CONFIRMED，其它渠道暂时仅通知 SUCCESS。
     * <p>
     * B币支付预下单模式，预下单后发起取消会通知PAY_CANCEL，超时未确认支付会自动取消通知PAY_CANCEL
     */
    private String payStatus;
    /**
     * 支付渠道id, 用户实际选择的支付实体渠道。(payChannel 代表笼统的微信、支付宝等第三方渠道， payChannelId 代表实际签约的实体渠道 id)
     */
    private String payChannelId;
    /**
     * 支付渠道，alipay(支付宝)、open_alipay(支付宝2.0)、ali_global（支付宝跨境）、wechat(微信) ,wx_global(微信跨境) ,paypal(paypal),
     * iap(In App Purchase)、qpay(QQ支付)、huabei(花呗支付)、ali_bank（网银支付）、bocom（交行信用卡支付）、bp（B币支付）、
     * ott(云视听小电视支付)、ali_withhold(支付宝代扣)、ali_period_withhold(支付宝周期性代扣)、wechat_score（微信信用分）、ali_huabei(花呗)、
     * ali_score(支付宝预授权) 、cmbPay(招行一网通支付) 、wechat_partner(微信服务商)
     *
     * @see PayChannelType
     */
    private String payChannel;
    /**
     * 支付渠道名称 如支付宝、微信、PayPal、IAP、QQ、花呗分期、网银支付、B币支付、花呗、招行一网通支付
     */
    private String payChannelName;
    /**
     * 支付渠道账号
     */
    private String payAccount;
    /**
     * 支付银行
     */
    private String payBank;
    /**
     * 货币类型，默认人民币CNY
     */
    private String feeType;
    /**
     * 实际支付金额（如果是虚拟币，则乘以100），单位为：分
     */
    private Long payAmount;
    /**
     * json字符串，具体可以参考https://info.bilibili.co/pages/viewpage.action?pageId=*********
     */
    private String payMsgContent;
    /**
     * 支付请求时的扩展json串
     */
    private String extData;
    /**
     * IAP代扣过期时间，毫秒值,业务方需要判断expiredTime的值，因为重复通知返回的expiredTime是一样的
     */
    private Long expiredTime;
    /**
     * 订单支付时间，格式：0000-00-00 00:00:00
     */
    private String orderPayTime;
    /**
     * 请求时间戳，毫秒
     */
    private Long timestamp;
    /**
     * 追踪id
     */
    private String traceId;
    /**
     * 签名类型 ，默认MD5
     */
    private String signType;
    private String sign;
}
