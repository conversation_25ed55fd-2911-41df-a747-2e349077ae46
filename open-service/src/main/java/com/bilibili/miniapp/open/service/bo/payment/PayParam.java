package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.annotations.Sign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付SDK需要的参数，不要动！！！
 * <p>
 * 1、<a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957974">现金：支付</a>
 * <p>
 * 2、<a href="https://info.bilibili.co/pages/viewpage.action?pageId=846771222">B币：发起充值并消费</a>
 *
 * <AUTHOR>
 * @date 2025/1/16 16:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayParam implements Serializable {
    private static final long serialVersionUID = 1196510098315613567L;
    /**
     * 业务方id
     */
    @Sign
    private Integer customerId;
    /**
     * 业务方订单号，max_length=30
     */
    @Sign
    private String orderId;
    /**
     * 业务方订单原始需支付金额，用于展示优惠幅度用，单位为分
     */
    @Sign
    private Long originalAmount;
    /**
     * 业务方订单实际需支付金额，单位为分
     */
    @Sign
    private Long payAmount;

    /**
     * 用户mid
     */
    @Sign
    private Long uid;

    /**
     * 业务订单创建时间，毫秒级时间戳
     */
    @Sign
    private String orderCreateTime;

    /**
     * 交易失效时长，秒为单位
     */
    @Sign
    private Integer orderExpire;

    /**
     * 商品id，max_length=64
     */
    @Sign
    private String productId;
    /**
     * 支付订单显示内容  不能使用特殊字符,  如果需要使用，请业务方自行测试验证
     * <p>
     * max_length=128
     */
    @Sign
    private String showContent;

    /**
     * 支付订单显示标题（用户可见，比如支付交易记录）不能使用特殊字符,  如果需要使用，请业务方自行测试验证
     * <p>
     * max_length=128
     */
    @Sign
    private String showTitle;
    /**
     * 设备标识， app uuid等
     */
    @Sign
    private String deviceInfo;
    /**
     * 支付设备渠道类型， 1 pc 2 H5 3 app 4公众号 5 业务方发起代扣扣款 6 微信小程序 7聚合二维码/OTT二维码 8:qq小程序
     */
    @Sign
    private Integer deviceType;
    /**
     * 用户端支付设备UA， pc/h5浏览器UA，ios-IOS、Android-ANDROID
     */
    @Sign
    private String createUa;

    /**
     * 请求标识id
     */
    @Sign
    private String traceId;
//    private String productUrl;
    /**
     * 横竖屏  0 ： 竖屏  1：横屏 2：自适应
     */
    @Sign
    private Integer orientation;
    /**
     * 交易扩展信息，业务方扩展json串，支付通知时原样返回
     */
    @Sign
    private String extData;
    /**
     * 毫秒级请求时间戳
     */
    @Sign
    private Long timestamp;
    /**
     * 业务方业务类型，用于业务方定制支付渠道，不同的serviceType可以配置成不同的支付渠道列表
     * <p>
     * 每个渠道列表可以自定义顺序
     */
    @Sign
    private Integer serviceType;
    /**
     * 支付成功后跳转的地址，支付宝h5和pc支付必传 微信h5支付必传
     * <p>
     * max_length=350
     */
    @Sign
    private String returnUrl;
    /**
     * 支付完成后，异步通知商户服务支付信息
     * <p>
     * max_length=350
     */
    @Sign
    private String notifyUrl;
    /**
     * 支付货币类型，默认人民币CNY （币种表见文末）虚拟币币种请填写指定类型
     */
    @Sign
    private String feeType;
    /**
     * 签名校验类型，目前仅支持MD5
     */
    @Sign
    private String signType;
    /**
     * 接口版本，目前1.0
     */
    @Sign
    private String version;

    private String sign;
}
