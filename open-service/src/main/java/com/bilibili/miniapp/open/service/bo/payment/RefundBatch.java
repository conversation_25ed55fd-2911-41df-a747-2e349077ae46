package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.enums.RefundStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 退款批次
 *
 * <AUTHOR>
 * @date 2025/1/19 21:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundBatch implements Serializable {

    private static final long serialVersionUID = 90621898657598412L;

    /**
     * 支付中台的本次退款批次id
     */
    private String refundNo;

    /**
     * 业务侧本次退款批次id，如开平
     */
    private String customerRefundId;

    /**
     * 退款状态
     *
     * @see RefundStatus
     */
    private String refundStatus;

    /**
     * 退款状态描述，如退款中
     *
     * @see RefundStatus
     */
    private String refundStatusDesc;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 退款金额
     */
    private Long refundAmount;

    /**
     * 本次退款B币券金额（单位 分）
     */
    private Long refundCounponAmount;

    /**
     * 本次退款非iOS B币金额（单位 分）
     */
    private Long defaultBpAmount;

    /**
     * 本次退款iOS B币金额（分）
     */
    private Long iosBpAmount;

    /**
     * 本次退款B币金额（分）
     */
    private Long refundBpAmount;

    /**
     * 业务侧透传的额外信息
     */
    private String extData;

    /**
     * 退款时间，yyyy-MM-dd HH:mm:ss
     */
    private String refundEndTime;
}
