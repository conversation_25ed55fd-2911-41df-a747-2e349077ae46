package com.bilibili.miniapp.open.service.bo.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957982">业务方发起退款</a>
 * 只有当errno为0，并且refundStatus是REFUND_CREATE或者REFUND_SUCCESS，表示发起退款成功，至于退款结果是成功还是失败，需要根据回调通知判断。
 *
 * <AUTHOR>
 * @date 2025/1/19 21:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundInfo implements Serializable {

    private static final long serialVersionUID = 8326973273602407246L;

    /**
     * 支付平台订单 id
     */
    private Long txId;
    /**
     * 业务方订单号，最大长度64
     */
    private String orderId;

    /**
     * 标识一次退款请求，同一笔交易多次退款需要保证唯一，不传表示全量退款，传表示批次退款。退款失败需要使用相同批次号进行退款
     */
    private String customerRefundId;

    /**
     * 支付平台本次退款id
     */
    private String refundId;

    /**
     * REFUND_CREATE或者REFUND_SUCCESS表示发起退款成功
     */
    private String refundStatus;

    /**
     * 退款状态描述，如退款中
     */
    private String refundStatusDesc;

    /**
     * 剩余金额
     */
    private Long remainAmount;

    /**
     * 退款次数
     */
    private Long refundTimes;

    /**
     * 业务自定义请求附加json串
     */
    private String extData;
}
