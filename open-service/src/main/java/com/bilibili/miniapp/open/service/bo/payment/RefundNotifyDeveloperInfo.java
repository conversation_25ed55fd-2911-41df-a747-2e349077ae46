package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.annotations.Sign;
import com.bilibili.miniapp.open.common.enums.RefundStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 退款回调开发者的请求体，snake case规范，勿动！！！
 *
 * <AUTHOR>
 * @date 2025/1/24 17:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundNotifyDeveloperInfo implements Serializable {
    private static final long serialVersionUID = -8121485328858139706L;
    /**
     * 订单id
     */
    @Sign(key = "order_id")
    private String order_id;

    /**
     * 开发者订单id
     */
    @Sign(key = "dev_order_id")
    private String dev_order_id;

    /**
     * 开发者退款批次id，如果后续支持部分退款，则同一订单的不同退款记录批次id不同
     */
    @Sign(key = "dev_refund_id")
    private String dev_refund_id;

    /**
     * 退款金额
     */
    @Sign(key = "refund_amount")
    private Long refund_amount;

    /**
     * 退款状态
     *
     * @see RefundStatus
     */
    @Sign(key = "refund_status")
    private Integer refund_status;

    /**
     * 退款时间
     */
    @Sign(key = "refund_time")
    private Long refund_time;

    /**
     * 开发者透传的拓展信息
     * <p>
     * 需要签名
     */
    @Sign(key = "extra_data")
    private String extra_data;
}
