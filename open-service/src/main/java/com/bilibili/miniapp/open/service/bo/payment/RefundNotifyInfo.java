package com.bilibili.miniapp.open.service.bo.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 退款回调
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957983">支付平台退款回调业务方</a>
 * <p>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957984">退款订单查询</a>
 *
 * <AUTHOR>
 * @date 2025/1/19 21:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundNotifyInfo implements Serializable {
    private static final long serialVersionUID = -4592774921207183090L;

    /**
     * 业务方id
     */
    private Integer customerId;

    /**
     * 支付平台订单 id
     */
    private Long txId;
    /**
     * 业务方订单号，最大长度64
     */
    private String orderId;

    /**
     * 退款次数
     */
    private Long refundCount;

    /**
     * 付款渠道
     */
    private Integer payChannel;

    /**
     * 付款渠道id
     */
    private Integer payChannelId;

    /**
     * 剩余可退金额
     */
    private Long leftAmount;

    private String sign;

    /**
     * 退款批次
     */
    private List<RefundBatch> batchRefundList;
}
