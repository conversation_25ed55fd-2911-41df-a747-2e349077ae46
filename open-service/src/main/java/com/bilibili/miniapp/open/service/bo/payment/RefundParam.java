package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.annotations.Sign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957982">业务方发起退款</a>
 * 只有当errno为0，并且refundStatus是REFUND_CREATE或者REFUND_SUCCESS，表示发起退款成功，至于退款结果是成功还是失败，需要根据回调通知判断。
 *
 * <AUTHOR>
 * @date 2025/1/19 21:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundParam implements Serializable {

    private static final long serialVersionUID = -593559732770245244L;

    /**
     * 业务方id
     */
    @Sign
    private Integer customerId;

    /**
     * 支付平台订单 id
     */
    @Sign
    private Long txId;
    /**
     * 业务方订单号，最大长度64
     */
    @Sign
    private String orderId;

    /**
     * 标识一次退款请求，同一笔交易多次退款需要保证唯一，不传表示全量退款，传表示批次退款。退款失败需要使用相同批次号进行退款
     */
    @Sign
    private String customerRefundId;

    /**
     * 支付金额,单位分
     */
    @Sign
    private Long totalAmount;

    /**
     * 本次退款金额,单位分
     */
    @Sign
    private Long refundAmount;

    /**
     * 退款原因描述
     */
    @Sign
    private String refundDesc;

    /**
     * 业务自定义请求附加json串
     */
    @Sign
    private String extData;

    /**
     * 退款异步通知接口
     */
    @Sign
    private String notifyUrl;

    /**
     * 退款类型
     * <p>
     * 0：原路退款给用户（默认值）
     * 1：退款转打款
     */
    @Sign
    private Integer refundType;

    /**
     * 请求标识id
     */
    @Sign
    private String traceId;
    /**
     * 时间戳，单位毫秒
     */
    @Sign
    private Long timestamp;
    /**
     * 签名校验类型，目前仅支持MD5
     */
    @Sign
    private String signType;
    /**
     * 接口版本，目前版本1.0
     */
    @Sign
    private String version;
    private String sign;
}
