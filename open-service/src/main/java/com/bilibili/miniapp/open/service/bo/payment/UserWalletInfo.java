package com.bilibili.miniapp.open.service.bo.payment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户钱包信息
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102958069">钱包余额接口</a>
 *
 * <AUTHOR>
 * @date 2025/1/18 21:04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserWalletInfo implements Serializable {

    private static final long serialVersionUID = -3026908871259484640L;

    /**
     * mid
     */
    private Long mid;
    /**
     * 总余额，包含（defaultBp+iosBp+couponBalance），单位元
     */
    private BigDecimal totalBp;
    /**
     * 剩余非IOS B币, 单位元
     */
    private BigDecimal defaultBp;
    /**
     * 剩余IOS B币，单位元
     */
    private BigDecimal iosBp;
    /**
     * 剩余 B 币券，单位元
     * <p>
     * 注意要消费b币券需要支付侧配置允许消费开关
     */
    private BigDecimal couponBalance;
    /**
     * 可用余额，单位元
     * <p>
     * 该值等于totalBp
     */
    private BigDecimal availableBp;
    /**
     * 不可用余额，单位元
     */
    private BigDecimal unavailableBp;
    /**
     * 不可用说明
     */
    private String unavailableReason;
    /**
     * C端用户提示信息，服务端调用可忽略
     */
    private String tip;
    /**
     * B币打通标志，业务调用可忽略
     */
    private Integer needShowClassBalance;
}
