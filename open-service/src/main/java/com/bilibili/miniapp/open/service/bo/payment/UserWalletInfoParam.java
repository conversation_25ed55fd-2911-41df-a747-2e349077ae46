package com.bilibili.miniapp.open.service.bo.payment;

import com.bilibili.miniapp.open.common.annotations.Sign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询用户钱包余额接口
 * <p>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102958069">查询用户钱包余额接口</a>
 *
 * <AUTHOR>
 * @date 2025/1/18 20:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserWalletInfoParam implements Serializable {
    private static final long serialVersionUID = 3916777206810954389L;

    /**
     * 业务方id
     */
    @Sign
    private Integer customerId;
    /**
     * 客户端类型 1. IOS端 2. 安卓端 3.PC 4.h5
     */
    @Sign
    private Integer platformType;
    /**
     * 用户mid
     */
    @Sign
    private Long mid;
    /**
     * 请求标识id
     */
    @Sign
    private String traceId;
    /**
     * 精确到毫秒的时间戳
     */
    @Sign
    private String timestamp;
    /**
     * 签名校验类型，目前仅支持MD5
     */
    @Sign
    private String signType;
    private String sign;
}
