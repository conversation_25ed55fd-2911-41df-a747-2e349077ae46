package com.bilibili.miniapp.open.service.bo.retry;

import com.bilibili.miniapp.open.common.enums.RetryStatus;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2025/1/17 21:13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenRetry implements Serializable {

    private static final long serialVersionUID = 6422557350054928070L;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 业务类型
     *
     * @see RetryBizType
     */
    private Integer bizType;

    /**
     * 重试所需要的业务信息，尽可能简短，json格式
     */
    private String bizData;

    /**
     * 重试时的请求id，因为统一业务场景会有多次重试任务，比如订单变更新消息可能会多次，则每次分配不同的id
     */
    private String reqId;


    /**
     * 重试状态，0：失败，1：重试成功，2：已废弃（达到重试上限后依然失败）
     *
     * @see RetryStatus
     */
    private Integer retryStatus;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 下次重试的时间
     */
    private Timestamp nextTime;
}
