package com.bilibili.miniapp.open.service.bo.retry;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.service.biz.retry.OpenRetryCallable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/17 21:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenRetryContext implements Serializable {

    private static final long serialVersionUID = 6815980971173667054L;
    /**
     * 业务id，一定存在
     */
    @Positive
    private long bizId;

    /**
     * 重试时的请求id，因为统一业务场景会有多次重试任务，比如订单变更新消息可能会多次，则每次分配不同的id
     * <p>
     * 鼓励业务方使用一些有意义的业务id进行填充（保证自己的业务是唯一的），否则请置为null/empty
     * <p>
     * 当调用方首次主动调用（业务主动调用），如果该字段为空，则【重试框架】会主动生成UUID
     *
     * @see java.util.Random
     */
    private String reqId;

    /**
     * 重试次数
     * <p>
     * 如果是系统自动重试，则此字段表示已经重试的次数（不包含当前执行行为），
     * <p>
     * 如果是调用方首次主动调用，则该字段默认是0（建议），当然了调用方可以根据自身情况赋值（建议不要）
     */
    private int retryCount;

    /**
     * 业务类型，一定存在
     */
    @NotNull
    private RetryBizType bizType;

    /**
     * 重试所需要的业务信息，尽可能简短
     * <p>
     * 如果是系统自动重试，则此字段不为null，如果未能成功从底表反序列化，则会使用默认的JSONObject（属性为空）
     * <p>
     * 如果是调用方首次主动调用，则该字段可能为null，此时如果{@link OpenRetryCallable#call(OpenRetryContext)}执行失败，则底表会持久化{}
     */
    @Nullable
    private JSONObject bizData;
}
