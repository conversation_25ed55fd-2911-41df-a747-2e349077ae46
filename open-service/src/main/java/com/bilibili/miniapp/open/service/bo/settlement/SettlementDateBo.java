package com.bilibili.miniapp.open.service.bo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 结算日期BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementDateBo {
    
    /**
     * 日期
     */
    private String date;
    
    /**
     * 汇联易预提单id
     */
    private String accrualId;
}
