package com.bilibili.miniapp.open.service.bo.up_info;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDetailBo {

    private long mid;
    private String userId;
    private boolean bindTel;
    private String hideTel;
    private long countryId;
    private String countryCode;
    private boolean bindEmail;
    private boolean emailVerified;
    private String hideEmail;
    private int status;
    private String joinIp;
    private long joinTime;
    private boolean isTourist;
    private boolean isFastReg;
    private boolean bindSafeQuestion;
    private boolean emptyPwd;
    private int deleted;
    private boolean isRIPUser;
    /**
     * 是否是繁体版SNS注册账号
     */
    private boolean isIntlSnsUser;

}
