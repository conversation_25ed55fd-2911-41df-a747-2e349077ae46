package com.bilibili.miniapp.open.service.bo.up_info;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description 用户敏感信息
 * @Date 2025/3/13
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSensitiveInfoBo {

    private long mid;

    private String userid;

    private String tel;

    private String cid;

    private String email;

    private String cname;

    /**
     * 加密后的信息
     */
    private String payload;
}
