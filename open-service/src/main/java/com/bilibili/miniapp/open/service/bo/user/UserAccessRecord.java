package com.bilibili.miniapp.open.service.bo.user;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppUserAccessLogPo;
import com.bilibili.miniapp.open.service.databus.entity.UserAccessMsg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 用户访问记录
 * @Date 2025/2/26
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAccessRecord {

    private Long mid;

    private String appId;

    private Timestamp ctime;

    private Timestamp mtime;

    public static UserAccessRecord fromPo(MiniAppUserAccessLogPo po) {
        UserAccessRecord userAccessRecord = new UserAccessRecord();
        userAccessRecord.setMid(po.getMid());
        userAccessRecord.setAppId(po.getAppId());
        userAccessRecord.setMtime(po.getMtime());
        return userAccessRecord;
    }

    public MiniAppUserAccessLogPo toPo() {
        return MiniAppUserAccessLogPo.builder()
                .appId(appId)
                .mid(mid)
                .mtime(mtime)
                .build();
    }

    public static UserAccessRecord fromMsg(UserAccessMsg msg) {
        return UserAccessRecord.builder()
                .appId(msg.getAppId())
                .mid(msg.getMid())
                .ctime(msg.getCtime() == 0 ? null : new Timestamp(msg.getCtime()))
                .mtime(msg.getMtime() == 0 ? null : new Timestamp(msg.getMtime()))
                .build();
    }
}
