package com.bilibili.miniapp.open.service.bo.user;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserRecentAccessDetail implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 小程序 appId
     */
    private String appId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 图标
     */
    private String icon;

    /**
     * 跳转链接，会根据用户上传小程序包判断使用的是新框架还是老框架
     */
    private String linkUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 简介
     */
    private String summary;

    /**
     * 最近访问时间
     */
    private Long recentVisitTime;
}