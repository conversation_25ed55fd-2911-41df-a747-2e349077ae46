package com.bilibili.miniapp.open.service.bo.youku;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/7
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YkOrderCallbackReq {
    private Long orderId;
    private String orderCode;
    private String orderType;
    private String userId;
    private String payAmt;
    private String payTime;
    private Long productId;
    private String productName;
    private String skuName;
    private String payType;
    private Integer refundState;
    private String refundTime;
    private String refundAmt;
    private Long signOrderId;
    private Integer signState;
    private String signOffDate;
    private String args;
    private String app;
    private Long time;
}
