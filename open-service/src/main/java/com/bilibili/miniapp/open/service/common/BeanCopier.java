package com.bilibili.miniapp.open.service.common;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
public class BeanCopier {


    public static <S, T> T copy(S s, Class<T> tClazz) {
        if (Objects.isNull(s)) {
            return null;
        }
        try {
            T t = tClazz.getConstructor().newInstance();
            BeanUtils.copyProperties(s, t);
            return t;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <S, T> List<T> copy(List<S> sources, Class<T> targetType) {
        if (CollectionUtils.isEmpty(sources)) {
            return Collections.emptyList();
        }
        return sources.stream().map(s -> {
            try {
                T t = targetType.getConstructor().newInstance();
                BeanUtils.copyProperties(s, t);
                return t;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
    }
}
