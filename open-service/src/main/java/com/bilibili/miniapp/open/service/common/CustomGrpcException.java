package com.bilibili.miniapp.open.service.common;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import lombok.Getter;

/**
 * 为 grpc自定义 抛出异常使用 让调用方感知错误码使用
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Getter
public class CustomGrpcException extends RuntimeException {
    private final int code;
    private final String desc;


    public CustomGrpcException(ErrorCodeType errorCodeType) {
        this.code = errorCodeType.getCode();
        this.desc = errorCodeType.getMessage();
    }

}
