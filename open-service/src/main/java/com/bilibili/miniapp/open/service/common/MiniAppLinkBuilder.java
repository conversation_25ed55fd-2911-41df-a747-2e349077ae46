package com.bilibili.miniapp.open.service.common;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.web.util.UriComponentsBuilder;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 小程序路径构建器（简化版）
 * 特性：
 * 1. 基础URL + 应用ID自动拼接
 * 2. 可选路径动态拼接
 * 3. 安全的参数编码机制
 * 4. 链式API配置
 */
public class MiniAppLinkBuilder {
    private String baseUrl;
    private String appId;
    private String path;
    private final Map<String, String> paramMappings = new LinkedHashMap<>();
    private final Map<String, Object> parameters = new LinkedHashMap<>();

    public MiniAppLinkBuilder baseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
        return this;
    }

    public MiniAppLinkBuilder appId(String appId) {
        this.appId = appId;
        return this;
    }

    /**
     * 设置路径（可选）
     * @param path 支持标准路径格式 "pages/detail" 或 "/pages/detail"
     */
    public MiniAppLinkBuilder path(String path) {
        this.path = path;
        return this;
    }

    /**
     * 配置参数映射关系
     * @param queryParam URL参数名
     * @param paramKey 调用方参数键
     */
    public MiniAppLinkBuilder mapParam(String queryParam, String paramKey) {
        paramMappings.put(queryParam, paramKey);
        return this;
    }

    /**
     * 注入参数值集合
     */
    public MiniAppLinkBuilder withParams(Map<String, Object> params) {
        parameters.putAll(params);
        return this;
    }

    public String build() {
        validateConfig();
        UriComponentsBuilder uriBuilder = createBaseBuilder();
        appendPathIfNeeded(uriBuilder);
        appendQueryParams(uriBuilder);
        return uriBuilder.toUriString();
    }

    private void validateConfig() {
        Objects.requireNonNull(baseUrl, "基础URL不能为空");
        Objects.requireNonNull(appId, "应用ID不能为空");
    }

    private UriComponentsBuilder createBaseBuilder() {
        String normalizedBase = baseUrl.endsWith("/")
                ? baseUrl + appId
                : baseUrl + "/" + appId;
        return UriComponentsBuilder.fromHttpUrl(normalizedBase);
    }

    private void appendPathIfNeeded(UriComponentsBuilder builder) {
        if (path != null && !path.trim().isEmpty()) {
            String normalizedPath = path.startsWith("/")
                    ? path.substring(1)
                    : path;
            builder.pathSegment(normalizedPath.split("/"));
        }
    }

    private void appendQueryParams(UriComponentsBuilder builder) {
        // 配置参数映射为空时，直接拼接参数
        if (MapUtils.isEmpty(paramMappings)) {
            parameters.forEach(builder::queryParam);
        }
        paramMappings.forEach((queryKey, paramKey) -> {
            Object value = parameters.get(paramKey);
            if (value != null) {
                builder.queryParam(queryKey, value);
            }
        });
    }
    public static void main(String[] args) {
        // 基础用例
        String url1 = new MiniAppLinkBuilder()
                .baseUrl("https://miniapp.bilibili.com/applet")
                .appId("bili7d0c88d96079f2f7")
                .build();
        System.out.println(url1);

        // 带路径用例
        String url2 = new MiniAppLinkBuilder()
                .baseUrl("https://miniapp.bilibili.com/appletx")
                .appId("bili7d0c88d96079f2f7")
                .path("pages/detail")
                .mapParam("bl_album_id", "seasonId")
                .mapParam("bl_episode_id", "episodeId")
                .withParams(Map.of("seasonId", 789, "episodeId", 456))
                .build();
        System.out.println(url2);

        // 含多级路径参数
        String url3 = new MiniAppLinkBuilder()
                .baseUrl("https://miniapp.bilibili.com/appletx")
                .appId("bili7d0c88d96079f2f7")
                .withParams(Map.of("source_from", 10001))
                .build();
        System.out.println(url3);

        // 含特殊字符参数
        String url4 = new MiniAppLinkBuilder()
                .baseUrl("https://miniapp.bilibili.com/applet")
                .appId("bili7d0c88d96079f2f7")
                .path("search")
                .withParams(Map.of("keyword", "java&python"))
                .build();
        System.out.println(url4);
    }
}
