package com.bilibili.miniapp.open.service.config;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/1
 **/

@Data
@Configuration
@WatchedProperties
@ConfigurationProperties
@PropertySource(value = {"classpath:app-path.properties"}, factory = PaladinPropertySourceFactory.class)
@Getter
public class AppPathConfigCenter {

    private Map<String, String> p = new HashMap<>();

    @Data
    public static class AppPathConfig {
        private String path;
        private Map<String, Object> params;
    }
}
