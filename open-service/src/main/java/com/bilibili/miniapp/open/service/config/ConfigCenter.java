package com.bilibili.miniapp.open.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Data
@Configuration
@WatchedProperties
@ConfigurationProperties("miniapp.open")
@PropertySource(value = {"classpath:miniapp-open-platform.properties"}, factory = PaladinPropertySourceFactory.class)
public class ConfigCenter {
    private String seasonMock;
    private String adMock;
    private String env;
    private List<String> icpWhiteList = new ArrayList<>();
    private ShortPlayConfig shortPlay = new ShortPlayConfig();
    private BackdoorConfig backdoor = new BackdoorConfig();
    private PaymentConfig payment = new PaymentConfig();
    private OrderConfig order = new OrderConfig();
    private CommentBlueLinkConfig commentBlueLink = new CommentBlueLinkConfig();
    private UserAccessConfig userAccess = new UserAccessConfig();
    private MiniAppConfig miniAppConfig = new MiniAppConfig();
    private MainSiteConfig mainSite = new MainSiteConfig();
    private YouKuConfig youKu = new YouKuConfig();
    private SeasonConfig seasonConfig = new SeasonConfig();
    private AppTemplateConfig appTemplateConfig = new AppTemplateConfig();
    private RobotConfig robotConfig = new RobotConfig();
    private EsConfig esConfig = new EsConfig();
    private RegulationConfig regulation = new RegulationConfig();
    private ContractConfig contractConfig = new ContractConfig();
}
