package com.bilibili.miniapp.open.service.config;

import com.bilibili.concurrent.metrics.ExecutorMetricsCollectorCache;
import com.bilibili.miniapp.open.common.util.TraceUtil;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pleiades.venus.config.WatchedProperties;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/28
 */

@Configuration
public class IaaSettlementConfiguration {



    @Bean
    public ThreadPoolExecutor iaaSettlementThreadPoolExecutor(){
        final ThreadPoolExecutor executor= new ThreadPoolExecutor(
                8,
                100,
                10,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), "IAA_SETTLE_EXEC"),
                new ThreadPoolExecutor.AbortPolicy());

        ExecutorMetricsCollectorCache.addThreadPoolExecutor("IAA_SETTLE_EXEC", executor);

        return executor;
    }

    @Bean
    public ThreadPoolExecutor iaaDailyScheduleExecutor(){
        final ThreadPoolExecutor executor= new ThreadPoolExecutor(
                8,
                100,
                10,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(TraceUtil.decorate(r), "IAA_SCHEDULE_EXEC"),
                new ThreadPoolExecutor.AbortPolicy());

        ExecutorMetricsCollectorCache.addThreadPoolExecutor("IAA_SCHEDULE_EXEC", executor);

        return executor;
    }


    /**
     * <AUTHOR>
     * @desc
     * @date 2025/3/19
     */
    @Data
    @Configuration
    @ConfigurationProperties(prefix = "iaa.settlement")
    @WatchedProperties
    public static class IaaSettlementConfig {


        private String crmChargeBillTitle = "%s广告金激励";


        private String huilianyiServiceName = "sycpb.miniapp.open-platform";

        private String huilianyiSecret = "591a61b7-dld7-11xc-80fe-fa163e5ccbeb";


        /**
         * 汇联易token缓存时间 中间层保留2h，这里金保留1h
         */
        private Integer huilianyiTokenCacheSeconds = 3600;


        /**
         * 扫表的批次大小
         */
        private Integer reSettleBatchSize = 200;

        private Integer scanHistorySettlementsBatchSize = 2000;

        private Integer scanHistoryWithdrawBillBatchSize = 2000;


        private Integer huilianyiAsyncApiProbeMaxTimes = 120;


        // 建议10min 最大
        private Integer huilianyiAsyncApiProbeIntervalSeconds = 5;


        /**
         * 预提单 业务参数
         */
        private HuilianyiAccrualParams accrualParams = new HuilianyiAccrualParams();

        private String taxRate = "0.06";


        /**
         * 付款单 业务参数
         */
        private HuilianyiExpenseParams expenseParams = new HuilianyiExpenseParams();


        private String accrualBusinessCodeTpl = "IAA_YT_%s";

        private String expenseBusinessCodeTpl = "IAA_FK_%s";

        private String expenseBusinessCodePrefix = "IAA_FK_";


        private GameOpenPlatformParams gameOpenPlatformParams = new GameOpenPlatformParams();

        private Boolean skipCheckInvoiceBusinessEntity = false;

        private Boolean checkSettlementAndBillAmtWhenApplyWithdraw = true;

        private Boolean forbidWithdrawWhenAmtNotMatch = false;


        private Integer ocrParallelTimeoutMillis = 5000;


        private Integer gameOpenPlatformApiRetryTimes = 3;

        private Integer gameOpenPlatformApiRetryIntervalMillis = 1000;

        private Boolean miniGameBusinessCacheEnabled = true;

        /**
         * 缓存时间 1h
         */
        private Integer miniGameBusinessEntityCacheSeconds = 3600;

        /**
         * 缓存时间 1h
         */
        private Integer miniGameBindingAccountCacheSeconds = 3600;

        /**
         * 小游戏发票和实际金额最大差值
         */
        private String miniGameInvoiceAndActualAmtMaxDiff = "0";


        private Map<String, String> accrualPeriodMagicReplacement = Map.of("202504", "202504");


        @Data
        @Accessors(chain = true)

        public static class HuilianyiAccrualParams {


            private String formCode = "xgjt";

            private String applicantEmployeeId4MiniGame = "037758";

            // TODO
            private String applicantEmployeeId4MiniApp = "gly";

            private Integer status = 1002;

            private String reason = "IAA业务预提";

            private String company = "1110";

            private String department = "-11452";

            private String taxrate = "0.06";

            private String costCentre4MiniGame = "SY070500";

            // TODO
            private String costCentre4MiniApp = "SY03050200";

            @Deprecated
            private String payee = "接口获取供应商编码";

            private String prepay = "false";

            private String expenseTypeCode = "EXP461319";

            /**
             * sample 9999.99
             */
            @Deprecated
            private String amount = "半月预提金额";

        }


        @Data
        @Accessors(chain = true)
        public static class HuilianyiExpenseParams {

            private String formCode = "xg-zfd";

            private String applicantEmployeeId4MiniGame = "037758";

            // TODO
            private String applicantEmployeeId4MiniApp = "gly";

            //        private String accountNumber = "对应供应商的银行账号";

            private String paymentMethodCode = "0101";

            private Boolean autoSubmit = true;

            private String company = "1110";

            private String department4MiniGame = "-11452";

            // TODO
            private String department4MiniApp = "-11452";

            private String costCentre4MiniGame = "SY070500";

            // TODO
            private String costCentre4MiniApp = "SY03050200";

            private String pastPeriod = "1";

            private String currency = "CNY";

            private String fullPayout = "true";

            private String prepay = "false";

            private Integer paymentScheduleType = 1001;

            @Deprecated
            private String realPaymentAmount = "400";


            private String expenseTypeCode = "EXP461319";

            @Deprecated
            private String occurrenceDate = "提单日期";


            private String invoiceCurrencyCode = "CNY";

            @Deprecated
            private String amount = "本次付款的金额";

            private String costCenter = "SY070500";


        }


        @Data
        @Accessors(chain = true)
        public static class GameOpenPlatformParams {


            private String appkey = "aaed7d28849a915ff";

            private String appsecret = "636bb59dd8074d6f991a7a460e76f573";


        }
    }
}
