package com.bilibili.miniapp.open.service.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/

@Data
public class MainSiteConfig {

    // 隐私信息密钥
    private String sensitiveEncryptKey;
    private String sensitiveEncryptIv;

    private long userDetailTimeout = 500;

    private long preAuthCodeExpire = 5 * 60;

    private long userSensitiveInfoTimeout = 500;
}
