package com.bilibili.miniapp.open.service.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/3
 **/

@Data
public class MiniAppConfig {

    /**
     * 小程序基础信息缓存过期时间（分钟）
     */
    private Integer expireTime = 5;

    /**
     * 过期时间随机百分比
     */
    private double expireJitterPercent = 0.15;

    /**
     * 创建小程序默认最低版本
     */
    private String minVersion = "4.0.4";

    /**
     * 手机号授权登录白名单
     */
    private List<String> authPhoneWhiteList = new ArrayList<>();

    /**
     *  手机号加密密钥
     */
    private String phoneEncryptKey;

    /**
     * 模板小程序默认域名白名单
     */
    private String defaultWhiteDomains = "[\"https://miniapp.bilibili.com\"]";
}
