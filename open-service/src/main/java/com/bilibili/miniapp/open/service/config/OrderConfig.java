package com.bilibili.miniapp.open.service.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/16 23:08
 */
@Data
public class OrderConfig {
    //https://doc.weixin.qq.com/doc/w3_AGEACAbzADgMxHpg6T8Q0ioC2uHHr?scode=ANYAEAdoABET3bjqtJAVAAMQZxACU
    //自然流量：30%分销比
    private Integer distRatio = 30;
    //B币支付的结算比，可给开发者结算的比例，实际可结算的金额等于payAmount * (settleRatio / 100)
    private Integer bpSettleRatio = 70;
}
