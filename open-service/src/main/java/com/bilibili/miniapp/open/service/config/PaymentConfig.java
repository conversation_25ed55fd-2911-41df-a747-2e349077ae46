package com.bilibili.miniapp.open.service.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/16 16:00
 */
@Data
public class PaymentConfig {
    //（开平用）用于加密预支付信息的密钥
    private String openPayEncryptKey;
    //（开平用）用于加密预支付信息的向量
    private String openPayEncryptIv;
    //（开平用）用于支付确认提示
    private String openPayShowTitle = "确认订单并支付";
    //（支付中台用）业务方id
    private Integer customerId;
    //（支付中台用）服务类型
    private Integer payServiceType;
    //（支付中台用）支付有效期，单位秒，默认15分钟
    private Integer payPeriod = 900;
    //（支付中台用）支付回调地址（内网地址）
    private String payNotifyUrl = "https://miniapp.bilibili.co/open/web_api/v1/platform/payment/notify";
    //（支付中台用）支付中台分配的签名token
    private String payToken;
    //（支付中台用）退款回调地址（内网地址）
    private String refundNotifyUrl = "https://miniapp.bilibili.co/open/web_api/v1/platform/payment/refund/notify";
}
