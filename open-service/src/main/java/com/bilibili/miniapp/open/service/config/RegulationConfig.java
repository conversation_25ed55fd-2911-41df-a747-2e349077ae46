package com.bilibili.miniapp.open.service.config;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/

@Data
public class RegulationConfig {

    /**
     * 调用regulation-api的token
     */
    private String token = "game123@bili";

    private String host = "https://game-regulation-api-dev.biligame.com";
//    private String host = "http://localhost:8082";

    private List<Long> abortGovCodes = List.of(2002L, 2029L);
}
