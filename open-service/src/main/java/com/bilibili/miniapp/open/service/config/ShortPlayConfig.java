package com.bilibili.miniapp.open.service.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/2 12:07
 */
@Data
public class ShortPlayConfig {
    //剧查询超时时间，单位毫秒
    private long seasonTimeout = 500;
    //集查询超时时间，单位毫秒
    private long episodeTimeout = 500;
    //稿件查询超时时间，单位毫秒
    private long archiveTimeout = 500;
    //用户查询超时时间，单位毫秒
    private long userTimeout = 500;
    //查询是否是小程序渠道短剧，单位毫秒
    private long isMiniAppTimeout = 500;
    //短剧授权的小程序缓存时间，单位分钟
    private int seasonAuthCacheTime = 5;
}
