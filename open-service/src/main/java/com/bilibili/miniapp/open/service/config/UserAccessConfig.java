package com.bilibili.miniapp.open.service.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Data
public class UserAccessConfig {

    // 最近访问最大记录数
    private Integer maxRecent = 500;

    private Integer recentDays = 90;

    private String jumpUrlBase_0 = "https://miniapp.bilibili.com/applet";

    private String jumpUrlBase_1 = "https://miniapp.bilibili.com/appletx";
}
