/**
 * 一、动态配置形式说明
 * 方式1、@DynamicValue + @Value注解形式
 * 注意：
 * （1）@Value中需要指定完整的配置名
 * （2）如果对应的配置项不属于application文件的，则需要依赖-Dkraken.paladin.namespaces=f1,f2，其中f1和f2即为配置项所在文件（不包含文件后缀）
 * <p>
 * 方式2、@WatchedProperties + @ConfigurationProperties(prefix = "xxx")形式
 * 注意：
 * （1）配置类的属性名必须等于最后的配置名，比如prefix="xx.yy.zz"，fieldName=age，那么此时的完整配置项=xx.yy.zz.age才能正常注入age
 * （2）如果对应的配置项不属于application文件的，则需要依赖-Dkraken.paladin.namespaces=f1,f2，其中f1和f2即为配置项
 * * 所在文件（不包含文件后缀），或者使用@PropertySource(value = {"classpath:xxx.xxx"}, factory = PaladinPropertySourceFactory.class)
 * <p>
 * 参考文档：https://info.bilibili.co/display/INF/warp-paladin
 * <p>
 * <p>
 * 二、其他
 *
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
package com.bilibili.miniapp.open.service.config;