package com.bilibili.miniapp.open.service.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.crt.S3CrtHttpConfiguration;
import software.amazon.awssdk.services.s3.internal.crt.S3CrtAsyncClient;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2024/8/27 11:01
 */
@Configuration
public class BossConfiguration {

    @Bean(name = "s3AsyncClient")
    public S3AsyncClient registerS3AsyncClient(BossConfigurationProperties properties) {
        AwsBasicCredentials credentials = AwsBasicCredentials.create(properties.getAccessKey(), properties.getSecretKey());
        StaticCredentialsProvider provider = StaticCredentialsProvider.create(credentials);
        S3Configuration config = S3Configuration.builder().pathStyleAccessEnabled(true).build();
        return S3AsyncClient.builder()
                .endpointOverride(URI.create(properties.getUploadHost()))
                .credentialsProvider(provider)
                .region(Region.of("boss"))
                .serviceConfiguration(config)
                .build();
    }

    @Bean(name = "s3Client")
    public S3Client registerS3Client(BossConfigurationProperties properties) {
        AwsBasicCredentials credentials = AwsBasicCredentials.create(properties.getAccessKey(), properties.getSecretKey());
        StaticCredentialsProvider provider = StaticCredentialsProvider.create(credentials);
        S3Configuration config = S3Configuration.builder().pathStyleAccessEnabled(true).build();
        return S3Client.builder()
                .endpointOverride(URI.create(properties.getUploadHost()))
                .credentialsProvider(provider)
                .region(Region.of("boss"))
                .serviceConfiguration(config)
                .build();
    }


    @Data
    @Configuration
    @PropertySource(value = {"classpath:boss.properties"}, factory = PaladinPropertySourceFactory.class)
    @ConfigurationProperties(prefix = "boss")
    public static class BossConfigurationProperties {
        private String uploadHost;
        private String downloadHost;
        private String accessKey;
        private String secretKey;
        private String bucket;
        private String region;
    }
}
