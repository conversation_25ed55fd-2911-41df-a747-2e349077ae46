package com.bilibili.miniapp.open.service.configuration;

import com.bilibili.miniapp.open.service.biz.resource.impl.BFSClient;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.processor.LargeJsonProcessor;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Configuration
public class OpenServiceConfiguration {

    @Bean
    public BFSClient buildBFSClient(BFSConfigurationProperties properties) {
        return new BFSClient(properties.getHost(), 0,
                properties.getAccessKey(), properties.getAccessSecret(),
                properties.getBucketName());
    }

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobSpringExecutor buildXxlJobSpringExecutor(XXLConfigurationProperties xxlConfigurationProperties) {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAppName(xxlConfigurationProperties.getAppName());
        xxlJobSpringExecutor.setAdminAddresses(xxlConfigurationProperties.getAdminAddress());
        xxlJobSpringExecutor.setAccessToken(xxlConfigurationProperties.getAccessToken());
        xxlJobSpringExecutor.setPort(xxlConfigurationProperties.getPort());
        xxlJobSpringExecutor.setLogPath(xxlConfigurationProperties.getLogPath());
        xxlJobSpringExecutor.setLogRetentionDays(xxlConfigurationProperties.getLogRetentionDays());
        return xxlJobSpringExecutor;
    }

    @Bean
    public LargeJsonProcessor largeJsonProcessor(ConfigCenter configCenter) {
        return new LargeJsonProcessor(configCenter);
    }


    @Data
    @Configuration
    @PropertySource(value = {"classpath:job.properties"}, factory = PaladinPropertySourceFactory.class)
    @ConfigurationProperties("xxl.job")
    static class XXLConfigurationProperties {
        private String adminAddress;
        private String accessToken;
        private Integer port;
        private String logPath;
        private Integer logRetentionDays;
        private String appName;
    }


    @Data
    @Configuration
    @PropertySource(value = {"classpath:bfs.properties"}, factory = PaladinPropertySourceFactory.class)
    @ConfigurationProperties("bfs")
    static class BFSConfigurationProperties {
        private String host;
        private String bucketName;
        private String accessKey;
        private String accessSecret;
    }
}
