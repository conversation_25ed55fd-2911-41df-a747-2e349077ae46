package com.bilibili.miniapp.open.service.databus;

import com.bilibili.business.cmpt.idatabus.client.spring.bean.DataBusConfigBean;
import com.bilibili.business.cmpt.idatabus.client.spring.core.AbstractDataBusConfigLoader;
import com.bilibili.warp.paladin.ConfigSourceHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Slf4j
public class OpenPlatformDataBusConfigLoader extends AbstractDataBusConfigLoader {
    @Override
    protected DataBusConfigBean doLoad() {
        Optional<ByteArrayResource> resource = ConfigSourceHolder.get()
                .getNullableEntry("idatabus")
                .map(entry -> new ByteArrayResource(
                        entry.getRawValue().getBytes(StandardCharsets.UTF_8),
                        String.format("content from paladin key: %s", "idatabus")));
        if (resource.isPresent()) {
            try {
                return new Yaml(new Constructor(DataBusConfigBean.class)).load(resource.get().getInputStream());
            } catch (IOException e) {
                log.error("[MiniAppOpenPlatformDataBusConfigLoader]:fail to load DataBusConfig from location");
                throw new RuntimeException(e);
            }
        }
        log.error("[MiniAppOpenPlatformDataBusConfigLoader]:fail to load DataBusConfig from location");
        throw new RuntimeException("fail to load DataBusConfig from location");
    }
}
