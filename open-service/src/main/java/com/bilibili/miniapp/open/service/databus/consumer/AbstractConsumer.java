package com.bilibili.miniapp.open.service.databus.consumer;

import com.alibaba.fastjson.JSON;
import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.Consumer;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.miniapp.open.common.util.TraceUtil;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/01/118 14:34
 */
@Slf4j
public abstract class AbstractConsumer<Msg> implements Consumer<Msg> {
    protected abstract void doConsume(Msg msg, ConsumeMessageContext ctx) throws Exception;

    @Override
    public void consume(Msg msg, ConsumeMessageContext ctx) throws Exception {
        Transaction tx = Cat.getProducer().newTransaction("AbstractConsumer", getName());
        Exception e = null;
        try {
            MDC.put(TraceUtil.TRACE_ID, TraceUtil.genTraceId());
            if (logEnable()) {
                log.info("AbstractConsumer [{}] start, msg:{}", getName(), JSON.toJSONString(msg));
            }
            doConsume(msg, ctx);
            tx.setStatus(Transaction.SUCCESS);
        } catch (Exception ex) {
            log.error("AbstractConsumer [{}] error", getName(), ex);
            tx.setStatus("1");//error
            e = ex;
            throw ex;
        } finally {
            tx.complete();
            if (logEnable()) {
                log.info("AbstractConsumer [{}] end, cost:{}", getName(), tx.getDurationInMillis() / 1000);
            }
            if (Objects.isNull(e) && ackIfNecessary()) {
                ctx.ack();
            }
            MDC.remove(TraceUtil.TRACE_ID);
        }
    }

    protected String getName() {
        DataBusConsumer annotation = this.getClass().getAnnotation(DataBusConsumer.class);
        return Objects.isNull(annotation) ? "unknown" : annotation.value();
    }

    protected boolean logEnable() {
        return true;
    }

    /**
     * 如果没有出现异常，是否可以ack
     * 注意：如果consumer本身配置的是autoCommit=true，则该方法无论返回true or false，都会被ack
     */
    protected boolean ackIfNecessary() {
        return false;
    }
}
