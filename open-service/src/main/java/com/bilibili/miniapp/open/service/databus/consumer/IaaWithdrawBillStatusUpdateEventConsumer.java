package com.bilibili.miniapp.open.service.databus.consumer;

import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.miniapp.open.service.databus.entity.IaaWithdrawBillEventMsg;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/26
 */
@Slf4j
@Service
public class IaaWithdrawBillStatusUpdateEventConsumer  implements MessageListener {

    private final DatabusProperties databusProperties;
    private String topic;

    private String group;

    public static final String configKey = "iaa-withdraw-bill";


    public IaaWithdrawBillStatusUpdateEventConsumer(DatabusProperties databusProperties) {
        this.databusProperties = databusProperties;
        this.topic = databusProperties.getProperties().get(configKey).getTopic();
        this.group = databusProperties.getProperties().get(configKey).getSub().getGroup();
    }

    @Override
    public String topic() {
        return topic;
    }

    @Override
    public String group() {
        return group;
    }

    @Override
    public boolean autoCommit() {
        return true;
    }

    @Override
    public void onMessage(AckableMessage message) {

        Try.run(() -> {

            log.info("IaaWithdrawBillStatusUpdateEventConsumer doConsume, only print={}",
                    new String(message.payload())

            );

        }).onFailure(t -> {
            log.error("Fail to process refresh job broadcast msg", t);
        });
    }
}
