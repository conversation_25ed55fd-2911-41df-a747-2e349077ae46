package com.bilibili.miniapp.open.service.databus.consumer;

import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.miniapp.open.service.biz.user.impl.UserAccessService;
import com.bilibili.miniapp.open.service.bo.user.UserAccessRecord;
import com.bilibili.miniapp.open.service.databus.entity.UserAccessMsg;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Component
@DataBusConsumer("MiniAppUserAccessConsumer")
public class MiniAppUserAccessConsumer extends AbstractConsumer<UserAccessMsg>{

    @Resource
    private UserAccessService userAccessService;

    @Override
    protected boolean ackIfNecessary() {
        return true;
    }

    @Override
    protected void doConsume(UserAccessMsg userAccessMsg, ConsumeMessageContext ctx) throws Exception {
        userAccessService.recordUserAccessForDb(UserAccessRecord.fromMsg(userAccessMsg));
    }
}
