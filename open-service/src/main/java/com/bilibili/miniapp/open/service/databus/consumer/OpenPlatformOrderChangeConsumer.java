package com.bilibili.miniapp.open.service.databus.consumer;

import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.miniapp.open.service.databus.entity.OrderMsg;
import lombok.extern.slf4j.Slf4j;

/**
 * 开平订单状态变更消息
 *
 * <AUTHOR>
 * @date 2025/1/18 14:31
 */
@Slf4j
@DataBusConsumer("OpenPlatformOrderChangeConsumer")
public class OpenPlatformOrderChangeConsumer extends AbstractConsumer<OrderMsg> {
    @Override
    protected boolean ackIfNecessary() {
        return true;
    }

    @Override
    protected void doConsume(OrderMsg orderMsg, ConsumeMessageContext ctx) throws Exception {
        //do nothing
    }
}
