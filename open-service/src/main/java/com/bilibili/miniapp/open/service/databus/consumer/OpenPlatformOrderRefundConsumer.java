package com.bilibili.miniapp.open.service.databus.consumer;

import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.miniapp.open.common.enums.RefundStatus;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.service.biz.payment.IPaymentService;
import com.bilibili.miniapp.open.service.biz.retry.IOpenRetryService;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;
import com.bilibili.miniapp.open.service.databus.entity.RefundMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 开平订单退款变更消息
 *
 * <AUTHOR>
 * @date 2025/1/19 19:48
 */
@Slf4j
@DataBusConsumer("OpenPlatformOrderRefundConsumer")
public class OpenPlatformOrderRefundConsumer extends AbstractConsumer<RefundMsg> {
    @Autowired
    private IOpenRetryService openRetryService;
    @Autowired
    private IPaymentService paymentService;

    @Override
    protected boolean ackIfNecessary() {
        return true;
    }

    @Override
    protected void doConsume(RefundMsg orderRefundMsg, ConsumeMessageContext ctx) throws Exception {
        RefundStatus refundStatus = RefundStatus.getByCodeWithoutEx(orderRefundMsg.getRefundStatus());
        switch (refundStatus) {
            case CREATE_INIT: {
                //如果刚发起开平退款，则发起支付中台退款
                //如果支付中台返回REFUND_CREATE，则更新退款记录
                //如果支付中台返回CREATE_SUCCESS，则更新退款记录同时回调开发者
                paymentService.refundWithPayPlatform(orderRefundMsg.getOrderId(), orderRefundMsg.getDevRefundId());
                break;
            }
            case CREATE_SUCCESS:
            case CREATE_FAILED: {
                //CREATE_SUCCESS：此时等待支付中台的退款回调即可，暂时无需处理
                //CREATE_FAILED：发起支付中台退款失败，业务不可达，终态，暂时无需处理
                break;
            }
            case SUCCESS:
            case FAILED: {
                //更新退款记录同时回调开发者SUCCESS/FAILED
                OpenRetryContext openRetryContext = OpenRetryContext.builder()
                        .bizType(RetryBizType.ORDER_REFUND_NOTIFY_DEVELOPER)
                        .bizId(orderRefundMsg.getOrderId())
                        .reqId(orderRefundMsg.getDevRefundId())
                        .build();
                openRetryService.retry(openRetryContext);
                break;
            }
            default:
        }
    }
}
