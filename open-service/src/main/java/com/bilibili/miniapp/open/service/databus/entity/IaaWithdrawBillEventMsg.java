package com.bilibili.miniapp.open.service.databus.entity;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import com.bilibili.miniapp.open.service.biz.settlement.vo.IaaWithdrawBillRecordDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class IaaWithdrawBillEventMsg implements SnakeCaseBody {

    private String type = "withdraw_bill_status_updated";

    private IaaWithdrawBillRecordDTO data;

}
