package com.bilibili.miniapp.open.service.databus.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小程序激活上报消息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniAppActivationMsg {

    /**
     * 用户ID
     */
    private Long mid;
    
    /**
     * 设备标识
     */
    private String buvid;
    
    /**
     * 创建时间（时间戳）
     */
    private Long ctime;
    
    /**
     * 追踪ID
     */
    private String trackId;
    
    /**
     * 来源
     */
    private String sourceFrom;
    
    /**
     * 小程序ID
     */
    private String appId;
}
