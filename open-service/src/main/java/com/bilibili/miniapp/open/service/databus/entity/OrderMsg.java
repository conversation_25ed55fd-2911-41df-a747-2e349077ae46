package com.bilibili.miniapp.open.service.databus.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/1/14 22:16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderMsg {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者订单id
     */
    private String devOrderId;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * mid
     */
    private Long mid;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 订单金额
     */
    private Long amount;

    /**
     * 支付金额，回调时会回写
     */
    private Long payAmount;

    /**
     * 结算比，0-100，默认100即pay_amount均可结算
     */
    private Integer settleRatio;

    /**
     * 分销比，0-100，表示平台的分账比例
     */
    private Integer distRatio;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 结算状态
     */
    private Integer settleStatus;

    /**
     * 平台类型，0：其他，1：iphone，2：android
     */
    private Integer platform;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 流量渠道
     */
    private Integer sourceChannel;

    /**
     * trace_id，用于支付交易追踪
     */
    private String traceId;

    /**
     * 支付时间，回调时会回写
     */
    private long payTime;

    /**
     * 创建时间
     */
    private long ctime;

    /**
     * 更新时间
     */
    private long mtime;

    /**
     * 创单支付期间，用户上下文信息
     */
    private JSONObject traceInfo;
}
