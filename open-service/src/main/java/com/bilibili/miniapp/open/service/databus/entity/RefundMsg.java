package com.bilibili.miniapp.open.service.databus.entity;

import com.bilibili.miniapp.open.common.enums.NotifyStatus;
import com.bilibili.miniapp.open.common.enums.RefundStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/1/19 19:35
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundMsg {
    /**
     * id
     */
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 开发者退款批次id，如果后续支持部分退款，则同一订单的不同退款记录批次id不同
     */
    private String devRefundId;

    /**
     * 退款金额，单位分
     */
    private Long refundAmount;

    /**
     * 退款状态，0：尚未发起退款，1：发起退款成功（退款中），2：发起退款失败，3：退款成功，4：退款失败
     *
     * @see RefundStatus
     */
    private Integer refundStatus;

    /**
     * 开发者退款回调状态，0：未回调，1：回调成功，2：回调失败
     *
     * @see NotifyStatus
     */
    private Integer notifyStatus;

    /**
     * trace_id，用于支付交易追踪
     */
    private String traceId;
}
