package com.bilibili.miniapp.open.service.databus.producer;

import com.bilibili.miniapp.open.service.databus.entity.IaaWithdrawBillEventMsg;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/26
 */
@Slf4j
@Component
public class IaaWithdrawBillStatusUpdateEventProducer {

    @Resource
    private DatabusTemplate databusTemplate;
    public static final String configKey = "iaa-withdraw-bill";

    private String topic;
    private String pubGroup;
    private ObjectMapper objectMapper;


    public IaaWithdrawBillStatusUpdateEventProducer(DatabusProperties databusProperties) {

        DatabusProperty databusProperty = databusProperties.getProperties().get(configKey);

        this.topic = databusProperty.getTopic();
        this.pubGroup = databusProperty.getPub().getGroup();
        this.objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .registerModule(new Jdk8Module())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL);

        log.info("aigc audit databus pub topic={}, group={}", topic, pubGroup);

    }


    public void publishBillEvent(IaaWithdrawBillEventMsg msg) {

        String serializedMessage = Try.of(() -> {

            return objectMapper.writeValueAsString(msg);

        }).getOrNull();

        if (StringUtils.isEmpty(serializedMessage)) {
            return;
        }

        log.info("Start to publish bill msg, msg={}", serializedMessage);

        // messageKey和value自定义，value会被配置的serializer序列化 不需要， 我自己序列化
        Message message = Message.Builder.of(String.valueOf(msg.getData().getBillId()), serializedMessage).build();

        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        Try.run(() -> {

            PubResult r = databusTemplate.pub(topic, pubGroup, message);

            if (!r.isSuccess()) {

                if (r.getThrowable() != null) {
                    throw new RuntimeException("Failed to publish  msg", r.getThrowable());
                }

                throw new RuntimeException("Failed to publish msg");
            }
        }).onFailure(t -> {
            log.error("Fail to publish msg={}", serializedMessage, t);
        });

    }


}
