package com.bilibili.miniapp.open.service.databus.producer;

import com.alibaba.fastjson.JSON;
import com.bilibili.business.cmpt.idatabus.client.spring.core.IDataBusProducer;
import com.bilibili.miniapp.open.service.databus.entity.MiniAppActivationMsg;
import com.bilibili.warp.databus.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 小程序激活上报消息生产者
 */
@Slf4j
@Component
public class MiniAppActivationProducer {
    
    @Resource(name = "MiniAppActivationProducer")
    private IDataBusProducer dataBusProducer;

    /**
     * 发布小程序激活上报消息
     *
     * @param msg 激活上报消息
     */
    public void publishActivationMsg(MiniAppActivationMsg msg) {
        try {
            String messageKey = msg.getAppId();
            dataBusProducer.send(Message.Builder.of(messageKey, msg).build());
        } catch (Exception e) {
            log.error("[MiniAppActivationProducer] publishActivationMsg error, msg={}", JSON.toJSONString(msg), e);
        }
    }
}
