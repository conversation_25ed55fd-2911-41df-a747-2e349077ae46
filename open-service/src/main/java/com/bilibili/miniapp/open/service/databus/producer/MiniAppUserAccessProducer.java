package com.bilibili.miniapp.open.service.databus.producer;

import com.alibaba.fastjson.JSON;
import com.bilibili.business.cmpt.idatabus.client.spring.core.IDataBusProducer;
import com.bilibili.miniapp.open.service.databus.entity.UserAccessMsg;
import com.bilibili.warp.databus.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 **/

@Slf4j
@Component
public class MiniAppUserAccessProducer {
    @Resource(name = "MiniAppUserAccessProducer")
    private IDataBusProducer dataBusProducer;

    public void publishUserAccessMsg(UserAccessMsg msg) {
        assert msg != null;
        try {
            log.info("[MiniAppUserAccessProducer] publishUserAccessMsg, msg={}", JSON.toJSONString(msg));
            this.dataBusProducer.send(Message.Builder.of(String.format("%s_%s", msg.getMid(), msg.getAppId()), msg).build());
        } catch (Exception e) {
            log.error("[MiniAppUserAccessProducer] publishUserAccessMsg error, msg={}", JSON.toJSONString(msg), e);
        }
    }
}
