package com.bilibili.miniapp.open.service.databus.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.business.cmpt.idatabus.client.spring.core.IDataBusProducer;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.service.biz.retry.IOpenRetryService;
import com.bilibili.miniapp.open.service.biz.retry.OpenRetryCallable;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;
import com.bilibili.miniapp.open.service.databus.entity.OrderMsg;
import com.bilibili.warp.databus.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/17 20:56
 */
@Slf4j
@Component
public class OpenPlatformOrderChangeProducer implements OpenRetryCallable {
    @Resource(name = "OpenPlatformOrderChangeProducer")
    private IDataBusProducer dataBusProducer;
    @Autowired
    private IOpenRetryService openRetryService;

    public void publishOrderChangeMsg(OrderMsg orderMsg) {
        OpenRetryContext openRetryContext = OpenRetryContext.builder()
                .bizId(orderMsg.getOrderId())
                .bizType(bizType())
                .bizData((JSONObject) JSONObject.toJSON(orderMsg))
                .build();
        openRetryService.retry(openRetryContext);
    }

    @Override
    public RetryBizType bizType() {
        return RetryBizType.ORDER_CHANGE_PUBLISH_MQ;
    }

    @Override
    public boolean call(OpenRetryContext ctx) {
        JSONObject bizData = ctx.getBizData();
        assert bizData != null;
        try {
            OrderMsg orderMsg = bizData.toJavaObject(OrderMsg.class);
            log.info("[OpenPlatformOrderChangeProducer] publishOrderChangeMsg, msg={}", JSON.toJSONString(orderMsg));
            this.dataBusProducer.send(Message.Builder.of(Objects.toString(orderMsg.getOrderId()), orderMsg).build());
            return true;
        } catch (Exception e) {
            log.error("[OpenPlatformOrderChangeProducer] publishOrderChangeMsg error, msg={}", bizData.toJSONString(), e);
        }
        return false;
    }
}
