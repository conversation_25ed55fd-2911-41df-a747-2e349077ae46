package com.bilibili.miniapp.open.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18
 **/

@AllArgsConstructor
@Getter
public enum IcpBeiAnConfigStatusTypeEnum {
    // 0-无效 1-有效
    INVALID(0, "无效"),
    VALID(1, "有效"),
    ;

    private final Integer code;
    private final String desc;
    public static IcpBeiAnConfigStatusTypeEnum getByCode(Integer code) {
        for (IcpBeiAnConfigStatusTypeEnum status : IcpBeiAnConfigStatusTypeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
