package com.bilibili.miniapp.open.service.enums;

import com.bapis.pgc.service.season.season.PaymentStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Getter
@AllArgsConstructor
public enum SeasonPaymentStatus {

    //0-免费，1-IAA，2-IAP
    FREE(0, PaymentStatusEnum.FREE, "免费"),
    IAA(1, PaymentStatusEnum.IAA_PAYMENT, "IAA"),
    IAP(2, PaymentStatusEnum.IAP_PAYMENT, "IAP"),
    IAA_OR_IAP(3, PaymentStatusEnum.IAA_OR_IAP_PAYMENT, "IAA/IAP"),
    ;

    private final int code;
    private final PaymentStatusEnum remoteStatus;
    private final String desc;


    public static SeasonPaymentStatus getByRemotePaymentStatus(PaymentStatusEnum paymentStatusEnum) {
        for (SeasonPaymentStatus value : values()) {
            if (value.getRemoteStatus() == paymentStatusEnum) {
                return value;
            }
        }
        return FREE;
    }

    public static SeasonPaymentStatus getByCode(int code) {
        for (SeasonPaymentStatus value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return FREE;
    }
}
