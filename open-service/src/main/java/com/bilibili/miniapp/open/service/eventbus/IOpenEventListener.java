package com.bilibili.miniapp.open.service.eventbus;

import java.util.EventListener;

/**
 * <AUTHOR>
 * @date 2025/01/17 19:37
 */
public interface IOpenEventListener<E extends OpenEvent> extends EventListener {
    /**
     * Determine whether the event can be matched and consumed.
     * If so, return true; otherwise, return false
     */
    boolean match(E event);

    /**
     * If the {@link #match(OpenEvent)} method returns true, the event will be posted to this method.
     */
    void onEvent(E event);
}
