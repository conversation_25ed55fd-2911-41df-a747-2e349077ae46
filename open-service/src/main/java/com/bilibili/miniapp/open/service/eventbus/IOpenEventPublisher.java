package com.bilibili.miniapp.open.service.eventbus;

/**
 * <AUTHOR>
 * @date 2025/01/17 19:37
 */
public interface IOpenEventPublisher<E extends OpenEvent> {
    /**
     * 发布事件
     * <p>
     * 注意：任何事件处理过程出现异常则会阻塞后续Listener
     *
     * @see IOpenEventListener
     */
    void publish(E event) throws Exception;

    /**
     * 发布事件-不会抛出异常
     */
    void publishWithoutEx(E event);

    /**
     * 发布事件-异步
     * <p>
     * 注意：如果异步线程池已满，则会由调用方线程执行，此时是同步行为
     */
    void publishAsync(E event) throws Exception;
}
