package com.bilibili.miniapp.open.service.eventbus;

import java.util.EventObject;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/01/17 19:37
 */
public abstract class OpenEvent extends EventObject {
    private static final long serialVersionUID = 918587240336690753L;

    /**
     * Constructs a prototypical Event.
     *
     * @param source The object on which the Event initially occurred.
     * @throws IllegalArgumentException if source is null.
     */
    public OpenEvent(Object source) {
        super(source);
    }

    public OpenEvent() {
        super("");
    }


    public enum ActionType {
        //添加
        ADD,
        //修改
        MODIFY,
        //删除
        REMOVE,
        //禁止
        DISABLE,
        //启用
        ENABLE,
        //开始
        START,
        //结束
        END,
        //刷新（也许数据本身没有任何变化）
        REFRESH,
        ;

        public static ActionType getActionTypeWithSimple(String action) {
            if (Objects.equals(action, "insert")) {
                return ActionType.ADD;
            }
            if (Objects.equals(action, "update")) {
                return ActionType.MODIFY;
            }
            if (Objects.equals(action, "delete")) {
                return ActionType.REMOVE;
            }
            return null;
        }
    }
}
