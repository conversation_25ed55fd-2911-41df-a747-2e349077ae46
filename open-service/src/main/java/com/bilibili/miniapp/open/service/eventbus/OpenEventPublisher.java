package com.bilibili.miniapp.open.service.eventbus;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.util.FunctionUtil;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * <AUTHOR>
 * @date 2025/01/17 19:37
 */
@Slf4j
@Component
public class OpenEventPublisher implements IOpenEventPublisher<OpenEvent> {
    /**
     * Use {@link Order} annotation to specify injection order
     */
    @Autowired
    private List<IOpenEventListener<OpenEvent>> listeners;

    @Override
    public void publish(OpenEvent event) throws Exception {
        log.info("[OpenEventPublisher] publish event={}", JSON.toJSONString(event));
        Optional.ofNullable(listeners)
                .ifPresent(ls -> ls.stream()
                        .filter(listener -> listener.match(event))
                        .forEach(listener -> listener.onEvent(event)));
    }

    @Override
    public void publishWithoutEx(OpenEvent event) {
        log.info("[OpenEventPublisher] publishWithoutEx event={}", JSON.toJSONString(event));
        Optional.ofNullable(listeners)
                .ifPresent(ls -> ls.stream()
                        .filter(listener ->
                                FunctionUtil.invokeWithoutEx(event, listener::match, () -> false))
                        .forEach(listener ->
                                FunctionUtil.invokeWithoutEx(event, et -> {
                                    listener.onEvent(et);
                                    return 0;
                                }, () -> 0)));
    }

    @Override
    public void publishAsync(OpenEvent event) throws Exception {
        ThreadPoolExecutor executor = ThreadPoolUtil.getExecutor(ThreadPoolType.MINI_APP_OPEN_PLATFORM_EVENT);
        executor.execute(() -> publishWithoutEx(event));
    }
}
