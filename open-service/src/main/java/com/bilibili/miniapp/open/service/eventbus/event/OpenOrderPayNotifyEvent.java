package com.bilibili.miniapp.open.service.eventbus.event;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.eventbus.OpenEvent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 订单支付回调事件
 *
 * <AUTHOR>
 * @date 2025/1/17 19:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenOrderPayNotifyEvent extends OpenEvent implements Serializable {
    private static final long serialVersionUID = -105990866009037572L;

    @JSONField(serialzeFeatures = {SerializerFeature.WriteEnumUsingName})
    private ActionType actionType;

    /**
     * 当前订单信息
     */
    private Order order;
}
