package com.bilibili.miniapp.open.service.eventbus.listener;

import com.bilibili.miniapp.open.service.biz.icp.IcpService;
import com.bilibili.miniapp.open.service.eventbus.event.IcpReportEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/24
 **/

@Component
@Slf4j
public class IcpReportEventListener implements ApplicationListener<IcpReportEvent> {

    @Resource
    private IcpService icpService;

    @Override
    public void onApplicationEvent(IcpReportEvent event) {
        Long flowId = event.getFlowId();
        log.info("[IcpReportEventListener] onApplicationEvent flowId: {}", flowId);
        try {
            icpService.reportIcpInfo(flowId);
        } catch (Exception e) {
            // 这里如果失败会有任务进行补偿 IcpReportJob
            log.error("[IcpReportEventListener] reportIcpInfo error, flowId: {}", flowId, e);
        }
    }
}
