package com.bilibili.miniapp.open.service.eventbus.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.databus.entity.OrderMsg;
import com.bilibili.miniapp.open.service.databus.producer.OpenPlatformOrderChangeProducer;
import com.bilibili.miniapp.open.service.eventbus.IOpenEventListener;
import com.bilibili.miniapp.open.service.eventbus.OpenEvent;
import com.bilibili.miniapp.open.service.eventbus.event.OpenOrderEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/17 20:40
 */
@Slf4j
@Component
public class OpenOrderChangeDataBusListener implements IOpenEventListener<OpenEvent> {

    @Autowired
    private OpenPlatformOrderChangeProducer openPlatformOrderChangeProducer;

    @Override
    public boolean match(OpenEvent event) {
        return event instanceof OpenOrderEvent;
    }

    @Override
    public void onEvent(OpenEvent event) {
        OpenOrderEvent openOrderEvent = (OpenOrderEvent) event;
        Order order = openOrderEvent.getOrder();

        OrderMsg orderMsg = new OrderMsg();
        BeanUtils.copyProperties(order, orderMsg);
        orderMsg.setCtime(Objects.nonNull(order.getCtime()) ? order.getCtime().getTime() : 0);
        orderMsg.setMtime(Objects.nonNull(order.getMtime()) ? order.getMtime().getTime() : 0);
        orderMsg.setPayTime(Objects.nonNull(order.getPayTime()) ? order.getPayTime().getTime() : 0);

        if (Objects.nonNull(order.getExtra()) && StringUtils.hasText(order.getExtra().getTraceInfo())) {
            JSONObject traceInfo;
            try {
                traceInfo = JSON.parseObject(order.getExtra().getTraceInfo());
                orderMsg.setTraceInfo(traceInfo);
            } catch (Exception e) {
                //ignore
                //最差情况下的兜底：如果trace信息无法解析，则直接丢掉
                log.error("[OpenOrderChangeDataBusListener] failed parse trace info, order_id={}, trace_info={}",
                        order.getOrderId(), order.getExtra().getTraceInfo(), e);
            }
        }
        openPlatformOrderChangeProducer.publishOrderChangeMsg(orderMsg);
    }
}
