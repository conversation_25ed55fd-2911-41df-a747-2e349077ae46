package com.bilibili.miniapp.open.service.eventbus.listener;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.enums.RetryBizType;
import com.bilibili.miniapp.open.service.biz.retry.IOpenRetryService;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.bo.retry.OpenRetryContext;
import com.bilibili.miniapp.open.service.eventbus.IOpenEventListener;
import com.bilibili.miniapp.open.service.eventbus.OpenEvent;
import com.bilibili.miniapp.open.service.eventbus.event.OpenOrderPayNotifyEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/19 12:56
 */
@Slf4j
@Component
public class OpenOrderPayNotifyListener implements IOpenEventListener<OpenEvent> {
    @Autowired
    private IOpenRetryService openRetryService;

    @Override
    public boolean match(OpenEvent event) {
        return event instanceof OpenOrderPayNotifyEvent;
    }

    @Override
    public void onEvent(OpenEvent event) {
        OpenOrderPayNotifyEvent openOrderEvent = (OpenOrderPayNotifyEvent) event;
        Order order = openOrderEvent.getOrder();
        //处理开发者订单回调
        processOrderPayNotify(order);
    }

    /**
     * 消费订单变更消息，如果已支付且未回调开发者，则触发回调
     */
    private void processOrderPayNotify(Order order) {
        //支付回调逻辑中，会重新判断订单支付状态，因此只要核心字段即可
        Order simpleOrder = Order.builder()
                .orderId(order.getOrderId())
                .appId(order.getAppId())
                .devOrderId(order.getDevOrderId())
                .traceId(order.getTraceId())
                .build();
        OpenRetryContext retryContext = OpenRetryContext.builder()
                .bizType(RetryBizType.ORDER_PAY_NOTIFY_DEVELOPER)
                .bizId(order.getOrderId())
                .reqId(order.getTraceId())
                .bizData((JSONObject) JSONObject.toJSON(simpleOrder))
                .build();
        openRetryService.retry(retryContext);
    }
}
