package com.bilibili.miniapp.open.service.eventbus.listener;

import com.bilibili.miniapp.open.service.bo.order.Refund;
import com.bilibili.miniapp.open.service.databus.entity.RefundMsg;
import com.bilibili.miniapp.open.service.databus.producer.OpenPlatformOrderRefundProducer;
import com.bilibili.miniapp.open.service.eventbus.IOpenEventListener;
import com.bilibili.miniapp.open.service.eventbus.OpenEvent;
import com.bilibili.miniapp.open.service.eventbus.event.OpenRefundEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/19 19:32
 */
@Slf4j
@Component
public class OpenOrderRefundDataBusListener implements IOpenEventListener<OpenEvent> {

    @Autowired
    private OpenPlatformOrderRefundProducer openPlatformOrderRefundProducer;

    @Override
    public boolean match(OpenEvent event) {
        return event instanceof OpenRefundEvent;
    }

    @Override
    public void onEvent(OpenEvent event) {
        OpenRefundEvent refundEvent = (OpenRefundEvent) event;
        Refund orderRefund = refundEvent.getRefund();
        RefundMsg refundMsg = new RefundMsg();
        BeanUtils.copyProperties(orderRefund, refundMsg);
        openPlatformOrderRefundProducer.publishOrderRefundMsg(refundMsg);
    }
}
