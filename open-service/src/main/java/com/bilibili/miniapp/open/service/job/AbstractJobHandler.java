package com.bilibili.miniapp.open.service.job;

import com.alibaba.fastjson.JSONArray;
import com.bilibili.miniapp.open.common.annotations.ParallelJob;
import com.bilibili.miniapp.open.common.util.TraceUtil;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Slf4j
public abstract class AbstractJobHandler extends IJob<PERSON>andler {
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Transaction t = Cat.getProducer().newTransaction("AbstractJobHandler", getName());
        AtomicReference<ReturnT<String>> atomicReferenceRes = new AtomicReference<>();
        try {
            MDC.put(TraceUtil.TRACE_ID, TraceUtil.genTraceId());
            log.info("AbstractJobHandler {} start, param:{}", getName(), param);
            ParallelJob parallel = this.getClass().getAnnotation(ParallelJob.class);
            if (Objects.isNull(parallel)) {
                atomicReferenceRes.set(doExecute(param));
            } else {
                List<Pair<String, String>> result = Collections.synchronizedList(Lists.newArrayList());
                List<String> paramArr = JSONArray.parseArray(param, String.class);
                CompletableFuture.allOf(paramArr.stream()
                        .map(p -> CompletableFuture.runAsync(TraceUtil.decorate(() -> {
                            ReturnT<String> pRes;
                            try {
                                pRes = doExecute(p);
                            } catch (Exception e) {
                                pRes = new ReturnT<>(e.getMessage());
                            }
                            if (Objects.isNull(pRes)) {
                                pRes = new ReturnT<>("");
                            }
                            result.add(new Pair<>(p, pRes.getContent()));
                        }))).toArray(CompletableFuture[]::new)).thenRun(() -> {
                    //汇总每个参数和对应的执行结果
                    atomicReferenceRes.set(new ReturnT<>(JSONArray.toJSONString(result)));
                }).join();
            }
            t.setStatus(Transaction.SUCCESS);
        } catch (Exception ex) {
            log.error("AbstractJobHandler {} error, param:{}", getName(), param, ex);
            Cat.logEvent(getName(), "failed");
            t.setStatus("1");//error
        } finally {
            t.complete();
            log.info("AbstractJobHandler {} end, param:{}, result:{}, cost:{}", getName(), param,
                    Objects.nonNull(atomicReferenceRes.get()) ? atomicReferenceRes.get().getContent() : "", t.getDurationInMillis() / 1000);
            MDC.remove(TraceUtil.TRACE_ID);
        }
        return Objects.nonNull(atomicReferenceRes.get()) ? atomicReferenceRes.get() : ReturnT.SUCCESS;
    }

    public abstract ReturnT<String> doExecute(String param) throws Exception;

    protected String getName() {
        JobHandler annotation = this.getClass().getAnnotation(JobHandler.class);
        return Objects.isNull(annotation) ? "unknown" : annotation.value();
    }

}
