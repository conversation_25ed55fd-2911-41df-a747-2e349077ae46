package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 正常无需配置， 由berserker进行调度即可， 如果berserker的webhook有问题，可以用xxl调度
 * <AUTHOR>
 * @desc
 * @date 2025/3/28
 */
@Component
@JobHandler("IaaDailySettleJob")
public class IaaDailySettleJob extends AbstractJobHandler{

    @Resource
    private IaaSettlementService iaaSettlementService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        iaaSettlementService.onDailySettlementsReady(null);

        return ReturnT.SUCCESS;
    }


}
