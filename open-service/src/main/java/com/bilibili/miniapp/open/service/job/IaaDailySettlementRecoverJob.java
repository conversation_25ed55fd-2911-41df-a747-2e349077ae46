package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementDailyCheck;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/28
 */
@Component
@JobHandler("IaaDailySettlementRecoverJob")
public class IaaDailySettlementRecoverJob extends AbstractJobHandler{


    @Resource
    private IaaSettlementDailyCheck iaaSettlementDailyCheck;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        iaaSettlementDailyCheck.onDailyRecoverCheck4InvalidSettlements();

        return ReturnT.SUCCESS;
    }

}
