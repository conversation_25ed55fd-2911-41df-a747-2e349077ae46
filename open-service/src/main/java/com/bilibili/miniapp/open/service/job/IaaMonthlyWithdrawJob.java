package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.settlement.IaaSettlementService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/28
 */
@Component
@JobHandler("IaaMonthlyWithdrawJob")
public class IaaMonthlyWithdrawJob extends AbstractJobHandler{

    @Resource
    private IaaSettlementService iaaSettlementService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        iaaSettlementService.onMonthlyWithdrawDateSchedule(null);

        return ReturnT.SUCCESS;
    }


}
