package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.common.enums.IcpFlowStatusEnum;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl.MiniAppIcpRepository;
import com.bilibili.miniapp.open.service.biz.icp.IcpService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/27
 **/

@Component
@JobHandler("IcpReportJob")
@Slf4j
public class IcpReportJob extends AbstractJobHandler {

    @Resource
    private MiniAppIcpRepository icpRepository;

    @Resource
    private IcpService icpService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (StringUtils.isNotBlank(param)) {
            icpService.reportIcpInfo(Long.valueOf(param));
            return ReturnT.SUCCESS;
        }
        List<IcpReportState> icpReportStates = icpRepository.queryIcpReportStateByStatus(
                List.of(IcpFlowStatusEnum.ICP_STATUS_GOV_SMS_VERIFY.getStatus())
                , 0
        );
        if (CollectionUtils.isEmpty(icpReportStates)) {
            XxlJobLogger.log("没有需要上报的备案数据");
            return ReturnT.SUCCESS;
        }
        for (IcpReportState icpReportState : icpReportStates) {
            try {
                icpService.reportIcpInfo(icpReportState.getFlowId());
                XxlJobLogger.log("上报备案数据成功，flowId: {}", icpReportState.getFlowId());
            } catch (Exception e) {
                log.error("上报备案数据失败，flowId: {}, error: {}", icpReportState.getFlowId(), e.getMessage(), e);
                XxlJobLogger.log("上报备案数据失败，flowId: {}, error: {}", icpReportState.getFlowId(), e.getMessage());
            }
        }
        return ReturnT.SUCCESS;
    }
}
