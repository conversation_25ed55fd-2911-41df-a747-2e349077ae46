package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportInfo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IcpReportState;
import com.bilibili.miniapp.open.repository.mysql.miniapp.repo.impl.MiniAppIcpRepository;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRegulationRemoteService;
import com.bilibili.regulation.api.dto.BeiAnStatusDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 负责更新已经备案的小程序的备案号
 * @Date 2025/5/9
 **/

@Component
@JobHandler("IcpReportStatusJob")
@Slf4j
public class IcpReportStatusJob extends AbstractJobHandler{

    @Resource
    private MiniAppIcpRepository icpRepository;

    @Resource
    private MiniAppRegulationRemoteService regulationRemoteService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<IcpReportState> reportStates = icpRepository.queryIcpReportFinish();
        // 移除已经备案的小程序
        reportStates.removeIf(reportState -> StringUtils.isNotBlank(reportState.getRecordNumber()));
        if (reportStates.isEmpty()) {
            XxlJobLogger.log("没有需要更新备案号的小程序");
            log.info("[IcpReportStatusJob] 没有需要更新备案号的小程序");
            return ReturnT.SUCCESS;
        }
        for (IcpReportState reportState : reportStates) {
            try {
                IcpReportInfo reportInfo = icpRepository.getIcpReportInfoByFlowId(reportState.getFlowId(), true);
                if (reportInfo == null) {
                    XxlJobLogger.log("没有找到备案信息，flowId: {}", reportState.getFlowId());
                    log.info("[IcpReportStatusJob] 没有找到备案信息，flowId: {}", reportState.getFlowId());
                    continue;
                }
                BeiAnStatusDto beiAnStatusDto = regulationRemoteService.queryBeiAnStatus(reportInfo.getCompany().getLicenseNo(), reportInfo.getApp().getName(), reportInfo.getApp().getAppId());
                if (beiAnStatusDto == null || !beiAnStatusDto.isBazt() || StringUtils.isBlank(beiAnStatusDto.getWzbah())) {
                    XxlJobLogger.log("没有找到备案号，flowId: {}", reportState.getFlowId());
                    log.info("[IcpReportStatusJob] 没有找到备案号，flowId: {}", reportState.getFlowId());
                    continue;
                }
                // 更新备案号
                reportState.setRecordNumber(beiAnStatusDto.getWzbah());
                icpRepository.saveIcpReportState(reportState);
                XxlJobLogger.log("更新备案号成功，flowId: {}, recordNumber: {}", reportState.getFlowId(), reportState.getRecordNumber());
                log.info("[IcpReportStatusJob] 更新备案号成功，flowId: {}, recordNumber: {}", reportState.getFlowId(), reportState.getRecordNumber());
            } catch (Exception e) {
                log.error("更新备案号失败，flowId: {}, error: {}", reportState.getFlowId(), e.getMessage(), e);
                XxlJobLogger.log("更新备案号失败，flowId: {}, error: {}", reportState.getFlowId(), e.getMessage());
            }
        }
        return ReturnT.SUCCESS;
    }
}
