package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.retry.IOpenRetryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/18 15:43
 */
@Component
@JobHandler("OpenRetryJob")
public class OpenRetryJob extends AbstractJobHandler {
    @Autowired
    private IOpenRetryService openRetryService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        openRetryService.retry();
        return ReturnT.SUCCESS;
    }
}
