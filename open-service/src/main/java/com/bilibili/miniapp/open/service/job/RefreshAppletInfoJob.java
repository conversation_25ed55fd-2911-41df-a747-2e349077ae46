package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.applet.IAppletService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/21
 **/

@Component
@JobHandler("RefreshAppletInfoJob")
public class RefreshAppletInfoJob extends AbstractJobHandler{

    @Resource
    private IAppletService appletService;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        appletService.refreshAppletInfoCache();
        return ReturnT.SUCCESS;
    }
}
