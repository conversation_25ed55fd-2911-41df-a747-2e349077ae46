package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.youku.YoukuImageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/22
 **/

@Component
@JobHandler("ReplacementYouKuImageJob")
public class ReplacementYouKuImageJob extends AbstractJobHandler{

    @Resource
    private YoukuImageService youkuImageService;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        try {
            XxlJobLogger.log("开始扫描优酷节目图片......");
            youkuImageService.scanShowAndReplacement(0L, 1000);
            XxlJobLogger.log("优酷节目图片扫描完成......");
        } catch (Exception e) {
            XxlJobLogger.log("优酷节目图片处理失败");
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        try {
            XxlJobLogger.log("开始扫描优酷视频图片......");
            youkuImageService.scanVideoAndReplacement(0L, 1000);
            XxlJobLogger.log("优酷视频图片扫描完成......");
        } catch (Exception e) {
            XxlJobLogger.log("优酷视频图片处理失败");
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
