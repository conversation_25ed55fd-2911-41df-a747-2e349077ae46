package com.bilibili.miniapp.open.service.job;

import com.bilibili.miniapp.open.service.biz.youku.YoukuSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/24
 **/

@Component
@JobHandler("YouKuIncrSyncJob")
public class YouKuIncrSyncJob extends AbstractJobHandler {

    @Resource
    private YoukuSyncService youkuSyncService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        // 默认同步一天的增量数据
        int hour = 24;
        if (StringUtils.hasText(param)) {
            hour = Integer.parseInt(param);
        }
        // 同步增量上线数据
        youkuSyncService.syncIncrementMediaData(hour, "上线");
        // 同步增量下线数据
        youkuSyncService.syncIncrementMediaData(hour, "下线");
        return ReturnT.SUCCESS;
    }
}
