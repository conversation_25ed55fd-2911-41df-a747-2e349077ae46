package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.common.util.TimeUtil;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.IdentityAuthInfo;
import com.bilibili.miniapp.open.repository.mysql.miniapp.bo.icp.MiniAppIcpConfig;
import com.bilibili.miniapp.open.service.bo.icp.IcpConfig;
import com.bilibili.regulation.api.dto.IcpConfigDto;
import com.bilibili.regulation.api.dto.IdentityAuthInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/

@Mapper(imports = {TimeUtil.class})
public interface IcpBizMapper {

    IcpBizMapper MAPPER = Mappers.getMapper(IcpBizMapper.class);

    IcpConfig toIcpConfig(MiniAppIcpConfig icpConfig);

    IcpConfig.IcpDocumentType toIcpDocumentType(MiniAppIcpConfig.MiniAppIcpDocumentType icpDocumentType);

    IcpConfig.IcpAreaCode toIcpAreaCode(MiniAppIcpConfig.MiniAppIcpAreaCode icpAreaCode);

    IcpConfig.IcpOrgType toIcpOrgType(MiniAppIcpConfig.MiniAppIcpOrgType icpOrgType);

    IcpConfig.IcpAppServiceType toIcpAppServiceType(MiniAppIcpConfig.MiniAppIcpAppServiceType icpAppServiceType);

    MiniAppIcpConfig toMiniAppIcpConfig(IcpConfig icpConfig);
    MiniAppIcpConfig.MiniAppIcpDocumentType toMiniAppIcpDocumentType(IcpConfig.IcpDocumentType icpDocumentType);
    MiniAppIcpConfig.MiniAppIcpAreaCode toMiniAppIcpAreaCode(IcpConfig.IcpAreaCode icpAreaCode);
    MiniAppIcpConfig.MiniAppIcpOrgType toMiniAppIcpOrgType(IcpConfig.IcpOrgType icpOrgType);
    MiniAppIcpConfig.MiniAppIcpAppServiceType toMiniAppIcpAppServiceType(IcpConfig.IcpAppServiceType icpAppServiceType);


    @Mapping(target = "fzrZjyxqStart", expression = "java(TimeUtil.timestampToString(identityAuthInfo.getFzrZjyxqStart(), \"yyyy.MM.dd\"))")
    @Mapping(target = "fzrZjyxqEnd", expression = "java(identityAuthInfo.getFzrZjyxqEnd() == null ? \"长期\" : TimeUtil.timestampToString(identityAuthInfo.getFzrZjyxqEnd(), \"yyyy.MM.dd\"))")
    @Mapping(target = "wzFzrZjyxqStart", expression = "java(TimeUtil.timestampToString(identityAuthInfo.getWzFzrZjyxqStart(), \"yyyy.MM.dd\"))")
    @Mapping(target = "wzFzrZjyxqEnd", expression = "java(identityAuthInfo.getWzFzrZjyxqEnd() == null ? \"长期\" : TimeUtil.timestampToString(identityAuthInfo.getWzFzrZjyxqEnd(), \"yyyy.MM.dd\"))")
    IdentityAuthInfoDto toIdentityAuthInfoDto(IdentityAuthInfo identityAuthInfo);
}
