package com.bilibili.miniapp.open.service.mapper;

import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.miniapp.open.common.mapper.CommonMapper;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAppAdmissionPo;
import com.bilibili.miniapp.open.service.bo.miniapp.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@Mapper(uses = CommonMapper.class)
public interface MiniAppBizMapper {

    MiniAppBizMapper MAPPER = Mappers.getMapper(MiniAppBizMapper.class);

    @Mapping(target = "admissionId", ignore = true)
    @Mapping(target = "permission", ignore = true)
    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "auditStatus", ignore = true)
    @Mapping(target = "auditFailureReason", ignore = true)
    @Mapping(target = "appName", source = "name")
    @Mapping(target = "appLogo", source = "logo")
    MiniAppListBo toBo(MiniAppDTO miniAppDTO);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "permission", ignore = true)
    @Mapping(target = "companyName", ignore = true)
    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "admissionId",source = "admissionPo.id")
    //只有未生成小程序的时候才会从准入表获取数据，所以从表获取或者从编辑内容获取都可以
    @Mapping(target = "auditFailureReason", source = "admissionPo.failReason")
    @Mapping(target = "appId",source = "admissionPo.appId")
    @Mapping(target = "appName",source = "admissionPo.appName")
    MiniAppListBo toMiniAppListBo(MiniAppOpenAppAdmissionPo admissionPo, MiniAppBo miniAppBo);

    @Mapping(target = "categoryId", ignore = true)
    @Mapping(target = "categoryCertifications", ignore = true)
    @Mapping(target = "appName", source = "name")
    @Mapping(target = "appLogo", source = "logo")
    @Mapping(target = "appDescription", source = "introduction")
    @Mapping(target = "admissionId", ignore = true)
    MiniAppBo toMiniAppBo(MiniAppDTO miniApp);

    default MiniAppBo map(String editInfo) {
        return JSON.parseObject(editInfo, MiniAppBo.class);
    }

    @Mapping(target = "createTime", source = "mtime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    MiniAppAdmissionAuditListBo toAuditListBo(MiniAppOpenAppAdmissionPo po);

    @Mapping(target = "oldInfo", ignore = true)
    @Mapping(target = "newInfo", ignore = true)
    @Mapping(target = "companyName", ignore = true)
    @Mapping(target = "createTime", source = "mtime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "auditFailReason", source = "failReason")
    MiniAppAdmissionAuditDetailBo toMiniAppAuditBaseDetail(MiniAppOpenAppAdmissionPo admissionPo);

    @Mapping(target = "categoryName", ignore = true)
    MiniAppAuditBo toMiniAppAuditBo(MiniAppBo miniAppBo);
}
