package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderExtraPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenOrderPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo;
import com.bilibili.miniapp.open.service.bo.order.Order;
import com.bilibili.miniapp.open.service.bo.order.OrderExtra;
import com.bilibili.miniapp.open.service.bo.order.Refund;
import com.bilibili.miniapp.open.service.bo.order.RefundExtra;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/16 15:36
 */
@Mapper
public interface OrderMapper {
    OrderMapper MAPPER = Mappers.getMapper(OrderMapper.class);

    Order toOrder(MiniAppOpenOrderPo po);

    List<Order> toOrder(List<MiniAppOpenOrderPo> poList);


    OrderExtra toOrderExtra(MiniAppOpenOrderExtraPo po);

    List<OrderExtra> toOrderExtra(List<MiniAppOpenOrderExtraPo> poList);


}
