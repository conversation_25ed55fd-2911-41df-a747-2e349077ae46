package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundExtraPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenRefundPo;
import com.bilibili.miniapp.open.service.bo.order.Refund;
import com.bilibili.miniapp.open.service.bo.order.RefundExtra;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/19 20:51
 */
@Mapper
public interface PaymentMapper {
    PaymentMapper MAPPER = Mappers.getMapper(PaymentMapper.class);

    Refund toOrderRefund(MiniAppOpenRefundPo po);

    List<Refund> toOrderRefund(List<MiniAppOpenRefundPo> poList);


    RefundExtra toOrderRefundExtra(MiniAppOpenRefundExtraPo po);

    List<RefundExtra> toOrderRefundExtra(List<MiniAppOpenRefundExtraPo> poList);


}
