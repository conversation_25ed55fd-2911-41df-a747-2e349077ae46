package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.service.bo.ogv.PlatformSeasonInfoBo;
import com.bilibili.miniapp.open.service.bo.ogv.SeasonBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
@Mapper
public interface SeasonBizMapper {
    SeasonBizMapper MAPPER = Mappers.getMapper(SeasonBizMapper.class);


    @Mapping(target = "alreadyPublished", expression = "java(alreadyPublishedSeasonIdSet.contains(seasonBo.getSeasonId()))")
    PlatformSeasonInfoBo toBo(SeasonBo seasonBo, Set<Long> alreadyPublishedSeasonIdSet);
}
