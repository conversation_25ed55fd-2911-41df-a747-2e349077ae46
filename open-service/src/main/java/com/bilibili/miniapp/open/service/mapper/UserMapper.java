package com.bilibili.miniapp.open.service.mapper;

import com.bapis.passport.service.user.UserDetailReply;
import com.bapis.passport.service.user.UserSensitiveReply;
import com.bilibili.miniapp.open.service.bo.up_info.UserDetailBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserSensitiveInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 **/

@Mapper
public interface UserMapper {

    UserMapper USER_MAPPER = Mappers.getMapper(UserMapper.class);

    UserDetailBo rpcReplytoUserDetailBo(UserDetailReply reply);

    @Mapping(source = "payLoad", target = "payload")
    UserSensitiveInfoBo rpcReplytoUserSensitiveInfoBo(UserSensitiveReply reply);
}
