package com.bilibili.miniapp.open.service.mapper;

import com.bilibili.miniapp.open.repository.mysql.miniapp.po.MiniAppOpenYoukuOrderPo;
import com.bilibili.miniapp.open.service.bo.youku.YkOrderCallbackReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/8
 **/

@Mapper
public interface YkCallbackBizMapper {

    YkCallbackBizMapper MAPPER = Mappers.getMapper(YkCallbackBizMapper.class);

    MiniAppOpenYoukuOrderPo toYkOrderCallback(YkOrderCallbackReq req);
}
