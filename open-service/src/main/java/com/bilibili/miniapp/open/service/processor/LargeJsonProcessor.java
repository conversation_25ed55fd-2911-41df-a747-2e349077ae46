package com.bilibili.miniapp.open.service.processor;

import com.bilibili.miniapp.open.common.enums.ThreadPoolType;
import com.bilibili.miniapp.open.common.util.ThreadPoolUtil;
import com.bilibili.miniapp.open.repository.bo.youku.ShowInfo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.*;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.*;
import java.net.*;
import java.io.*;
import java.nio.channels.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;

import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.*;


@Slf4j
public class LargeJsonProcessor {
    private final ObjectMapper objectMapper;

    private final ConfigCenter configCenter;
    
    public LargeJsonProcessor(ConfigCenter configCenter) {
        this.objectMapper = new ObjectMapper()
            .configure(JsonParser.Feature.AUTO_CLOSE_SOURCE, false)
                .enable(JsonReadFeature.ALLOW_MISSING_VALUES.mappedFeature())
                .enable(JsonReadFeature.ALLOW_TRAILING_COMMA.mappedFeature())
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .disable(StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION.mappedFeature())
            .registerModule(new JavaTimeModule());
        this.configCenter = configCenter;
    }

    public void process(URL jsonUrl, Consumer<List<ShowInfo>> batchConsumer) throws Exception {
        Path tempFile = cacheRemoteFile(jsonUrl);
        processLocalFile(tempFile, batchConsumer);
        Files.deleteIfExists(tempFile);
    }

    private Path cacheRemoteFile(URL url) throws IOException {
        try {
            // 设置全局的 SSLContext
            setUnsecureSSL();
        } catch (Exception e) {
            throw new IOException("Failed to initialize unsecure SSL context", e);
        }
        log.info("Start downloading remote JSON file");
        Path tempFile = Files.createTempFile("json-cache", ".tmp");
        ReadableByteChannel inChannel = Channels.newChannel(url.openStream());
        FileChannel outChannel = FileChannel.open(tempFile, StandardOpenOption.WRITE);
        outChannel.transferFrom(inChannel, 0, Long.MAX_VALUE); // 直接落盘
        log.info("File cached to: {}", tempFile);
        return tempFile;
    }

    private static void setUnsecureSSL() throws Exception {
        // Create a trust manager that does not validate certificate chains
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };

        // Install the all-trusting trust manager
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, new java.security.SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

        // Create an all-trusting host name verifier
        HostnameVerifier allHostsValid = (hostname, session) -> true;

        // Install the all-trusting host verifier
        HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
    }

    private void processLocalFile(Path file, Consumer<List<ShowInfo>> batchConsumer) throws IOException {
        try (InputStream inputStream = Files.newInputStream(file);
             Reader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {

            JsonParser parser = objectMapper.getFactory()
                    .createParser(reader)
                    .enable(JsonParser.Feature.ALLOW_COMMENTS);

            processJsonStream(parser, batchConsumer);
        }
    }

    private void processJsonStream(JsonParser parser, Consumer<List<ShowInfo>> batchConsumer) throws IOException {
        if (parser.nextToken() != JsonToken.START_ARRAY) {
            throw new IllegalStateException("Expected JSON array as root element");
        }

        int processBatchSize = configCenter.getYouKu().getProcessBatchSize();
        List<ShowInfo> batch = new ArrayList<>(processBatchSize);
        int counter = 0;
        
        while (parser.nextToken() != JsonToken.END_ARRAY) {
            ShowInfo entry = objectMapper.readValue(parser, ShowInfo.class);
            batch.add(entry);

            if (++counter % processBatchSize == 0) {
                executeBatchProcessing(new ArrayList<>(batch), batchConsumer);
                batch.clear();
                log.debug("Processed {} items", counter);
            }
        }
        
        if (!batch.isEmpty()) {
            executeBatchProcessing(batch, batchConsumer);
        }
        log.info("Total processed items: {}", counter);
    }

    private void executeBatchProcessing(List<ShowInfo> batch, Consumer<List<ShowInfo>> consumer) {
        if (configCenter.getYouKu().isProcessAsync()) {
            submitBatchProcessingAsync(batch, consumer, ThreadPoolUtil.getExecutor(ThreadPoolType.LARGE_JSON));
        } else {
            submitBatchProcessing(batch, consumer);
        }
    }

    private void submitBatchProcessingAsync(List<ShowInfo> batch, Consumer<List<ShowInfo>> consumer, ExecutorService executorService) {
        executorService.submit(() -> {
            // 修复无限递归发生在Cat组件的TransactionHelper.migrateMessage()方法。该异常源于线程池环境下的上下文传递问题，具体表现为：
            // 父线程和子线程共享Transaction对象导致递归迁移
            // Cat的事务上下文未被正确隔离
            // 在异步任务中重复创建事务导致方法调用栈溢出
            // 原因是consumer中调用了 FunctionUtil.batch() 内部继承了父线程的上下文
            submitBatchProcessing(batch, consumer);
        });
    }

    private void submitBatchProcessing(List<ShowInfo> batch, Consumer<List<ShowInfo>> consumer) {
        Cat.getManager().reset();
        Transaction t = Cat.newTransaction("BatchTask", "Process");
        try {
            consumer.accept(batch);
            t.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            t.setStatus(e);
            log.error("Batch processing failed", e);
        } finally {
            t.complete();
            Cat.logMetricForCount("batch.process.count"); // 显式结束事务
        }
    }
}
