package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import io.grpc.Metadata;
import io.grpc.stub.AbstractStub;
import io.grpc.stub.MetadataUtils;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/1/2 11:57
 */
public abstract class AbstractGrpcClient {

    protected <S extends AbstractStub<S>> S withOptions(S stub, GrpcCallContext context) {
        if (Objects.isNull(context)) {
            return stub;
        }
        S modifiedStub = stub;

        // 处理超时
        if (context.getTimeout() > 0) {
            modifiedStub = modifiedStub.withDeadlineAfter(context.getTimeout(), TimeUnit.MILLISECONDS);
        }

        return modifiedStub;
    }
}
