package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bapis.archive.service.*;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.service.bo.common.ArchiveBo;
import com.google.common.collect.Maps;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/5 17:31
 */
@Slf4j
@Component
public class ArchiveServiceGrpcClient extends AbstractGrpcClient {
    @RPCClient("archive.service")
    private ArchiveGrpc.ArchiveBlockingStub archiveStub;


    public Map<Long, ArchiveBo> queryArchive(List<Long> aidList, GrpcCallContext context) throws Exception {
        Map<Long, ArchiveBo> archiveMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(aidList)) {
            return archiveMap;
        }
        ArcsRequest request = ArcsRequest.newBuilder().addAllAids(aidList).build();
        String requestJson = null;
        try {
            requestJson = JsonFormat.printer().print(request);
            log.info("[ArchiveServiceGrpcClient] arcs request={}", request.getAllFields());
            ArcsReply arcsReply = withOptions(archiveStub, context).arcs(request);
            arcsReply.getArcsMap().forEach((k, v) -> {
                ArchiveBo archiveBo = ArchiveBo.builder()
                        .aid(v.getAid())
                        .cid(v.getFirstCid())
                        .cover(v.getPic())
                        .title(v.getTitle())
                        .desc(v.getDesc())
                        .width((int) v.getDimension().getWidth())
                        .height((int) v.getDimension().getHeight())
                        .duration(v.getDuration())
                        .firstFrame(v.getFirstFrame())
                        .build();
                archiveMap.put(k, archiveBo);
            });
        } catch (Exception e) {
            log.error("[ArchiveServiceGrpcClient] arcs error,request={}", requestJson, e);
            throw e;
        }
        return archiveMap;
    }
}
