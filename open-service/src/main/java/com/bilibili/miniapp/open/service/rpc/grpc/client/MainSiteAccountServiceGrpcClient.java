package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bapis.account.service.AccountGrpc;
import com.bapis.account.service.Info;
import com.bapis.account.service.InfosReply;
import com.bapis.account.service.MidsReq;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.service.bo.up_info.UserInfoBo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.util.JsonFormat;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 主站账号
 * <a href="https://git.bilibili.co/bapis/bapis/-/blob/master/account/service/api.proto">主站账号grpc</a>
 *
 * <AUTHOR>
 * @date 2024/12/27 18:57
 */
@Slf4j
@Component
public class MainSiteAccountServiceGrpcClient extends AbstractGrpcClient {

    @RPCClient("account.service")
    private AccountGrpc.AccountBlockingStub accountBlockingStub;


    /**
     * 查询用户信息
     */
    public UserInfoBo queryUser(Long mid, GrpcCallContext context) throws Exception {
        if (!NumberUtil.isPositive(mid)) {
            return null;
        }
        return this.queryUser(Lists.newArrayList(mid), context).get(mid);
    }

    /**
     * 查询用户信息
     */
    public Map<Long, UserInfoBo> queryUser(List<Long> midList, GrpcCallContext context) {
        Map<Long, UserInfoBo> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(midList)) {
            return result;
        }
        try {
            MidsReq accountRequest = MidsReq.newBuilder().addAllMids(midList).build();
            log.info("[MainSiteAccountServiceGrpcClient] queryUser accountRequest={}", JsonFormat.printer().print(accountRequest));
            InfosReply infoReply = withOptions(accountBlockingStub, context).infos3(accountRequest);
            return midList.stream()
                    .map(mid -> infoReply.getInfosOrDefault(mid, Info.getDefaultInstance()))
                    .filter(info -> NumberUtil.isPositive(info.getMid()))
                    .map(info -> UserInfoBo.builder().mid(info.getMid()).face(info.getFace()).name(info.getName()).build())
                    .collect(Collectors.toMap(UserInfoBo::getMid, Function.identity()));
        } catch (Exception e) {
            log.info("[MainSiteAccountServiceGrpcClient] queryUser error, midList={}", midList, e);
            if (e instanceof StatusRuntimeException) {
                String regex = ".*4\\d{2}.*";
                Pattern pattern = Pattern.compile(regex);
                Status status = ((StatusRuntimeException) e).getStatus();
                if (status != null && status.getDescription() != null && pattern.matcher(status.getDescription()).matches()) {
                    throw new ServiceException(ErrorCodeType.NO_DATA);
                }
            }
            throw new RuntimeException(e);
        }
    }
}
