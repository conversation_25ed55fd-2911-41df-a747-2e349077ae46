package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bapis.passport.service.identify.*;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.service.bo.up_info.CookieInfoBo;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import pleiades.venus.starter.rpc.client.RPCClient;

/**
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Slf4j
@Component
public class MainSitePassportServiceGrpcClient extends AbstractGrpcClient{

    @RPCClient("passport.service.identify")
    private IdentifyGrpc.IdentifyBlockingStub identifyBlockingStub;

    /**
     * 验证cookie是否有效
     */
    public CookieInfoBo identify(String cookie) {
        if (StringUtils.isBlank(cookie)) {
            return CookieInfoBo.builder().build();
        }
        try {
            log.info("[MainSitePassportServiceGrpcClient] getCookieInfo request cookie={}", cookie);
            GetCookieInfoReply cookieInfoReply = identifyBlockingStub.getCookieInfo(GetCookieInfoReq.newBuilder().setCookie(cookie).build());
            log.info("[MainSitePassportServiceGrpcClient] getCookieInfo response={}", JsonFormat.printer().print(cookieInfoReply));
            return CookieInfoBo.builder()
                    .loggedIn(cookieInfoReply.getIsLogin())
                    .mid(cookieInfoReply.getMid())
                    .build();
        } catch (Exception e) {
            log.error("[MainSitePassportServiceGrpcClient] getCookieInfo error", e);
            throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), "failed to verify the legitimacy of user identity");
        }
    }

    public CookieInfoBo identifyToken(String accessKey) {
        if (StringUtils.isBlank(accessKey)) {
            return CookieInfoBo.builder().build();
        }
        try {
            log.info("[MainSitePassportServiceGrpcClient] getCookieInfo request accessKey={}", accessKey);
            GetTokenInfoReply cookieInfoReply = identifyBlockingStub.getTokenInfo(GetTokenInfoReq.newBuilder().setToken(accessKey).build());
            log.info("[MainSitePassportServiceGrpcClient] getCookieInfo response={}", JsonFormat.printer().print(cookieInfoReply));
            return CookieInfoBo.builder()
                    .loggedIn(cookieInfoReply.getIsLogin())
                    .mid(cookieInfoReply.getMid())
                    .build();
        } catch (Exception e) {
            log.error("[MainSitePassportServiceGrpcClient] getCookieInfo error", e);
            throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), "failed to verify the legitimacy of user identity");
        }
    }
}
