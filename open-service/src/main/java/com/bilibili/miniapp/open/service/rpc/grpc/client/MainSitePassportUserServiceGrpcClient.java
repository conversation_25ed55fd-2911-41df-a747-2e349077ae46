package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bapis.passport.service.user.*;
import com.bilibili.miniapp.open.common.entity.GrpcCallContext;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.NumberUtil;
import com.bilibili.miniapp.open.service.bo.up_info.UserDetailBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserSensitiveInfoBo;
import com.bilibili.miniapp.open.service.mapper.UserMapper;
import com.google.protobuf.util.JsonFormat;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description 主站账号信息接口
 * @Date 2025/3/13
 **/
@Slf4j
@Component
public class MainSitePassportUserServiceGrpcClient extends AbstractGrpcClient{

    @RPCClient("passport.service.user")
    private PassportUserGrpc.PassportUserBlockingStub userBlockingStub;

    /**
     * 查询用户信息
     * @param mid
     * @param context
     * @return
     * @throws Exception
     */
    public UserDetailBo getUserDetail(Long mid, GrpcCallContext context) throws Exception {
        if (!NumberUtil.isPositive(mid)) {
            return null;
        }
        try {
            UserDetailReq userDetailReq = UserDetailReq.newBuilder().setMid(mid).build();
            log.info("[MainSitePassportUserServiceGrpcClient] getUserDetail userDetailReq={}", JsonFormat.printer().print(userDetailReq));
            UserDetailReply userDetailReply = withOptions(userBlockingStub, context).userDetail(userDetailReq);
            log.info("[MainSitePassportUserServiceGrpcClient] getUserDetail userDetailReply={}", JsonFormat.printer().print(userDetailReply));
            return UserMapper.USER_MAPPER.rpcReplytoUserDetailBo(userDetailReply);
        } catch (Exception e) {
            log.error("[MainSitePassportUserServiceGrpcClient] getUserDetail error, mid={}", mid, e);
            if (e instanceof StatusRuntimeException) {
                String regex = ".*4\\d{2}.*";
                Pattern pattern = Pattern.compile(regex);
                Status status = ((StatusRuntimeException) e).getStatus();
                if (status != null && status.getDescription() != null && pattern.matcher(status.getDescription()).matches()) {
                    throw new ServiceException(ErrorCodeType.NO_DATA);
                }
            }
            throw e;
        }
    }


    public UserSensitiveInfoBo getUserSensitiveInfo(Long mid, String appId, GrpcCallContext context) throws Exception {
        if (!NumberUtil.isPositive(mid)) {
            return null;
        }
        try {
            UserSensitiveReq userSensitiveReq = UserSensitiveReq.newBuilder()
                    .setMid(mid)
                    .setChallenge(appId)
                    .setPrivacyLogParams(PrivacyInheritParams.newBuilder()
                            .setReqMid(mid)
                            .build())
                    .build();
            log.info("[MainSitePassportUserServiceGrpcClient] getUserSensitiveInfo userSensitiveReq={}", JsonFormat.printer().print(userSensitiveReq));
            UserSensitiveReply userSensitiveReply = withOptions(userBlockingStub, context).userSensitiveInfo(userSensitiveReq);
            log.info("[MainSitePassportUserServiceGrpcClient] getUserSensitiveInfo userDetailReply={}", JsonFormat.printer().print(userSensitiveReply));
            return UserMapper.USER_MAPPER.rpcReplytoUserSensitiveInfoBo(userSensitiveReply);
        } catch (Exception e) {
            log.error("[MainSitePassportUserServiceGrpcClient] getUserSensitiveInfo error, mid={}", mid, e);
            if (e instanceof StatusRuntimeException) {
                String regex = ".*4\\d{2}.*";
                Pattern pattern = Pattern.compile(regex);
                Status status = ((StatusRuntimeException) e).getStatus();
                if (status != null && status.getDescription() != null && pattern.matcher(status.getDescription()).matches()) {
                    throw new ServiceException(ErrorCodeType.NO_DATA);
                }
            }
            throw e;
        }
    }
}
