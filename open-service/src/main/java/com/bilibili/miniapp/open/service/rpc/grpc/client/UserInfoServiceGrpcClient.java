package com.bilibili.miniapp.open.service.rpc.grpc.client;

import com.bapis.account.service.AccountGrpc;
import com.bapis.account.service.Info;
import com.bapis.account.service.InfosReply;
import com.bapis.account.service.MidsReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UserInfoServiceGrpcClient extends AbstractGrpcClient {

    @RPCClient(value = "account.service")
    private AccountGrpc.AccountBlockingStub accountBlockingStub;

    public Map<Long, Info> infoMap(List<Long> mids) {
        if (CollectionUtils.isEmpty(mids)) {
            return Collections.emptyMap();
        }
        try {
            MidsReq midsReq = MidsReq.newBuilder()
                    .addAllMids(mids)
                    .build();
            InfosReply infosReply = accountBlockingStub.infos3(midsReq);
            return infosReply.getInfosMap();
        } catch (Exception e) {
            log.error("根据mid查询up主详情信息时异常，参数:{}", mids, e);
            throw new RuntimeException(e);
        }
    }
}
