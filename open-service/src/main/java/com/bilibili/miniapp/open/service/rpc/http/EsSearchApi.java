package com.bilibili.miniapp.open.service.rpc.http;

import okhttp3.RequestBody;
import org.elasticsearch.action.search.SearchResponse;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Path;

/**
 * <AUTHOR>
 * @date 2025/4/23
 */
@RESTClient(name = "es", host = "discovery://datacenter.search-plat.search-proxy")
public interface EsSearchApi {

    @POST("/v3/proxy/{index}/_search")
    BiliCall<String> search(@Header("AuthToken") String token,
                                    @Path("index") String index,
                                    @Body RequestBody body);

}

