package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.rpc.http.model.GameBindAdAccountInfo;
import com.bilibili.miniapp.open.service.rpc.http.model.GameContractInfo;
import java.util.Map;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;
import retrofit2.http.QueryMap;
import retrofit2.http.QueryName;

/**
 *
 * @link https://info.bilibili.co/pages/viewpage.action?pageId=*********
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
@RESTClient(name= "game-open-platform",host = "discovery://game.tgamesdk.game-open-platform-mng")
public interface GameOpenPlatformApi {


    @GET("/admin/finance/mini/iaa/contract_info")
    BiliCall<Response<GameContractInfo>> getContractInfo(
//            @Query("app_id") String appId,
//            @Query("appkey") String appkey,
//            @Query("ts") Long ts,
//            @Query("sign") String sign

            @QueryMap Map<String ,String> queryMap
    );

    @GET("/admin/finance/mini/ad_account")
    BiliCall<Response<GameBindAdAccountInfo>> getBindAdAccountInfo(
//            @Query("app_id") String appId,
//            @Query("appkey") String appkey,
//            @Query("ts") Long ts,
//            @Query("sign") String sign
            @QueryMap Map<String ,String> queryMap
    );



}
