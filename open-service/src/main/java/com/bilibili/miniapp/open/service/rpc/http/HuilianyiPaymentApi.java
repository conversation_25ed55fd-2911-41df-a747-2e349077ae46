package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.rpc.http.model.*;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.*;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@RESTClient(name= "huilianyi-payment",host = "discovery://ops.fin-api.payproxy")
public interface HuilianyiPaymentApi {


    /**
     * @param param {@link com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest}
     * @return
     */
    @POST("/payproxy/relay/accrual/create")
    BiliCall<Response<CallWrapper<HuilianyiAccrualCreateResult>>> createAccrual(
            @Header("Authorization") String authorization,
            @Body RequestBody param);



    /**
     * @param param {@link com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest}
     * @return
     */
    @POST("/payproxy/relay/accrual/create/async")
    BiliCall<Response<HuilianyiAsyncTask>> createAccrualAsync(
            @Header("Authorization") String authorization,
            @Body RequestBody param);


    /**
     *
     * @param authorization
     * @param param {@link com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest}
     * @return
     */
    @POST("/payproxy/relay/expenseReport/corporate/create")
    BiliCall<Response<CallWrapper<HuilianyiExpenseCreateResult>>> createExpense(
            @Header("Authorization") String authorization,
            @Body RequestBody param);


    @GET("/payproxy/open/query/async/mission/status")
    BiliCall<Response<HuilianyiAsyncTaskResult>> probeTask(@Query("taskId") String taskId);


    /**
     *
     * @param authorization
     * @param param {@link com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateRequest}
     * @return
     */
    @POST("/payproxy/relay/expenseReport/corporate/create/async")
    BiliCall<Response<HuilianyiAsyncTask>> createExpenseAsync(
            @Header("Authorization") String authorization,
            @Body RequestBody param);







    /**
     *
     * @param param {@link com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiTokenAcquireRequest}
     * @return
     */
    @POST("/payproxy/token")
    BiliCall<Response<HuilianyiTokenResult>> acquireToken(

            @Body RequestBody param
    );


    /**
     *
     * @param authorization
     * @param param {@link com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrRequest}
     * @return
     */
    @POST("/payproxy/relay/receipt/ocr")
    BiliCall<Response<CallWrapper<DataWrapper<HuilianyiOcrResult>>>> receiptOcr(
            @Header("Authorization") String authorization,
            @Body RequestBody param
    );


    @POST("/payproxy/relay/upload/attachments")
    @Multipart
    BiliCall<Response<CallWrapper<HuilianyiUploadResult>>> uploadAttachments(
            @Header("Authorization") String authorization,
            @Part MultipartBody.Part file
    );

}
