package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.dto.applet.Code2SessionDTO;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * /miniapp/url/service
 */
@RESTClient(name = "miniapp-app", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppAppletRemoteService {

    String basePath = "/applet/service";

    /**
     * 小程序登录凭证校验
     *
     * @param appid
     * @param secret
     * @param jsCode
     * @param grantType 授权类型，此处只需填写 authorization_code
     * @return
     */
    @GET(value = basePath + "/jscode2session")
    BiliCall<Code2SessionDTO> getCode2Session(@Query(value = "appid") String appid,
                                              @Query(value = "secret") String secret,
                                              @Query(value = "js_code") String jsCode,
                                              @Query(value = "grant_type") String grantType);

}
