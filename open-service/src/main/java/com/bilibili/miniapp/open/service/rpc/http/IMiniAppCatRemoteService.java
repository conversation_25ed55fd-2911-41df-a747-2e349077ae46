package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCatMultiDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCatUpdateQuery;
import com.bilibili.miniapp.open.common.entity.Response;
import okhttp3.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RESTClient(name = "miniapp-app", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppCatRemoteService {

    /**
     * 批量获取小程序拥有的末级类目和类目树映射，没有类目的小程序在映射中不存在key-value
     *
     * @param appIds 企业小程序id
     * @return 小程序不拥有类目时，返回null
     */
    @GET(value = "/miniapp/cat/service/infos")
    BiliCall<Response<Map<String, MiniAppCatMultiDTO>>> getMiniAppMultiCatDTOMaps(@Query(value = "appIds") List<String> appIds);


    /**
     * 修改小程序类目
     *
     * @param miniAppCatUpdateQuery 修改结构体
     * @return 成功true 失败false
     * @see MiniAppCatUpdateQuery
     */
    @POST(value = "/miniapp/cat/service")
    BiliCall<Response<Boolean>> updateMiniAppCats(@Body RequestBody miniAppCatUpdateQuery);

}
