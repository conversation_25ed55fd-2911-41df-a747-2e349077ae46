package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.dto.miniapp.channel.MiniAppChannelMinBaseVersionUpdateRequest;
import com.bilibili.miniapp.open.common.entity.Response;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RESTClient(name = "miniapp-app", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppChannelRemoteService {

    /**
     * 修改小程序渠道发布支持的最低版本库信息
     * @see MiniAppChannelMinBaseVersionUpdateRequest
     */
    @POST(value = "/miniapp/channel/service/miniapp/updateChannelBindInfo")
    BiliCall<Response<Boolean>> updateChannelBindInfo(@Body RequestBody request);

}
