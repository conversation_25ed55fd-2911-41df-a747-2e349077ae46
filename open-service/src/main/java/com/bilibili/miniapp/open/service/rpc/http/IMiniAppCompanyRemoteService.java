package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.dto.miniapp.*;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyAddQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyUpdateQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyUserAddQuery;
import com.bilibili.miniapp.open.common.entity.Response;
import com.github.pagehelper.PageInfo;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import javax.validation.constraints.NotNull;
import java.util.List;

@RESTClient(name = "miniapp-company", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppCompanyRemoteService {

    /**
     * 添加小程序企业
     *
     * @param miniAppCompanyAddQuery 查询结构体
     * @return 小程序企业id companyId
     * @see MiniAppCompanyAddQuery
     */
    @POST(value = "/miniapp/company/service/company")
    BiliCall<Response<String>> insertCompany(@Body RequestBody miniAppCompanyAddQuery);

    /**
     * 更新小程序企业
     *
     * @param miniAppCompanyUpdateQuery 查询结构体
     * @return 小程序企业id companyId
     * @see MiniAppCompanyUpdateQuery
     */
    @POST(value = "/miniapp/company/service/company/modify")
    BiliCall<Response<String>> updateCompany(@Body RequestBody miniAppCompanyUpdateQuery);

    /**
     * 添加小程序企业商家用户
     *
     * @param miniAppCompanyUserAddQuery 查询结构体
     * @return 成功true 失败抛出异常
     * @see MiniAppCompanyUserAddQuery
     */
    @POST(value = "/miniapp/company/service/company/user")
    BiliCall<Response<Boolean>> insertCompanyUser(@Body RequestBody miniAppCompanyUserAddQuery);

    /**
     * 小程序企业列表
     *
     * @param miniAppCompanyQuery 查询结构体
     * @return 小程序企业列表
     * @see MiniAppCompanyQuery
     */
    @POST(value = "/miniapp/company/service/company/user/list")
    BiliCall<Response<PageInfo<MiniAppCompanyUserDetailDTO>>> listCompanyDTOS(@Body RequestBody miniAppCompanyQuery);

    /**
     * 部门列表
     *
     * @return 小程序企业列表
     */
    @GET(value = "/miniapp/company/service/department/list")
    BiliCall<Response<List<MiniAppDepartmentDTO>>> listDepartmentDTOS();

    /**
     * 根据社会统一信用证代码或营业执照注册号获取企业信息
     *
     * @param code 社会统一信用证代码或营业执照注册号
     * @return 小程序企业信息
     */
    @GET(value = "/miniapp/company/service/company")
    BiliCall<Response<MiniAppCompanyDTO>> getCompany(@Query(value = "code") String code);

    /**
     * 小程序企业查询接口
     *
     * @param mid 商家mid
     * @return 商家mid小程序企业开通状态 0 - 未开通 1 - 已开通
     */
    @GET(value = "/miniapp/company/service/status")
    BiliCall<Response<Integer>> getCompanyStatus(@Query(value = "mid") @NotNull Long mid);

    /**
     * 企业开通见证宝
     *
     * @param companyId 企业id
     * @return 成功true 失败false
     */
    @GET(value = "/miniapp/company/service/witness")
    BiliCall<Response<Boolean>> addWitness(@Query(value = "companyId") String companyId);

    /**
     * 获取见证宝登录信息
     *
     * @param companyId 企业id
     * @return 见证宝登录信息
     */
    @GET(value = "/miniapp/company/service/witness/login/info")
    BiliCall<Response<MiniAppCompanyWitnessDTO>> getWitnessLoginInfo(@Query(value = "companyId") String companyId);

    /**
     * 获取小程序企业详情
     *
     * @param companyId 企业id
     * @return 小程序企业详情
     */
    @GET(value = "/miniapp/company/service/company/detail")
    BiliCall<Response<MiniAppCompanyDetailDto>> getCompanyDetail(@Query(value = "companyId") String companyId);

}
