package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppMemberQuery;
import com.bilibili.miniapp.open.common.entity.Response;
import com.github.pagehelper.PageInfo;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RESTClient(name = "miniapp-company", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppMemberRemoteService {

    /**
     * 获取指定成员的小程序信息(非管理员)
     *
     * @see MiniAppMemberQuery
     */
    @POST(value = "/miniapp/member/service/list")
    BiliCall<Response<PageInfo<MiniAppMemberDTO>>> listMiniAppMember(@Body RequestBody miniAppMemberQuery);

}
