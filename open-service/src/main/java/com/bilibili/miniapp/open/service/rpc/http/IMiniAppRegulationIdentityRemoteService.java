package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.regulation.api.dto.BiliApiRespDTO;
import com.bilibili.regulation.api.dto.IdentityResultDto;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/12
 **/

@RESTClient(name = "miniapp-regulation-identity", host = "http://game-regulation-api.bilibili.co")
public interface IMiniAppRegulationIdentityRemoteService {

    @POST(value = "/identity/queryQrCodeV2")
    BiliCall<BiliApiRespDTO<String>> queryQrCode(@Query("token") String token,
                                                 @Body RequestBody req);

    @GET(value = "/identity/sync")
    BiliCall<BiliApiRespDTO<IdentityResultDto>> sync(@Query("token") String token,
                                                     @Query("isp_wzid") Long ispWzid,
                                                     @Query("type") Integer type);
}
