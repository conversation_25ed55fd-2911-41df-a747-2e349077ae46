package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.regulation.api.dto.*;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/21
 **/

@RESTClient(name = "miniapp-regulation", host = "http://game-regulation-api.bilibili.co")
public interface IMiniAppRegulationRemoteService {

    @POST(value = "/beian/report")
    BiliCall<BiliApiRespDTO<ReportBeiAnRespDto>> reportBeiAnInfo(@Query("token") String token,
                                                   @Body RequestBody beiAnInfoRespVo);

    @GET(value = "/beian/config")
    BiliCall<BiliApiRespDTO<IcpConfigDto>> getIcpConfig(@Query("token") String token);

    @GET(value = "/beian/status")
    BiliCall<BiliApiRespDTO<BeiAnStatusDto>> queryBeiAnStatus(@Query("token") String token,
                                                              @Query("zjhm") String zjhm,
                                                              @Query("app_id") String appId,
                                                              @Query("app_name") String appName);

}
