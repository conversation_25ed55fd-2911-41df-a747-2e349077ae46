package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.cmd.miniapp.ImmediatePublishRequest;
import com.bilibili.mall.miniapp.cmd.miniapp.MiniAppStorageUpdateCmd;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppAllStorageDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppStorageCheckDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppStorageCheckUpdateQuery;
import com.bilibili.miniapp.open.common.entity.Response;
import okhttp3.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;
import java.util.Map;

/**
 * /miniapp/storage/service
 *
 * <AUTHOR>
 * @date 2025/3/6
 */
@RESTClient(name = "miniapp-app", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppStorageRemoteService {

    String BASE_PATH = "/miniapp/storage/service";

    /**
     * 获取小程序线上版本列表
     *
     * @return 小程序审核版本列表
     */
    @GET(value = BASE_PATH + "/prod/info")
    BiliCall<Response<Map<String, MiniAppStorageCheckDTO>>> getMiniAppProdStorageInfo(@Query("appIds") List<String> appIds);


    /**
     * 获取小程序开发版本、审核版本、线上版本
     */
    @GET(value = BASE_PATH + "/all")
    BiliCall<Response<MiniAppAllStorageDTO>> getMiniAppAllStorage(@Query("appId") String appId);

    /**
     * 更新小程序开发版本为审核版本
     *
     * @param miniAppStorageUpdateCmd 修改结构体
     * @return 成功true 失败false
     * @see MiniAppStorageUpdateCmd
     */
    @POST(value = BASE_PATH + "/development/check/submit")
    BiliCall<Response<Boolean>> updateDevToCheck(@Body RequestBody miniAppStorageUpdateCmd);

    /**
     * 小程序发布审核
     *
     * @param miniAppStorageCheckUpdateQuery 结构体
     * @return 成功true 失败false
     * @see MiniAppStorageCheckUpdateQuery
     */
    @POST(value = BASE_PATH + "/check/update")
    BiliCall<Response<Boolean>> updateCheck(@Body RequestBody miniAppStorageCheckUpdateQuery);

    /**
     * 全量发布，立即发布
     *
     * @return 成功true 失败false
     * @see ImmediatePublishRequest
     */
    @POST(value = BASE_PATH + "/immediate/publish")
    BiliCall<Response<Boolean>> immediatePublish(@Body RequestBody immediatePublishRequest);

    /**
     * 小程序上传构建包
     *
     * @return
     */
    @POST(value = BASE_PATH + "/code/build/upload/v4")
    BiliCall<Response<Integer>> uploadBuildPackageV4(@Query(value = "url") String url,
                                                     @Query(value = "subUrl") String subUrl,
                                                     @Query(value = "appId") String appId,
                                                     @Query(value = "mid") Long mid,
                                                     @Query(value = "staff") String staff,
                                                     @Query(value = "version") String version,
                                                     @Query(value = "newVersion") String newVersion,
                                                     @Query(value = "description") String description,
                                                     @Query(value = "appletVersion") Integer appletVersion);


}
