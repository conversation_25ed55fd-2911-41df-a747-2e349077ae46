package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.mall.miniapp.query.miniapp.MiniAppUrlUpdateQuery;
import com.bilibili.miniapp.open.common.entity.Response;
import okhttp3.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * /miniapp/url/service
 */
@RESTClient(name = "miniapp-app", host = "discovery://open.mall.mall-miniapp")
public interface IMiniAppUrlRemoteService {

    String basePath = "/miniapp/url/service";

    /**
     * 修改小程序服务器域名
     *
     * @param miniAppUrlUpdateQuery 查询结构体
     * @return 修改结果
     * @see MiniAppUrlUpdateQuery
     */
    @POST(value = basePath + "/url")
    BiliCall<Response<Boolean>> updateServer(@Body RequestBody miniAppUrlUpdateQuery);

}
