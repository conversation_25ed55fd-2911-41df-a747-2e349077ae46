package com.bilibili.miniapp.open.service.rpc.http;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.entity.BiliPayResponse;
import com.bilibili.miniapp.open.service.bo.payment.BpQuickPayInfo;
import com.bilibili.miniapp.open.service.bo.payment.BpQuickPayParam;
import com.bilibili.miniapp.open.service.bo.payment.UserWalletInfo;
import com.bilibili.miniapp.open.service.bo.payment.UserWalletInfoParam;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;


/**
 * 钱包相关接口
 *
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102958069">获取用户钱包B币信息-服务端调用(需签名)</a>
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957973">B币支付</a>
 *
 * <AUTHOR>
 * @date 2025/1/18 20:43
 */
@RESTClient(name = "payplatform-accesslayer", host = "discovery://main.npay.payplatform-accesslayer")
public interface IPayPlatformAccessLayerRemoteService {

    /**
     * 查询钱包B币余额
     *
     * @param param 参考{@link UserWalletInfoParam}
     * @return BiliCall<BiliPayResponse < JSONObject>>
     * @see UserWalletInfoParam
     * @see UserWalletInfo
     */
    @POST(value = "/payplatform/cashier/wallet-int/getUserWalletInfo")
    BiliCall<BiliPayResponse<JSONObject>> getUserWalletInfo(@Body RequestBody param);

    /**
     * B币快捷支付
     *
     * @param param 参考{@link BpQuickPayParam}
     * @return BiliCall<BiliPayResponse < JSONObject>>
     * @see BpQuickPayParam
     * @see BpQuickPayInfo
     */
    @POST(value = "/payplatform/cashier/bp/quick/pay")
    BiliCall<BiliPayResponse<JSONObject>> quickPay(@Body RequestBody param);
}
