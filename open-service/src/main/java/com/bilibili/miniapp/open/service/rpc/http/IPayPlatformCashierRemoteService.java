package com.bilibili.miniapp.open.service.rpc.http;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.entity.BiliPayResponse;
import com.bilibili.miniapp.open.service.bo.payment.BpPaymentRechargeConsumeParam;
import com.bilibili.miniapp.open.service.bo.payment.PayParam;
import com.bilibili.miniapp.open.service.bo.payment.RefundInfo;
import com.bilibili.miniapp.open.service.bo.payment.RefundParam;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * B币支付接口
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=846771222">B币充值和消费</a>
 *
 * <AUTHOR>
 * @date 2025/1/16 17:13
 */
@RESTClient(name = "payplatform-cashier", host = "discovery://main.npay.payplatform-cashier")
public interface IPayPlatformCashierRemoteService {
    /**
     * B币充值和消费一体接口
     *
     * @param param 参考{@link BpPaymentRechargeConsumeParam}
     * @return BiliCall<BiliPayResponse < JSONObject>>
     * @see PayParam
     * @see BpPaymentRechargeConsumeParam
     */
    @POST(value = "/payplatform/cashier/bp/rechargeAndConsume")
    BiliCall<BiliPayResponse<JSONObject>> rechargeAndConsume(@Body RequestBody param);


    /**
     * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957982">业务方发起退款</a>
     * todo: 待确认是不是这个discovery
     *
     * @param param 参考{@link RefundParam}
     * @return BiliCall<BiliPayResponse < JSONObject>>
     * @see RefundParam
     * @see RefundInfo
     */
    @POST(value = "/payplatform/refund/request")
    BiliCall<BiliPayResponse<JSONObject>> refund(@Body RequestBody param);


}
