package com.bilibili.miniapp.open.service.rpc.http;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.entity.BiliPayResponse;
import com.bilibili.miniapp.open.service.bo.payment.RefundInfo;
import com.bilibili.miniapp.open.service.bo.payment.RefundParam;
import okhttp3.RequestBody;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 支付相关
 *
 * <AUTHOR>
 * @date 2025/02/11 21:17
 */
@RESTClient(name = "payplatform-accesslayer", host = "discovery://main.npay.payplatform-accesslayer")
public interface IPayPlatformRemoteService {
    /**
     * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=102957982">业务方发起退款</a>
     *
     * @param param 参考{@link RefundParam}
     * @return BiliCall<BiliPayResponse < JSONObject>>
     * @see RefundParam
     * @see RefundInfo
     */
    @POST(value = "/payplatform/refund/request")
    BiliCall<BiliPayResponse<JSONObject>> refund(@Body RequestBody param);


}
