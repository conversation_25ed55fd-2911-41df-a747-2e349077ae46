package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.miniapp.open.service.bo.youku.MediaResource;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.GET;
import retrofit2.http.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/18
 **/

@RESTClient(name = "youku-server", host = "https://api-yksem.youku.com")
public interface YouKuRemoteService {

    @GET("/api/out/list/getAllAddress")
    BiliCall<List<MediaResource>> getAllAddress(@Query("pid") String pid,
                                                @Query("tm") long tm,
                                                @Query("type") String type,
                                                @Query("sign") String sign,
                                                @Query("category") String category);

    @GET("/api/out/list/getIncrementAddress")
    BiliCall<List<String>> getIncrementAddress(@Query("pid") String pid,
                                                @Query("tm") long tm,
                                                @Query("hour") Integer hour,
                                                @Query("type") String type,
                                                @Query("sign") String sign,
                                                @Query("category") String category);
}
