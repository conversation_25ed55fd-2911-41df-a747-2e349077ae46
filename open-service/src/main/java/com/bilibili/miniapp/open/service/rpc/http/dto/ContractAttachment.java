package com.bilibili.miniapp.open.service.rpc.http.dto;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import javax.validation.constraints.NotBlank;

/**
 * 合同附件
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class ContractAttachment {
    
    /**
     * 模板名称（必填）
     */
    @NotNull
    private String name;

    /**
     * 模板boss key（必填）
     */
    @NotNull
    private String bossKey;
}