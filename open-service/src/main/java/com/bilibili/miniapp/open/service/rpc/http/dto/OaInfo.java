package com.bilibili.miniapp.open.service.rpc.http.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OA审核信息
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OaInfo {
    
    /**
     * 原合同ID
     */
    private String originContractId;
    
    /**
     * 财务条款
     */
    private String financial;
    
    /**
     * 是否框架合同
     * 0-不是
     * 1-是
     */
    private Integer isFrameworkContract;
    
    /**
     * 收款/付款
     */
    private String payment;
    
    /**
     * 币种
     */
    private String currency;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 业务风险
     */
    private String businessRisk;
    
    /**
     * 验收方式
     */
    private String acceptance;
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 合作模式
     */
    private String cooperation;
    
    /**
     * 变更补充项
     */
    private String changeSupplement;
    
    /**
     * 合同续约、解约条款
     */
    private String extension;
    
    /**
     * OA发起合同类型
     * 0 - 起草合同
     * 1 - 补充合同
     * 2 - 终止合同
     */
    private Long oaContractType;
    
    /**
     * 原OA合同ID
     */
    private String originOaContractId;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 金额
     */
    private Long amount;
    
    /**
     * 合同全称
     */
    private String contractFullName;
    
    /**
     * 是否超预算
     * 1 - 是
     * 2 - 否
     */
    private Long overBudget;
    
    /**
     * 合同类别
     */
    private String contractType;
    
    /**
     * 我方取得的所有权，知识产权
     */
    private String property;
    
    /**
     * 收付款方式
     */
    private String paymentType;
    
    /**
     * 结束时间
     */
    private Long endTime;
}
