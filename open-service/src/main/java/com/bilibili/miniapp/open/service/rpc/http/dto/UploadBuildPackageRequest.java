package com.bilibili.miniapp.open.service.rpc.http.dto;

import lombok.Data;

@Data
public class UploadBuildPackageRequest {
    private String url;
    /**
     * 非必传
     */
    private String subUrl;
    private String appId;
    /**
     * 非必传
     */
    private Long mid;
    /**
     * 非必传
     */
    private String staff;
    private String version;
    private String newVersion;
    private String description;
    /**
     * 非必传，默认1，代表新框架小程序
     */
    private Integer appletVersion;
}