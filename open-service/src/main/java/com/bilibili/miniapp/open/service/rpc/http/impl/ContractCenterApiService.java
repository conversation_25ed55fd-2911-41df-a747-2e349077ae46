package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.ContractCenterApi;
import com.bilibili.miniapp.open.service.rpc.http.dto.ContractSignUrlResult;
import com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractRequest;
import com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractResult;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@Service
@Slf4j
public class ContractCenterApiService extends AbstractOpenService {

    @Autowired
    private ContractCenterApi contractCenterApi;


    public CreateContractResult createContract(CreateContractRequest request) {
        log.info("[ContractCenterApiService] 创建结算合同, request={}", JSON.toJSONString(request));
        CreateContractResult result = call("创建结算合同", contractCenterApi::createContract, request);
        log.info("[ContractCenterApiService] 创建结算合同成功, result={}", JSON.toJSONString(result));
        return result;
    }


    public ContractSignUrlResult getContractSignUrl(String contractId) {
        return call("获取合同签署链接", () -> contractCenterApi.getSignUrl(contractId, "乙方"));
    }
}
