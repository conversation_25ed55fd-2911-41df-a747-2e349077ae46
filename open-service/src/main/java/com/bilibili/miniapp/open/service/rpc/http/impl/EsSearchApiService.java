package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.EsConfig;
import com.bilibili.miniapp.open.service.rpc.http.EsSearchApi;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.xcontent.LoggingDeprecationHandler;
import org.elasticsearch.xcontent.NamedXContentRegistry;
import org.elasticsearch.xcontent.XContentFactory;
import org.elasticsearch.xcontent.XContentParser;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/23
 */
@Component
@Slf4j
public class EsSearchApiService extends AbstractOpenService  {

    @Autowired
    private EsSearchApi esSearchApi;

    @Autowired
    private ConfigCenter configCenter;

    public SearchResponse searchEventLog(String index, String request) {
        String token = configCenter.getEsConfig().getToken();
        String response = callWithoutWrap("查询小程序EventLog",
                () -> esSearchApi.search(token, index, toRequestBody(request)));
        try {
            XContentParser parser = XContentFactory.xContent(XContentType.JSON)
                    .createParser(NamedXContentRegistry.EMPTY,
                            LoggingDeprecationHandler.INSTANCE,
                            response);
            return SearchResponse.fromXContent(parser);
        } catch (Exception e) {
            log.error("解析ES响应失败", e);
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR.getCode(), "解析ES响应失败");
        }
    }
}
