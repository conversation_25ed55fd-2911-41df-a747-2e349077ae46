package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.dto.applet.Code2SessionDTO;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppAppletRemoteService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MiniAppAppletRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppAppletRemoteService appletRemoteService;

    public Code2SessionDTO getCode2Session(String appid, String secret, String jsCode, String grantType) {
        return callWithoutWrap("获取Code2Session",
                () -> appletRemoteService.getCode2Session(appid, secret, jsCode, grantType));
    }
}
