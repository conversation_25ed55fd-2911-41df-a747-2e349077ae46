package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCatMultiDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyDetailDto;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyUserDetailDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCatUpdateQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyAddQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyUserAddQuery;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppCatRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppCompanyRemoteService;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Component;
import pleiades.component.http.client.BiliCall;
import retrofit2.http.Query;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MiniAppCatRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppCatRemoteService catRemoteService;

    public Map<String, MiniAppCatMultiDTO> getMiniAppMultiCatMap(List<String> appIds) {
        return call("获取指定小程序的类目信息", () -> catRemoteService.getMiniAppMultiCatDTOMaps(appIds));
    }

    public boolean updateMiniAppCats(String appId, List<Integer> catIds) {
        MiniAppCatUpdateQuery catUpdateQuery = MiniAppCatUpdateQuery.builder()
                .appId(appId)
                .catIdList(catIds)
                .build();
        return call("更新小程序的类目信息",
                catRemoteService::updateMiniAppCats,
                catUpdateQuery);
    }
}
