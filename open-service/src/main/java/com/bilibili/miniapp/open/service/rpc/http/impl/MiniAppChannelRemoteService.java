package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.dto.miniapp.channel.MiniAppChannelMinBaseVersionUpdateRequest;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppChannelRemoteService;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.starter.http.client.RESTClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

@Component
public class MiniAppChannelRemoteService extends AbstractOpenService {


    @Autowired
    private IMiniAppChannelRemoteService miniAppChannelRemoteService;


    public boolean updateChannelBindInfo(MiniAppChannelMinBaseVersionUpdateRequest request) {
        return call("更新小程序版本信息",
                miniAppChannelRemoteService::updateChannelBindInfo,
                request);
    }
}
