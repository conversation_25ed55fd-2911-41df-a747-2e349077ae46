package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyDetailDto;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppCompanyUserDetailDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyAddQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyQuery;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppCompanyUserAddQuery;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppCompanyRemoteService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MiniAppCompanyRemoteService extends AbstractOpenService {

    @Autowired
    private IMiniAppCompanyRemoteService miniAppCompanyRemoteService;

    /**
     * @return company_id 企业id
     */
    public String createCompany(MiniAppCompanyAddQuery miniAppCompanyAddQuery) {
        return call("新建企业", miniAppCompanyRemoteService::insertCompany, miniAppCompanyAddQuery);
    }

    public Boolean bindCompanyUser(Long mid, String companyId) {
        return call("新建企业用户",
                miniAppCompanyRemoteService::insertCompanyUser,
                new MiniAppCompanyUserAddQuery(mid, companyId));
    }

    public PageInfo<MiniAppCompanyUserDetailDTO> listCompanies(MiniAppCompanyQuery miniAppCompanyQuery) {
        return call("获取企业列表",
                miniAppCompanyRemoteService::listCompanyDTOS,
                miniAppCompanyQuery);
    }

    public MiniAppCompanyDTO getCompany(String companyId) {
        return call("获取企业信息",
                () -> miniAppCompanyRemoteService.getCompany(companyId));
    }

    public MiniAppCompanyDetailDto getCompanyDetail(String companyId) {
        return call("获取企业信息详情",
                () -> miniAppCompanyRemoteService.getCompanyDetail(companyId));
    }

}
