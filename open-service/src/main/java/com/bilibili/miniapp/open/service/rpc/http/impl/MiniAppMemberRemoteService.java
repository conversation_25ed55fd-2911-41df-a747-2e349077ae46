package com.bilibili.miniapp.open.service.rpc.http.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.mall.miniapp.enums.MiniAppUserTypeEnum;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppMemberQuery;
import com.bilibili.miniapp.open.repository.redis.RedisKeyPattern;
import com.bilibili.miniapp.open.repository.redis.redisson.RedissonCacheRepository;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppMemberRemoteService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Component
public class MiniAppMemberRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppMemberRemoteService memberRemoteService;
    @Autowired
    private RedissonCacheRepository redis;

    public PageInfo<MiniAppMemberDTO> listMiniAppMember(MiniAppMemberQuery miniAppMemberQuery) {
        return call("获取指定成员的小程序信息(非管理员)",
                memberRemoteService::listMiniAppMember,
                miniAppMemberQuery);
    }

    public List<MiniAppMemberDTO> queryMemberInfo(long mid) {
        MiniAppMemberQuery miniAppMemberQuery = new MiniAppMemberQuery();
        miniAppMemberQuery.setUser(String.valueOf(mid));
        miniAppMemberQuery.setPageNum(1);
        //mall-miniapp默认限制最大为100
        miniAppMemberQuery.setPageSize(100);
        miniAppMemberQuery.setUserType(MiniAppUserTypeEnum.UID.getUserType());
        PageInfo<MiniAppMemberDTO> page = listMiniAppMember(miniAppMemberQuery);
        if (CollectionUtils.isNotEmpty(page.getList())) {
            return page.getList();
        } else {
            return new ArrayList<>();
        }
    }

    public List<MiniAppMemberDTO> queryMemberInfoFromCache(long mid) {
        String key = StrUtil.format(RedisKeyPattern.MID_MEMBER_APP_LIST.getPattern(), mid);
        List<MiniAppMemberDTO> cache = redis.getObject(key, new TypeReference<>() {
        });
        if (cache != null) {
            return cache;
        }

        List<MiniAppMemberDTO> result = queryMemberInfo(mid);
        if (result == null) {
            result = new ArrayList<>();
        }
        redis.setObject(key, result, 5, TimeUnit.MINUTES);
        return result;
    }
}
