package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.OkHttpUtil;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppRegulationIdentityRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppRegulationRemoteService;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.bilibili.regulation.api.dto.*;
import com.bilibili.regulation.common.constant.Constants;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/22
 **/

@Service
@Slf4j
public class MiniAppRegulationRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppRegulationRemoteService regulationRemoteService;

    @Resource
    private IMiniAppRegulationIdentityRemoteService regulationIdentityRemoteService;

    @Resource
    private ConfigCenter configCenter;

    public ReportBeiAnRespDto reportBeiAnInfo(BeiAnInfoDto beiAnInfoDto) {
        if (isUat()) {
            BiliApiRespDTO<?> response = OkHttpUtil.bodyPost(getRegulationHost() + "/beian/report?token=" + getToken())
                    .param("token", getToken())
                    .json(JsonUtil.writeValueAsString(beiAnInfoDto))
                    .callForObjectUnsafe(BiliApiRespDTO.class);
            if (!Objects.equals(Constants.CODE_SUCCESS, response.getCode())) {
                log.info("[reportBeiAnInfo] error, code={}, msg={}", response.getCode(), response.getMessage());
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), response.getMessage());
            }
            return JsonUtil.readValue(JsonUtil.writeValueAsString(response.getData()), ReportBeiAnRespDto.class);
        }
        return callBiliResponse("上报备案数据", regulationRemoteService::reportBeiAnInfo, beiAnInfoDto, getToken());
    }

    public String queryQrCodeV2(IdentityAuthInfoDto identityAuthInfoDto) {
        if (isUat()) {
            BiliApiRespDTO<?> response = OkHttpUtil.bodyPost(getRegulationHost() + "/identity/queryQrCodeV2?token=" + getToken())
                    .json(JsonUtil.writeValueAsString(identityAuthInfoDto))
                    .callForObjectUnsafe(BiliApiRespDTO.class);
            if (!Objects.equals(Constants.CODE_SUCCESS, response.getCode())) {
                log.info("[queryQrCode] error, code={}, msg={}", response.getCode(), response.getMessage());
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), response.getMessage());
            }
            return JsonUtil.readValue(JsonUtil.writeValueAsString(response.getData()), String.class);
        }
        return callBiliResponse("人证核验获取二维码", regulationIdentityRemoteService::queryQrCode, identityAuthInfoDto, getToken());
    }

    public IdentityResultDto sync(Long ispWzid, Integer type) {
        if (isUat()) {
            BiliApiRespDTO<?> response = OkHttpUtil.get(getRegulationHost() + "/identity/sync?token=" + getToken() + "&isp_wzid=" + ispWzid + "&type=" + type)
                    .callForObjectUnsafe(BiliApiRespDTO.class);
            if (!Objects.equals(Constants.CODE_SUCCESS, response.getCode())) {
                log.info("[queryQrCode] error, code={}, msg={}", response.getCode(), response.getMessage());
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), response.getMessage());
            }
            return JsonUtil.readValue(JsonUtil.writeValueAsString(response.getData()), IdentityResultDto.class);
        }
        return callBiliResponse("人证核验同步素材", () -> regulationIdentityRemoteService.sync(getToken(), ispWzid, type));
    }

    public BeiAnStatusDto queryBeiAnStatus(String zjhm, String appName, String appId) {
        if (isUat()) {
            BiliApiRespDTO<?> response = OkHttpUtil.get(getRegulationHost() + "/beian/status?token=" + getToken() + "&zjhm=" + zjhm + "&app_name=" + appName + "&app_id=" + appId)
                    .callForObjectUnsafe(BiliApiRespDTO.class);
            if (!Objects.equals(Constants.CODE_SUCCESS, response.getCode())) {
                log.info("[queryQrCode] error, code={}, msg={}", response.getCode(), response.getMessage());
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), response.getMessage());
            }
            return JsonUtil.readValue(JsonUtil.writeValueAsString(response.getData()), BeiAnStatusDto.class);
        }
        return callBiliResponse("查询备案状态", () -> regulationRemoteService.queryBeiAnStatus(getToken(), zjhm, appId, appName));
    }

    private String getRegulationHost() {
        return configCenter.getRegulation().getHost();
    }
    private String getToken() {
        return configCenter.getRegulation().getToken();
    }

    private boolean isUat() {
        return "uat".equals(configCenter.getEnv());
    }

}
