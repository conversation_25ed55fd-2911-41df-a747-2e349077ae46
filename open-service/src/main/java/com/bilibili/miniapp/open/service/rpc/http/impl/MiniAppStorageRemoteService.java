package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.cmd.miniapp.ImmediatePublishRequest;
import com.bilibili.mall.miniapp.cmd.miniapp.MiniAppStorageUpdateCmd;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppAllStorageDTO;
import com.bilibili.mall.miniapp.dto.miniapp.MiniAppStorageCheckDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppStorageCheckUpdateQuery;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppStorageRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.dto.UploadBuildPackageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import retrofit2.http.Body;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@Component
public class MiniAppStorageRemoteService extends AbstractOpenService {

    @Autowired
    private IMiniAppStorageRemoteService miniAppStorageRemoteService;

    public Map<String, MiniAppStorageCheckDTO> getMiniAppProdStorageInfo(List<String> appIds) {
        return call("查询指定appIds上线的小程序",
                () -> miniAppStorageRemoteService.getMiniAppProdStorageInfo(appIds));
    }

    public MiniAppAllStorageDTO getAllStorage(String appId) {
        return call("查询指定appId上线的小程序",
                () -> miniAppStorageRemoteService.getMiniAppAllStorage(appId));
    }

    public boolean updateDevToCheck(MiniAppStorageUpdateCmd miniAppStorageUpdateCmd) {
        return call("更新小程序为审核状态",
                miniAppStorageRemoteService::updateDevToCheck,
                miniAppStorageUpdateCmd);
    }

    public boolean passOrRejectAppPublish(MiniAppStorageCheckUpdateQuery updateQuery) {
        return call("审核小程序发布",
                miniAppStorageRemoteService::updateCheck,
                updateQuery);
    }

    public boolean immediatePublish(ImmediatePublishRequest publishRequest) {
        return call("立即发布小程序",
                miniAppStorageRemoteService::immediatePublish,
                publishRequest);
    }

    public Integer uploadBuildPackageV4(UploadBuildPackageRequest req) {
        return call("上传小程序代码",
                () -> miniAppStorageRemoteService.uploadBuildPackageV4(
                        req.getUrl(),
                        req.getSubUrl(),
                        req.getAppId(),
                        req.getMid(),
                        req.getStaff(),
                        req.getVersion(),
                        req.getNewVersion(),
                        req.getDescription(),
                        req.getAppletVersion())
        );
    }
}
