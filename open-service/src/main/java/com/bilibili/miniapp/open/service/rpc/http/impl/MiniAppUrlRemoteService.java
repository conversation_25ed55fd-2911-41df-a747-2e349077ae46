package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.query.miniapp.MiniAppUrlUpdateQuery;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppUrlRemoteService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Component
public class MiniAppUrlRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppUrlRemoteService urlRemoteService;

    public boolean updateServer(MiniAppUrlUpdateQuery miniAppUrlUpdateQuery) {
        return call("更新小程序白名单域名",
                urlRemoteService::updateServer,
                miniAppUrlUpdateQuery);
    }
}
