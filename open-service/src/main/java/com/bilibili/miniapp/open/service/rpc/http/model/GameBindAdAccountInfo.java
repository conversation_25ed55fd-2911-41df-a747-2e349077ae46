package com.bilibili.miniapp.open.service.rpc.http.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
@Data
@Accessors(chain = true)
public class GameBindAdAccountInfo implements SnakeCaseBody {



    private Long userId;

    //
    private String adAccountId;

}
