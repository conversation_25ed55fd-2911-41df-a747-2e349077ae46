package com.bilibili.miniapp.open.service.rpc.http.model;

import com.bilibili.miniapp.open.repository.mysql.settlement.po.SnakeCaseBody;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
@Data
@Accessors(chain = true)
public class GameContractInfo implements SnakeCaseBody {

    private static final DateTimeFormatter contractTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    private String eContractId;

    private String contractId;

    private Long userId;

    private String companyName;

    private String startTime;

    private String endTime;

    private String supplierCode;

    private String subBankName;

    private String bankAccount;



    public LocalDate getStartTimeAsLocalDate() {
        return LocalDate.parse(startTime, contractTimeFormatter);
    }

    public LocalDate getEndTimeAsLocalDate() {
        return LocalDate.parse(endTime, contractTimeFormatter);
    }
}
