package com.bilibili.miniapp.open.service.rpc.http.model;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

/**
 * {@see https://opendocs.huilianyi.com/implement/business-data/accrual/create-accrual.html}
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@Data
@Accessors(chain = true)
public class HuilianyiAccrualCreateRequest {

    private AccrualCreateInput accrualCreateInput;


    @Data
    @Accessors(chain = true)
    public static class AccrualCreateInput {


        private OpenCreateAccrualHeadDTO accrualHead;

        private List<OpenCreateAccrualLineDTO> accrualLines;


    }



    /**
     * 预提单头信息DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenCreateAccrualHeadDTO {
        /**
         * 预提单表单编码（必填）
         */
        private String formCode;

        /**
         * 申请人工号（必填）
         */
        private String applicantEmployeeId;

        /**
         * 预提单单号（非必填）
         */
        private String businessCode;

        /**
         * 预提单状态：1001-编辑中,1002-审批中（非必填，默认1002）
         */
        private Integer status;

        /**
         * 自定义扩展字段（非必填）
         */
        private List<CustomFormValueDTO> customFormValues;

        // 其他字段参考文档添加...
        // getters & setters...
    }

    /**
     * 预提行信息DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenCreateAccrualLineDTO {
        /**
         * 费用类型编码（必填）
         */
        private String expenseTypeCode;

        /**
         * 金额（必填）
         */
        private BigDecimal amount;

        /**
         * 备注（非必填）
         */
        private String remark;

        /**
         * 预提分摊行列表（非必填）
         */
        private List<OpenCreateAccrualApportionmentDTO> accrualApportionmentDTOList;

        /**
         * 费用行自定义字段（非必填）
         */
        private List<OpenCreateAccrualExpenseTypeFieldDTO> lineFieldList;

        // 其他字段参考文档添加...
        // getters & setters...
    }

    /**
     * 自定义表单值DTO
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomFormValueDTO {
        /**
         * 控件编码（非必填）
         */
        private String fieldCode;

        /**
         * 控件值（非必填）
         */
        private String value;

        // getters & setters...
    }

    /**
     * 预提分摊行DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenCreateAccrualApportionmentDTO {
        /**
         * 分摊金额（必填）
         */
        private BigDecimal amount;

        /**
         * 分摊项列表（必填）
         */
        private List<OpenApportionViewRequestDTO> openApportionViewRequestDTOList;

        // 其他字段参考文档添加...
        // getters & setters...
    }

    /**
     * 分摊项视图请求DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenApportionViewRequestDTO {
        /**
         * 分摊类型：department/costCenter（必填）
         */
        private String fieldType;

        /**
         * 成本中心编码（当fieldType=costCenter时必填）
         */
        private String costCenterCode;

        /**
         * 部门/成本中心项编码（必填）
         */
        private String costCenterItemCode;

        // getters & setters...
    }

    /**
     * 费用行自定义字段DTO
     */
    @Data
    @Accessors(chain = true)
    public  static class OpenCreateAccrualExpenseTypeFieldDTO {
        /**
         * 字段标识（必填，如com_budget）
         */
        private String messageKey;

        /**
         * 字段值（非必填）
         */
        private String value;

        /**
         * 控件编码（非必填）
         */
        private String fieldCode;

        // getters & setters...
    }



}
