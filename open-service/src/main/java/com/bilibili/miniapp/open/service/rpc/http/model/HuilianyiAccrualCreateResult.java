package com.bilibili.miniapp.open.service.rpc.http.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/20
 */
@Data
@Accessors(chain = true)
public class HuilianyiAccrualCreateResult {


    private String message;

    /**
     * 0000： 成功
     *
     *
     * 120003	缺少必填项:{0}	无
     * 120514	申请人不在成本中心项中	无
     * 129707	分摊行金额不能为空	无
     * 129708	分摊项不能为空	无
     * 129709	分摊项类型不能为空	无
     * 129710	分摊项类型不正确	无
     * 129711	分摊项编码不能为空	无
     * 129712	分摊部门{0}不存在	无
     * 129713	成本中心分摊项对应的成本中心code不能为空	无
     * 129714	分摊成本中心{0}不存在	无
     * 129715	分摊成本中心项{0}不存在	无
     * 120602	费用类型不存在	无
     * 129784	预提行费用数据异常:{0}	无
     * 129785	预提行费用自定义字段异常:{0}	无
     * 129786	预提行费用自定义字段必填:{0}	无
     * 129790	预提行费用自定义字段超长:{0}	无
     * 129791	配置了单选却传入了多个值	无
     * 129792	控件未配置值列表:{0}	无
     * 129793	控件未配置有效值列表:{0}	无
     * 129794	{0}值编码 {1} 无对应数据	无
     * 129795	数字控件非数字	无
     * 129796	数字控件精度异常	无
     * 129797	日期格式异常	无
     * 129798	时间处理异常	无
     * 129799	工号无效	无
     * 129800	存在异常城市数据	无
     * 129801	供应商数据异常	无
     * 129802	openapi创预提单扩展字段附件控件值转换异常	无
     * 129803	预提单附件个数不超过三个	无
     * 12810007	字段{0}超长,请检查！	无
     * 12810004	金额格式有误或金额小于等于零,当前录入金额：{0}	无
     * 129829	只能创建预提来源为手工的单据类型	无
     * 129836	供应商不存在	无
     * 120182	用户主岗信息不存在	无
     * 120183	岗位账套信息异常	无
     * 129844	申请单{0}不可被关联	无
     * 129845	对公申请单{0}不可被关联	无
     * 129846	合同{0}不可被关联	无
     * 129847	传入的entityType：{0}有误	无
     * 129848	申请单分摊行{0}，不在传入的关联申请单之中	无
     * 129849	对公申请单分摊行{0}，不在传入的关联对公申请单之中	无
     * 129850	合同分摊行{0}，不在传入的关联合同之中	无
     * 129851	表单未开启关联申请	无
     * 129852	表单未开启关联对公申请	无
     * 129853	表单未开启关联合同	无
     * 129854	申请单费用行，其中的分摊行参数，存在申请分摊行id为空，或者存在重复分摊行id	无
     * 129855	对公申请单费用行，其中的分摊行参数，存在申请分摊行id为空，或者存在重复分摊行id	无
     * 129856	合同费用行，其中的分摊行参数，存在申请分摊行id为空，或者存在重复分摊行id	无
     * 12fin-86378@129841	{0}分摊行{1}，本次预提金额{2}大于可预提金额{3}	无
     * 129857	预提单单据头自定义字段格式有误：{0}	无
     * 129858	没有关联单据信息，却有关联分摊行id：{0}	无
     * 129883	传入的相关人存在离职或不存在的数据	无
     * 129900	项目任务存在相同的code：{0}	无
     * 129897	该供应商没有银行账户：{0}	无
     * 129898	传了供应商银行账户，却没有传供应商：{0}	无
     * 129899	该供应商银行账户：{0}，不属于该供应商：{1}	无
     * 12130001	费用类型{0}不属于表单可关联范围	无
     * 12130028	费用行code存在重复的费用行code	无
     * 12130030	费用行编号：{0}，超过了最大长度50	无
     * 12130031	传了外部员工账户，却没有传外部员工：{0}	无
     * 12130032	该外部员工没有银行账户：{0}	无
     * 12130033	该银行账户：{0}，不属于该外部员工：{1}	无
     * 12130034	预提单头子级成本中心项【{0}】的父级成本中心项【{1}】的归属关系有误	无
     * 12130035	预提单分摊行子级成本中心项【{0}】的父级成本中心项【{1}】的归属关系有误	无
     * 12130036	外部员工工号中，有不存在的员工	无
     * 12130037	外部员工工号中，有离职的员工	无
     * 12130038	外部员工传多个时，不允许传外部员工账号	无
     * 12130039	同一个费用行中，不允许同时存在正数和负数金额的分摊行	无
     * 12130040	预提单设置没有开启负数金额	无
     * 12130057	预提单和费用下的供应商存在申请人无权限的供应商，请检查供应商权限	无
     * 12fin-91733@12130066	费用编号{0}，分摊行金额{1}，成本中心{2}不属于公司、部门、相关人的可见范围下	无
     * 12fin-91733@12130067	费用编号{0}，分摊行金额{1}，项目任务{2}不属于公司、部门、相关人的可见范围下	无
     * 12130073	必填字段[分摊备注]为空	无
     * 12fin-94072@12130074	单据头【成本中心：{0}】已禁用，请返回修改	无
     * 12fin-94072@12130075	费用行编号：{0}，分摊金额：{1}，【成本中心：{2}】已禁用，请返回修改	无
     * 12fin-94072@12130076	单据头【成本中心项：{0}】已禁用，请返回修改	无
     * 12fin-94072@12130077	费用行编号：{0}，分摊金额：{1}，【成本中心项：{2}】已禁用，请返回修改	无
     * 12fin-94260@12130081	复制审批历史的单号找不到删除记录，请检查是否错误	无
     */
    private String errorCode;


    /**
     * 预提单编码
     */
    private String key;

}
