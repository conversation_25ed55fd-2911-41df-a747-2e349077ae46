package com.bilibili.miniapp.open.service.rpc.http.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/25
 */
@Data
public class HuilianyiAsyncTaskResult {


    private String message;

    private String errorCode;

    private String key;

    private String taskId;



    public boolean isSuccess(){

        return "0000".equals(errorCode) && key != null;
    }

    public boolean isFailed(){

        return !isSuccess() && !"任务进行中".equals(message);
    }


    public boolean isProcessing(){

        return !isSuccess() && !isFailed();
    }


}
