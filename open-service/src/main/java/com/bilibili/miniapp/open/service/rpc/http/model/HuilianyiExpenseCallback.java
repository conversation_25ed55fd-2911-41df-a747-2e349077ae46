package com.bilibili.miniapp.open.service.rpc.http.model;

import com.google.common.collect.Sets;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class HuilianyiExpenseCallback {


    private String businessCode;

    private String expenseType;

    private String expenseNameCode;

    private String workCode;


    private String deptName;


    private String expenseDept;

    /**
     * 101 关闭
     * 102 审批通过
     * 103 已删除
     * 104 付款失败
     * 105 付款成功
     * 106 退票
     * 107 审批驳回
     * 108 支付单失败
     * 109 支付单成功
     */
    private String expenseStatus;

    private String payFailReason;

    public boolean isSuccess() {

        return "105".equals(expenseStatus);
    }

    public boolean isFail() {
        return Sets.newHashSet("101","104", "106", "107", "108").contains(expenseStatus);
    }
}
