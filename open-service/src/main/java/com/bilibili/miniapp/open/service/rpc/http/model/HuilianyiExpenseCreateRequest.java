package com.bilibili.miniapp.open.service.rpc.http.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * https://opendocs.huilianyi.com/implement/business-data/expense-report/create-corp-expense-report.html
 *  根据这个地址  生成java对象， 并使用@Data@Accessors(chain = true)注解标记，并生成注释
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class HuilianyiExpenseCreateRequest {


    private CorporatePaymentCreateRequestDTO expenseCreateInput;


    /**
     * 对公支付单创建请求DTO
     */
    @Data
    @Accessors(chain = true)
    public static class CorporatePaymentCreateRequestDTO {
        /**
         * 单据申请人工号（必填）
         */
        private String employeeId;

        /**
         * 单据创建人工号（创建代理制单时使用）
         */
        private String createdByEmployeeId;

        /**
         * 表单编码（必填）
         */
        private String formCode;

        /**
         * 合同编号
         */
        private String contractNumber;

        private List<String> contractNumberList;

        /**
         * 关联的申请单单号
         */
        private String referenceApplicationBusinessCode;

        /**
         * 付款方式编码
         */
        private String paymentMethodCode;


        private String businessCode;

        /**
         * 银行卡号
         */
        private String accountNumber;

        private List<String> accrualBusinessCodeList;

        /**
         * 计划付款日期（UTC格式）
         */
        private Date planedPaymentDate;

        /**
         * 是否自动提交单据
         */
        private Boolean autoSubmit;

        /**
         * 自定义表单值列表（必填）
         */
        private List<OpenCustomFormValueRequestDTO> openCustomFormValueRequestDTOList;

        /**
         * 付款计划列表
         */
        private List<OpenPaymentScheduleRequestDTO> openPaymentScheduleRequestDTOList;

        // 其他字段根据文档补充...
    }

    /**
     * 自定义表单值DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenCustomFormValueRequestDTO {
        /**
         * 控件编码（fieldCode和fieldOID二选一）
         */
        private String fieldCode;

        /**
         * 控件OID（fieldCode和fieldOID二选一）
         */
        private String fieldOID;

        /**
         * 控件值
         */
        private String value;

        /**
         * 是否唯一字段（仅单行输入框支持）
         */
        private Boolean uniqueField;

        public OpenCustomFormValueRequestDTO(String fieldCode, String value) {
            this.fieldCode = fieldCode;
            this.value = value;
        }
    }

    /**
     * 付款计划DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenPaymentScheduleRequestDTO {
        /**
         * 付款行类型（1001-到票付款行，1002-前期发票，1003-预付付款行）
         */
        private Integer paymentScheduleType;


        private String paymentMethodCode;

        /**
         * 本次支付金额（BigDecimal类型）
         */
        private BigDecimal realPaymentAmount;

        /**
         * 银行卡号
         */
        private String accountNumber;

        /**
         * 计划付款日期（UTC格式）
         */
        private Date planedPaymentDate;


        private OpenInvoiceCreateDTO openInvoiceCreateDTO;

        private List<OpenAccrualWriteOffRequestDTO> openAccrualWriteOffRequestDTOList;


        /**
         * 合同关联信息列表
         */
        private List<OpenContractRelationRequestDTO> openContractRelationRequestDTOList;

        /**
         * 预付关联分摊信息
         */
        private OpenPrepaymentRelatedApportionRequestDTO openPrepaymentRelatedApportionRequestDTO;

        // 其他字段根据文档补充...
    }

    /**
     * 合同关联信息DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenContractRelationRequestDTO {
        /**
         * 合同行号
         */
        private Integer contractLineNumber;

        /**
         * 合同阶段编码
         */
        private String stageCode;

        /**
         * 关联金额（BigDecimal类型）
         */
        private BigDecimal assignedAmount;

        private String contractNumber;
    }

    /**
     * 预付关联分摊信息DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenPrepaymentRelatedApportionRequestDTO {
        /**
         * 费用类型编码
         */
        private String expenseTypeCode;

        /**
         * 分摊明细列表
         */
        private List<OpenPrepaymentApportionRequestDTO> openPrepaymentApportionRequestDTOList;
    }

    /**
     * 预付分摊明细DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenPrepaymentApportionRequestDTO {
        /**
         * 分摊视图列表
         */
        private List<OpenApportionViewRequestDTO> openApportionViewRequestDTOList;

        /**
         * 分摊金额（BigDecimal类型）
         */
        private BigDecimal amount;

        /**
         * 公司编码
         */
        private String companyCode;
    }

    /**
     * 分摊视图DTO
     */
    @Data
    @Accessors(chain = true)
    public static class OpenApportionViewRequestDTO {
        /**
         * 字段类型（如department/costCenter）
         */
        private String fieldType;

        /**
         * 成本中心编码
         */
        private String costCenterCode;

        /**
         * 成本中心项编码
         */
        private String costCenterItemCode;
    }



    @Data
    @Accessors(chain = true)
    public static class OpenAccrualWriteOffRequestDTO {

        /**
         * 预提核销金额
         */
        private BigDecimal accrualWriteOffAmount;

        /**
         * 预提核销业务编码
         */
        private String accrualWriteOffBusinessCode;
    }



    @Data
    @Accessors(chain = true)
    public static class OpenInvoiceCreateDTO {


        private BigDecimal amount;


        private String expenseTypeCode;

        private String invoiceCurrencyCode;

        private String occurrenceDate;


        private Object data;


        private List<OpenReceiptRequestDTO> openReceiptRequestDTOList;


    }



    @Data
    @Accessors(chain = true)
    public static class OpenReceiptRequestDTO {
        private String receiptTypeNo;

        private String billingCode;

        private String billingNo;
        private String billingDate;
        // 税前
        private BigDecimal fee;
        // 税后
        private BigDecimal feeWithoutTax;
        // 税
        private BigDecimal tax;
        private String checkCode;
        private String attachmentOID;
    }



//
//'''json
//    {
//        "accountNumber": "",
//            "accrualBusinessCodeList": [
//        ""
// ],
//        "autoSubmit": true,
//            "businessCode": "",
//            "employeeId": "",
//            "formCode": "xg-zfd",
//            "openCustomFormValueRequestDTOList": [
//        {
//            "fieldCode": "department",
//                "value": "-9690"
//        },
//        {
//            "fieldCode": "costCentre",
//                "value": ""
//        },
//        {
//            "fieldCode": "PastPeriod",
//                "value": "1"
//        },
//        {
//            "fieldCode": "period",
//                "value": "202502"
//        },
//        {
//            "fieldCode": "company",
//                "value": "1040"
//        },
//        {
//            "fieldCode": "currency",
//                "value": "CNY"
//        },
//        {
//            "fieldCode": "reason",
//                "value": ""
//        },
//        {
//            "fieldCode": "remark",
//                "value": ""
//        },
//        {
//            "fieldCode": "prepay",
//                "value": ""
//        },
//        {
//            "fieldCode": "fullPayout",
//                "value": ""
//        },
//        {
//            "fieldCode": "payee",
//                "value": ""
//        }
// ],
//        "openPaymentScheduleRequestDTOList": [
//        {
//            "openAccrualWriteOffRequestDTOList": [
//            {
//                "accrualWriteOffAmount": 36.94,
//                    "accrualWriteOffBusinessCode": ""
//            }
//   ],
//            "openInvoiceCreateDTO": {
//            "amount": 36.94,
//                    "data": [
//            {
//                "messageKeyCode": "helios.dateCombined",
//                    "value": "{\"duration\":30,\"endDate\":\"2024-10-31 00:00:00\",\"startDate\":\"2024-10-01 00:00:00\"}"
//            }
//    ],
//            "expenseTypeCode": "EXP510535",
//                    "invoiceCurrencyCode": "CNY",
//                    "occurrenceDate": "2024-11-27 16:44:50",
//                    "openInvoiceApportionRequestDTOList": [
//            {
//                "amount": 36.94,
//                    "openApportionViewRequestDTOList": [
//                {
//                    "costCenterCode": "costcenter",
//                        "costCenterItemCode": "KT010200",
//                        "fieldType": "costCenter"
//                },
//                {
//                    "costCenterCode": "PastPeriod",
//                        "costCenterItemCode": "",
//                        "fieldType": "costCenter"
//                }
//      ],
//                "remark": ""
//            }
//    ],
//            "openReceiptRequestDTOList": [
//            {
//                "attachmentOID": "f5dd47f1-15b9-4192-bac3-71e4cce48e71",
//                    "billingCode": "",
//                    "billingDate": "2024-11-05",
//                    "billingNo": "24347200000062737962",
//                    "checkCode": "",
//                    "fee": 36.94,
//                    "feeWithoutTax": 32.69,
//                    "receiptTypeNo": "113",
//                    "tax": 4.25
//            }
//    ]
//        },
//            "paymentMethodCode": "0101",
//                "paymentScheduleType": 1001,
//                "realPaymentAmount": 36.94,
//                "remark": ""
//        }
// ],
//        "paymentMethodCode": "0101"
//    }
//'''



}
