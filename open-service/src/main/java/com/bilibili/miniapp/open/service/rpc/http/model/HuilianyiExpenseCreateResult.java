package com.bilibili.miniapp.open.service.rpc.http.model;

import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateResult.ExpenseCreateResultData;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class HuilianyiExpenseCreateResult  {



    private String message;

    private String errorCode;

    private String key;

    private ExpenseCreateResultData data;


    public boolean isSuccess(){

        return "0000".equals(errorCode) && StringUtils.isNotEmpty(key);

    }


    /**
     *     "data":{
     *       "status":1001,
     *       "entityLabel":[{
     *         "name":"费用超申请",
     *         "level":"ERROR",
     *         "toast":"测试费用 可报销CNY -320.00，实际报销CNY 80.00，超出CNY -400.00",
     *         "type":"EXPENSE_OVER_APPLICATION"
     *       }],
     *       "submitErrorCode": "1297011",
     *       "submitErrorMessage": "付款行必须关联合同，请返回修改数据"
     *     }
     */
    @Data
    @Accessors(chain = true)
    public static class ExpenseCreateResultData{


        private Integer status;

        private String submitErrorCode;

        private String submitErrorMessage;

        private List<EntityLabel> entityLabel;




    }



    @Data
    @Accessors(chain = true)
    public static class EntityLabel {


        private String name;

        private String level;

        private String toast;

        private String type;


    }




}
