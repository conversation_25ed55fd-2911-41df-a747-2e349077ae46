package com.bilibili.miniapp.open.service.rpc.http.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/21
 */
@Data
@Accessors(chain = true)
public class HuilianyiOcrRequest {



    private ReceiptOcrInput receiptOcrInput;



    @Data
    @Accessors(chain = true)
    public static class ReceiptOcrInput{

        /**
         * 图片/PDF地址（与data二选一）
         */
        private String imageURL;

        /**
         * 图片/PDF的base64编码（与imageURL二选一）
         */
        private String data;

        /**
         * 订单号（非免费类型必填）
         */
        private String orderNo;

        /**
         * 操作人名称（用于使用记录）
         */
        private String operatorName;

        /**
         * 操作人工号（用于使用记录）
         */
        private String operatorEmployeeId;

        /**
         * 是否返回切图对象
         */
        private Boolean needSlicingAttachment;

    }




}
