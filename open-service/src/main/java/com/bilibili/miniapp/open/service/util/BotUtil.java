package com.bilibili.miniapp.open.service.util;

import com.alibaba.fastjson.JSON;
import com.bilibili.miniapp.open.common.util.OkHttpUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/4/23
 */
@Slf4j
public class BotUtil {

    public static void sendWithMarkdown(String botUrl, String content) {
        RequestBody requestBody = RequestBody.create(JSON.toJSONString(
                WeiXinInfo.builder()
                        .msgtype("markdown")
                        .markdown(WeiXinText.builder()
                                .content(content)
                                .build())
                        .build()), MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .addHeader("Content-Type", "application/json")
                .url(botUrl)
                .post(requestBody)
                .build();
        try {
            Response response = OkHttpUtil.CLIENT.newCall(request).execute();
            response.close(); // 内部静默关闭
            if (!response.isSuccessful()) {
                throw new RuntimeException("Request Failure, Code: "
                        + response.code() + " Message: " + response.message());
            }
        } catch (IOException e) {
            log.error("发送http请求时异常", e);
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class WeiXinInfo {
        private String msgtype;

        private WeiXinText markdown;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class WeiXinText {
        private String content;
    }
}
