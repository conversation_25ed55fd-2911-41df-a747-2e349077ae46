package com.bilibili.miniapp.open.service.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/27
 */
public class DateTimeUtil {

    private static DateTimeFormatter logdateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");



    public static Date localDateTime2Date(LocalDateTime time){

        return new Date(time.toInstant(java.time.ZoneOffset.of("+8")).toEpochMilli());
    }


    public static String toLogdate(LocalDate time) {

        return time.format(logdateFormatter);
    }

    public static LocalDate fromLogDate(String logdate) {
        return LocalDate.parse(logdate, logdateFormatter);
    }

}
