package com.bilibili.miniapp.open.service.util;

import com.bilibili.miniapp.open.common.util.OkHttpUtil;
import com.bilibili.miniapp.open.service.bo.FileDownloadInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpHeaders;

/**
 * <AUTHOR>
 * @date 2025/5/19
 */
@Slf4j
public class DownloadUtil {

    public static FileDownloadInfo downloadFile(String url) {
        Request request = new Request.Builder()
                .header(HttpHeaders.CONNECTION, "close")
                .url(url)
                .get()
                .build();
        try {
            try (Response response = OkHttpUtil.CLIENT.newCall(request).execute()) {
                byte[] bytes = null;
                if (response.body() != null) {
                    bytes = response.body().bytes();
                }
                String mediaType = response.header("content-type");
                FileDownloadInfo fileDownloadInfo = new FileDownloadInfo();
                fileDownloadInfo.setBytes(bytes);
                fileDownloadInfo.setMedia(mediaType);
                return fileDownloadInfo;
            }

        } catch (Exception e) {
            log.error("下载文件[{}]发生异常:{}", url, ExceptionUtils.getStackTrace(e));
            return null;
        }
    }
}
