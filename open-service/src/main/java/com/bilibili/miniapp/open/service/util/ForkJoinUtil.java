package com.bilibili.miniapp.open.service.util;

import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrResult;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/10
 */
@Slf4j
public class ForkJoinUtil {


    /**
     * 注意会吞掉异常,看需要可改
     * @param tasks
     * @param executor
     */
    public static void runnableForkJoin(
            List<Runnable> tasks, ExecutorService executor
    ){

        List<CompletableFuture<Void>> futures = tasks.stream().map(r -> {
            return CompletableFuture.runAsync(r, executor);
        }).collect(Collectors.toList());

        CompletableFuture.allOf(futures.stream().collect(Collectors.toList())
                        .toArray(CompletableFuture[]::new))
                .thenRunAsync(() -> {
                    futures.forEach(f -> f.join());
                }, executor)
                .exceptionally(ex -> {
                    log.error("Fail to execute fork-join task", ex);
                    return null;
                }).join();
    }




    public static <T> List<T> callableForkJoin(
            List<Supplier<T>> tasks, ExecutorService executor
    ){

        List<CompletableFuture<T>> futures = tasks.stream().map(r -> {
            return CompletableFuture.supplyAsync(r, executor);
        }).collect(Collectors.toList());

        return CompletableFuture.allOf(futures.stream().collect(Collectors.toList())
                        .toArray(CompletableFuture[]::new))
                .thenApplyAsync(v -> {
                    return futures.stream().map(f -> f.join())
                            .collect(Collectors.toList());

                }, executor)
                .exceptionally(ex -> {
                    log.error("Fail to execute fork-join task", ex);
                    return Collections.emptyList();
                }).join();
    }
}
