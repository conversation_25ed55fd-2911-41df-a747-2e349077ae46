package com.bilibili.miniapp.open.service.util;

import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/28
 */
public class ProbeUtil {




    public static  <T> T submitAndProbeSync(
            Supplier<String> submit,
            Function<String, T> probe,
            Function<T, <PERSON><PERSON>an> stopFlagGetter,
            Integer maxTimes,
            Integer intervalSeconds) {

        // 参数校验（参考网页3的重构原则）
        if (submit == null || probe == null || stopFlagGetter == null ) {
            throw new IllegalArgumentException("Functional parameters cannot be null");
        }
        if (maxTimes <= 0 || intervalSeconds <= 0) {
            throw new IllegalArgumentException("maxTimes and intervalSeconds must be positive");
        }

        String taskId;
        try {
            taskId = submit.get(); // 提交任务（参考网页5的WS连接重试逻辑）
            if (taskId == null || taskId.isEmpty()) {
                throw new IllegalStateException("Task submission failed: empty task ID");
            }
        } catch (Exception e) {
            throw new RuntimeException("Task submission failed", e);
        }

        // 轮询机制（参考网页5的断网重连机制）
        int attempts = 0;
        while (attempts < maxTimes) {
            try {
                T result = probe.apply(taskId);

                if (stopFlagGetter.apply(result)) {
                    return result; // 成功条件达成
                }


                // 等待间隔（参考网页5的心跳间隔逻辑）
                TimeUnit.SECONDS.sleep(intervalSeconds);
                attempts++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Probing interrupted", e);
            } catch (Exception e) {
                // 记录错误日志后可选择继续重试或终止
                attempts++;
                if (attempts >= maxTimes) {
                    throw new RuntimeException("Probing failed after retries", e);
                }
            }
        }
        throw new RuntimeException("Task timeout after " + maxTimes + " attempts");
    }

}
