package com.bilibili.miniapp.open.service.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.DigestUtils;

public class SignUtil {

    public static String generateSign(String json, String secret) {
        return DigestUtils.md5DigestAsHex((json + secret).getBytes());
    }

    public static boolean verifySign(String json, String sign, String secret) {
        return generateSign(json, secret).equalsIgnoreCase(sign);
    }

    public static void main(String[] args) {
        String json = "{\"app\":\"B站\",\"pay_amt\":\"0.1\",\"sign_off_date\":\"20250513\",\"refund_amt\":\"0.1\",\"product_name\":\"VIP会员\",\"pay_time\":\"2025-04-25 17:27:32\",\"order_code\":\"5607691982\",\"args\":\"platform=android&appname=YOUKU_BILIBILI_MINI_APP&scene=0&ver=0.0.1&eid=&mid=&ctime=&dev=asus&ug_from=bli&sourcefrom=&from_avid=&from_trackid=&vid=&sid=&p_vid=XMzcxNDY5ODQ4&p_sid=cbff984c962411de83b1&renew_amount=25.0&renew_days=31.0&sign_days=3.0\",\"sign_state\":2,\"user_id\":\"e53e7b0c7f584a50fa39294a5a1508ee\",\"refund_time\":\"2025-05-13 19:10:05\",\"product_id\":128,\"sku_name\":\"3天VIP\",\"pay_type\":\"支付宝\",\"time\":1747210843738,\"order_id\":102982111504228,\"order_type\":\"签约订单\",\"refund_state\":1}";
        System.out.println(generateSign(json, "04b2563f9bf65d8cfe466aaa82f90044"));
    }
}