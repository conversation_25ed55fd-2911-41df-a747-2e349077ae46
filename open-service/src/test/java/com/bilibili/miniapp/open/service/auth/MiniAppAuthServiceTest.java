package com.bilibili.miniapp.open.service.auth;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppExchangeInfoDTO;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.repository.redis.ICacheRepository;
import com.bilibili.miniapp.open.service.biz.auth.AuthValidationService;
import com.bilibili.miniapp.open.service.biz.auth.GrpcUserService;
import com.bilibili.miniapp.open.service.biz.auth.PreAuthCodeManager;
import com.bilibili.miniapp.open.service.biz.auth.UserInfoDecryptionService;
import com.bilibili.miniapp.open.service.biz.auth.impl.MiniAppAuthService;
import com.bilibili.miniapp.open.service.bo.auth.EncryptedEntity;
import com.bilibili.miniapp.open.service.bo.auth.MiniAppAuthBo;
import com.bilibili.miniapp.open.service.bo.auth.PreAuthInfo;
import com.bilibili.miniapp.open.service.bo.up_info.UserDetailBo;
import com.bilibili.miniapp.open.service.bo.up_info.UserSensitiveInfoBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.config.MainSiteConfig;
import com.bilibili.miniapp.open.service.config.MiniAppConfig;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppRemoteService;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;

import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/14
 **/

@ExtendWith(MockitoExtension.class)
public class MiniAppAuthServiceTest {

    @Mock
    private GrpcUserService userService;

    @Mock
    private PreAuthCodeManager codeManager;

    @Mock
    private AuthValidationService validationService;

    @Mock
    private UserInfoDecryptionService decryptionService;

    @Mock
    private IMiniAppRemoteService remoteService;

    @Mock
    private ConfigCenter configCenter;

    @Mock
    private ICacheRepository cacheRepository;

    @InjectMocks
    private MiniAppAuthService authService;

    private final String TEST_APP_ID = "testApp123";
    private final Long TEST_MID = 123456L;
    private final String TEST_OPEN_ID = "open_09876";
    private final String TEST_PRE_CODE = "PRE_AUTH_123";

    @Test
    void whenGetPreAuthCode_ThenReturnsValidResponse() {
        // 准备测试数据
        MainSiteConfig mainSiteConfig = new MainSiteConfig();
        mainSiteConfig.setPreAuthCodeExpire(600);

        UserDetailBo mockUser = UserDetailBo.builder()
                .hideTel("138****0000")
                .build();

        // Mock依赖行为
        when(userService.fetchUserDetail(TEST_MID)).thenReturn(mockUser);
        when(codeManager.generateCode()).thenReturn(TEST_PRE_CODE);
        when(configCenter.getMainSite()).thenReturn(mainSiteConfig);

        // 执行测试方法
        MiniAppAuthBo result = authService.getPreAuthCode(TEST_APP_ID, TEST_MID);

        // 验证结果
        assertEquals(TEST_PRE_CODE, result.getPreauthCode());
        assertEquals("138****0000", result.getMaskedPhone());
        assertEquals(600, result.getExpiresIn());

        // 验证依赖调用
        verify(validationService).validateUserState(mockUser);
        verify(codeManager).cacheCode(TEST_APP_ID, TEST_MID, TEST_PRE_CODE);
    }

    @Test
    void whenUserStateInvalid_ThenThrowException() {
        // Mock一个无效用户状态
        doThrow(new ServiceException("用户状态异常"))
                .when(validationService)
                .validateUserState(any());

        // 执行并断言异常
        assertThrows(ServiceException.class,
                () -> authService.getPreAuthCode(TEST_APP_ID, TEST_MID));
    }

    @Test
    void whenUserHasNoPhone_ThenReturnEmptyMaskedPhone() {
        // 准备测试数据
        MainSiteConfig mainSiteConfig = new MainSiteConfig();
        mainSiteConfig.setPreAuthCodeExpire(600);
        UserDetailBo userWithoutPhone = UserDetailBo.builder().build();

        when(userService.fetchUserDetail(TEST_MID)).thenReturn(userWithoutPhone);
        when(configCenter.getMainSite()).thenReturn(mainSiteConfig);

        MiniAppAuthBo result = authService.getPreAuthCode(TEST_APP_ID, TEST_MID);

        assertEquals("", result.getMaskedPhone());
    }

    @Test
    void whenAuthWithValidCode_ThenReturnEncryptedInfo() throws Exception {
        // Mock环境配置
        MainSiteConfig mainSiteConfig = new MainSiteConfig();
        mainSiteConfig.setPreAuthCodeExpire(300);
        MiniAppConfig miniAppConfig = new MiniAppConfig();
        miniAppConfig.setAuthPhoneWhiteList(Collections.singletonList(TEST_APP_ID));

        when(configCenter.getMiniAppConfig()).thenReturn(miniAppConfig);

        when(configCenter.getMainSite()).thenReturn(mainSiteConfig);

        // Mock锁机制
        RLock mockLock = mock(RLock.class);
        when(cacheRepository.tryLock(anyString())).thenReturn(mockLock);

        // Mock找回的敏感信息
        UserSensitiveInfoBo decryptedInfo = new UserSensitiveInfoBo();
        EncryptedEntity expectedEntity = new EncryptedEntity();

        PreAuthInfo preAuthInfo = new PreAuthInfo();
        preAuthInfo.setStatus(0);
        preAuthInfo.setPreAuthCode(TEST_PRE_CODE);
        when(cacheRepository.getObject(anyString(), eq(PreAuthInfo.class)))
                .thenReturn(preAuthInfo);

        when(userService.fetchSensitiveInfo(TEST_MID, TEST_APP_ID))
                .thenReturn(new UserSensitiveInfoBo());
        when(decryptionService.decrypt(any())).thenReturn(decryptedInfo);
        when(decryptionService.encryptPhoneInfo(decryptedInfo))
                .thenReturn(expectedEntity);

        // 执行认证
        EncryptedEntity result = authService.authLogin(TEST_APP_ID, TEST_MID, TEST_PRE_CODE);

        // 验证加密流程
        assertEquals(expectedEntity, result);
        verify(cacheRepository).setObject(
                anyString(),
                any(PreAuthInfo.class),
                eq(300L),
                eq(TimeUnit.SECONDS)
        );
    }

    @Test
    void whenAppIdNotInWhitelist_ThenThrowAuthException() {
        MiniAppConfig miniAppConfig = new MiniAppConfig();
        miniAppConfig.setAuthPhoneWhiteList(Collections.singletonList("otherAppId"));

        when(configCenter.getMiniAppConfig()).thenReturn(miniAppConfig);
        // Mock白名单检查失败

        // 断言异常类型及错误信息
        ServiceException exception = assertThrows(ServiceException.class,
                () -> authService.authLogin("invalidApp", TEST_MID, TEST_PRE_CODE));

        assertEquals(ErrorCodeType.UNAUTHORIZED.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains("无访问权限"));
    }

    // 验证预授权码重复使用场景
    @Test
    void whenReusePreAuthCode_ThenRejectRequest() {
        PreAuthInfo usedAuthInfo = new PreAuthInfo();
        usedAuthInfo.markAsUsed();

        // 准备测试数据
        MainSiteConfig mainSiteConfig = new MainSiteConfig();
        mainSiteConfig.setPreAuthCodeExpire(600);

        MiniAppConfig miniAppConfig = new MiniAppConfig();
        miniAppConfig.setAuthPhoneWhiteList(Collections.singletonList(TEST_APP_ID));

        doThrow(new ServiceException(ErrorCodeType.CONCURRENT_OPERATE))
                .when(cacheRepository)
                .tryLock(anyString());

        when(configCenter.getMiniAppConfig()).thenReturn(miniAppConfig);

        assertThrows(ServiceException.class,
                () -> authService.authLogin(TEST_APP_ID, TEST_MID, TEST_PRE_CODE));
    }

}
