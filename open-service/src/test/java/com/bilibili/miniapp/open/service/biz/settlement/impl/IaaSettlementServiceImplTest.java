package com.bilibili.miniapp.open.service.biz.settlement.impl;

import static org.junit.Assert.*;

import java.math.BigDecimal;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/9
 */
public class IaaSettlementServiceImplTest {

    @Test
    public void processHistoricalSettlements() {
    }



    @Test
    public void test(){

        System.out.println(BigDecimal.ZERO.equals(new BigDecimal("0.00")));
        System.out.println(BigDecimal.ZERO.compareTo(new BigDecimal("0.00")));

    }
}