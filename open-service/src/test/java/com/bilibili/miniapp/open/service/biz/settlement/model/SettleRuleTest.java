package com.bilibili.miniapp.open.service.biz.settlement.model;


import java.math.BigDecimal;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/18
 */
public class SettleRuleTest {


    private SettleRule rule = new SettleRule();



    @Test
    public  void calculateBusinessWithdraw() {

        BigDecimal r = rule.calculateBusinessWithdraw(new BigDecimal(100), "20241101");

        System.out.println(r);

        Assert.assertTrue(r.compareTo(new BigDecimal(90)) == 0);



    }


    @Test
    public void calculateBusinessCrmCharge() {

        BigDecimal r = rule.calculateBusinessCrmCharge(new BigDecimal(100));
        System.out.println(r);


        Assert.assertTrue(r.compareTo(new BigDecimal(12)) == 0);

    }


    @Test
    public void calculateNaturalWithdraw1() {

        BigDecimal r = rule.calculateNaturalWithdraw(new BigDecimal(10000), new BigDecimal(500000));

        System.out.println(r);

        Assert.assertTrue(r.compareTo(new BigDecimal(5000)) == 0);

    }

    @Test
    public void calculateNaturalWithdraw2() {

        BigDecimal r = rule.calculateNaturalWithdraw(new BigDecimal(10000), new BigDecimal(495000));

        System.out.println(r);

        Assert.assertTrue(r.compareTo(new BigDecimal(5500)) == 0);


    }


    @Test
    public void calculateNaturalWithdraw3() {

        BigDecimal r = rule.calculateNaturalWithdraw(new BigDecimal(10000), new BigDecimal(490000));

        System.out.println(r);

        Assert.assertTrue(r.compareTo(new BigDecimal(6000)) == 0);

    }
}