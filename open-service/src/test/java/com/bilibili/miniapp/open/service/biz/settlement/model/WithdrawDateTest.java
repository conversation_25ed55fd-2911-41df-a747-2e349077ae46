package com.bilibili.miniapp.open.service.biz.settlement.model;

import static org.junit.Assert.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/9
 */
public class WithdrawDateTest {


    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
    @Test
    public void generatePreviousDateOfCurrentDate() {

        System.out.println(WithdrawDate.generatePreviousDateOfCurrentDate(
                LocalDate.parse("20250101", df)
        ));

        System.out.println(WithdrawDate.generatePreviousDateOfCurrentDate(
                LocalDate.parse("20250116", df)
        ));

        System.out.println(WithdrawDate.generatePreviousDateOfCurrentDate(
                LocalDate.parse("20250228", df)
        ));

        System.out.println(WithdrawDate.generatePreviousDateOfCurrentDate(
                LocalDate.parse("20250331", df)
        ));

        System.out.println(WithdrawDate.generatePreviousDateOfCurrentDate(
                LocalDate.parse("20250310", df)
        ));




    }

    @Test
    public void generatePreviousDateOfCurrentDateNext() {

        System.out.println(new WithdrawDate("202501_1").generateNext());
        System.out.println(new WithdrawDate("202501_2").generateNext());






    }



}