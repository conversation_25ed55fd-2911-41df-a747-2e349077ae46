package com.bilibili.miniapp.open.service.rpc.http;

import com.bilibili.discovery.DiscoveryClientBuilder;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.rpc.http.model.GameContractInfo;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.BeforeClass;
import org.junit.Test;
import pleiades.component.http.client.BiliHttpClient;
import pleiades.component.http.client.BiliHttpClient.Options;
import pleiades.venus.breaker.exception.BiliBreakerRejectedException;
import retrofit2.Call;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/31
 */
public class GameOpenPlatformApiTest {

    private static GameOpenPlatformApi api;


    private String appId = "biligame82833db7ab871bd8";

    private String appkey = "aaed7d28849a915ff";

    private String appsecret = "636bb59dd8074d6f991a7a460e76f573";




    @BeforeClass
    public static void init() throws Exception {

        api = BiliHttpClient.builder()
                .hostname("discovery://game.tgamesdk.game-open-platform-mng")
                .envZone("sh001")
                .discoveryClient(
                        new DiscoveryClientBuilder("zhen-local", "sh001", "uat")
                                .build()
                )
                .options(new Options(30000, 30000, 30000))
                .target(GameOpenPlatformApi.class, "discovery://game.tgamesdk.game-open-platform-mng");



    }

    @Test
    public void getContractInfo() throws IOException, BiliBreakerRejectedException {

        Map<String, String> params = new HashMap<>();


        params.put("app_id", appId);
        params.put("appkey", appkey);
        params.put("ts", String.valueOf(System.currentTimeMillis()));
        params.put("sign", sign(params));

        Response<GameContractInfo> r = api.getContractInfo(
                params
        ).execute().body();

        System.out.println(r);


    }


    private String sign(Map<String, String> paramsTreeMap) {

        AtomicReference<String> signCalc = new AtomicReference<>("");

        paramsTreeMap.entrySet().stream()
                .filter(entry -> !"file".equals(entry.getKey()))
                .sorted(Comparator.comparing(Entry::getKey))
                .forEach(entry -> {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    try {
                        signCalc.set(String.format("%s%s=%s&", signCalc.get(), key,
                                encodedFix(URLEncoder.encode(value, "UTF-8"))));
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                });

        if (!signCalc.get().isEmpty()) {
            signCalc.set(signCalc.get().substring(0, signCalc.get().length() - 1));
        }
        signCalc.set(DigestUtils.md5Hex(String.format("%s%s", signCalc.get(), appsecret)));
        return signCalc.get();

    }


    private static String encodedFix(String encoded) {
        // required
        encoded = encoded.replace("+", "%20");
        encoded = encoded.replace("*", "%2A");
        encoded = encoded.replace("%7E", "~");

        // optional
        encoded = encoded.replace("!", "%21");
        encoded = encoded.replace("(", "%28");
        encoded = encoded.replace(")", "%29");
        encoded = encoded.replace("'", "%27");
        return encoded;
    }



    @Test
    public void getBindAdAccountInfo() {
    }
}