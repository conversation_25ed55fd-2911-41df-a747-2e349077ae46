package com.bilibili.miniapp.open.service.rpc.http;

import com.alibaba.fastjson.JSON;
import com.bilibili.discovery.DiscoveryClientBuilder;
import com.bilibili.miniapp.open.service.biz.settlement.model.IaaWithdrawBill.ExpenseExtra;
import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams.SimpleOcrResult;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig.HuilianyiAccrualParams;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig.HuilianyiExpenseParams;
import com.bilibili.miniapp.open.service.rpc.http.model.CallWrapper;
import com.bilibili.miniapp.open.service.rpc.http.model.DataWrapper;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateRequest;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAccrualCreateResult;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAsyncTask;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiAsyncTaskResult;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiExpenseCreateResult;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrRequest;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrRequest.ReceiptOcrInput;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrResult;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiTokenAcquireRequest;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiTokenResult;
import com.bilibili.miniapp.open.service.util.JsonUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.junit.BeforeClass;
import org.junit.Test;
import pleiades.component.http.client.BiliHttpClient;
import pleiades.component.http.client.BiliHttpClient.Options;
import pleiades.venus.breaker.exception.BiliBreakerRejectedException;
import retrofit2.Response;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/20
 */
public class HuilianyiPaymentApiTest {


    private static HuilianyiPaymentApi huilianyiPaymentApi;

    private static IaaSettlementConfig settlementConfig;

    private String payee = "S_240209";


    private String contractId = "CON00167303";


    private String accountNumber = "3301040160016891925" ;

    private String invoiceImageUrl = "http://i0.hdslb.com/bfs/sycp/mgk/collage/png/202503/be57da6192227f0057780834cc2fd8e2.png";



    @BeforeClass
    public static void init() throws Exception {

        huilianyiPaymentApi = BiliHttpClient.builder()
                .hostname("discovery://ops.fin-api.payproxy")
                .envZone("sh001")
                .discoveryClient(
                        new DiscoveryClientBuilder("zhen-local", "sh001", "uat")
                        .build()
                )
                .options(new Options(30000, 30000, 30000))
                .target(HuilianyiPaymentApi.class, "discovery://ops.fin-api.payproxy");

        settlementConfig = new IaaSettlementConfig();

    }

    @Test
    public void createAccrual() throws BiliBreakerRejectedException {

        docreateAccrual();
    }


    public String docreateAccrual() throws BiliBreakerRejectedException {



        com.bilibili.miniapp.open.common.entity.Response<CallWrapper<HuilianyiAccrualCreateResult>> r =
                huilianyiPaymentApi.createAccrual(
                        "Bearer " + getToken(),

                        toAccrualRequestBody()

                ).execute().body();

        System.out.println(r);

        return r.getData().getRelayJsonStringOutput().getKey();

    }

    @Test
    public void createAccrualAsync() throws BiliBreakerRejectedException, InterruptedException {

        docreateAccrualAsync();
    }


    public String docreateAccrualAsync() throws BiliBreakerRejectedException, InterruptedException {



        com.bilibili.miniapp.open.common.entity.Response<HuilianyiAsyncTask> r = huilianyiPaymentApi.createAccrualAsync(
                "Bearer " + getToken(),

                toAccrualRequestBody()

        ).execute().body();

        System.out.println("createAsync result: " + r);

        Thread.sleep(1000);


        while (true){

            com.bilibili.miniapp.open.common.entity.Response<HuilianyiAsyncTaskResult> probe = huilianyiPaymentApi.probeTask(
                    r.getData().getTaskId()).execute().body();


            System.out.println("probe result: " + probe);

            if (probe.getData().isSuccess() ){
                return probe.getData().getKey();
            }

            else if ( probe.getData().isFailed()) {

                throw new IllegalArgumentException("create accrual failed");


            }

            Thread.sleep(2000);

        }



    }



    private RequestBody toAccrualRequestBody(){
        HuilianyiAccrualCreateRequest request = new AccrualCreateMandatoryParams()
                .setPayee(payee)
                .setBusinessCode("zhensheng-yuti-" + System.currentTimeMillis())
                .setAmtInCny(new BigDecimal("100.00"))
                .toHuilianyiAccrualCreateRequest(new HuilianyiAccrualParams());

        RequestBody body = toRequestBody(request);

        System.out.println(body);

        return body;
    }




    @Test
    public void createExpenseSync() throws BiliBreakerRejectedException, InterruptedException {

        RequestBody body = toExpenseRequestBody(true);

        System.out.println(body);

        com.bilibili.miniapp.open.common.entity.Response<CallWrapper<HuilianyiExpenseCreateResult>> r = huilianyiPaymentApi.createExpense(
                "Bearer " + getToken(),

                body

        ).execute().body();


        System.out.println(r);

    }


    @Test
    public void testOcr() throws BiliBreakerRejectedException {

        doOcr();

    }

    @Test
    public void testOcr2() throws BiliBreakerRejectedException {

        HuilianyiOcrRequest request = new HuilianyiOcrRequest()
                .setReceiptOcrInput(
                        new ReceiptOcrInput()
                                .setImageURL("http://uat-boss.bilibili.co/game-gift-service-bucket/FINANCE/invoice/32231008/bdc5606d1fbc7fa7761e401c0c0d5ea2.pdf")
                                .setNeedSlicingAttachment(true)
                );

        RequestBody body = toRequestBody(request);



        System.out.println(body);

        com.bilibili.miniapp.open.common.entity.Response<CallWrapper<DataWrapper<HuilianyiOcrResult>>> r = huilianyiPaymentApi.receiptOcr(
                "Bearer " + getToken(),

                body

        ).execute().body();



        System.out.println(r);


    }

    public HuilianyiOcrResult doOcr() throws BiliBreakerRejectedException {

        HuilianyiOcrRequest request = new HuilianyiOcrRequest()
                .setReceiptOcrInput(
                        new ReceiptOcrInput()
                                .setImageURL(invoiceImageUrl)
                                .setNeedSlicingAttachment(true)
                );

        RequestBody body = toRequestBody(request);



        System.out.println(body);

        com.bilibili.miniapp.open.common.entity.Response<CallWrapper<DataWrapper<HuilianyiOcrResult>>> r = huilianyiPaymentApi.receiptOcr(
                "Bearer " + getToken(),

                body

        ).execute().body();



        System.out.println(r);

        return r.getData().getRelayJsonStringOutput().getData();

    }





    private RequestBody toExpenseRequestBody(boolean sync) throws BiliBreakerRejectedException, InterruptedException {
        Object request = new ExpenseCreateMandatoryParams()
                .setPayee(payee)
                .setContractNumber(contractId)
                .setBusinessCode("zhensheng-" + System.currentTimeMillis())
                .setAccrualId2AmtInCny(Map.of(
                        sync ? docreateAccrual() : docreateAccrualAsync(), new BigDecimal("50.00"),
                        sync ? docreateAccrual() : docreateAccrualAsync(), new BigDecimal("50.00")))
                .setInvoiceImgUrls(Lists.newArrayList(invoiceImageUrl))
                .setOcrResults(Map.of(invoiceImageUrl,
                        SimpleOcrResult.fromInvoiceWrapper(null)))
                .setWithdrawAmtInCny(new BigDecimal("100.00"))
                .setBankNumber(accountNumber)
                .toHuilianyiExpenseCreateRequest(new HuilianyiExpenseParams());

        RequestBody body = toRequestBody(request);

        Thread.sleep(1000L);

        return body;
    }




    @Test
    public void createExpenseAsync() throws BiliBreakerRejectedException, InterruptedException {


        RequestBody body = toExpenseRequestBody(false);

        System.out.println(body);

        com.bilibili.miniapp.open.common.entity.Response<HuilianyiAsyncTask> r = huilianyiPaymentApi.createExpenseAsync(
                "Bearer " + getToken(),
                body
        ).execute().body();

        System.out.println("createAsync result: " + r);

        Thread.sleep(5000);


        while (true){

            com.bilibili.miniapp.open.common.entity.Response<HuilianyiAsyncTaskResult> probe = huilianyiPaymentApi.probeTask(
                    r.getData().getTaskId()).execute().body();

            System.out.println("probe result: " + probe);

            if (probe.getData().isSuccess() || probe.getData().isFailed()) {
                break;
            }

            Thread.sleep(2000);

        }






    }

    //curl -X POST  'http://uat-ee.bilibili.co/api/payproxy/token' -H 'Content-Type: application/json' -d '{"askString":"test_wrong"}' -v
    @Test
    public void acquireToken() throws BiliBreakerRejectedException {

        long time = System.currentTimeMillis();

        String string =
                Joiner.on(":").join(
                        settlementConfig.getHuilianyiServiceName(),
                        settlementConfig.getHuilianyiSecret(),
                        time
                );

        String jsonString = JSON.toJSONString(new HuilianyiTokenAcquireRequest()

                .setAskString(Base64.getEncoder().encodeToString(string.getBytes(StandardCharsets.UTF_8)))
        );
        RequestBody body = RequestBody.Companion.create(jsonString, MediaType.parse("application/json"));

        Response<com.bilibili.miniapp.open.common.entity.Response<HuilianyiTokenResult>> r = huilianyiPaymentApi.acquireToken(
                body
        ).execute();

        System.out.println(r);

    }


    private String getToken() throws BiliBreakerRejectedException {
        long time = System.currentTimeMillis();

        String string =
                Joiner.on(":").join(
                        settlementConfig.getHuilianyiServiceName(),
                        settlementConfig.getHuilianyiSecret(),
                        time
                );

        String jsonString = JSON.toJSONString(new HuilianyiTokenAcquireRequest()

                .setAskString(Base64.getEncoder().encodeToString(string.getBytes(StandardCharsets.UTF_8)))
        );
        RequestBody body = RequestBody.Companion.create(jsonString, MediaType.parse("application/json"));

        Response<com.bilibili.miniapp.open.common.entity.Response<HuilianyiTokenResult>> r = huilianyiPaymentApi.acquireToken(
                body
        ).execute();

        System.out.println(r);

        return r.body().getData().getToken();


    }


    @Test
    public void replayExtra() throws BiliBreakerRejectedException, InterruptedException {

        String extra = "{\"params\":{\"invoice_img_urls\":[\"http://i0.hdslb.com/bfs/game/c642c12fcacdd90060d523eb8cc89a470bc6ad47.pdf\",\"http://i0.hdslb.com/bfs/game/98a6e8963218c344191b554f293f56b8f8b49c6a.pdf\",\"http://i0.hdslb.com/bfs/game/bf697865645cd3f2c8d47f1b5669bcd67932a686.pdf\",\"http://i0.hdslb.com/bfs/game/699ba87e41c2f17e8e736ee3f33dbe16f2128db3.pdf\",\"http://i0.hdslb.com/bfs/game/161679ffe8eee6679276ec164b07051fd90864ea.pdf\",\"http://i0.hdslb.com/bfs/game/1469da7f5da27caf48d8d64c85bbd9c764a33a20.pdf\",\"http://i0.hdslb.com/bfs/game/a8b6b5be12e41b2a1559efcadbbf2aacd9fabb65.pdf\"],\"ocr_results\":{\"http://i0.hdslb.com/bfs/game/1469da7f5da27caf48d8d64c85bbd9c764a33a20.pdf\":{\"attach_id\":\"ebe3e40f-8cf5-4dda-80e5-602af7bb8959\",\"bill_date\":\"2021年01月21日\",\"bill_code\":\"031002000111\",\"bill_no\":\"12965919\",\"fee\":89.35,\"fee_without_tax\":86.75,\"receipt_type_no\":\"10\",\"tax\":2.60},\"http://i0.hdslb.com/bfs/game/a8b6b5be12e41b2a1559efcadbbf2aacd9fabb65.pdf\":{\"attach_id\":\"8ca4b1ea-2459-4060-9d89-18d00bd39204\",\"bill_date\":\"2021年06月25日\",\"bill_code\":\"************\",\"bill_no\":\"84676415\",\"fee\":177.77,\"fee_without_tax\":172.59,\"receipt_type_no\":\"10\",\"tax\":5.18},\"http://i0.hdslb.com/bfs/game/bf697865645cd3f2c8d47f1b5669bcd67932a686.pdf\":{\"attach_id\":\"ca5f2fab-1229-4cfc-8a51-744e7f2c100c\",\"bill_date\":\"2024年12月04日\",\"bill_code\":\"\",\"bill_no\":\"24312000000381065779\",\"fee\":38.90,\"fee_without_tax\":38.51,\"receipt_type_no\":\"113\",\"tax\":0.39},\"http://i0.hdslb.com/bfs/game/699ba87e41c2f17e8e736ee3f33dbe16f2128db3.pdf\":{\"attach_id\":\"cda04319-9d92-4cbf-b452-568f350306eb\",\"bill_date\":\"2021年04月28日\",\"bill_code\":\"************\",\"bill_no\":\"76392255\",\"fee\":208.76,\"fee_without_tax\":202.68,\"receipt_type_no\":\"10\",\"tax\":6.08},\"http://i0.hdslb.com/bfs/game/161679ffe8eee6679276ec164b07051fd90864ea.pdf\":{\"attach_id\":\"8becec78-2c32-4391-81e3-865f5c91a898\",\"bill_date\":\"2025年03月12日\",\"bill_code\":\"\",\"bill_no\":\"25312000000075250575\",\"fee\":39.65,\"fee_without_tax\":37.41,\"receipt_type_no\":\"113\",\"tax\":2.24},\"http://i0.hdslb.com/bfs/game/c642c12fcacdd90060d523eb8cc89a470bc6ad47.pdf\":{\"attach_id\":\"e04fcc8b-2297-4d5d-ab9a-c78dfaadd095\",\"bill_date\":\"2024年04月15日\",\"bill_code\":\"************\",\"bill_no\":\"********\",\"fee\":97.40,\"fee_without_tax\":94.56,\"receipt_type_no\":\"10\",\"tax\":2.84},\"http://i0.hdslb.com/bfs/game/98a6e8963218c344191b554f293f56b8f8b49c6a.pdf\":{\"attach_id\":\"035931d3-734d-408e-b184-a60821454b6a\",\"bill_date\":\"2021年06月03日\",\"bill_code\":\"************\",\"bill_no\":\"********\",\"fee\":149.28,\"fee_without_tax\":144.93,\"receipt_type_no\":\"10\",\"tax\":4.35}},\"withdraw_amt_in_cny\":4.40,\"accrual_id2_amt_in_cny\":{\"IAA_YT_697006404912230400\":4.40},\"contract_number\":\"HTSQ2022072800017\",\"bank_number\":\"***************\",\"payee\":\"S_131066\",\"business_code\":\"IAA_FK_697655633276129280\"},\"expense_code\":\"\",\"expense_message\":\"创建付款单失败: 传入的发票（************-********）:查验失败 检验码不能为空，无法生成费用\"}";
        ExpenseExtra expenseExtra = JsonUtil.readValue(extra, ExpenseExtra.class);

        RequestBody body = Try.of(() -> {
            Object request = expenseExtra.getParams()
                    .toHuilianyiExpenseCreateRequest(new HuilianyiExpenseParams());

            RequestBody b = toRequestBody(request);

            Thread.sleep(1000L);

            return b;
        }).get();

        System.out.println(body);

        com.bilibili.miniapp.open.common.entity.Response<HuilianyiAsyncTask> r = huilianyiPaymentApi.createExpenseAsync(
                "Bearer " + getToken(),
                body
        ).execute().body();

        System.out.println("createAsync result: " + r);

        Thread.sleep(5000);

        while (true) {

            com.bilibili.miniapp.open.common.entity.Response<HuilianyiAsyncTaskResult> probe = huilianyiPaymentApi.probeTask(
                    r.getData().getTaskId()).execute().body();

            System.out.println("probe result: " + probe);

            if (probe.getData().isSuccess() || probe.getData().isFailed()) {
                break;
            }

            Thread.sleep(2000);

        }

    }


    protected RequestBody toRequestBody(Object obj) {
        String jsonString = JSON.toJSONString(obj);
        return RequestBody.Companion.create(jsonString, MediaType.parse("application/json"));
    }
}