package com.bilibili.miniapp.open.service.util;

import static org.junit.Assert.*;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import org.junit.Test;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/4/10
 */
public class ForkJoinUtilTest {

    @Test
    public void runnableForkJoin() {

        ForkJoinUtil.runnableForkJoin(
                List.of(
                        () -> {
                            System.out.println("Task 1");
                        },
                        () -> {
                            System.out.println("Task 2");
                        },
                        () -> {
                            System.out.println("Task 3");
                        }
                ),
                Executors.newFixedThreadPool(10)
        );
    }


    @Test
    public void runnableForkJoin2() {

        ForkJoinUtil.runnableForkJoin(
                List.of(
                        () -> {
                            System.out.println("Task 1");
                        },
                        () -> {
                            System.out.println("Task 2");
                        },
                        () -> {
                            throw new IllegalArgumentException("Task 3 failed");
                        }
                ),
                Executors.newFixedThreadPool(10)
        );
    }


}